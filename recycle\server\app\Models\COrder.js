'use strict'

const Model = use('Model')

class COrder extends Model {
  static get table() {
    return 'c_order'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return 'updatedAt'
  }
  worker() {
    return this.belongsTo('App/Models/Worker', 'workerID', 'id')
  }
  company() {
    return this.belongsTo('App/Models/Company', 'companyID', 'id')
  }
  cancel() {
    return this.hasMany('App/Models/ClientOrderCancel', 'id', 'orderID').with('worker')
  }
  sales() {
    return this.belongsTo('App/Models/Salesman', 'salesman_id', 'id').with('manager')
  }
  doneInfo() {
    return this.hasOne('App/Models/WorkerWalletLog', 'id', 'orderID')
  }
  setImages(value) {
    return JSON.stringify(value)
  }
  getImages(value) {
    return JSON.parse(value)
  }
}

module.exports = COrder
