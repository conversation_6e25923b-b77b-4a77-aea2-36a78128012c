import { Button, Tabs, Tag, Space, message, Input, Select, notification, Modal, Upload, Card, List, Descriptions, Spin, Typography, Empty, Table, Drawer, Timeline, Tooltip } from 'antd'
import ProCard from '@ant-design/pro-card'
import { effect, reducer, requestGet, useConnect } from 'dva17'
import { useEffect, useRef, useState } from 'react'
import { EDelPrice, EGet, EGetCommission, EGetCommissionLogs, EGetPrice, EPostPrice, EPutPrice, NHiOrder, } from '../../common/action'
import { computeAuthority } from '../../utils/Authorized/authority'
import type { ActionType, ProColumns } from '@ant-design/pro-table'
import ProTable, { } from '@ant-design/pro-table'
import ProForm, { ProFormInstance, ProFormSelect, ProFormText } from '@ant-design/pro-form'
import { PlusOutlined, DownloadOutlined, UploadOutlined, InboxOutlined, HistoryOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import {  SourceLevels, typeList } from '../../common/enum'
import { stringify } from 'qs'
import { SERVER_HOME } from '../../common/config'
import moment from 'moment'

const { Option } = Select
const { TabPane } = Tabs
const { Dragger } = Upload

// 添加修改记录的类型定义
type CommissionLog = {
  id: number
  priceSetID: number
  adminID: number
  action: 'create' | 'update' | 'delete'
  before_data: string | null
  after_data: string | null
  remark: string
  createdAt: string
  updatedAt?: string
  admin?: {
    id: number
    username: string
    name: string
    level: string
  }
}

type CommissionItem = {
  id: number
  area: string
  type: string
  province: string
  city: string
  source: string
  price: number
  level?: string
}

export default () => {
  const [visible, setVisible] = useState<boolean>(false)
  const [showData, setShowData] = useState<any>(null)
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>()
  const [activeTabKey, setActiveTabKey] = useState<string>('tableView')
  const [groupedData, setGroupedData] = useState<{ [key: string]: CommissionItem[] }>({})
  const [loading, setLoading] = useState<boolean>(false)
  const [importModalVisible, setImportModalVisible] = useState<boolean>(false)
  const [importResult, setImportResult] = useState<any>(null)
  const proTableParamsRef = useRef<any>({})

  // 修改记录相关状态
  const [logsVisible, setLogsVisible] = useState<boolean>(false)
  const [logsLoading, setLogsLoading] = useState<boolean>(false)
  const [currentLogs, setCurrentLogs] = useState<CommissionLog[]>([])
  const [currentLogItem, setCurrentLogItem] = useState<CommissionItem | null>(null)

  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    if (activeTabKey === 'groupedView') {
      fetchDataForGroupedView()
    }
  }, [activeTabKey])

  /*--------------------- 响应 ---------------------*/
  const fetchDataForGroupedView = async () => {
    setLoading(true)
    try {
      // 获取所有数据，不分页
      const response = await effect(NHiOrder, EGetPrice, { pageSize: 1000 }) as any
      const data = response?.data || []

      // 按品类分组
      const grouped: { [key: string]: CommissionItem[] } = {}
      data.forEach((item: CommissionItem) => {
        if (!grouped[item.type]) {
          grouped[item.type] = []
        }
        grouped[item.type].push(item)
      })

      setGroupedData(grouped)
    } catch (error) {
      notification.error({
        message: '获取数据失败',
        description: '无法获取佣金配置分组数据，请稍后重试。'
      })
    } finally {
      setLoading(false)
    }
  }

  const noticeAction = (isSuccess: boolean, type: '修改' | '删除' | '新建' | '操作') => {
    const messageContent = `${type}${isSuccess ? '成功！' : '失败！'}`;
    if (isSuccess) {
      notification.success({
        message: '成功！',
        description: messageContent,
        duration: 2
      });
    } else {
      notification.error({
        message: '失败！',
        description: messageContent,
        duration: 2
      });
    }
    // 根据当前视图刷新数据
    refreshCurrentView();
  };

  const refreshCurrentView = () => {
    if (activeTabKey === 'tableView') {
      if (actionRef.current) {
        actionRef.current.reload();
      }
    } else {
      fetchDataForGroupedView();
    }
  };

  const handleEdit = async (row: any) => {
    setVisible(true)
    setShowData(row)
  }

  const handleDel = async (row: any) => {
    Modal.confirm({
      title: '确认删除该数据',
      content: <div>删除后数据无法恢复！<br /><b>区域:</b> {row.area}, <b>品类:</b> {row.type}, <b>平台:</b> {row.source}</div>,
      okText: '确认删除',
      cancelText: '取消',
      onOk: async () => {
        try {
          await effect(NHiOrder, EDelPrice, { id: row.id })
          noticeAction(true, '删除')
        } catch (error) {
          noticeAction(false, '删除')
        }
      },
      width: 500,
    })
  }

  // 查看修改记录
  const handleViewLogs = async (row: CommissionItem) => {
    setLogsLoading(true)
    setCurrentLogItem(row)
    setLogsVisible(true)

    try {
      const response = await effect(NHiOrder, EGetCommissionLogs, { id: row.id }) as any
      setCurrentLogs(response?.data || [])
    } catch (error) {
      console.error('获取修改记录失败:', error)
      notification.error({
        message: '获取修改记录失败',
        description: '无法获取佣金配置修改记录，请稍后重试。'
      })
    } finally {
      setLogsLoading(false)
    }
  }

  // 格式化修改记录数据
  const formatLogData = (log: CommissionLog) => {
    const beforeData = log.before_data ? JSON.parse(log.before_data) : null
    const afterData = log.after_data ? JSON.parse(log.after_data) : null
    const changes = []

    if (log.action === 'create') {
      if (afterData) {
        return `创建价格配置: ${afterData.type || ''} - ${afterData.province || ''} - ${afterData.city || ''} - ¥${afterData.price || 0}`
      }
      return '创建价格配置'
    } else if (log.action === 'delete') {
      if (beforeData) {
        return `删除价格配置: ${beforeData.type || ''} - ${beforeData.province || ''} - ${beforeData.city || ''} - ¥${beforeData.price || 0}`
      }
      return '删除价格配置'
    } else if (log.action === 'update') {
      // 比较变更内容
      if (beforeData && afterData) {
        if (beforeData.type !== afterData.type) {
          changes.push(`品类: ${beforeData.type || '空'} → ${afterData.type || '空'}`)
        }
        if (beforeData.province !== afterData.province) {
          changes.push(`省份: ${beforeData.province || '空'} → ${afterData.province || '空'}`)
        }
        if (beforeData.city !== afterData.city) {
          changes.push(`城市: ${beforeData.city || '空'} → ${afterData.city || '空'}`)
        }
        if (beforeData.town !== afterData.town) {
          changes.push(`区域: ${beforeData.town || '空'} → ${afterData.town || '空'}`)
        }
        if (beforeData.price !== afterData.price) {
          changes.push(`价格: ¥${beforeData.price || 0} → ¥${afterData.price || 0}`)
        }

        return changes.length > 0 ? changes.join(', ') : '无变更内容'
      }
      return '更新价格配置'
    }

    return log.remark || '未知操作'
  }

  const handleOK = async () => {
    try {
      const values = await formRef.current?.validateFieldsReturnFormatValue?.();
      if (!values) return;

      if (!values.type || values.price === undefined || values.price === null || !values.source) {
        message.error({ content: '区域、品类、平台和价格均为必填项!' });
        return;
      }

      let success = false;
      if (showData && showData.id) {
        await effect(NHiOrder, EPutPrice, { id: showData.id, ...values });
        success = true;
      } else {
        await effect(NHiOrder, EPostPrice, { ...values });
        success = true;
      }

      if (success) {
        setVisible(false);
        setShowData(null);
        noticeAction(success, showData ? '修改' : '新建');
      }
    } catch (error) {
      console.error('表单提交错误:', error);
      message.error('请检查表单输入项!');
    }
  };

  // 导出Excel功能
  const handleExport = async () => {
    try {
      const params = { ...proTableParamsRef.current };
      delete params.current;
      delete params.pageSize;

      const queryString = stringify(params);
      const exportUrl = `${SERVER_HOME}priceSetexport?${queryString}`;

      const link = document.createElement('a');
      link.href = exportUrl;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      notification.success({
        message: '导出任务已开始',
        description: '文件将很快开始下载。',
        duration: 3,
      });
    } catch (error) {
      console.error('导出佣金数据失败:', error);
      notification.error({
        message: '导出失败',
        description: '无法导出佣金数据，请稍后重试。',
      });
    }
  };


  const columns: ProColumns<CommissionItem>[] = [

    {
      title: '省份',
      dataIndex: 'province',
      copyable: true,
      ellipsis: true
    },
    {
      title: '城市',
      dataIndex: 'city',
      copyable: true,
      ellipsis: true
    },
    {
      title: '区域',
      dataIndex: 'area',
      copyable: true,
      ellipsis: true
    },
    {
      title: '品类',
      dataIndex: 'type',
      copyable: true,
      ellipsis: true,
    },
    {
      title: '平台',
      dataIndex: 'source',
      copyable: true,
      ellipsis: true,
    },
    {
      title: '信息费',
      dataIndex: 'price',
      valueType: 'money',
      align: 'right',
      copyable: false,
      search: false,
      ellipsis: true
    },
    {
      title: '操作',
      width: '220px',
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      render: (_, row: any) => [
        <Button
          key="edit"
          type="link"
          onClick={() => handleEdit(row)}
          disabled={!computeAuthority('佣金编辑')}
        >
          修改
        </Button>,
        <Button
          key="delete"
          type="link"
          danger
          onClick={() => handleDel(row)}
          disabled={!computeAuthority('佣金编辑')}
        >
          删除
        </Button>,
        <Button
          key="logs"
          type="link"
          icon={<HistoryOutlined />}
          onClick={() => handleViewLogs(row)}
        >
          记录
        </Button>
      ],
    },
  ];

  // 渲染分组卡片视图
  const renderGroupedView = () => {
    return (
      <div style={{ marginTop: 16 }}>
        {Object.keys(groupedData).length === 0 && !loading ? (
          <Empty description="暂无数据" />
        ) : (
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
            {Object.entries(groupedData).map(([type, items]) => (
              <Card
                key={type}
                title={`${type} (${items.length})`}
                style={{ width: 400, marginBottom: 16 }}
                extra={
                  <Button
                    type="link"
                    onClick={() => {
                      setShowData({ type });
                      setVisible(true);
                    }}
                    disabled={!computeAuthority('佣金编辑')}
                  >
                    新建
                  </Button>
                }
              >
                <List
                  dataSource={items}
                  renderItem={item => (
                    <List.Item
                      key={item.id}
                      actions={[
                        <Button
                          key="edit"
                          type="link"
                          onClick={() => handleEdit(item)}
                          disabled={!computeAuthority('佣金编辑')}
                        >
                          修改
                        </Button>,
                        <Button
                          key="delete"
                          type="link"
                          danger
                          onClick={() => handleDel(item)}
                          disabled={!computeAuthority('佣金编辑')}
                        >
                          删除
                        </Button>,
                        <Button
                          key="logs"
                          type="link"
                          onClick={() => handleViewLogs(item)}
                        >
                          记录
                        </Button>,
                      ]}
                    >
                      <List.Item.Meta
                        title={`${item.source || ''} - ${item.province || ''} - ${item.city || ''} - ${item.area || ''}`}
                        description={<div style={{ color: '#f50' }}>价格: ¥{item.price}</div>}
                      />
                    </List.Item>
                  )}
                />
              </Card>
            ))}
          </div>
        )}
      </div>
    );
  };

  /*--------------------- 渲染 ---------------------*/
  return (
    <>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
        <div>
          <Button
            icon={<DownloadOutlined />}
            onClick={handleExport}
            style={{ marginRight: 8 }}
            disabled={!computeAuthority('佣金查看')}
          >
            导出 Excel
          </Button>

        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setShowData(null);
            setVisible(true);
          }}
          disabled={!computeAuthority('佣金编辑')}
        >
          新建佣金
        </Button>
      </div>

      <Tabs activeKey={activeTabKey} onChange={setActiveTabKey}>
        <TabPane tab="表格视图" key="tableView">
          <ProCard>
            <ProTable<CommissionItem>
              actionRef={actionRef}
              columns={columns}
              request={async (params = {}, sort, filter) => {
                // 保存当前查询参数（不含分页）给导出功能使用
                const { current, pageSize, ...restParams } = params;
                proTableParamsRef.current = restParams;

                // 排序处理
                let sortParam = {};
                if (sort) {
                  const sortField = Object.keys(sort)[0];
                  const sortOrder = Object.values(sort)[0] === 'ascend' ? 'asc' : 'desc';
                  if (sortField && sortOrder) {
                    params.sorter = `${sortField}_${sortOrder}`;
                  }
                }

                const response = await effect(NHiOrder, EGetPrice, { ...params }) as any;
                return {
                  data: response?.data || [],
                  total: response?.total || 0,
                  success: true,
                };
              }}
              pagination={{
                showSizeChanger: true,
              }}
              rowKey="id"
              search={{
                labelWidth: 'auto',
              }}
              dateFormatter="string"
              headerTitle="佣金配置列表"
              toolBarRender={() => [
                <Tooltip title={
                  <div>
                    {/* 区域：全部 + 空气热泵热水器 = 全国统一价格<br/>
                    陕西省, 山东省, 山西省, 江西省, 贵州省 省统一价格<br/>
                    海南省 三亚市, 海口市  市统一价格<br/>
                    海南省 其他 海南省价<br/>
                    四川 重庆 城市等级价格<br/>
                    昆明市 大理 武汉市 市统一价 <br/>
                    云南省 湖北省  省统一价<br/>
                    其他地方 省统一价格<br/> */}
                  </div>
                }
                  color={"#2db7f5"} key={"#2db7f5"}>
                  <Button icon={<QuestionCircleOutlined />} />
                </Tooltip>
                ,
                <Button key="3" type="primary"
                  disabled={!computeAuthority('佣金编辑')}
                  onClick={() => {
                    setVisible(true)
                    setShowData(null)
                  }}>
                  <PlusOutlined />
                  新建
                </Button>,
              ]}
            />
          </ProCard>
        </TabPane>
        <TabPane tab="卡片分组视图" key="groupedView">
          {loading ? <Spin size="large" /> : renderGroupedView()}
        </TabPane>
      </Tabs>

      {/* 佣金新建/编辑弹窗 */}
      <Modal
        destroyOnClose={true}
        open={visible}
        title={`${showData?.id ? '编辑佣金' : '新建佣金'}`}
        onCancel={() => {
          setVisible(false)
          setShowData(null)
        }}
        onOk={handleOK}
        width={600}
      >
        <div style={{ width: '100%' }}>
          <ProForm
            submitter={false}
            formRef={formRef}
          >
            <ProFormText
              initialValue={showData?.province}
              width="md"
              name="province"
              label="省份"
              placeholder="请输入省份"
            />
            <ProFormText
              initialValue={showData?.city}
              width="md"
              name="city"
              label="城市"
              placeholder="请输入城市"
            />
            <ProFormText
              initialValue={showData?.area}
              width="md"
              name="area"
              label="区域"
              placeholder="请输入区域"
            />
            <ProFormText
              initialValue={showData?.type}
              name="type"
              width="md"
              label="品类"
              placeholder="请选择品类"
              rules={[{ required: true, message: '请选择一个品类!' }]}
            />
            <ProFormText
              initialValue={showData?.source}
              name="source"
              width="md"
              label="平台"
              placeholder="请选择平台"
              rules={[{ required: true, message: '请选择一个平台!' }]}
            />
            <ProFormText
              initialValue={showData?.price}
              width="md"
              name="price"
              label="价格"
              placeholder="请输入价格"
              rules={[
                { required: true, message: '价格不能为空!' }
              ]}
              fieldProps={{ type: 'number', step: '0.01' }}
            />
          </ProForm>
        </div>
      </Modal>



      {/* 修改记录抽屉 */}
      <Drawer
        title={
          <div>
            <Typography.Title level={4} style={{ margin: 0 }}>
              价格配置记录
            </Typography.Title>
            <Typography.Text type="secondary">
              {currentLogItem?.type} - {currentLogItem?.province} - {currentLogItem?.city}
            </Typography.Text>
          </div>
        }
        placement="right"
        width={700}
        onClose={() => {
          setLogsVisible(false)
          setCurrentLogs([])
          setCurrentLogItem(null)
        }}
        open={logsVisible}
        extra={
          <Space>
            <Typography.Text type="secondary">
              共 {currentLogs.length} 条记录
            </Typography.Text>
          </Space>
        }
      >
        {logsLoading ? (
          <div style={{ display: 'flex', justifyContent: 'center', padding: '40px 0' }}>
            <Spin size="large" />
          </div>
        ) : currentLogs.length === 0 ? (
          <Empty description="暂无修改记录" />
        ) : (
          <Timeline mode="left">
            {currentLogs.map(log => (
              <Timeline.Item
                key={log.id}
                color={log.action === 'create' ? 'green' : log.action === 'update' ? 'blue' : 'red'}
              >
                <Card 
                  size="small" 
                  style={{ 
                    marginBottom: 16,
                    border: `1px solid ${log.action === 'create' ? '#52c41a' : log.action === 'update' ? '#1890ff' : '#ff4d4f'}15`
                  }}
                >
                  <div style={{ marginBottom: 8 }}>
                    <Space>
                      <Tag color={log.action === 'create' ? 'green' : log.action === 'update' ? 'blue' : 'red'}>
                        {log.action === 'create' ? '创建' : log.action === 'update' ? '更新' : '删除'}
                      </Tag>
                      <Typography.Text type="secondary">
                        {moment(log.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                      </Typography.Text>
                    </Space>
                  </div>
                  
                  <div style={{ marginBottom: 8 }}>
                    <Typography.Text>
                      {formatLogData(log)}
                    </Typography.Text>
                  </div>

                  {log.remark && (
                    <div style={{ marginBottom: 8 }}>
                      <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                        备注: {log.remark}
                      </Typography.Text>
                    </div>
                  )}
                  
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                      操作人: {log.admin?.name || log.admin?.username || '未知'}
                      {log.admin?.level && ` (${log.admin.level})`}
                    </Typography.Text>
                    
                    {(log.before_data || log.after_data) && (
                      <Button 
                        type="link" 
                        size="small" 
                        onClick={() => {
                          const beforeData = log.before_data ? JSON.parse(log.before_data) : null
                          const afterData = log.after_data ? JSON.parse(log.after_data) : null
                          
                          Modal.info({
                            title: '详细变更内容',
                            width: 600,
                            content: (
                              <div>
                                {beforeData && (
                                  <div style={{ marginBottom: 16 }}>
                                    <Typography.Title level={5}>修改前:</Typography.Title>
                                    <pre style={{ backgroundColor: '#f5f5f5', padding: 8, borderRadius: 4 }}>
                                      {JSON.stringify(beforeData, null, 2)}
                                    </pre>
                                  </div>
                                )}
                                {afterData && (
                                  <div>
                                    <Typography.Title level={5}>修改后:</Typography.Title>
                                    <pre style={{ backgroundColor: '#f5f5f5', padding: 8, borderRadius: 4 }}>
                                      {JSON.stringify(afterData, null, 2)}
                                    </pre>
                                  </div>
                                )}
                              </div>
                            )
                          })
                        }}
                      >
                        查看详情
                      </Button>
                    )}
                  </div>
                </Card>
              </Timeline.Item>
            ))}
          </Timeline>
        )}
      </Drawer>
    </>
  )
}
