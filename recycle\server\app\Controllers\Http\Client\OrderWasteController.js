'use strict'

const _ = require('lodash')
const moment = require('moment')

const {OrderWaste} = require('../../../Models')
const {ERR, E} = require('../../../../../../constants')

//订单废品关联表
class OrderWasteController {
	async index ({request, response}) {
		let {page=1, perPage=10} = request.all()
		let query = OrderWaste.query()
		let vo = await query.paginate(page, perPage)
		response.json(vo)
	}
	async show({request, params, response}) {
		let vo = await OrderWaste.query().where('id', params.id).first()
		if (!vo){
			throw ERR.RESTFUL_GET_ID
		}
		response.json(vo)
	}
	async store ({request, response}) {
		let {orderID, wasteID, wasteTypeID, wasteAttribute1ID, wasteAttribute2ID, wasteAttribute3ID, wasteAttribute4ID, wasteAttribute5ID} = request.all()
		if (!orderID || !wasteID || !wasteTypeID || !wasteAttribute1ID || !wasteAttribute2ID || !wasteAttribute3ID || !wasteAttribute4ID || !wasteAttribute5ID){
			throw ERR.INVALID_PARAMS
		}
		let vo = await OrderWaste.create({orderID, wasteID, wasteTypeID, wasteAttribute1ID, wasteAttribute2ID, wasteAttribute3ID, wasteAttribute4ID, wasteAttribute5ID})
		response.json(vo)
	}
	async update ({request, params, response}) {
		let vo = await OrderWaste.find(params.id)
		if (!vo){
			throw ERR.RESTFUL_UPDATE_ID
		}
		_.assign(vo, request.all())
		await vo.save()
		response.json(vo)
	}
	async destroy ({request, params, response}) {
		let vo = await OrderWaste.find(params.id)
		if (!vo){
			throw ERR.RESTFUL_DELETE_ID
		}
		await vo.delete()
		response.json(vo)
	}
}

module.exports = OrderWasteController