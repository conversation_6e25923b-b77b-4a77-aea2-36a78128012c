import Taro from '@tarojs/taro'
import React, { Component } from 'react'
import T from '../../config/T'
import { connect } from 'react-redux'
import { View, Image, Button, Text, Checkbox } from '@tarojs/components'
import { AtModal } from 'taro-ui'
import './successfulOrder.less'
import E from '../../config/E'

class SuccessfulOrder extends Component {
  constructor() {
    super(...arguments)
    this.state = {
      step: 0,
      locale: 'zh_CN',
      statusAry: [T.successfulOrderPage.status_1, T.successfulOrderPage.status_2, T.successfulOrderPage.status_3],
    }
  }

  config = {}

  //-----------------------生命周期-------------------------//
  componentWillMount() { }

  componentDidMount() {
    const locale = Taro.getStorageSync('locale')
    this.setState({ locale })
    Taro.setNavigationBarTitle({ title: locale == 'en' ? 'Success' : '预约确认' })
  }

  componentWillUnmount() {
    Taro.reLaunch({ url: '/pages/home/<USER>' })
  }

  componentDidShow() { }

  componentDidHide() { }

  //-----------------------事件-------------------------//

  //-----------------------渲染-------------------------//
  render() {
    let { step, statusAry } = this.state
    let { orderID } = this.props
    // const {haveChosenClothing, haveChosenPage} = this.props.oldGoods
    return (
      <View className="successfulOrder">
        <View className="step_wrapper">
          <View className="step_icon_wrapper">
            {[1, 2, 3].map((point, index) => (
              <View
                className="step_point_wrapper"
                key={index + point}
                style={index === step ? { background: '#f0a6ae' } : null}
              >
                <View className="point" style={index === step ? { background: '#15b381' } : null}></View>
              </View>
            ))}
            {[1, 2].map((line, index) => (
              <View className="step_line" key={index + line} style={index === step ? { left: '48rpx' } : { left: '294rpx' }}></View>
            ))}
          </View>
          <View className="step_text_wrapper">
            {statusAry.map((text, index) => (
              <Text key={index + text} style={step === index ? { color: '#333333', fontWeight: 500 } : null}>
                {text}
              </Text>
            ))}
          </View>
        </View>

        <Image src={('https://oss.evergreenrecycle.cn/donggua/client/images/chenggong.png')} className="successful_Icon" />

        <Text className="successful_title">{T.successfulOrderPage.success}</Text>
        <View className="successful_des">
          <Text>{T.successfulOrderPage.contact_1}</Text>
          <Text>{T.successfulOrderPage.contact_2}</Text>
        </View>

        <View
          className="successful_button"
          style={{ marginTop: '70px' }}
          onClick={() => {
            Taro.navigateTo({ url: `/pages/orderDetail/orderDetail?orderNumber=${orderID}&manage=${E.OrderStatus.Reservation}` })
          }}
        >
          {T.successfulOrderPage.viewDetails}
        </View>
        <View
          className="successful_button"
          style={{ background: '#ccc', color: '#7C8696' }}
          onClick={() => {
            Taro.reLaunch({ url: '/pages/home/<USER>' })
          }}
        >
          {T.successfulOrderPage.backHome}
        </View>
        <Text
          className="successful_cancel"
          onClick={() => {
            Taro.navigateTo({ url: `/pages/cancleOrder/cancleOrder?orderID=${orderID}&cancle=true&manage=` })
          }}
        >
          {T.successfulOrderPage.cancel}
        </Text>
      </View>
    )
  }
}

export default connect(({ NOrder: { orderID } }) => ({ orderID }))(SuccessfulOrder)
