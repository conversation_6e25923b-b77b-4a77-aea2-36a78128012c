import Taro from '@tarojs/taro'
import React, { Component } from 'react'
import T from '../../config/T'
import { connect } from 'react-redux'
import { View, Image, Button, Text, Checkbox, ScrollView, Icon } from '@tarojs/components'
import { AtModal, AtActivityIndicator, AtBadge } from 'taro-ui'
// import 'taro-ui/dist/style/components/badge.scss'
import './orderManage.less'
import E from '../../config/E'
import dayjs from 'dayjs'
import icon from '../../assets/icon/index'

class OrderManage extends Component {
  constructor() {
    super(...arguments)
    this.state = {
      locale: 'zh_CN',
      whichManage: '预订',
      user: null,
      dargStyle: {
        //下拉框的样式
        top: 0 + 'px',
      },
      downDragStyle: {
        //下拉图标的样式
        height: 0 + 'px',
      },
      downText: '下拉刷新',
      upDragStyle: {
        //上拉图标样式
        height: 0 + 'px',
      },
      pullText: '上拉加载更多',
      start_p: {},
      scrollY: true,
      dargState: 0, //刷新状态 0不做操作 1刷新 -1加载更多
      currentPage: 1,
      historyList: null,
      orderList: null,
      readList: [],
    }
  }

  config = {}

  //-----------------------生命周期-------------------------//
  componentWillMount() {
    let params = Taro.getCurrentInstance().router.params.manage
    if (params) {
      this.setState({
        whichManage: params,
      })
    }
  }

  componentDidMount() {
    const locale = Taro.getStorageSync('locale')
    let user = Taro.getStorageSync('userInfo') && JSON.parse(Taro.getStorageSync('userInfo'))
    this.setState({ user, locale })
    setTimeout(() => {
      this.getOrderList()
    })
    Taro.setNavigationBarTitle({ title: locale == 'en' ? 'Order Manager' : '订单管理' })
  }

  componentWillReceiveProps(nextProps) {
    if (!this.props.isOrderListGet && nextProps.isOrderListGet) {
      let { historyList } = this.state
      let { orderList } = nextProps
      if (historyList) {
        orderList.data = [...historyList.data, ...orderList.data]
      }
      this.setState({
        orderList,
      })
    }
  }

  componentWillUnmount() {
    this.props.dispatch({
      type: 'NOrder/EGetAllOrder',
      payload: {
        userID: this.state.user.id,
      },
    })
  }

  componentDidShow() { }

  componentDidHide() { }

  //-----------------------事件-------------------------//
  reduction() {
    //还原初始设置
    const time = 0.5
    this.setState({
      upDragStyle: {
        //上拉图标样式
        height: 0 + 'px',
        transition: `all ${time}s`,
      },
      dargState: 0,
      dargStyle: {
        top: 0 + 'px',
        transition: `all ${time}s`,
      },
      downDragStyle: {
        height: 0 + 'px',
        transition: `all ${time}s`,
      },
      scrollY: true,
    })
    setTimeout(() => {
      this.setState({
        dargStyle: {
          top: 0 + 'px',
        },
        upDragStyle: {
          //上拉图标样式
          height: 0 + 'px',
        },
        pullText: '上拉加载更多',
        downText: '下拉刷新',
      })
    }, time * 1000)
  }
  touchStart = e => {
    this.setState({
      start_p: e.touches[0],
    })
  }
  touchmove(e) {
    let that = this
    let move_p = e.touches[0], //移动时的位置
      deviationX = 0.3, //左右偏移量(超过这个偏移量不执行下拉操作)
      deviationY = 70, //拉动长度（低于这个值的时候不执行）
      maxY = 100 //拉动的最大高度

    let start_x = this.state.start_p.clientX,
      start_y = this.state.start_p.clientY,
      move_x = move_p.clientX,
      move_y = move_p.clientY

    //得到偏移数值
    let dev = Math.abs(move_x - start_x) / Math.abs(move_y - start_y)
    if (dev < deviationX) {
      //当偏移数值大于设置的偏移数值时则不执行操作
      let pY = Math.abs(move_y - start_y) / 3.5 //拖动倍率（使拖动的时候有粘滞的感觉--试了很多次 这个倍率刚好）
      if (move_y - start_y > 0) {
        //下拉操作
        if (pY >= deviationY) {
          this.setState({ dargState: 1, downText: '释放刷新' })
        } else {
          this.setState({ dargState: 0, downText: '下拉刷新' })
        }
        if (pY >= maxY) {
          pY = maxY
        }
        this.setState({
          dargStyle: {
            top: pY + 'px',
          },
          downDragStyle: {
            height: pY + 'px',
          },
          scrollY: false, //拖动的时候禁用
        })
      }
      if (start_y - move_y > 0) {
        //上拉操作
        if (pY >= deviationY) {
          this.setState({ dargState: -1, pullText: '释放加载更多' })
        } else {
          this.setState({ dargState: 0, pullText: '上拉加载更多' })
        }
        if (pY >= maxY) {
          pY = maxY
        }
        this.setState({
          dargStyle: {
            top: -pY + 'px',
          },
          upDragStyle: {
            height: pY + 'px',
          },
          scrollY: false, //拖动的时候禁用
        })
      }
    }
  }
  pull() {
    //上拉
    let { currentPage, historyList, orderList } = this.state
    this.setState({
      historyList: orderList,
    })
    if (orderList.lastPage > currentPage) {
      this.setState({ currentPage: currentPage + 1 })
      setTimeout(() => {
        this.getOrderList()
      })
    }
  }
  down() {
    //下拉
    console.log('下拉')
    // this.props.onDown()
  }
  ScrollToUpper() {
    //滚动到顶部事件
    console.log('滚动到顶部事件')
    // this.props.Upper()
  }
  ScrollToLower() {
    //滚动到底部事件
    console.log('滚动到底部事件')
    // this.props.Lower()
  }
  touchEnd(e) {
    if (this.state.dargState === 1) {
      this.down()
    } else if (this.state.dargState === -1) {
      this.pull()
    }
    this.reduction()
  }

  changeManage(index) {
    this.setState({
      whichManage: index,
      currentPage: 1,
      historyList: null,
      orderList: null,
    })
    setTimeout(() => {
      this.getOrderList()
    })
  }

  getOrderList() {
    let { user, whichManage, currentPage } = this.state
    this.props.dispatch({
      type: 'NOrder/EGetOrderList',
      payload: {
        userID: user.id,
        status: whichManage,
        sort: 'desc',
        page: currentPage,
      },
    })
  }

  orderDetail(e, index) {
    let { readList } = this.state
    readList.push(index)
    this.setState({ readList })
    setTimeout(() => {
      Taro.navigateTo({ url: `/pages/orderDetail/orderDetail?orderNumber=${index}&manage=${this.state.whichManage}` })
    })
  }

  call(e, phone) {
    Taro.makePhoneCall({
      phoneNumber: phone,
    })
  }

  onScrollToUpper() { }

  // or 使用箭头函数
  // onScrollToUpper = () => {}

  onScroll(e) {
  }

  //-----------------------渲染-------------------------//
  render() {
    let dargStyle = this.state.dargStyle
    let downDragStyle = this.state.downDragStyle
    let upDragStyle = this.state.upDragStyle

    let { whichManage, orderList, currentPage, readList, locale } = this.state
    return (
      <View className="orderManage">
        <View className="top_title">
          {[
            { title: T.booked, code: E.OrderStatus.Reservation },
            { title: T.inProgress, code: E.OrderStatus.InProgress },
            { title: T.completed, code: E.OrderStatus.Completed },
            { title: T.canceled, code: E.OrderStatus.Cancelled },
          ].map((value, index) => (
            <View
              className="manage_item"
              onClick={() => {
                this.changeManage(value.code)
              }}
              key={index + value}
            >
              <View className="item_wrapper" style={whichManage === value.code ? { color: '#333333' } : null}>
                {value.title}
                <View className="item_mark" style={whichManage !== value.code ? { background: '#fff' } : null}>
                  {whichManage == value.code && <Image mode="widthFix" src={'https://oss.evergreenrecycle.cn/donggua/client/images/activate.png'} style={{ width: '36rpx' }} />}
                </View>
              </View>
            </View>
          ))}
        </View>
        <View className="downDragBox" style={downDragStyle}>
          <AtActivityIndicator></AtActivityIndicator>
          <Text className="downText">{this.state.downText}</Text>
        </View>
        <ScrollView
          style={dargStyle}
          onTouchMove={e => {
            this.touchmove(e)
          }}
          onTouchEnd={e => {
            this.touchEnd(e)
          }}
          onTouchStart={e => {
            this.touchStart(e)
          }}
          onScrollToUpper={e => {
            this.ScrollToUpper(e)
          }}
          onScrollToLower={e => {
            this.ScrollToLower(e)
          }}
          className="dragUpdata"
          scrollY={this.state.scrollY}
          scrollWithAnimation
        >
          <View className="make_content"></View>
          {orderList &&
            orderList.data.length > 0 &&
            orderList.data.map((value, index) => (
              <View className="order_item" key={index + value}>
                {/* <AtBadge
                  dot={whichManage === E.OrderStatus.Completed && value.isRead === 0 && !readList.includes(value.id) ? true : false}
                ></AtBadge> */}
                <View className="line1">
                  <View>
                    <Image src={'https://oss.evergreenrecycle.cn/donggua/client/images/state1.png'} mode="widthFix" style={{ width: '32rpx' }} />
                    <View>
                      {value.workTime
                        ? `${dayjs(value.workTime).format('YYYY-MM-DD')} 
                      ${dayjs(value.workTime).format('HH:mm') === '09:00'
                          ? `${locale == 'en' ? 'Morning' : '上午'}`
                          : `${locale == 'en' ? 'Afternoon' : '下午'}`
                        }`
                        : T.orderManagePage.spotRecycling}
                    </View>
                  </View>

                  <View className={`${whichManage === E.OrderStatus.Cancelled && 'cancelled'}`}>
                    {whichManage === E.OrderStatus.Cancelled
                      ? T.canceled
                      : whichManage === E.OrderStatus.Completed
                        ? T.completed
                        : whichManage === E.OrderStatus.InProgress
                          ? T.inProgress
                          : T.booked}
                  </View>
                </View>

                <View className="line2">
                  <Text>
                    {value.waste_1st_ID
                      ? value.waste_1st_ID === 1
                        ? locale == 'en'
                          ? 'Used BooksClothes'
                          : '旧衣旧书'
                        : value.waste_1st_ID === 3
                          ? locale == 'en'
                            ? 'Appliances'
                            : '大家电'
                          : value.waste_1st_ID === 2
                            ? locale == 'en'
                              ? 'Waste'
                              : '生活废品'
                            : locale == 'en'
                              ? 'Furniture'
                              : '大家具'
                      : locale == 'en'
                        ? 'Used BooksClothes'
                        : '旧衣旧书'}
                    -{value?.wasteInfo?.type}
                  </Text>
                </View>
                {value.waste_1st_ID === 1 || value.waste_1st_ID === 2 || value.waste_1st_ID === 5 ? null : (
                  <View className="line2">
                    {T.orderManagePage.appraisal}：
                    {value.estimatedMoney ? `¥${value.estimatedMoney / 100}` : T.orderManagePage.toBeAssessed}
                  </View>
                )}
                {whichManage === E.OrderStatus.Completed || (value.waste_1st_ID === 4 && value.actualMoney) ? (
                  <View>
                    {value.waste_1st_ID === 1 ? (
                      <View className="line2">
                        {T.orderManagePage.dealPrice}：{T.orderManagePage.publicRecycling}
                      </View>
                    ) : (
                      <View className="line2">
                        {T.orderManagePage.dealPrice}：￥{value.actualMoney ? value.actualMoney / 100 : 0}
                        {value.waste_1st_ID === 4 && whichManage === E.OrderStatus.InProgress ? (
                          <Text>{T.orderManagePage.toBePaid}</Text>
                        ) : null}
                      </View>
                    )}
                  </View>
                ) : null}
                <View className="line3">
                  <Text>{T.orderManagePage.orderNo}：</Text>
                  <Text>{value.orderNO}</Text>
                </View>
                {whichManage === E.OrderStatus.InProgress ? (
                  <View className="inprogress_item">
                    <View>
                      {T.orderManagePage.recyclerMan}：{value.worker.workerName}
                    </View>
                    <View className="right_item" data-set="mark">
                      <Text>{value.worker.mobile}</Text>
                      <Image src={require('../../assets/icon/telephone.png')} />
                    </View>
                  </View>
                ) : null}
                <View
                  className="mask_item"
                  onClick={e => {
                    this.orderDetail(e, value.id)
                  }}
                ></View>
                {whichManage === E.OrderStatus.InProgress ? (
                  <View
                    className="call_worker"
                    onClick={e => {
                      this.call(e, value.worker.mobile)
                    }}
                  ></View>
                ) : null}
              </View>
            ))}
          {orderList && orderList.data.length == 0 && whichManage == "预订" && (
            <View className='xiadan' onClick={() => {

              Taro.navigateTo({ url: `/pages/appliances/appliances?id=3` })

            }}>
              <Image src={("https://c.evergreenrecycle.cn/hanhan/images/emptyOrder.png")} mode="widthFix" />
              <View>您还没有预约订单，快去预约下单吧！</View>
            </View>
          )}

          <View className="bottom_item"></View>
        </ScrollView>
        {orderList && (
          <View className="upDragBox" style={upDragStyle}>
            <AtActivityIndicator></AtActivityIndicator>
            <Text className="downText">{orderList.lastPage > currentPage ? '上拉加载更多' : '已到达最底部'}</Text>
          </View>
        )}
      </View>
    )
  }
}
export default connect(({ NOrder: { orderList, isOrderListGet } }) => ({
  orderList,
  isOrderListGet,
}))(OrderManage)
