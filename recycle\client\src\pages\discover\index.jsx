import { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { View, Text, Image, Input } from '@tarojs/components'
import './index.less'
import { NPublics } from '../../config/constants'
import BaseLayout from '../../components/baseLayout'
import discoverImg from '../../assets/discover/index'
import ImageShow from '../../components/imageShow/imageShow'
import ContentText from '../../components/contentText/contentText'
import NavCustomBar from '../../components/navbar'
import Taro, { useDidShow } from '@tarojs/taro'

definePageConfig({
  navigationBarTitleText: '首页',
  // navigationStyle: 'default',
  "navigationStyle": "custom"
})
export default () => {
  const { discoveryList, isAddItem, upvoteList } = useSelector(state => state.NPublics)

  const dispatch = useDispatch()
  const [type, setType] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [currentIndex, setCurrentIndex] = useState(null)
  const [isFocus, setIsFocus] = useState(null)
  const [currentDiscovery, setCurrentDiscovery] = useState(null)
  const [replayContent, setReplayContent] = useState(null)
  const [toUser, setToUser] = useState(null)

  const tabList = [
    { title: '全部', activate: '', name: '' },
    { title: '活动', activate: '', name: 'activities' },
    { title: '媒体报道', activate: '', name: 'media' },
  ]

  function getTheDiscoveryList() {
    dispatch.NPublics[NPublics.EGetDiscoveryList]({
      page: currentPage,
      type: tabList[type].name,
    })
  }

  useDidShow(() => {
    Taro.hideTabBar()
  })
  
  useEffect(() => {
    getTheDiscoveryList()
  }, [currentIndex, type])


  function getReplayContent(e) {
    setReplayContent(e.target.value)
  }

  function sendReplay() {
    if (!replayContent) {
      Taro.showToast({
        title: '回复内容不能为空',
        icon: 'none',
      })
      return
    }
    let options = {
      discoveryID: currentDiscovery.id,
      content: replayContent,
    }
    if (toUser && toUser.toUserID) {
      options = { ...options, toUserName: toUser.toUserName, toUserID: toUser.toUserID }
    }
    // return
    dispatch.NPublics[NPublics.EReplayTheContent]({
      ...options,
    })

    setCurrentDiscovery(null)
    setReplayContent('')
    setIsFocus(false)
    setToUser(null)
  }

  return (
    <BaseLayout menuIndex={1} title="环境动态" navigationBar={false} footer={true}>
      <NavCustomBar mainTitle='环境动态' needBackIcon={false} />
      <View className="discover_page">
        <View className="tabBox">
          {tabList.map((v, i) => {
            return (
              <View
                className={i === type && 'activate'}
                onClick={() => {
                  setType(i)
                }}
              >
                {v.title}
              </View>
            )
          })}
        </View>
        <View className="successfulOrder">
          <View className="content_wrapper">
            {discoveryList
              ? discoveryList.map((discovery, index) => (
                <View className="content" key={JSON.stringify(discovery)}>
                  <View className="left_wrapper">
                    <View className="the_log">
                      <Image src={discoverImg.recycleLogo} />
                    </View>
                  </View>
                  <View className="right_wrapper">
                    <View className="title_wrapper">
                      <View className="left_title">
                        <Text>冬瓜回收</Text>
                        <Text>共创绿色环保世界</Text>
                      </View>
                      <View className="right_title">#{tabList[type].title}</View>
                    </View>
                    <View className="content_text">
                      <ContentText data={discovery.content.split('\n')} />
                    </View>
                    <View className="image_wrapper">{<ImageShow data={JSON.parse(discovery.images)} />}</View>
                    {/* <View className="operate_wrapper">
                        <View className="operate_item">
                          <View
                            onClick={() => {
                              dispatch.NPublics[NPublics.EChangeUpvote]({
                                discoveryID: discovery.id,
                              })
                            }}
                          >
                            {upvoteList && (
                              <AtIcon prefixClass="icon" value="heart-fill" size="24" color={getTheHeartColor(discovery.id)}></AtIcon>
                            )}

                            <Text>{discovery.likeCount ? discovery.likeCount : ''}</Text>
                          </View>
                          <View
                            onClick={() => {
                              replayTheContent(discovery)
                            }}
                          >
                            <AtIcon prefixClass="icon" value="message-fill" size="24" color="#ddd"></AtIcon>
                            <Text>{discovery.comments.length > 0 ? discovery.comments.length : ''}</Text>
                          </View>
                        </View>
                      </View>
                      {discovery.comments.length > 0 ? (
                        <View className="comment_wrapper">
                          {discovery.comments.map((comment, mark) => (
                            <View key={JSON.stringify(comment) + mark} className="comment">
                              <Text
                                className="user_name"
                                onClick={() => {
                                  replayPerson(comment.userID, comment.userName, discovery)
                                }}
                              >
                                {comment.userName}
                              </Text>
                              {comment.toUserName ? (
                                <View>
                                  回复
                                  <Text
                                    className="user_name"
                                    onClick={() => {
                                      replayPerson(comment.toUserID, comment.toUserName, discovery)
                                    }}
                                  >
                                    {comment.toUserName}
                                  </Text>
                                </View>
                              ) : null}
                              <Text>：</Text>
                              {comment.content}
                            </View>
                          ))}
                        </View>
                      ) : null} */}
                  </View>
                </View>
              ))
              : null}
          </View>
          {isAddItem ? <View className="add_item"></View> : null}
          {isFocus ? (
            <View className="input_mask">
              <View
                className="mask"
                onClick={() => {
                  setIsFocus(isFocus)
                }}
              ></View>
              <View className="input_wrapper">
                <Input
                  className="the_input"
                  placeholder="请输入您要回复的内容"
                  cursor-spacing="15"
                  focus={isFocus}
                  onInput={e => {
                    getReplayContent(e)
                  }}
                />
                <View
                  className="send_button"
                  onClick={() => {
                    sendReplay()
                  }}
                >
                  发送
                </View>
              </View>
            </View>
          ) : null}
        </View>
      </View>
    </BaseLayout>
  )
}

