import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'
import { RSetState, RAdd, EPost, EGet, EPut, EDelete, EGetList, NSalesman, } from '../common/action'

export default {
  namespace: <PERSON><PERSON><PERSON><PERSON>,
  state: {
    salesmanList: [],
  },
  reducers: {
    [RSetState](state: any, payload: any) {
      return { ...state, ...payload }
    },
    [RAdd](state: { count: any }, payload: any) {
      return { ...state, count: state.count + payload }
    },
  },
  effects: {
    async [EGetList]({ payload }: any, { reducer }: any) {
      const response = requestGet('/salesman', payload)
      reducer(RSetState, { salesmanList: response })
      return response
    },
    async [EPost]({ payload }: any, { reducer }: any) {
      const result = await requestPost('salesman', payload)
      return result
    },
    async [EPut]({ payload }: any, { reducer }: any) {
      const { id } = payload
      delete payload.id
      const result = await requestPut(`salesman/${id}`, payload)
      return result
    },
    async [EDelete]({ payload }: any, { reducer }: any) {
      const result = await requestDelete(`salesman/${payload.id}`, payload)
      return result
    },

  },
}
