'use strict'

const _ = require('lodash')
const moment = require('moment')

const { OrderRating, Order, Worker } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//公司信息
class OrderRatingController {
  //获取订单评价详情
  async index({ request, response }) {
    let { orderID } = request.all()
    if (!orderID) {
      throw ERR.INVALID_PARAMS
    }
    let vo = await OrderRating.query().where('orderID', orderID).first()
    response.json(vo)
  }
  async show({ request, params, response }) {
    let vo = await OrderRating.query().where('id', params.id).first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
  async getWorker({ request, response }) {
    let { workerID } = request.all()
    let worker = await Worker.find(workerID)
    return worker
  }
  //创建订单评价
  async store({ request, response }) {
    let { companyID, workerID, orderID, workerRating, workerRatingText } = request.all()
    if (!orderID || !workerRating || !workerRatingText) {
      throw ERR.INVALID_PARAMS
    }
    let order = await Order.query().where('id', orderID).first()
    if (!order) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    let vo = await OrderRating.create({ companyID: order.companyID, workerID, orderID, workerRating, workerRatingText, wasteType: order.wasteType })
    if (workerID && vo) {
      let worker = await Worker.find(vo.workerID)
      if (workerRating === 1) {
        let negativeCount = worker.negativeCount
        negativeCount = negativeCount + 1
        await _.assign(worker, { negativeCount })
      } else if (workerRating === 2) {
        let neutralCount = worker.neutralCount
        neutralCount = neutralCount + 1
        await _.assign(worker, { neutralCount })
      } else {
        let positiveCount = worker.positiveCount
        positiveCount = positiveCount + 1
        await _.assign(worker, { positiveCount })
      }
      await worker.save()
    }
    response.json(vo)
  }
}

module.exports = OrderRatingController
