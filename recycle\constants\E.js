const E = {
  // 订单状态
  orderUpdates: {
    "TMS_CANCEL": { name: "TMS_CANCEL", remark: "取消订单" },
    "TMS_ORDER_RECEIVED": { name: "TMS_ORDER_RECEIVED", remark: "接受订单" },
    "TMS_REJECT": { name: "TMS_REJECT", remark: "拒绝订单" },
    "TMS_COMPLETE": { name: "TMS_COMPLETE", remark: "订单完成" },
    "TMS_UNCANVASS": { name: "TMS_UNCANVASS", remark: "揽件失败" },
    "TMS_MODIFY_ADDRESS": { name: "TMS_MODIFY_ADDRESS", remark: "改地址" },
  },
  // action：预约成功 TMS_RESERVE 改约成功 TMS_RESCHEDULE 上⻔取件 TMS_ACCEPT 已质检 TMS_CHECK 回收商关闭订单 ORDER_CLOSED 货物已退回 GOOD_RETURN
  actions: {
    "TMS_RESERVE": { name: "TMS_RESERVE", remark: "预约成功" },
    "TMS_RESCHEDULE": { name: "TMS_RESCHEDULE", remark: "改约成功" },
    "TMS_ACCEPT": { name: "TMS_ACCEPT", remark: "上⻔取件" },
    "TMS_CHECK": { name: "TMS_CHECK", remark: "已质检" },
    "ORDER_CLOSED": { name: "ORDER_CLOSED", remark: "回收商关闭订单" },
    "GOOD_RETURN": { name: "GOOD_RETURN", remark: "货物已退回" },
  },
  msg_type: [
    "TMS_EVENT_CALLBACK",
    "TRACEPUSH",
  ],
  // 事件code
  eventCode: {
    "EXCHANGE_SUCCESS": { name: "EXCHANGE_SUCCESS", remark: "实物换单成功" },
    "EXCHANGE_FAIL": { name: "EXCHANGE_FAIL", remark: "实物换单失败" },
    "STRAIGHT_DIRECTION_SUCCESS": { name: "STRAIGHT_DIRECTION_SUCCESS", remark: "流向直发完成" },
    "MODIFY_ADDRESS_SUCCESS": { name: "MODIFY_ADDRESS_SUCCESS", remark: "改地址回告成功" },
    "MODIFY_ADDRESS_FAILED": { name: "MODIFY_ADDRESS_FAILED", remark: "改地址回告失败" },
    "TRACKING_INFO_CALLBACK": { name: "TRACKING_INFO_CALLBACK", remark: "回收单溯源信息更新" },
  },
  PermissionItem: [
    {
      title: '订单管理',
      key: '订单管理',
      children: [
        { title: '订单查看', key: '订单查看' },
        { title: '订单操作', key: '订单操作' }
      ]
    },
    {
      title: '回收价格管理',
      key: '回收价格管理',
      children: [
        { title: '回收价格查看', key: '回收价格查看' },
        { title: '回收价格编辑', key: '回收价格编辑' }
      ]
    },
    {
      title: '佣金管理',
      key: '佣金管理',
      children: [
        { title: '佣金查看', key: '佣金查看' },
        { title: '佣金编辑', key: '佣金编辑' }
      ]
    },
    {
      title: '回收人员管理',
      key: '回收人员管理',
      children: [
        { title: '回收人员查看', key: '回收人员查看' },
        { title: '回收人员编辑', key: '回收人员编辑' },
        { title: '回收人员审核', key: '回收人员审核' }
      ]
    },
    {
      title: '服务商管理',
      key: '服务商管理',
      children: [
        { title: '服务商查看', key: '服务商查看' },
        { title: '服务商新建与编辑', key: '服务商新建与编辑' }
      ]
    },
    {
      title: '账户与权限管理',
      key: '账户与权限管理',
      children: [
        { title: '账户的查看', key: '账户的查看' },
        { title: '账户新建与编辑', key: '账户新建与编辑' },
        { title: '权限编辑', key: '权限编辑' }
      ]
    },
    {
      title: '业务数据总览',
      key: '业务数据总览',
      children: [
        { title: '数据查询', key: '数据查询' },
        { title: '数据导出', key: '数据导出' }
      ]
    },
    {
      title: '公益模块',
      key: '公益模块',
      children: [
        { title: '帖子操作', key: '帖子操作' },
        { title: '评论审核', key: '评论审核' }
      ]
    }
  ],
  Permission: {
    Primary: '初级', //可管理 订单
    Intermediate: '中级', //可管理 订单 价格 佣金
    Senior: '高级' //可管理 回收人员 服务商 订单 价格 佣金
  },
  WasteType: {
    ClothingAndBook: '旧衣旧书',
    LivingWaste: '生活废品',
    Appliances: '大家电',
    Furniture: '大家具',
  },
  SubAddressType: [
    { label: '学校', value: '1' },
    { label: '机关单位', value: '2' },
    { label: '企业', value: '3' },
    { label: '医院', value: '4' },
    { label: '商场', value: '5' },
    { label: '其他', value: '6' }
  ],
  period: [
    { label: '年度', value: '1' },
    { label: '季度', value: '2' },
    { label: '月度', value: '3' },
    { label: '天', value: '4' }
  ],
  RectificationStatus: {
    待整改: '待整改',
    '已整改(待审核)': '已整改(待审核)',
    审核完成: '审核完成'
  },
  ReportStatus: {
    审核中: '审核中',
    审核通过: '审核通过',
    审核未通过: '审核未通过'
  },
  ComplaintStatus: {
    受理中: '受理中',
    已受理: '已受理',
    驳回: '驳回'
  },
  HttpMethod: {
    Get: 'get',
    Post: 'post',
    Put: 'put',
    Patch: 'patch',
    Delete: 'delete'
  },
  OrderStatus: {
    Init: '初始化',
    Reservation: '预订',
    Pending: '待处理',
    TransferOrder: '转派',
    Dispatched: '已派单',
    InProgress: '进行中',
    Cancelled: '取消',
    Hacker: '黑客攻击',
    Completed: '完成'
  },
  WorkerLevel: {
    TeamLeader: '组长',
    TeamMember: '组员'
  },
  WorkerAccountStatus: {
    Init: '初始',
    Agree: '同意',
    Reject: '拒绝'
  },
  CancleOrderStatus: {
    Client: '客户取消',
    Master: '师傅取消',
    MasterRevoke: '师傅撤销',
    Admin: '后台取消'
  },

  PayType: {
    Debug: '测试支付',
    Wechat: '微信支付'
  },
  AdminLevel: {
    总部: '总部',
    服务商: '服务商',
  },
  OrderFrom: {
    菜鸟回收: '菜鸟回收',
    嗨回收: '嗨回收',
    电话下单: '电话下单',
    二维码跳转下单: '二维码跳转下单',
  },
  DiscoveryType: {
    WELFARE: 'welfare',
    ACTIVITIES: 'activities',
    MEDIA: 'media'
  },
  ScoreReason: {
    // 获取
    GainByOrderCancel: '订单取消返还',

    AddByAdmin: '管理员操作',
    SendScore: '积分赠送',

    // 消费
    ConsumeByOrder: '订单消费'
  }
}

// 将空值用键名填充
function fillValue(dict) {
  for (var key in dict) {
    if (typeof dict[key] === 'object' && dict[key]) {
      fillValue(dict[key])
    } else if (null === dict[key]) {
      dict[key] = key
    }
  }
}
fillValue(E)

module.exports = E
