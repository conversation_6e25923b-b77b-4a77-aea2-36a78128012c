'use strict'

const Model = use('Model')

/**
 * 嗨回收价格配置日志模型
 * 记录嗨回收平台的价格设置修改历史
 */
class HiPriceLog extends Model {
  static get table() {
    return 'hi_price_set_log'
  }

  static get primaryKey() {
    return 'id'
  }

  static get createdAtColumn() {
    return 'createdAt'
  }

  static get updatedAtColumn() {
    return null
  }

  /**
   * 关联管理员信息
   */
  admin() {
    return this.belongsTo('App/Models/AdminUser', 'adminID', 'id')
  }

  /**
   * 关联价格配置信息
   */
  priceSet() {
    return this.belongsTo('App/Models/HiPrice', 'priceSetID', 'id')
  }

  /**
   * 获取操作类型文本
   */
  static getActionText(action) {
    const actionMap = {
      'create': '创建',
      'update': '修改',
      'delete': '删除'
    }
    return actionMap[action] || '未知操作'
  }

  /**
   * 解析before_data JSON
   */
  getBeforeData(value) {
    try {
      return value ? JSON.parse(value) : null
    } catch (error) {
      return null
    }
  }

  /**
   * 解析after_data JSON
   */
  getAfterData(value) {
    try {
      return value ? JSON.parse(value) : null
    } catch (error) {
      return null
    }
  }

  /**
   * 设置before_data JSON
   */
  setBeforeData(value) {
    return value ? JSON.stringify(value) : null
  }

  /**
   * 设置after_data JSON
   */
  setAfterData(value) {
    return value ? JSON.stringify(value) : null
  }
}

module.exports = HiPriceLog 