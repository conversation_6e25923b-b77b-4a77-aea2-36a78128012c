/**
 * Created by <PERSON>(qq:24242811) on 2019/1/10.
 */
const Route = use('Route')
const Env = use('Env')

// 无需登录
Route.group(() => {
  // 管理员
  Route.post('user/autoLogin', 'AdminUserController.autoLogin')
  Route.post('user/login', 'AdminUserController.login')
  Route.resource('debug', 'DebugController') // 调试
  Route.post('debug/smsTest', 'DebugController.smsTest') // 调试
  Route.get('downloadFile', 'OrderController.downloadFile')
  Route.post('joinTeam', 'AdminUserController.joinTeam')
  Route.post('cancelBundle', 'DebugController.cancelBundle') // 批量取消
  Route.get('getData', 'DebugController.getData')
  Route.get('getCharts', 'DebugController.getCharts')
  Route.get('getLineCharts', 'DebugController.getLineCharts')
  Route.get('getWorkerOrderData', 'DebugController.getWorkerOrderData')
  Route.get('getHiProvinceCharts', 'DebugController.getHiProvinceCharts')
  Route.get('getHiChannelCharts', 'DebugController.getHiChannelCharts')
  Route.get('getHiCategoryCharts', 'DebugController.getHiCategoryCharts')

  Route.post('user/JDlogin', 'AdminUserController.JDlogin')
  Route.get('exportJDOrder', 'OrderImportController.exportJDOrder')
  Route.get('exportOrderMaintain', 'OrderImportController.exportOrderMaintain')
  Route.get('exportJDPrice', 'JDPriceController.exportTable')
  Route.get('exportHiOrder', 'HiOrderController.exportXLS')
})
  .prefix('admin/v1')
  .namespace('Admin')
// 需要登录
Route.group(() => {
  Route.resource('joinUs', 'JoinTeamController')
  Route.post('worker/whichWorkers', 'WorkerController.which')
  Route.resource('waste1stCategory', 'Waste1stCategoryController') // 废品一级分类
  Route.resource('waste2ndCategory', 'Waste2ndCategoryController') // 废品二级分类
  Route.resource('attributeType', 'AttributeTypeController') // 废品属性类型
  Route.resource('order', 'OrderController') // 订单
  Route.post('orderNew', 'OrderController.handleNew') // 订单
  Route.resource('worker', 'WorkerController') // 上门师傅信息
  Route.resource('priceSet', 'PriceSetController') // 价格设置
  Route.post('bundlePriceSet', 'PriceSetController.bundlePriceSet')
  Route.resource('clientCommission', 'ClientCommissionController') // 客户佣金设置
  Route.post('bundleClientPrice', 'ClientCommissionController.bundleClientPrice')
  Route.get('HiPrice/:id/logs', 'HiPriceController.getLogs') // 价格配置修改记录
  Route.get('priceSetexport', 'HiPriceController.exportTable') // 价格配置导出
  Route.post('address/all', 'AddressController.all')
  Route.resource('waste', 'WasteController') // 废品
  Route.resource('attribute', 'AttributeController') // 废品属性
  Route.post('worker/addWorkAddress', 'WorkerController.addWorkAddress')
  Route.post('workerCharge', 'WorkerController.chargeWallet')
  Route.post('worker/removeWorkAddress', 'WorkerController.removeWorkAddress')
  Route.post('worker/getTown', 'WorkerController.getTown')
  Route.resource('collection', 'CollectionController') // 数据统计
  Route.post('collection/workerCollection', 'CollectionController.workerCollection') // 回收人员统计
  Route.get('getWorkerArea/:id', 'WorkerAreaController.getArea') // 公司服务区域
  Route.post('worker/workerReview', 'WorkerController.workerReview') // 未审核人员
  Route.resource('company', 'CompanyController') // 公司信息
  Route.get('getCompanyArea/:id', 'CompanyAreaController.getCompanyArea') // 公司服务区域
  Route.get('adminUser/getUserInfo', 'AdminUserController.getUserInfo') // 后台登录者详情
  Route.resource('adminUser', 'AdminUserController') // 后台登录账户管理
  Route.resource('orderWaste', 'OrderWasteController') // 订单废品关联表
  Route.resource('companySelfWaste', 'CompanySelfWastePriceController') // 服务商设置回收物
  Route.resource('companySelfAttribute', 'CompanySelfAttributeController') // 服务商设置回收物属性
  Route.resource('discovery', 'DiscoveryController') // 帖子 创建及编辑
  Route.resource('discoveryComment', 'DiscoveryCommentController') // 帖子评论
  Route.resource('companyArea', 'CompanyAreaController')
  Route.resource('workerArea', 'WorkerAreaController')
  Route.resource('workerInsure', 'WorkerInsureController')
  Route.post('order/orderBack', 'OrderController.orderBack') // 订单撤回
  Route.post('order/orderSend', 'OrderController.orderSend') // 批量派单
  Route.resource('permission', 'AdminUserPermissionController') // 权限管理
  Route.post('order/complaints', 'OrderController.complaints') // 投诉订单
  Route.post('order/devideOrder', 'OrderController.devideOrder') // 订单重分配
  Route.get('orderCount', 'OrderController.getOrderCount') // 订单分类数目
  Route.post('order/rating', 'OrderController.rating') // 被评价订单
  Route.resource('pay', 'PayController') // 订单支付记录
  Route.post('pay/formatType', 'PayController.formatType')
  Route.get('orderLog/:id', 'OrderController.getOrderLog') // 订单记录
  Route.get('worker/payLog/:id', 'OrderController.getPayLog') // 服务人员支付记录
  Route.resource('workerPoint', 'WorkerPointController') // 订单支付记录
  Route.get('workerPaylist', 'WorkerPointController.workerPaylist') // 服务人员支付记录
  Route.post('masterPayRefund', 'WorkerPointController.masterPayRefund') // 押金退款
  Route.get('noticeOrder', 'OrderController.getNoticeOrder')
  Route.resource('salesman', 'SalesmanController') //地推专员
  Route.get('walletlog', 'WorkerController.walletlog') //walletlog  
  Route.put('orderEdit/:id', 'OrderController.orderEdit')
  Route.post('orderimport/batchDestroy', 'OrderImportController.batchDestroy') // 批量删除订单
  Route.post('hiOrder/batchDestroy', 'HiOrderController.batchDestroy') // 批量删除订单
  Route.resource('orderimport', 'OrderImportController') //地推专员
  Route.resource('logMsg', 'LogMsgController')

  Route.put('remind/:id', 'CollectionController.remind')
  Route.post('comfirmOrder', 'OrderController.comfirmOrder')
  Route.post('comfirmOrderByAdmin', 'OrderController.comfirmOrderByAdmin')
  Route.resource('chargePrice', 'ChargePriceController')
  Route.put('changeWorker/:id', 'OrderController.changeWorker')
  Route.resource('clientEstimate', 'ClientEstimateController')

  Route.get('clientOrder/orderCount', 'ClientOrderController.getOrderCount') // 订单分类数目
  Route.post('clientOrder/complaints', 'ClientOrderController.complaints') // 投诉订单  
  Route.post('clientOrder/devideOrder', 'ClientOrderController.devideOrder') // 订单重分配
  Route.post('clientOrder/rating', 'ClientOrderController.rating') // 被评价订单
  Route.get('clientOrderLog/:id', 'ClientOrderController.getOrderLog') // 订单记录
  Route.get('clientOrder/worker/payLog/:id', 'ClientOrderController.getPayLog') // 服务人员支付记录
  Route.resource('clientOrder', 'ClientOrderController')
  Route.put('clientOrder/changeWorker/:id', 'ClientOrderController.changeWorker')
  Route.post('clientOrder/comfirmOrder', 'ClientOrderController.comfirmOrder')
  Route.put('clientOrder/orderEdit/:id', 'ClientOrderController.orderEdit')
  Route.get('clientOrder/noticeOrder', 'ClientOrderController.getNoticeOrder')
  Route.post('clientOrder/orderSend', 'ClientOrderController.orderSend') // 批量派单
  Route.post('clientOrder/orderBack', 'ClientOrderController.orderBack') // 订单撤回
  Route.post('clientOrder/orderNew', 'ClientOrderController.orderNew') // 订单


  Route.get('JDworkerPaylist', 'JDWorkerPointController.workerPaylist') // 服务人员支付记录
  Route.post('JDworker/workerReview', 'JDWorkerController.workerReview') // 未审核人员
  Route.post('JDworker/workerMaintain', 'JDWorkerController.workerMaintain') // 待维护人员
  Route.put('JDworker/workerMaintain/:id', 'JDWorkerController.putWorkerMaintain') // 同意维护
  Route.resource('JDworker', 'JDWorkerController') // 上门师傅信息
  Route.resource('JDPrice', 'JDPriceController')
  Route.post('JDworker/coworker', 'JDWorkerController.createCoWorker')
  Route.put('JDworker/coworker/:id', 'JDWorkerController.updateCoWorker')

  Route.post('JDworker/addWorkAddress', 'JDWorkerController.addWorkAddress')
  Route.post('JDworkerCharge', 'JDWorkerController.chargeWallet')
  Route.post('JDworker/removeWorkAddress', 'JDWorkerController.removeWorkAddress')
  Route.post('JDworker/getTown', 'JDWorkerController.getTown')
  Route.get('JDwalletlog', 'JDWorkerController.walletlog') //walletlog  
  Route.post('JDworker/whichWorkers', 'JDWorkerController.which')
  Route.delete('JDCoWorker/:id', 'JDWorkerController.deleteCoWorker')

  Route.put('orderMaintainUpdate/:id', 'OrderImportController.orderMaintainUpdate') // 更新订单维护
  Route.get('orderMaintainList', 'OrderImportController.orderMaintainList') // 订单维护列表

  // 小工相关
  Route.post('worker/addAssistant', 'WorkerController.addAssistant')
  Route.post('worker/assistantOrders', 'OrderController.getAssistantOrders')

  Route.resource('hiWorker', 'HiWorkerController')
  Route.resource('hiOrder', 'HiOrderController')
  Route.resource('hiOrderMaintain', 'HiOrderImportController')
  Route.resource('hiOrderImport', 'HiOrderImportController')
  Route.resource('hiPrice', 'HiPriceController')
  Route.get('hiOrderMaintainList', 'HiOrderImportController.hiOrderMaintainList')
  Route.put('hiOrderMaintainUpdate/:id', 'HiOrderImportController.hiOrderMaintainUpdate')
  Route.get('hiOrderMaintainList', 'HiOrderImportController.hiOrderMaintainList')
  Route.put('hiOrderMaintainUpdate/:id', 'HiOrderImportController.hiOrderMaintainUpdate')
  Route.get('hiOrderMaintainList', 'HiOrderImportController.hiOrderMaintainList')
  Route.put('hiOrderMaintainUpdate/:id', 'HiOrderImportController.hiOrderMaintainUpdate')
  Route.get('HiWorkerPaylist', 'HiWorkerController.workerPaylist') // 服务人员支付记录
  Route.post('HiWorker/workerReview', 'HiWorkerController.workerReview') // 未审核人员
  Route.post('HiWorker/workerMaintain', 'HiWorkerController.workerMaintain') // 待维护人员
  Route.put('HiWorker/workerMaintain/:id', 'HiWorkerController.putWorkerMaintain') // 同意维护
  Route.resource('HiWorker', 'HiWorkerController') // 上门师傅信息
  Route.resource('JDPrice', 'JDPriceController')
  Route.post('HiWorker/coworker', 'HiWorkerController.createCoWorker')
  Route.put('HiWorker/coworker/:id', 'HiWorkerController.updateCoWorker')

  Route.post('HiWorker/addWorkAddress', 'HiWorkerController.addWorkAddress')
  Route.post('HiWorkerCharge', 'HiWorkerController.chargeWallet')
  Route.post('HiWorker/removeWorkAddress', 'HiWorkerController.removeWorkAddress')
  Route.post('HiWorker/getTown', 'HiWorkerController.getTown')
  Route.get('HiWalletlog', 'HiWorkerController.walletlog') //walletlog  
  Route.post('HiWorker/whichWorkers', 'HiWorkerController.which')
  Route.delete('JDCoWorker/:id', 'HiWorkerController.deleteCoWorker')
})
  .prefix('admin/v1')
  .namespace('Admin')
  .middleware('adminAuth')

// 文件上传接口
Route.group(() => {
  Route.post('file', 'FileController.file')
  Route.post('sms', 'SendSmsController.smsSend') // 短信提醒
  Route.post('importData', 'ImportDataController.importData')
  Route.get('logDataByExcelFile', 'Admin/CollectionController.exportXLS') // 数据导出
  Route.get('workerPaylistexport', 'Admin/WorkerPointController.exportXLS')
  Route.get('workerCollectionExportExcel', 'Admin/CollectionController.workerCollectionExportExcel') // 回收人员统计导出
  Route.get('exportMasterXLS', 'Admin/WorkerPointController.exportMasterXLS') // 师傅数据导出
  Route.get('exportWalletXLS', 'Admin/WorkerPointController.exportWalletXLS') // 师傅数据导出
  Route.resource('address', 'AddressController') // 地址
  // 嗨回收
  Route.post('uploadHiOrder', 'Admin/HiOrderImportController.uploadHiOrder')
  Route.get('exportHiOrderMaintain', 'Admin/HiOrderImportController.exportXLS')
  Route.get('exportHiPrice', 'Admin/HiPriceController.exportXLS')
  Route.get('HiExportMasterXLS', 'Admin/HiWorkerController.exportXLS')
  Route.get('exportHiWorkerMaintain', 'Admin/HiOrderImportController.exportXLS')
  Route.get('exportHiWorkerPrice', 'Admin/HiPriceController.exportXLS')

  Route.get('JDworkerPaylistexport', 'Admin/JDWorkerPointController.exportXLS')
  Route.get('JDexportMasterXLS', 'Admin/JDWorkerPointController.exportMasterXLS') // 师傅数据导出
  Route.get('JDexportWalletXLS', 'Admin/JDWorkerPointController.exportWalletXLS') // 师傅数据导出
}).prefix('admin/v1')

Route.group(() => {
  Route.get('pageNews', 'PageController.NewsList')
  Route.post('pageNews', 'PageController.NewsCreate')
  Route.put('pageNews/:id', 'PageController.NewsUpdate')
  Route.get('pageNews/:id', 'PageController.NewsDetail')
  Route.delete('pageNews/:id', 'PageController.NewsDelete')

  Route.get('pageOrder', 'PageController.OrderList')
  Route.post('pageOrder', 'PageController.OrderCreate')
  Route.put('pageOrder/:id', 'PageController.OrderUpdate')
  Route.delete('pageOrder/:id', 'PageController.OrderDelete')

  Route.get('pageForm', 'PageController.FormList')
  Route.post('pageForm', 'PageController.FormCreate')
  Route.put('pageForm/:id', 'PageController.FormUpdate')
  Route.delete('pageForm/:id', 'PageController.FormDelete')
})
.namespace('Admin')
.prefix('api/v1')
