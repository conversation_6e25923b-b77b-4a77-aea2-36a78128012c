'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Company, WorkerInsure, SysArea, ReqLog } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { CryptUtil } = require('../../../Util')
const Env = use('Env')

//公司信息
class WorkerInsureController {
  //回收人员work区域
  async index({ request, response }) {
    let { workerID, companyID } = request.all()
    if (!workerID && !companyID) {
      throw ERR.INVALID_PARAMS
    }
    let query = WorkerInsure.query()
    if (workerID) {
      query.where('workerID', workerID)
    }
    if (companyID) {
      query.where('companyID', companyID)
    }
    let vo = await query.first()
    response.json(vo)
  }
  //编辑回收人员保险
  async store({ request, response }) {
    let { companyID, workerID, files, overTime, remark } = request.all()
    if (!companyID || !workerID) {
      throw ERR.INVALID_PARAMS
    }
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台 编辑回收人员保险' })
    let isWorker = await WorkerInsure.findBy('workerID', workerID)
    if (!isWorker) {
      await WorkerInsure.create({ workerID, companyID, files, overTime, remark })
    } else {
      _.assign(isWorker, request.all())
      await isWorker.save()
    }
    let vo = await WorkerInsure.query()
      .where('companyID', companyID)
      .where('workerID', workerID).with('company').with('worker')
      .fetch()
    response.json(vo)
  }
  async update({ request, params, response }) {
    let vo = await WorkerInsure.find(params.id)
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台 编辑回收人员保险' })
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
  }
  async destroy({ request, params, response }) {
    let { adminUser: user } = request
    let { workerID } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台 删除回收人员保险' })
    if (user.level !== E.AdminLevel.总部) {
      throw ERR.USER_ROLE_NO_PRIVILEGE
    }
    let vo = WorkerInsure.query().where({ workerID })
    if (vo) {
      await vo.delete()
    }
    return ({ msg: 'delete ok', code: 200 })
  }
}

module.exports = WorkerInsureController
