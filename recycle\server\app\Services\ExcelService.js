const moment = require('moment')
const _ = require('lodash')
const Env = use('Env')
const xlsx = require('node-xlsx')
const fs = require('fs')
const Helpers = use('Helpers')

const ExcelService = {
  async generateExcel(fileName, titles, data, response, options) {
    data.unshift(titles)
    var buffer = xlsx.build([{ name: fileName, data: data }], options) // Returns a buffer
    fs.writeFileSync(Helpers.resourcesPath(`${fileName}.xlsx`), buffer)
    let exportName = `${fileName}.xlsx`
    response.attachment(Helpers.resourcesPath(exportName), exportName)
  }
}

module.exports = ExcelService
