.successfulOrder {
  width: 100vw;
  height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0px 2px 11px #e9ebed inset;
  .item_wrapper {
    width: 100%;
    .click_wrapper {
      margin: 0 auto;
      width: 658px;
      height: 100px;
      border-bottom: 2px solid #f3f3f3;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #556073;
      font-size: 28px;
      font-weight: 700;
      letter-spacing: 1px;
      .operate_item_image {
        height: 24px;
        width: 24px;
        transition: all 0.3s;
      }
      .rotate {
        transform: rotate(90deg);
      }
    }
    .content_wrapper {
      width: 100%;
      background: rgba(250, 245, 245, 1);
      padding: 30px 0;
      .content {
        padding: 0 46px;
        color: #15b381;
        font-size: 24px;
        line-height: 48px;
        &:nth-child(2) {
          margin: 20px 0;
        }
      }
    }
  }
}
