.cancleOrder {
  width: 100vw;
  height: 100vh;
  background: #f5f6f8;
  display: flex;
  flex-direction: column;
  align-items: center;
  letter-spacing: 2px;
  padding-bottom: 66px;
  box-shadow: 0px 2px 11px #e9ebed inset;
  padding-top: 40px;
  .recycler {
    border-radius: 16px;
    padding: 30px 20px;
    background: #fff;
    width: 660px;
    display: flex;
    align-items: center;
    .recycler_avatar {
      position: relative;
      top: 0;
      &::after {
        position: absolute;
        content: "";
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 32px;
        background: url("../../assets/icon/typebg.png") center ;
        background-size: cover;
      }
      height: 100px;
      width: 100px;
      line-height: 100px;
      text-align: center;
      border-radius: 50%;
      margin-right: 50px;
      background: #f5f6f8;
      font-weight: 800;
      font-size: 48px;
      color: #333333;
    }
    .recycler_operate {
      Text {
        font-size: 28px;
        color: #333333;
        font-weight: 600;
      }
      View {
        font-size: 24px;
        color: #556073;
        margin-top: 16px;
      }
    }
  }
  .content_wrapper {
    flex: 1;
    width: 660px;
    background: #ffffff;
    padding: 44px 36px;
    position: relative;
    .top_select {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      > Text {
        height: 40px;
        line-height: 40px;
        padding: 0px 22px;
        border-radius: 20px;
        background: #f3f3f3;
        font-size: 22px;
        color: #999999;
        letter-spacing: 1px;
        margin-bottom: 20px;
        margin-right: 20px;
      }
    }
    .input_title {
      display: inline-block;
      padding-top: 30px;
      font-size: 26px;
      color: #444444;
      font-weight: 700;
    }
    .detail_input {
      border: none;
      padding-left: 0;
    }
    .at-textarea__textarea {
      font-size: 24px;
      color: #333333;
      line-height: 36px;
    }
    .submit_button {
      width: 588px;
      height: 88px;
      background: #999999;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 30px;
      color: #ffffff;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 42px;
      letter-spacing: 2px;
      border-radius: 44px;
    }
  }
}
.expression_wrapper {
  display: flex;
  justify-content: space-between;
  width: 430px;
  margin-top: 50px;
  margin-bottom: 54px;
  .item_wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 90px;
    .expression {
      width: 56px;
      height: 56px;
    }
    > Text {
      margin-top: 12px;
      color: #999999;
      font-size: 26px;
    }
  }
}
