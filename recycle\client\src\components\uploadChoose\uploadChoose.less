.uploadChoose{
   position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    .uploadChoose_mask{
        position: absolute;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background-color: #000000;
        opacity:0.5;
        z-index: 1;
    }
    .uploadChoose_modal_frame{
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 2;
        width:100vw;
        height:49vw;
        border-top-left-radius: 1vw;
        border-top-right-radius: 1vw;
        background-color: rgba(255, 255, 255, 1);
        display: flex;
        flex-direction: column;
        .uploadChoose_Photograph{
            height: 17.3vw;
            line-height: 17.3vw;
            width: 88.5vw;
            margin:0 auto;
            color: rgba(16, 16, 16, 1);
            font-size:4.2vw;
            text-align:center;
            font-family: PingFangSC-regular;
            border-bottom:1px solid rgba(243, 243, 243, 1);
        }
        .uploadChoose_Album{
            height: 17.3vw;
            line-height: 15vw;
            width:100vw;
            color: rgba(16, 16, 16, 1);
            font-size:4.2vw;
            text-align:center;
            font-family: PingFangSC-regular;
            border-bottom:2.7vw solid #F3F3F3;
        }
        .uploadChoose_cancel{
            width:100vw;
            height:13.1vw;
            color: rgba(16, 16, 16, 1);
            font-size:4.2vw;
            font-family: PingFangSC-regular;
            display: flex;
            justify-content: center;
            align-items:center;
        }
    }
}