import {
  EGetPrice, EPutPrice, EPostPrice,
  RSetState, NHiOrder, EGetCommissionLogs,
  EDelPrice
} from '../common/action'
import { adapterPaginationResult } from '../common/utils'
import { message } from 'antd'
import { getPrice, putPrice, postPrice, getCommissionLogs, delPrice } from '../services/HiServices'


export default {
  namespace: NHiOrder,
  state: {
    priceList: null,
    searchQuery: {},
    loading: false
  },
  reducers: {
    [RSetState](state: any, payload: any) {
      return { ...state, ...payload }
    },
  },
  effects: {
    // 获取嗨回收信息费列表
    async [EGetPrice]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { searchQuery: payload })
      const response = await getPrice(payload)
      return adapterPaginationResult(response)
    },

    // 修改嗨回收信息费
    async [EPutPrice]({ payload }: any, { reducer }: any) {
      const response = await putPrice(payload)
      return response
    },

    // 新增嗨回收信息费
    async [EPostPrice]({ payload }: any, { reducer }: any) {
      const response = await postPrice(payload)
      if (response.success) {
        message.success('新增成功')
      } else {
        message.error('新增失败')
      }
      return response
    },
    // 获取佣金配置修改记录
    async [EGetCommissionLogs]({ payload }: any, { reducer }: any) {
      let res = await getCommissionLogs(payload)
      return adapterPaginationResult(res)
    },
    // 删除嗨回收信息费
    async [EDelPrice]({ payload }: any, { reducer }: any) {
      const response = await delPrice(payload)
      return response
    },
  }
} 