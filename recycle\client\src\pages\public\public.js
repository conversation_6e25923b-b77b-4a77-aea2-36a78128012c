import Taro, { checkIsSupportFacialRecognition } from '@tarojs/taro'
import React, { Component } from 'react'
import { connect } from 'react-redux'
import { View, Image, Button, Text, Checkbox } from '@tarojs/components'
import BaseLayout from '../../layouts/baseLayout'
import { AtModal, AtIcon } from 'taro-ui'
import './public.less'
import E from '../../config/E'
import '../../assets/iconfont/iconfont.css'

import ImageShow from '../../components/imageShow/imageShow'
import ContentText from '../../components/contentText/contentText'
import NavCustomBar from '../../components/navbar'

class Public extends Component {
  constructor() {
    super(...arguments)
    this.state = {
      locale: 'zh_CN',
      currentIndex: 0,
      type: '',
      currentPage: 1,
      isFocus: false,
      replayContent: '',
      currentDiscovery: null,
      toUser: {}
    }
  }

  config = {}

  //-----------------------生命周期-------------------------//
  componentWillMount() {
    Taro.setNavigationBarTitle({ title: '冬瓜回收动态' })
  }

  componentDidMount() {
    let { user } = this.props
    this.getTheDiscoveryList()
    this.getUpvoteList()
    const locale = Taro.getStorageSync('locale')
    this.setState({
      locale: locale
    })
    Taro.setNavigationBarTitle({ title: locale == 'en' ? 'Qing Shan Dynamics' : '冬瓜回收动态' })
  }

  componentWillReceiveProps(nextProps) {
    if (!this.props.isChangeUpvote && nextProps.isChangeUpvote) {
      this.getTheDiscoveryList()
    }
  }

  componentWillUnmount() { }

  componentDidShow() { }

  componentDidHide() { }

  //-----------------------事件-------------------------//
  getTheDiscoveryList = (async) => {
    let { currentPage, type } = this.state
    this.props.dispatch({
      type: 'NPublics/EGetDiscoveryList',
      payload: {
        page: currentPage,
        type
      }
    })
  }

  changeItem = async (index, type) => {
    await this.setState({
      currentIndex: index,
      type
    })
    await this.getTheDiscoveryList()
  }

  getTheTag(type) {
    let tag
    if (type === E.DiscoveryType.ACTIVITIES) {
      tag = T.topTitle3
    } else if (type === E.DiscoveryType.MEDIA) {
      tag = T.topTitle4
    }
    return tag
  }

  replayTheContent(discovery) {
    this.setState({
      isFocus: true,
      currentDiscovery: discovery
    })
  }

  getReplayContent(e) {
    this.setState({ replayContent: e.target.value })
  }

  sendReplay() {
    let { replayContent, currentDiscovery, toUser } = this.state
    if (!replayContent) {
      Taro.showToast({
        title: '回复内容不能为空',
        icon: 'none'
      })
      return
    }
    let options = {
      discoveryID: currentDiscovery.id,
      content: replayContent
    }
    if (toUser && toUser.toUserID) {
      options = { ...options, toUserName: toUser.toUserName, toUserID: toUser.toUserID }
    }
    // return
    this.props.dispatch({
      type: 'NPublics/EReplayTheContent',
      payload: {
        ...options
      }
    })
    this.setState({
      currentDiscovery: null,
      replayContent: '',
      isFocus: false,
      toUser: null
    })
  }

  replayPerson(id, name, discovery) {
    let { user } = this.props
    let toUser = {}
    if (user.id !== id) {
      toUser.toUserID = id
      toUser.toUserName = name
    }
    this.setState({
      isFocus: true,
      toUser,
      currentDiscovery: discovery
    })
  }

  getUpvoteList() {
    this.props.dispatch({ type: 'NPublics/EGetUpvoteList' })
  }
  getTheHeartColor(id) {
    let { upvoteList } = this.props
    let color = '#ddd'
    upvoteList.forEach((value, index) => {
      if (value.discoveryID === id) {
        color = '#fe346e'
        return
      }
    })
    return color
  }

  operatePhote(list, index) {
    wx.previewImage({
      current: index, // 当前显示图片的http链接
      urls: JSON.parse(list) // 需要预览的图片http链接列表
    })
  }

  //-----------------------渲染-------------------------//
  render() {
    let { currentIndex, isFocus } = this.state
    let { discoveryList, isAddItem, user, upvoteList } = this.props
    return (
      <BaseLayout menuIndex={1}>
      <NavCustomBar mainTitle='冬瓜回收动态' needBackIcon={true} />
        <View className="successfulOrder">
          <View className="title_wrapper1">
            {[
              { title: T.topTitle1, type: '' },
              { title: T.topTitle3, type: E.DiscoveryType.ACTIVITIES },
              { title: T.topTitle4, type: E.DiscoveryType.MEDIA }
            ].map((value, index) => (
              <View
                key={value + index}
                className="title"
                onClick={() => {
                  this.changeItem(index, value.type)
                }}>
                <Text style={index === currentIndex ? { color: '#333', fontWeight: 700 } : null}>{value.title}</Text>
                <View className="mark_item" style={index !== currentIndex ? { display: 'none' } : null}></View>
              </View>
            ))}
          </View>
          <View className="content_wrapper">
            {discoveryList
              ? discoveryList.map((discovery, index) => (
                <View className="content" key={JSON.stringify(discovery)}>
                  <View className="left_wrapper">
                    <View className="the_log">
                      <Image src={require('../../assets/icon/recycleLogo.jpg')} />
                    </View>
                  </View>
                  <View className="right_wrapper">
                    <View className="title_wrapper">
                      <View className="left_title">
                        <Text>冬瓜回收</Text>
                        <Text>共创绿色环保世界</Text>
                      </View>
                      <View className="right_title">#{this.getTheTag(discovery.type)}</View>
                    </View>
                    <View className="content_text">
                      <ContentText data={discovery.content.split('\n')} />
                    </View>
                    <View className="image_wrapper">{<ImageShow data={JSON.parse(discovery.images)} />}</View>
                    <View className="operate_wrapper">
                      <View className="operate_item">
                        <View
                          onClick={() => {
                            this.props.dispatch({
                              type: 'NPublics/EChangeUpvote',
                              payload: {
                                discoveryID: discovery.id
                              }
                            })
                          }}>
                          {upvoteList && <AtIcon prefixClass="icon" value="heart-fill" size="24" color={this.getTheHeartColor(discovery.id)}></AtIcon>}

                          <Text>{discovery.likeCount ? discovery.likeCount : ''}</Text>
                        </View>
                        <View
                          onClick={() => {
                            this.replayTheContent(discovery)
                          }}>
                          <AtIcon prefixClass="icon" value="message-fill" size="24" color="#ddd"></AtIcon>
                          <Text>{discovery.comments.length > 0 ? discovery.comments.length : ''}</Text>
                        </View>
                      </View>
                    </View>
                    {discovery.comments.length > 0 ? (
                      <View className="comment_wrapper">
                        {discovery.comments.map((comment, mark) => (
                          <View key={JSON.stringify(comment) + mark} className="comment">
                            <Text
                              className="user_name"
                              onClick={() => {
                                this.replayPerson(comment.userID, comment.userName, discovery)
                              }}>
                              {comment.userName}
                            </Text>
                            {comment.toUserName ? (
                              <View>
                                回复
                                <Text
                                  className="user_name"
                                  onClick={() => {
                                    this.replayPerson(comment.toUserID, comment.toUserName, discovery)
                                  }}>
                                  {comment.toUserName}
                                </Text>
                              </View>
                            ) : null}
                            <Text>：</Text>
                            {comment.content}
                          </View>
                        ))}
                      </View>
                    ) : null}
                  </View>
                </View>
              ))
              : null}
          </View>
          {isAddItem ? <View className="add_item"></View> : null}
          {isFocus ? (
            <View className="input_mask">
              <View
                className="mask"
                onClick={() => {
                  this.setState({ isFocus: false })
                }}></View>
              <View className="input_wrapper">
                <Input
                  className="the_input"
                  placeholder="请输入您要回复的内容"
                  cursor-spacing="15"
                  focus={isFocus}
                  onInput={(e) => {
                    this.getReplayContent(e)
                  }}
                />
                <View
                  className="send_button"
                  onClick={() => {
                    this.sendReplay()
                  }}>
                  发送
                </View>
              </View>
            </View>
          ) : null}
        </View>
      </BaseLayout>
    )
  }
}

export default connect(({ NUser: { userInfo }, NPublics: { discoveryList, upvoteList, isChangeUpvote }, NSystem: { isAddItem } }) => ({
  user: userInfo,
  discoveryList,
  isAddItem,
  upvoteList,
  isChangeUpvote
}))(Public)
