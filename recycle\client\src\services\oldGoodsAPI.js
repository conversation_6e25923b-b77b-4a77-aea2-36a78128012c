import { requestGet, requestPatch, requestPost } from '../utils/request'

export async function get_2nd_Category(payload) {
  return requestGet(`/waste1stCategory/${payload.id}/get2ndCategory`)
}
export async function getWaste(payload) {
  return requestGet(`/waste2ndCategory/${payload.id}/getWaste`)
}
export async function getSecondKind(payload) {
  return requestGet(`/getThirdList`, payload)
}
export async function getAttributeType(payload) {
  return requestGet(`/attributeType/${payload.id}`)
}
export async function getAttribute(payload) {
  return requestGet(`/attribute/${payload.id}`, payload)
}
export async function getEstimate(payload) {
  return requestGet(`/clientEstimate`, payload)
}

