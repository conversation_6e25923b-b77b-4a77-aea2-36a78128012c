/*编辑服务商的回收区域及对应的回收物类型*/
import { Fragment, useEffect, useState } from 'react'
import { notification, Select, Button, Tooltip } from 'antd'
import styles from './index.module.less'
import { effect, reducer, useConnect } from 'dva17'
import {
  NCompany, NWorker, EDetDetail, EGet,
  EGetAddress, EProvinceAddress, ECompanyArea, ECityAddress, EGetTownAddress, ECompanyEditArea, RSetState, EPostCompanyArea
} from './../../common/action'
import { useNavigate, useParams } from 'react-router-dom'
import { getFirstKind } from '../../services/price'
import { filter } from 'lodash'
import { LeftSquareOutlined } from '@ant-design/icons'
const { Option } = Select

export default () => {
  /*--------------------- 常变量 ---------------------*/
  const params = useParams()
  const navigate = useNavigate()
  const { companyDetail, isGetDetail, provinceList, cityList } = useConnect(NCompany)
  const { addressList, townAddressList } = useConnect(NWorker)
  const [selectProvince, setselectProvince] = useState<any>(null)
  const [provinceCode, setprovinceCode] = useState<any>(null)
  const [cityCode, setcityCode] = useState<any>(null)
  const [selectValue, setSelectValue] = useState<any>([])
  const [selectCity, setSelectCity] = useState<any>(null)
  const [firstKind, setFirstKind] = useState<any>(null)
  /*--------------------- 函数 ---------------------*/
  // 提示
  const openNotificationWithIcon = () => {
    notification.success({
      message: '成功！',
      description: '修改成功',
      duration: 2,
    })
  }
  // 省市选择
  function funSelectProvince(e: any) {
    let provinceCode
    setselectProvince(e)
    provinceList.forEach((province: any, index: number) => {
      if (province.name === e) {
        provinceCode = province.code
        setprovinceCode(provinceCode)
        effect(NCompany, ECityAddress, {
          code: provinceCode,
        })
        return
      }
    })
  }
  // 城市选择
  const funSelectCity = (e: any) => {
    setSelectCity(e)
    let cityCode
    cityList.forEach((city: any, index: number) => {
      if (city.name === e) {
        cityCode = city.code
        setcityCode(cityCode)
        effect(NWorker, EGetAddress, { code: cityCode })
        effect(NCompany, ECompanyArea, {
          companyID: companyDetail.id,
          areaCode: cityCode
        })
        return
      }
    })
  }
  // 报存城市修改 
  const saveRecycelAddress = () => {
    let id = params.id
    if (!selectCity || !selectProvince) {
      return
    }
    effect(NCompany, EPostCompanyArea, { id, postDatas: selectValue })
    effect(NCompany, ECompanyEditArea, {
      id,
      recycelProvince: selectProvince,
      recycelCity: selectCity,
      recycelCode: JSON.stringify([provinceCode, cityCode])
    }).then(() => {
      openNotificationWithIcon()
      navigate(`/Other/CompanyArea/${id}`)
    })
  }
  // 类型选择
  const setRecycelType = (kind: any, theIndex: any) => {
    firstKind.map((vo: any) => {
      if (kind.id === vo.id) {
        if (!vo.value) {
          vo.value = true
        } else {
          vo.value = false
        }
      }
    })
    setFirstKind(firstKind.concat())
  }

  // 区域选择
  const areaSelect = (address: any, index: number) => {
    addressList.map((vo: any) => {
      if (vo === address) {
        if (!vo.value) {
          vo.value = true
        } else {
          vo.value = false
        }
      }
    })
    reducer(NWorker, RSetState, { addressList: addressList.concat() })
  }
  // 街道选择
  const streetSelect = (address: any, index: number) => {
    townAddressList.map((vo: any) => {
      if (vo === address) {
        if (!vo.value) {
          vo.value = true
        } else {
          vo.value = false
        }
      }
    })
    reducer(NWorker, RSetState, { townAddressList: townAddressList.concat() })
  }
  // 区域全选
  const selectAllArea = () => {
    addressList.map((vo: any) => {
      if (!vo.value) {
        vo.value = true
      } else {
        vo.value = false
      }
    })
    reducer(NWorker, RSetState, { addressList: addressList.concat() })
  }
  // 街道全选
  const selectAllStreet = () => {
    townAddressList.map((vo: any) => {
      if (!vo.value) {
        vo.value = true
      } else {
        vo.value = false
      }
    })
    reducer(NWorker, RSetState, { townAddressList: townAddressList.concat() })
  }
  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    const id = params.id
    effect(NCompany, EDetDetail, { id })
    effect(NCompany, EGet)
    effect(NCompany, EProvinceAddress, { code: '' })
    getFirstKind({}).then((firstKind: any) => {
      setFirstKind(firstKind.data)
    })
  }, [])

  useEffect(() => {
    let objAddress = filter(addressList, { value: true })
    let objTown = filter(townAddressList, { value: true })
    let objType = filter(firstKind, { value: true })
    if (objType.length > 0 && objTown.length > 0) {
      let arrobj: any = []
      for (let index = 0; index < objType.length; index++) {
        const element = objType[index];
        for (let a = 0; a < objTown.length; a++) {
          let townItem: any = {}
          townItem.type = element.id
          townItem.typeName = element.name
          arrobj.push({ ...townItem, ...objTown[a] })
        }
      }
      arrobj.map((vo: any) => {
        let areaCode = vo.code.substring(0, 6)
        vo.areaCode = areaCode
        vo.area = filter(objAddress, { code: areaCode })
          && filter(objAddress, { code: areaCode })[0]
          && filter(objAddress, { code: areaCode })[0].name
        delete vo.value
      })
      setSelectValue(arrobj)
    }
  }, [addressList, townAddressList, firstKind])
  useEffect(() => {
    if (companyDetail) {
      setselectProvince(companyDetail.recycelProvince ? companyDetail.recycelProvince : '')
      setSelectCity(companyDetail.recycelCity ? companyDetail.recycelCity : '')
    }
  }, [isGetDetail])

  useEffect(() => {
    if (companyDetail) {
      let code = JSON.parse(companyDetail.recycelCode)
      effect(NWorker, EGetAddress, { code: (code && code[1]) || 11 })
      setprovinceCode(code && code[0])
      setcityCode(code && code[1])
      effect(NCompany, ECityAddress, {
        code: code && code[0],
      })
    }
  }, [companyDetail])

  useEffect(() => {
    let objSelect = filter(addressList, { value: true })
    if (addressList && objSelect.length > 0) {
      effect(NWorker, EGetTownAddress, {
        codeArr: objSelect.map((vo: any) => vo.code),
      })
    }
  }, [addressList])
  /*--------------------- 渲染 ---------------------*/
  return (
    <div className={styles.main}>
      <h2>
        <LeftSquareOutlined size={12} title='返回列表' onClick={() => { navigate(`/Other/CompanyArea/${params.id}`) }} />
        <span style={{ marginLeft: 10, fontSize: 16, fontWeight: 'bolder' }}>{companyDetail && companyDetail.companyName}-新增服务区域</span>
      </h2>
      <div className={styles.wrapper}>
        <span className={styles.title}>省份选择：</span>
        <div className={styles.right_wrapper}>
          <Select
            value={selectProvince}
            style={{ width: 200 }}
            onChange={e => {
              funSelectProvince(e)
            }}>
            {provinceList.map((province: any, index: any) => (
              <Option key={province.name}>{province.name}</Option>
            ))}
          </Select>
        </div>
      </div>
      <div className={styles.wrapper}>
        <span className={styles.title}>城市选择：</span>
        <div className={styles.right_wrapper}>
          <Select
            value={selectCity}
            style={{ width: 200 }}
            onChange={e => {
              funSelectCity(e)
            }}>
            {cityList && companyDetail && cityList.map((city: any, index: any) => <Option key={city.name}>{city.name}</Option>)}
          </Select>
        </div>
      </div>

      {addressList ? (
        <div className={styles.wrapper}>
          <span className={styles.title}>区域选择：<p style={{
            textAlign: "center", color: '#1890ff', cursor: 'pointer', textDecoration: 'underline'
          }}
            onClick={() => { selectAllArea() }}>全选</p></span>
          <div className={styles.right_wrapper}>
            {addressList.map((address: any, index: any) => (
              <div
                className={`${styles.area_item} ${address?.value ? styles.selected : null}`}
                key={address + index}
                onClick={() => {
                  areaSelect(address, index)
                }}>
                {address.name}
              </div>
            ))}
          </div>
        </div>
      ) : null}

      {townAddressList ? (
        <div className={styles.wrapper}>
          <span className={styles.title}>街道/镇：<p style={{
            textAlign: "center", color: '#1890ff', cursor: 'pointer', textDecoration: 'underline'
          }} onClick={() => { selectAllStreet() }}>全选</p></span>
          <div className={styles.right_wrapper}>
            {townAddressList.map((address: any, index: any) => (
              <div
                key={address.code + index}
                className={`${styles.area_item} ${address?.value ? styles.selected : null}`}
                onClick={() => {
                  streetSelect(address, index)
                }}>
                {address.name}
              </div>
            ))}
          </div>
        </div>
      ) : null}

      {townAddressList ? (
        <Fragment>
          <h3 style={{ marginTop: 20, marginBottom: 20 }}>服务商回收类型设置</h3>
          <div className={styles.wrapper}>
            <span className={styles.title}>回收类型：</span>
            <div className={styles.right_wrapper}>
              {firstKind
                ? firstKind.map((kind: any, index: any) => (
                  <div
                    className={`${styles.area_item} ${kind?.value ? styles.selected : null}`}
                    key={kind + index}
                    onClick={() => {
                      setRecycelType(kind, index)
                    }}>
                    {kind.name}
                  </div>
                ))
                : null}
            </div>
          </div>
          <div className={styles.wrapper}>
            <span className={styles.title}>是否保存：</span>
            <div className={styles.right_wrapper}>
              <Button
                onClick={() => {
                  saveRecycelAddress()
                }}
                style={{ background: '#1890ff', color: '#ffffff' }}>
                保存
              </Button>
            </div>
          </div>
        </Fragment>
      ) : null
      }
    </div >
  )
}
