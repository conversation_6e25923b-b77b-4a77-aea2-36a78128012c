import Taro from '@tarojs/taro'
import React, { Component } from 'react'
import T from '../../config/T'

// import BaseLayout from "../../layouts/baseLayout";
import { View, Image, Button, Text, Checkbox } from '@tarojs/components'
import { AtModal, AtIcon, AtImagePicker, AtMessage } from 'taro-ui'
import { useSelector, useDispatch } from 'react-redux'
import './index.less'
// import "./index.scss"
import { NOrder } from '../../config/constants'
import { useEffect, useState } from 'react'
import E from '../../config/E'
import Config from '../../config/config'
import 'taro-ui/dist/style/components/image-picker.scss'
import 'taro-ui/dist/style/components/icon.scss'

const Index = () => {
  const { haveChosen, whichType } = useSelector(state => state.NOldGoods)
  const { orderID } = useSelector(state => state.NOrder)
  const { isAddItem } = useSelector(state => state.NSystem)
  const dispatch = useDispatch()
  const [files, setFiles] = useState([])
  const [showRemind, setShowRemind] = useState(false)
  const [imgs, setImgs] = useState([])

  async function getUrl(list) {
    let imgs = []
    for (let i = 0; i < list.length; i++) {
      await Taro.uploadFile({
        url: `${Config.SERVER_HOME}file`,
        filePath: list[i].url,
        name: 'file',
        formData: {
          user: 'test',
        },
        success: async res => {
          var data = JSON.parse(res.data)
          if (data.url) {
            imgs.push(data.url)
          }
        },
      })
    }
    setImgs(imgs)
  }
  function previewImage(current) {
    wx.previewImage({
      current: current, // 当前显示图片的http链接
      urls: imgs, // 需要预览的图片http链接列表
    })
  }
  function onChange(files) {
    setFiles(files)
    getUrl(files)
  }
  function onFail(mes) {
    console.log(mes)
  }
  function onImageClick(index, file) {
    previewImage(imgs[index])
  }
  function uploadImg() {
    let haveChoose = haveChosen
    let type = Number(whichType.id)
    let list = []
    haveChoose.forEach((value, index) => {
      if (value.item.length > 0) {
        value.item.forEach((item, mark) => {
          let obj = {}
          // obj.orderID = orderID
          obj.wasteID = item.id
          obj.wasteTypeID = type === 1 || type === 2 ? item.name : value.name
          obj.wasteAttribute1ID = item.wasteAttribute1ID ? item.wasteAttribute1ID : null
          obj.wasteAttribute2ID = item.wasteAttribute2ID ? item.wasteAttribute2ID : null
          obj.wasteAttribute3ID = item.wasteAttribute3ID ? item.wasteAttribute3ID : null
          obj.wasteAttribute4ID = item.wasteAttribute4ID ? item.wasteAttribute4ID : null
          obj.wasteAttribute5ID = item.wasteAttribute5ID ? item.wasteAttribute5ID : null
          obj.wasteAttribute6ID = item.wasteAttribute6ID ? item.wasteAttribute6ID : null
          obj.wasteAttribute7ID = item.wasteAttribute7ID ? item.wasteAttribute7ID : null

          list.push(obj)
        })
      }
    })

    dispatch.NOrder[NOrder.ETheOrderInfo]({
      userUpload: JSON.stringify(imgs),
      from: E.OrderFrom.冬瓜回收,
      orderWastes: list,
    })
  }
  useEffect(() => {
    Taro.setNavigationBarTitle({ title: '照片上传' })
  }, [])
  return (
    <View className="uploadImg">
      <View className="upload_img">
        {files.length === 0 ? (
          <View className="upload_img_box_button">
            <Text className="upload_img_Icon_text">{T.uploadImg.uploadtxt}</Text>
          </View>
        ) : null}
        <AtImagePicker
          className={`${files.length === 0 ? 'theImage' : ''}`}
          length={3}
          count={6}
          files={files}
          onChange={onChange}
          onFail={onFail}
          onImageClick={onImageClick}
        />
      </View>
      <View className="have_chosen">
        <View className="have_chosen_title">
          <View className="have_chosen_title_text" style={{ fontSize: '3.7vw', color: '#101010' }}>
            <View></View>
            <View>{T.uploadImg.select}</View>
          </View>
          {/* （共选{haveChosen.length}种） */}
          {whichType && (whichType.id === 1 || whichType.id === 2) ? (
            <View className="waring_wrapper">
              <Text>（{T.uploadImg.minumun}）</Text>
              <Text
                onClick={() => {
                  setShowRemind(true)
                }}
              >
                {T.uploadImg.rule}
              </Text>
            </View>
          ) : (
            ''
          )}
        </View>

        {haveChosen.map((val, index) => (
          <View key={index + val}>
            {val.item.length > 0 ? (
              <View className="have_chosen_page_ul">
                <Text
                  className="have_chosen_ul_type"
                  style={whichType.id !== 1 && whichType.id !== 2 ? { borderColor: 'transparent' } : null}
                >
                  {val.title || val.name}
                </Text>
                <Text
                  className="have_chosen_ul_num"
                  style={{
                    marginLeft: '1vw',
                    fontSize: '3.2vw',
                    color: '#666666',
                  }}
                >
                  {`（已选${val.item && val.item.length ? val.item.length : '0'}种）`}
                </Text>
              </View>
            ) : null}
            {val.item && val.item.length > 0
              ? val.item.map((v, i) => (
                <View className="have_chosen_page_li" key={i + v}>
                  <Text className="have_chosen_page_li_text">{v.name || v.type}</Text>
                </View>
              ))
              : null}
          </View>
        ))}
      </View>
      <View
        className={`uploadImg_button isClick`}
        onClick={() => {
          uploadImg()
        }}
      >
        <View className="text_wrapper">
          <Text>
            {T.uploadImg.book}
          </Text>
        </View>
        {isAddItem ? <View className="add_item"></View> : null}
      </View>
      {showRemind ? (
        <View className="remind_wrapper">
          <View className="message_wrapper">
            <Text className="remindTitle">{'回收物起收规则'}</Text>
            <View className="content_wrapper">
              {E.RecycleRule.map((remind, mark) => (
                <Text className="remind_text" key={remind + mark}>
                  {remind}
                </Text>
              ))}
            </View>
            <View className="know_button_wrapper">
              <View
                className="know_button"
                onClick={() => {
                  setShowRemind(false)
                }}
              >
                {T.usedClothesModal.ok}
              </View>
            </View>
          </View>
        </View>
      ) : null}
      <AtMessage />
    </View>
  )
}

definePageConfig({
  navigationBarTitleText: '首页',
  navigationStyle: 'default',
  // "navigationStyle":"custom"
})
export default Index
