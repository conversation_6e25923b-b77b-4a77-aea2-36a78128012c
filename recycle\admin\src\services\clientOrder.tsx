import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'

export async function getOrderList(payload:any) {
  return requestGet('/clientOrder', payload)
}
export async function changeOrderStatus(payload:any) {
  return requestPut(`/clientOrder/${payload.id}`, payload)
}
export async function changeWorker(payload:any) {
  return requestPut(`/clientOrder/changeWorker/${payload.id}`, payload)
}
export async function changeOrderImportStatus(payload:any) {
  return requestPut(`/clientOrder/orderimport/${payload.id}`, payload)
}
export async function getWhichWorkers(payload:any) {
  return requestPost('/worker/whichWorkers', payload)
}
export async function sendSMS(payload:any) {
  return requestPost('sms', payload)
}
export async function getWorkerDetail(payload:any) {
  return requestGet(`worker/${payload.id}`)
}
export async function orderBack(payload:any) {
  return requestPost('clientOrder/orderBack', payload)
}
export async function orderSend(payload:any) {
  return requestPost('clientOrder/orderSend', payload)
}
export async function postNewOrder(payload:any) {
  return requestPost('clientOrder/orderNew', payload)
}
export async function postConfirmOrder(payload:any) {
  return requestPost('clientOrder/comfirmOrder', payload)
}
export async function seeComplaint(payload:any) {
  return requestPost('clientOrder/complaints', payload)
}
export async function devideOrder(payload:any) {
  return requestPost('clientOrder/devideOrder', payload)
}
export async function seeRating(payload:any) {
  return requestPost('clientOrder/rating', payload)
}
export async function getOrderDetail(payload:any) {
  return requestGet(`clientOrder/${payload.id}`, payload)
}
export async function getOrderCount(payload:any) {
  return requestGet('clientOrder/orderCount', payload)
}
export async function getOrderLog(payload:any) {
  return requestGet(`clientOrderLog/${payload.id}`, payload)
}

export async function getNotice(payload:any) {
  return requestGet(`noticeOrder`, payload)
}

export async function remindOrder(payload:any) {
  return requestPut(`/remind/${payload.id}`, payload)
}