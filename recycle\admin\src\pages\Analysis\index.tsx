import React, { Component, Fragment, useEffect, useState } from 'react'
import { Modal, Table } from 'antd'
import dayjs from 'dayjs'
import styles from './index.module.less'
import { getCompanyData, getTimeData } from '../../services/informationKanbanAPI'
import { useNavigate } from 'react-router-dom'
import { useConnect } from 'dva17'
import { NUser } from '../../common/action'

export default () => {
  const navigation = useNavigate()
  const { currentUser } = useConnect(NUser)
  const [companyData, setCompanyData] = useState<any>(null)
  const [orderList, setOrderList] = useState<any>([
    {
      imgUrl: ('https://c.evergreenrecycle.cn/zhihong/dayalready.png'),
      title: '总接单数',
      num: 0
    },
    {
      imgUrl: ('https://c.evergreenrecycle.cn/zhihong/allOrder.png'),
      title: '累计完成订单总数',
      num: 0
    },
    {
      imgUrl: ('https://c.evergreenrecycle.cn/zhihong/allMoney.png'),
      title: '累计流水',
      num: 0
    }
  ])

  /*--------------------- 响应 ---------------------*/
  /*--------------------- 渲染 ---------------------*/

  const [choiceData, setChoiceData] = useState<any>(dayjs(new Date())
    .add(-1, 'days')
    .format('YYYY-MM-DD'))

  //    展示订单数量和金额判断
  const [showNum, setShowNum] = useState<number>(1)
  //    事件区间选择
  const [showData, setShowData] = useState<number>(1)
  const [visible, setVisible] = useState<boolean>(false)
  const [showModalNum, setShowModalNum] = useState<number>(1)
  const [businessData, setBusinessData] = useState<any>([])
  const businessColumns = [
    {
      title: '行政区县',
      dataIndex: '1'
    },
    {
      title: '街道/镇',
      dataIndex: '2'
    },
    {
      title: '社区名称',
      dataIndex: '3'
    }
  ]
  const typeColumns = [
    {
      title: '类型',
      dataIndex: '1'
    },
    {
      title: '种类',
      dataIndex: '2'
    }
  ]
  const typeData = [
    {
      key: '1',
      1: '电视',
      2: '显像管/等离子/液晶电视'
    },
    {
      key: '2',
      1: '空调',
      2: '空调柜机/空调挂机'
    },
    {
      key: '3',
      1: '冰箱',
      2: '单门式/双门及以上'
    },
    {
      key: '4',
      1: '电脑',
      2: '笔记本/台式机'
    },
    {
      key: '5',
      1: '洗衣机',
      2: '双缸式/滚筒式/波轮式'
    },
    {
      key: '6',
      1: '玻璃',
      2: '大批量玻璃瓶/玻璃制品/大块透明玻璃'
    },
    {
      key: '7',
      1: '柜子',
      2: '衣柜/抽斗柜/电视柜/床头柜/橱柜/烟酒柜/书架/组装柜/文件柜/杂物柜'
    },
    {
      key: '8',
      1: '桌椅',
      2: '写字台/餐桌/梳妆台/麻将桌/椅子'
    },
    {
      key: '9',
      1: '床垫',
      2: '床垫'
    },
    {
      key: '10',
      1: '健身器材',
      2: '跑步机/登山机/自行车/综合式/按摩椅'
    },
    {
      key: '11',
      1: '床',
      2: '单人床/双人床/圆床/高低床/折叠车/儿童床'
    },
    {
      key: '12',
      1: '茶几',
      2: '茶几'
    },
    {
      key: '13',
      1: '沙发',
      2: '单人沙发/长沙发'
    }
  ]
  //   昨天数据
  const [addData, setAddData] = useState<any>(null)
  //   上周数据
  const [addWeek, setAddWeek] = useState<any>(null)
  //   上月数据
  const [addMonth, setAddMonth] = useState<any>(null)
  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    get_title()
    get_addData()
  }, [])
  // -------------------------------------事件------------------------------------
  const get_title = async () => {
    let completeData
    let HavehandData
    if (currentUser.level === '总部') {
      completeData = await getTimeData({
        payload: {
          status: '完成'
        }
      })
      HavehandData = await getTimeData({})
    } else {
      completeData = await getTimeData({
        payload: {
          status: '完成',
          id: currentUser.companyID
        }
      })
      HavehandData = await getTimeData({
        payload: {
          id: currentUser.companyID
        }
      })
    }
    let newOrderList = orderList
    newOrderList[0].num = await HavehandData.totalComplete
    newOrderList[1].num = await completeData.totalComplete
    newOrderList[2].num = await completeData.totalEstimatedMoney
    setOrderList(newOrderList.concat())
  }
  const get_addData = async () => {
    let data = await getTimeData({
      payload: {
        status: '完成',
        startDate: dayjs().subtract(1, 'days').format('YYYY-MM-DD'),
        endDate: dayjs().subtract(1, 'days').format('YYYY-MM-DD')
      }
    })
    setAddData(data)
  }

  const showModal = (num: number) => {
    setShowNum(num)
    setVisible(true)
  }
  const handleCancel = () => {
    setVisible(false)
  }
  // -------------------------------------渲染------------------------------------
  return (
    <div className={styles.main}>
      <div className={styles.main_changeData}>
      </div>
      <div className={styles.main_head}>
        {orderList && orderList.length > 0
          ? orderList.map((v: any, i: number) => (
            <div className={styles.main_head_item} key={i + v}>
              <img src={v.imgUrl} className={styles.main_head_item_icon} />
              <div className={styles.main_head_item_}>
                <div className={styles.main_head_item_text} style={{ fontSize: '16px' }}>
                  {v.title}
                </div>
                <div className={styles.main_head_item_text} style={{ fontSize: '22px' }}>
                  {i == 2 ? (v.num ? v.num / 100 : '0') : v.num}
                  {i == 2 ? '元' : '件'}
                </div>
              </div>
            </div>
          ))
          : null}
      </div>
      <div className={styles.main_dataAnalysis}>
        <div className={styles.main_dataAnalysis_left}>
          <div className={styles.left_nav}>
            <div className={styles.left_nav_title}>业务数据分析</div>
            <div className={styles.left_nav_button}>
              <div className={styles.button_}>
                <span
                  className={styles.button_type}
                  style={{ color: `${showNum == 1 ? 'rgba(23,210,159,1)' : ''}` }}
                  onClick={() => {
                    setShowNum(1)
                  }}>
                  订单数量
                </span>
                <span
                  className={styles.button_type}
                  onClick={() => {
                    setShowNum(2)
                  }}
                  style={{ color: `${showNum == 2 ? 'rgba(23,210,159,1)' : ''}` }}>
                  订单金额
                </span>
              </div>

              <div
                onClick={() => {
                  setShowData(1)
                }}
                className={styles.button_box}
                style={{ marginLeft: '20px', color: `${showData == 1 ? 'rgba(23,210,159,1)' : ''}` }}>
                昨日
              </div>
              <div
                onClick={() => {
                  setShowData(2)
                }}
                style={{ color: `${showData == 2 ? 'rgba(23,210,159,1)' : ''}` }}
                className={styles.button_box}>
                近7天
              </div>
              <div
                onClick={() => {
                  setShowData(3)
                }}
                style={{ color: `${showData == 3 ? 'rgba(23,210,159,1)' : ''}` }}
                className={styles.button_box}>
                近30天
              </div>
            </div>
          </div>
          <div className={styles.box}>
            {showData == 1 ? (
              <div className={styles.box_AddData}>
                {showNum == 1
                  ? `昨日完成单数：${addData && addData.totalComplete ? addData.totalComplete : '0'}件`
                  : `昨日订单金额：${addData && addData.totalEstimatedMoney ? addData.totalEstimatedMoney / 100 : '0.00'}元`}
              </div>
            ) : null}
          </div>
        </div>
        <div className={styles.main_dataAnalysis_right}>
          <div className={styles.right_item}>
            <span className={styles.item_left}>业务范围概览</span>
            <span
              onClick={() => {
                showModal(1)
              }}
              className={styles.item_right}>
              查看明细
            </span>
          </div>
          <div className={styles.right_item_}>
            覆盖行政区数：
            <span style={{ fontSize: '26px' }}>{'0'}</span>
          </div>
          <div className={styles.right_item_}>
            覆盖街道/镇数：
            <span style={{ fontSize: '26px' }}>{'0'}</span>
          </div>
          <div className={styles.right_item} style={{ marginTop: '20px' }}>
            <span className={styles.item_left}>回收类型</span>
          </div>
        </div>
      </div>
      <Modal open={visible} onCancel={handleCancel} footer={null}>
        <h4>{showModalNum == 1 ? '业务范围明细' : '回收物类型明细'}</h4>
        <Table
          columns={showModalNum == 1 ? businessColumns : typeColumns}
          dataSource={showModalNum == 1 ? businessData : typeData}
          pagination={false}
          scroll={{ y: '300px' }}
          size="middle"
        />
      </Modal>
    </div>
  )
}

