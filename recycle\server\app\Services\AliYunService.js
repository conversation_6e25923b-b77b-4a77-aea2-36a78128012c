'use strict'

const SMSClient = require('@alicloud/sms-sdk')
const axios = require('axios')
const _ = require('lodash')

const Env = use('Env')
const Helpers = use('Helpers')

const HOME_PATH = Env.get('HOME_PATH')

const { ERR, E } = require('../../../../constants');
const { LogExternalApiRequest, LogMsg } = require('../Models')
const { YUNQIXUN_USER_NAME, YUNQIXUN_PASSWORD } = require('../Util/Config')
const { calculateSign } = require('../Util/CryptUtil')

const AliYunService = {

  // 短信相关
  async sendSMS(PhoneNumbers, TemplateCode, TemplateParam, orderID = 0) {
    const smsClient = new SMSClient({
      accessKeyId: Env.get('ALI_ACCESS_KEY_ID'),
      secretAccessKey: Env.get('ALI_ACCESS_KEY_SECRET')
    })
    // console.log('[sendSMS]', TemplateParam)
    TemplateParam = (TemplateParam) ? JSON.stringify(TemplateParam) : ''
    const params = {
      PhoneNumbers,
      SignName: Env.get('SMS_SIGN_NAME'),
      TemplateCode: TemplateCode,
      TemplateParam: TemplateParam
    }
    console.log('[sendSMS]', params)
    try {
      const result = await smsClient.sendSMS(params)
      await LogMsg.create({
        orderID: orderID,
        success: result.Code === "OK" ? 1 : 0,
        openid: PhoneNumbers,
        msgBody: JSON.stringify(params),
        response: JSON.stringify(result)
      })
      return result
    } catch (error) {
      console.log(error);
    }
  },
  async yunqixunSms({ clientName, clientPhone, masterName, masterPhone, orderID }) {
    const userName = YUNQIXUN_USER_NAME
    const password = YUNQIXUN_PASSWORD
    const signName = "菜鸟回收"
    // const content = `【${signName}】尊敬的${clientName},您的以旧换新订单已安排师傅${masterName}-${masterPhone}，618活动期间部分区域订单量较大可能导致上门迟滞，给您带来不便还请您谅解，十分抱歉`
    // const content = `【${signName}】尊敬的${clientName},您的以旧换新订单已安排师傅${masterName}-${masterPhone}，临近年关部分区域回收师傅返乡可能导致上门迟滞，给您带来不便还请您谅解，十分抱歉！`;
    const content = `【${signName}】尊敬的${clientName},您的以旧换新订单已安排师傅${masterName}-${masterPhone}，请保持手机畅通，师傅会尽快联系您。谢谢！`;
    const timestamp = Date.now();
    const sign = calculateSign(userName, password, content, timestamp);
    const url = 'http://121.41.41.182:8001/sms/api/sendMessage';
    const data = {
      userName: userName,
      content: content,
      phoneList: [clientPhone],
      timestamp: timestamp,
      sign: sign
    };
    try {
      let response
      response = await axios.post(url, data, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json;charset=utf-8'
        }
      });
      // console.log(response.data);
      await LogMsg.create({
        orderID: orderID,
        openid: clientPhone,
        msgBody: JSON.stringify(content),
        response: JSON.stringify((response && response.data) || '发送成功')
      })
      return response.data;
    } catch (error) {
      console.error(error);
    }
  },
  async sendCodeSms({ clientPhone, code }) {
    const userName = YUNQIXUN_USER_NAME
    const password = YUNQIXUN_PASSWORD
    const signName = "菜鸟回收"
    const content = `【${signName}】您好，本次验证码为${code}，请在10分钟内完成验证。`;
    const timestamp = Date.now();
    const sign = calculateSign(userName, password, content, timestamp);
    const url = 'http://121.41.41.182:8001/sms/api/sendMessage';
    const data = {
      userName: userName,
      content: content,
      phoneList: [clientPhone],
      timestamp: timestamp,
      sign: sign
    };
    try {
      const response = await axios.post(url, data, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json;charset=utf-8'
        }
      });
      await LogMsg.create({
        openid: clientPhone,
        msgBody: JSON.stringify(data),
        response: JSON.stringify(response.data)
      })
      return response.data;
    } catch (error) {
      console.error(error);
    }
  }
}

module.exports = AliYunService
