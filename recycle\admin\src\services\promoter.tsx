import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'

export async function getPromoterList(payload:any) {
  return requestGet('/promoter', payload)
}
export async function createPromoter(payload:any) {
  return requestPost('/promoter', payload)
}
export async function editPromoter(payload:any) {
  return requestPut(`/promoter/${payload.id}`, payload)
}
export async function getDotList(payload:any) {
  return requestGet(`/dotList`, payload)
}
export async function getFile(payload:any) {
  return requestGet(`/downloadFile`, payload)
}

