import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'

export async function getComplaintList(payload: any) {
  return requestGet('/checkComplaint', payload)
}

export async function getComplaintDetail(payload: any) {
  return requestGet(`/checkComplaint/${payload}`)
}

export async function changeStatus(payload: any) {
  return requestPut(`/checkComplaint/${payload.id}`, {status: payload.status})
}
