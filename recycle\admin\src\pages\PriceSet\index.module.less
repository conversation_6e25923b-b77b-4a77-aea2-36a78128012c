.page {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.item_wrapper {
  .item {
    display: flex;
    margin-bottom: 20px;
    letter-spacing: 0.8px;
    //border-bottom: 1px solid #cccccc;
    &:last-child {
      border-bottom: none;
    }
    .item_title {
      display: inline-block;
      width: 90px;
      min-width: 90px;
      text-align: right;
      font-weight: 600;
    }
    .item_content {
      display: flex;
      flex: 1;
      flex-wrap: wrap;
      > img {
        width: 45%;
        height: 200px;
        margin-right: 20px;
      }
      .item_pic {
        height: 100px;
        width: 100px;
        border-radius: 4px;
        margin-right: 10px;
        margin-bottom: 10px;
        box-shadow: 0 2px 4px #cccccc;
      }
      .waste_wrapper {
        width: 100%;
        .waste_wrapper_title {
          width: 100%;
          display: flex;
          font-weight: 600;
          .title {
            width: 50%;
            text-align: center;
          }
        }
      }
      .content_wrapper {
        width: 100%;
        display: flex;
        margin: 10px 0;
        .name {
          width: 50%;
          text-align: center;
        }
        .price {
          width: 50%;
          text-align: center;
        }
      }
    }
  }
}
.operate_wrapper {
  color: #1890ff;
  span {
    cursor: pointer;
  }
}
.area_item {
  width: 120px;
  height: 34px;
  border: 1px solid #1890ff;
  border-radius: 4px;
  line-height: 34px;
  text-align: center;
  color: #1890ff;
  flex-shrink: 0;
  margin-right: 20px;
  margin-bottom: 10px;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.selected {
  background: #1890ff;
  color: #ffffff;
}

.newCompany_item_password {
  margin: 0 auto;
  margin-top: 20px;
  .newCompany_item_title {
    display: inline-block;
    width: 80px;
    height: 32px;
    line-height: 32px;
    text-align: right;
  }
}
