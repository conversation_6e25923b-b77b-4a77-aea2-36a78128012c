.successfulOrder {
  width: 100vw;
  height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0px 2px 11px #e9ebed inset;
  .title_wrapper1 {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1;
    background: #ffffff;
    display: flex;
    width: 100%;
    border-bottom: 1px solid #eeeeee;
    .title {
      width: 25%;
      text-align: center;
      line-height: 80px;
      height: 80px;
      position: relative;
      font-size: 28px;
      color: #556073;
      .mark_item {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 50px;
        height: 8px;
        border-radius: 4px;
          background: linear-gradient(to right, rgba(119, 220, 143, 1) 0% , rgba(21, 179, 129, 1) 100%);
      }
    }
  }
  .content_wrapper {
    margin-top: 80px;
    margin-bottom: 120px;
    width: 100%;
    .content {
      padding: 20px 40px;
      padding-bottom: 40px;
      position: relative;
      display: flex;
      width: 100%;
      letter-spacing: 0.8px;
      border-bottom: 1px solid #f5f6f8;
      .left_wrapper {
        width: 80px;
        margin-right: 10px;
        .the_log {
          width: 80px;
          height: 80px;
          // border: 1px solid #15b381;
          border-radius: 50%;
          overflow: hidden;
          display: flex;
          justify-content: center;
          align-items: center;
          image {
            width: 100%;
            height: 100%;
          }
        }
      }
      .right_wrapper {
        flex: 1;
        .title_wrapper {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .left_title {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 80px;
            > Text {
              font-size: 24px;
              color: #556073;
              &:first-child {
                font-size: 28px;
                color: #333333;
                font-weight: 700;
              }
            }
          }
          .right_title {
            border: 1px solid #15b381;
            color: #15b381;
            height: 42px;
            line-height: 42px;
            border-radius: 21px;
            padding: 0 20px;
            font-size: 24px;
            font-weight: 700;
          }
        }
        .content_text {
          margin: 20px 0;
          font-size: 28px;
          color: #333333;
        }
        .operate_wrapper {
          margin: 20px 0;
          width: 100%;
          display: flex;
          justify-content: flex-end;
          .operate_item {
            width: 200px;
            display: flex;
            justify-content: space-between;
            > View {
              width: 200px;
              display: flex;
              align-items: center;
              justify-content: space-around;
              font-size: 24px;
              color: #556073;
            }
          }
        }
        .comment_wrapper {
          background: #eeeeee;
          padding: 15px 15px;
          padding-bottom: 1px;
          .comment {
            .user_name {
              color: #15b381;
              font-weight: 700;
            }
            margin-bottom: 15px;
            line-height: 36px;
            font-size: 28px;
            color: #333333;
          }
        }
      }
    }
  }
}
.input_mask {
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: 2;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.1);
  .mask {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
  }
  .input_wrapper {
    width: 100%;
    height: 120px;
    position: absolute;
    left: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    background: #ffffff;
    .the_input {
      border: 1px solid #ccc;
      width: 520px;
      height: 60px;
      border-radius: 30px;
      padding-left: 24px;
      padding-right: 24px;
      font-size: 28px;
      letter-spacing: 1px;
      color: #333;
    }
    .send_button {
      height: 100%;
      display: flex;
      align-items: center;
      color: #333;
      font-size: 28px;
      padding-right: 20px;
    }
  }
}
.add_item {
  width: 100%;
  height: 48px;
}
