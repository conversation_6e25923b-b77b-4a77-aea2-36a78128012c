import { Modal, Button, Table, Image, Input } from 'antd'
import { useEffect, useState } from 'react'
import { useConnect, effect } from 'dva17'
import { NWorker, EGetWorkerReview, EChangeWorkerUse } from '../../common/action'
import { WorkerAccountStatus } from '../../common/enum'
import dayjs from 'dayjs'
import styles from './index.module.less'

// 申请列表组件
interface WorkerReviewModalProps {
  visible: boolean;
  companyID: number | null;
  onClose: () => void;
}

const WorkerReviewModal = ({ visible, onClose, companyID }: WorkerReviewModalProps) => {
  const { workerReviewList } = useConnect(NWorker);
  const [reviewPageCurrent, setReviewPageCurrent] = useState(1);
  const [reviewPageSize, setReviewPageSize] = useState(10);
  const [reviewTotal, setReviewTotal] = useState(0);
  const [searchName, setSearchName] = useState('');
  const [searchValue, setSearchValue] = useState('');

  useEffect(() => {
    if (visible ) {
      effect(NWorker, EGetWorkerReview, { companyID, current: reviewPageCurrent, pageSize: reviewPageSize, workerName: searchName });
    }
  }, [visible, companyID, reviewPageCurrent, reviewPageSize, searchName]);

  useEffect(() => {
    if (workerReviewList?.total) {
      setReviewTotal(workerReviewList.total);
    }
  }, [workerReviewList]);

  const refreshPage = () => {
    effect(NWorker, EGetWorkerReview, { companyID, current: reviewPageCurrent, pageSize: reviewPageSize, workerName: searchName });
  };

  const handleSearch = () => {
    setSearchName(searchValue);
    setReviewPageCurrent(1);
  };

  const handleReset = () => {
    setSearchValue('');
    setSearchName('');
    setReviewPageCurrent(1);
  };

  const seeDetail = (row: any) => {
    Modal.info({
      title: '师傅明细',
      content: (
        <div className={styles.item_wrapper}>
          <div className={styles.item} style={{ marginTop: 30, paddingBottom: 10 }}>
            <span className={styles.item_title}>姓名：</span>
            <div className={styles.item_content}>{row.workerName}</div>
          </div>
          <div className={styles.item}>
            <span className={styles.item_title}>手机号：</span>
            <div className={styles.item_content}>{row.mobile}</div>
          </div>
          <div className={styles.item}>
            <span className={styles.item_title}>地址：</span>
            <div className={styles.item_content}>{row.province + row.city + row.district + row.subDistrct + row.address}</div>
          </div>
          <div className={styles.item}>
            <span className={styles.item_title}>身份证号码：</span>
            <div className={styles.item_content}>{row.idCardNo}</div>
          </div>
          <div className={styles.item}>
            <span className={styles.item_title}>身份证图片：</span>
            <div className={styles.item_content}>
              <Image src={row.idCardImg} style={{ width: 200, height: 200 }} />
              <Image src={row.idCardBackImg} style={{ width: 200, height: 200 }} />
            </div>
          </div>
        </div>
      ),
      width: 700,
    });
  };

  const columns = [
    { title: '姓名', render: (record: any) => <span>{record.workerName}</span> },
    { title: '联系电话', render: (record: any) => <span>{record.mobile}</span> },
    { title: '网点', render: (record: any) => <span>{record?.company?.companyName}</span> },
    { 
      title: '申请时间', 
      dataIndex: 'createdAt', 
      render: (createdAt: any) => <span>{createdAt ? dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss') : '-'}</span> 
    },
    {
      title: '人员信息', 
      render: (record: any) => (
        <Button onClick={() => seeDetail(record)}>查看</Button>
      ),
    },
    {
      title: '操作',
      render: (record: any) => (
        <div className={styles.operate_wrapper}>
          <Button
            style={{ marginRight: 15 }}
            onClick={async () => {
              await effect(NWorker, EChangeWorkerUse, {
                id: record.id,
                isUse: WorkerAccountStatus.Agree,
              }).then(() => {
                refreshPage();
              });
            }}>
            同意
          </Button>
          <Button
            style={{ marginRight: 15 }}
            danger
            onClick={async () => {
              await effect(NWorker, EChangeWorkerUse, {
                id: record.id,
                isUse: WorkerAccountStatus.Reject,
              }).then(() => {
                refreshPage();
              });
            }}>
            拒绝
          </Button>
        </div>
      ),
    },
  ];

  return (
    <Modal
      title="申请人员列表"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
    >
      <div style={{ marginBottom: 16, display: 'flex' }}>
        <Input
          placeholder="请输入师傅姓名"
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          style={{ width: 200, marginRight: 8 }}
          onPressEnter={handleSearch}
        />
        <Button type="primary" onClick={handleSearch} style={{ marginRight: 8 }}>
          搜索
        </Button>
        <Button onClick={handleReset}>
          重置
        </Button>
      </div>
      <Table
        columns={columns}
        loading={!workerReviewList}
        dataSource={workerReviewList?.data || []}
        rowKey={(record) => JSON.stringify(record)}
        pagination={{
          current: reviewPageCurrent,
          pageSize: reviewPageSize,
          total: reviewTotal,
          onChange: async (page, pageSize) => {
            setReviewPageCurrent(page);
            if (pageSize) setReviewPageSize(pageSize);
          },
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条`
        }}
      />
    </Modal>
  );
};

export default WorkerReviewModal; 