---
description: 性能优化规范和最佳实践
---

# 性能优化规范和最佳实践

## 前端性能优化

### 小程序性能优化
```javascript
// 1. 图片优化
const optimizeImage = (imagePath) => {
  return {
    // 使用 WebP 格式
    webp: imagePath.replace(/\.(jpg|png)$/, '.webp'),
    // 懒加载
    lazyLoad: true,
    // 压缩质量
    quality: 80,
    // 渐进式加载
    progressive: true
  };
};

// 2. 页面分包配置
// app.config.js
export default {
  pages: [
    'pages/home/<USER>',
    'pages/order/index'
  ],
  subPackages: [
    {
      root: 'pages/user',
      pages: [
        'profile/index',
        'orders/index',
        'address/index'
      ]
    },
    {
      root: 'pages/worker',
      pages: [
        'dashboard/index',
        'orders/index'
      ]
    }
  ],
  // 预下载分包
  preloadRule: {
    'pages/home/<USER>': {
      network: 'all',
      packages: ['pages/user']
    }
  }
};

// 3. 数据缓存策略
const cacheManager = {
  // 内存缓存
  memoryCache: new Map(),
  
  // 本地存储缓存
  setCache: (key, data, expiry = 3600000) => { // 默认1小时
    const cacheData = {
      data,
      timestamp: Date.now(),
      expiry
    };
    Taro.setStorageSync(key, cacheData);
  },
  
  getCache: (key) => {
    const cacheData = Taro.getStorageSync(key);
    if (!cacheData) return null;
    
    const now = Date.now();
    if (now - cacheData.timestamp > cacheData.expiry) {
      Taro.removeStorageSync(key);
      return null;
    }
    
    return cacheData.data;
  },
  
  // 请求缓存装饰器
  withCache: (fn, cacheKey, expiry) => {
    return async (...args) => {
      const cached = cacheManager.getCache(cacheKey);
      if (cached) return cached;
      
      const result = await fn(...args);
      cacheManager.setCache(cacheKey, result, expiry);
      return result;
    };
  }
};

// 4. 组件优化
import { memo, useMemo, useCallback } from 'react';

const OrderItem = memo(({ order, onUpdate }) => {
  // 使用 useMemo 缓存计算结果
  const formattedPrice = useMemo(() => {
    return `¥${order.price.toFixed(2)}`;
  }, [order.price]);
  
  // 使用 useCallback 缓存事件处理函数
  const handleUpdate = useCallback(() => {
    onUpdate(order.id);
  }, [order.id, onUpdate]);
  
  return (
    <View className="order-item">
      <Text>{order.title}</Text>
      <Text>{formattedPrice}</Text>
      <Button onClick={handleUpdate}>更新</Button>
    </View>
  );
});
```

### React 管理后台性能优化
```typescript
// 1. 虚拟列表优化
import { FixedSizeList as List } from 'react-window';

const OrderList: React.FC<{ orders: Order[] }> = ({ orders }) => {
  const Row = ({ index, style }: any) => (
    <div style={style}>
      <OrderItem order={orders[index]} />
    </div>
  );

  return (
    <List
      height={600}
      itemCount={orders.length}
      itemSize={80}
      width="100%"
    >
      {Row}
    </List>
  );
};

// 2. 代码分割和懒加载
import { lazy, Suspense } from 'react';
import { Spin } from 'antd';

const OrderManage = lazy(() => import('./pages/OrderManage'));
const UserManage = lazy(() => import('./pages/UserManage'));

const App = () => (
  <Suspense fallback={<Spin size="large" />}>
    <Routes>
      <Route path="/orders" element={<OrderManage />} />
      <Route path="/users" element={<UserManage />} />
    </Routes>
  </Suspense>
);

// 3. ProTable 性能优化
const optimizedColumns = useMemo(() => [
  {
    title: '订单号',
    dataIndex: 'order_no',
    width: 120,
    fixed: 'left',
    // 避免不必要的渲染
    shouldCellUpdate: (record, prevRecord) => 
      record.order_no !== prevRecord.order_no
  },
  {
    title: '操作',
    width: 200,
    fixed: 'right',
    render: (_, record) => (
      <ActionButtons 
        record={record}
        // 使用 key 避免重新渲染
        key={`actions-${record.id}-${record.status}`}
      />
    )
  }
], []);

// 4. 数据预加载和缓存
const useOrdersData = (filters: any) => {
  const queryClient = useQueryClient();
  
  return useQuery({
    queryKey: ['orders', filters],
    queryFn: () => fetchOrders(filters),
    staleTime: 5 * 60 * 1000, // 5分钟内不重新请求
    cacheTime: 10 * 60 * 1000, // 缓存10分钟
    // 预加载下一页
    onSuccess: (data) => {
      if (data.hasNextPage) {
        queryClient.prefetchQuery({
          queryKey: ['orders', { ...filters, page: filters.page + 1 }],
          queryFn: () => fetchOrders({ ...filters, page: filters.page + 1 })
        });
      }
    }
  });
};
```

## 后端性能优化

### 数据库优化
```javascript
// 1. 查询优化
class OrderController {
  async index({ request, response }) {
    const page = request.input('page', 1);
    const limit = Math.min(request.input('limit', 20), 100); // 限制每页最大数量
    
    // 使用索引优化查询
    const orders = await Order.query()
      .select(['id', 'order_no', 'status', 'created_at', 'user_id']) // 只选择需要的字段
      .with('user', (builder) => {
        builder.select(['id', 'name', 'phone']); // 关联查询只选择必要字段
      })
      .whereIn('status', ['pending', 'assigned', 'in_progress']) // 使用索引
      .orderBy('created_at', 'desc')
      .paginate(page, limit);
      
    return response.json({
      code: 200,
      data: orders
    });
  }
  
  // 批量操作优化
  async batchUpdate({ request, response }) {
    const { order_ids, status } = request.all();
    
    // 使用事务批量更新
    await Database.transaction(async (trx) => {
      await Order.query(trx)
        .whereIn('id', order_ids)
        .update({ status, updated_at: new Date() });
        
      // 批量插入日志
      const logs = order_ids.map(id => ({
        order_id: id,
        action: 'status_change',
        new_status: status,
        created_at: new Date()
      }));
      
      await OrderLog.createMany(logs, trx);
    });
    
    return response.json({ code: 200, message: '批量更新成功' });
  }
}

// 2. 缓存策略
const Redis = use('Redis');

class CacheService {
  // 查询结果缓存
  static async getCachedOrders(cacheKey, queryFn) {
    const cached = await Redis.get(cacheKey);
    if (cached) {
      return JSON.parse(cached);
    }
    
    const result = await queryFn();
    await Redis.setex(cacheKey, 300, JSON.stringify(result)); // 缓存5分钟
    return result;
  }
  
  // 分布式锁
  static async withLock(lockKey, fn, timeout = 10000) {
    const lockValue = Date.now().toString();
    const acquired = await Redis.set(lockKey, lockValue, 'PX', timeout, 'NX');
    
    if (!acquired) {
      throw new Error('获取锁失败');
    }
    
    try {
      return await fn();
    } finally {
      // 安全释放锁
      const currentValue = await Redis.get(lockKey);
      if (currentValue === lockValue) {
        await Redis.del(lockKey);
      }
    }
  }
}

// 3. API 限流
class RateLimitMiddleware {
  async handle({ request, response }, next) {
    const ip = request.ip();
    const key = `rate_limit:${ip}`;
    
    const current = await Redis.incr(key);
    if (current === 1) {
      await Redis.expire(key, 60); // 1分钟窗口
    }
    
    if (current > 100) { // 每分钟最多100次请求
      return response.status(429).json({
        code: 429,
        message: '请求过于频繁，请稍后再试'
      });
    }
    
    await next();
  }
}
```

## 网络优化

### 请求优化
```javascript
// 1. 请求合并
class RequestBatcher {
  constructor() {
    this.batches = new Map();
    this.timers = new Map();
  }
  
  batch(key, request, delay = 100) {
    return new Promise((resolve, reject) => {
      if (!this.batches.has(key)) {
        this.batches.set(key, []);
      }
      
      this.batches.get(key).push({ request, resolve, reject });
      
      if (!this.timers.has(key)) {
        this.timers.set(key, setTimeout(() => {
          this.flush(key);
        }, delay));
      }
    });
  }
  
  async flush(key) {
    const batch = this.batches.get(key);
    if (!batch || batch.length === 0) return;
    
    this.batches.delete(key);
    this.timers.delete(key);
    
    try {
      const requests = batch.map(item => item.request);
      const results = await this.processBatch(requests);
      
      batch.forEach((item, index) => {
        item.resolve(results[index]);
      });
    } catch (error) {
      batch.forEach(item => item.reject(error));
    }
  }
}

// 2. 请求去重
const requestCache = new Map();

const dedupeRequest = (url, options) => {
  const key = `${url}_${JSON.stringify(options)}`;
  
  if (requestCache.has(key)) {
    return requestCache.get(key);
  }
  
  const promise = fetch(url, options)
    .finally(() => {
      setTimeout(() => requestCache.delete(key), 5000); // 5秒后清除缓存
    });
    
  requestCache.set(key, promise);
  return promise;
};

// 3. 预加载策略
const preloadManager = {
  preloadQueue: [],
  isPreloading: false,
  
  add(url, priority = 'low') {
    this.preloadQueue.push({ url, priority });
    this.process();
  },
  
  async process() {
    if (this.isPreloading) return;
    
    this.isPreloading = true;
    
    // 按优先级排序
    this.preloadQueue.sort((a, b) => {
      const priorities = { high: 3, medium: 2, low: 1 };
      return priorities[b.priority] - priorities[a.priority];
    });
    
    while (this.preloadQueue.length > 0) {
      const { url } = this.preloadQueue.shift();
      
      try {
        // 空闲时间预加载
        await new Promise(resolve => {
          if ('requestIdleCallback' in window) {
            requestIdleCallback(resolve);
          } else {
            setTimeout(resolve, 0);
          }
        });
        
        await fetch(url);
      } catch (error) {
        console.warn('预加载失败:', url, error);
      }
    }
    
    this.isPreloading = false;
  }
};
```

## 监控和分析

### 性能监控
```javascript
// 1. 前端性能监控
class PerformanceMonitor {
  static init() {
    // 页面加载性能
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0];
        const metrics = {
          dns: navigation.domainLookupEnd - navigation.domainLookupStart,
          tcp: navigation.connectEnd - navigation.connectStart,
          request: navigation.responseStart - navigation.requestStart,
          response: navigation.responseEnd - navigation.responseStart,
          dom: navigation.domContentLoadedEventEnd - navigation.navigationStart,
          load: navigation.loadEventEnd - navigation.navigationStart
        };
        
        this.reportMetrics('page_load', metrics);
      }, 0);
    });
    
    // 资源加载性能
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.duration > 1000) { // 加载时间超过1秒的资源
          this.reportMetrics('slow_resource', {
            name: entry.name,
            duration: entry.duration,
            size: entry.transferSize
          });
        }
      }
    });
    
    observer.observe({ entryTypes: ['resource'] });
  }
  
  static reportMetrics(type, data) {
    // 发送到性能监控服务
    fetch('/api/performance/report', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ type, data, timestamp: Date.now() })
    }).catch(console.error);
  }
}

// 2. 后端性能监控
class APIPerformanceMiddleware {
  async handle({ request, response }, next) {
    const start = process.hrtime.bigint();
    
    await next();
    
    const end = process.hrtime.bigint();
    const duration = Number(end - start) / 1000000; // 转换为毫秒
    
    // 记录慢请求
    if (duration > 1000) {
      console.warn('慢请求:', {
        method: request.method(),
        url: request.url(),
        duration: `${duration}ms`,
        memory: process.memoryUsage()
      });
    }
    
    // 添加性能头
    response.header('X-Response-Time', `${duration}ms`);
  }
}
```

## 部署优化

### 构建优化
```javascript
// Vite 配置优化
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd', '@ant-design/pro-components'],
          utils: ['lodash', 'dayjs']
        }
      }
    },
    chunkSizeWarningLimit: 1000,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },
  server: {
    // 开发服务器优化
    hmr: {
      overlay: false
    }
  }
});

// Taro 配置优化
const config = {
  mini: {
    optimizeMainPackage: {
      enable: true,
      exclude: ['pages/user/**/*']
    },
    miniCssExtractPluginOption: {
      ignoreOrder: true
    }
  },
  h5: {
    miniCssExtractPluginOption: {
      ignoreOrder: true
    },
    webpackChain(chain) {
      chain.optimization.splitChunks({
        chunks: 'all',
        cacheGroups: {
          vendor: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/]/,
            priority: 10
          }
        }
      });
    }
  }
};
```