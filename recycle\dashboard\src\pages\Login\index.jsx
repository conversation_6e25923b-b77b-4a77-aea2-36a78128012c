import { useState, useRef } from 'react'
import images from '../../assets/images'
import styles from './index.module.less'
import { Input, Toast } from 'antd-mobile'
import { useNavigate } from 'react-router-dom'
import { md5 } from '../../utils/md5'
import { effect } from '../../utils/dva17'
import { NUser, EPost } from '../../config/Constants'
import { useEffect } from 'react'

export default () => {
  const handler = useRef()
  const navigate = useNavigate()
  const [visible, setVisible] = useState(false)
  const [account, setAccount] = useState(null)
  const [password, setPassword] = useState(null)
  let accountValue = localStorage.getItem('accountValue')
  let passwordValue = localStorage.getItem('passwordValue')
  /*--------------------- 生命周期 ---------------------*/

  /*--------------------- 响应 ---------------------*/
  const onLogin = () => {
    if (!account || !password) {
      Toast.show({
        content: !account ? '请输入您的账号' : '请输入您的密码',
        icon: 'fail',
      })
      return
    }

    handler.current = Toast.show({
      icon: 'loading',
      content: '登录中…',
      maskClickable: false,
      duration: 0,
    })
    effect(NUser, EPost, { username: account, password: password })
      .then(() => {
        localStorage.setItem('accountValue', account)
        localStorage.setItem('passwordValue', password)
        handler.current?.close()
        navigate('/', { replace: true })
      })
      .catch(() => {
        handler.current?.close()
      })
  }



  useEffect(() => {
    if (accountValue && passwordValue) {
      setAccount(accountValue)
      setPassword(passwordValue)
    }
  }, [accountValue, passwordValue])
  /*--------------------- 渲染 ---------------------*/
  return (
    <div className={styles.login_contain}>
      <div className={styles.login_box}>
        <img className={styles.login_title} src={"https://oss.evergreenrecycle.cn/yshs/LOGO2.png"} />
        <div className={styles.login_form}>
          <div className={styles.form_item}>
            <div className={styles.label}>账号：</div>
            <Input
              autoComplete="off"
              className={styles.input}
              placeholder="请输入您的账号"
              maxLength={11}
              value={account}
              onChange={value => {
                setAccount(value.trim())
              }}
            />
          </div>
          <div className={[styles.form_item, styles.password].join(' ')}>
            <div className={styles.label}>密码：</div>
            <div className={styles.password_input}>
              <Input
                value={password}
                autocomplete="new-password"
                className={styles.input}
                placeholder="请输入您的登录密码"
                type={visible ? 'text' : 'password'}
                onChange={value => {
                  setPassword(value.trim())
                }}
              />
              <img
                className={styles.icon}
                src={visible ? images.showPassword : images.hidePassword}
                onClick={() => {
                  setVisible(!visible)
                }}
              />
            </div>
          </div>
        </div>
        <div className={styles.login_btn} onClick={onLogin}>
          <img src={images.loginBtn} />
        </div>
      </div>
    </div>
  )
}
