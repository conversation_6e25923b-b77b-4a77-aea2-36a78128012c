'use strict'

const _ = require("lodash");
const moment = require("moment");
const { <PERSON><PERSON><PERSON><PERSON>, Worker, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HiWorkerWalletLog } = require('../../../Models')
const Excel = require('exceljs')
const { ERR } = require("../../../../../constants");
const ExcelService = require('../../../Services/ExcelService');
const fs = require("fs-extra");
const Helpers = use("Helpers");
const Database = use('Database')

/**
 * 嗨回收订单导入控制器
 * 专门处理嗨回收平台的订单导入、导出和维护功能
 */
class HiOrderController {

  /**
   * 获取嗨回收订单列表
   */
  async index({ request, response }) {
    let {
      createdAt, workTime,
      current = 1, pageSize = 10, status = "完成", orderNo,
      waste_1st_type, userMobile, userName, workerName, from = "嗨回收",
      startDate, endDate,
      workStartDate, workEndDate, orderType, type, finishedAt,
      finishStartDate, finishEndDate, model, offTime,
      offStartDate, offEndDate, countName, countPhone,
      address, province, city
    } = request.all()

    let query = HiOrder.query().with('company')
    let totalQuery = HiOrder.query()

    if (status) {
      query.where('status', status)
      totalQuery.where('status', status)
    }

    if (orderType) {
      query.where('orderType', orderType)
      totalQuery.where('orderType', orderType)
    }

    if (model) {
      query.where('model', model)
      totalQuery.where('model', model)
    }

    if (countName) {
      let worker = await Worker.query().where('workerName', countName).first()
      if (worker) {
        query.where('workerID', worker.id)
        totalQuery.where('workerID', worker.id)
      } else {
        query.where('countName', 'LIKE', `%${countName}%`)
        totalQuery.where('countName', 'LIKE', `%${countName}%`)
      }
    }

    if (countPhone) {
      query.where('countPhone', 'LIKE', `%${countPhone}%`)
      totalQuery.where('countPhone', 'LIKE', `%${countPhone}%`)
    }

    if (startDate && endDate) {
      query.where('createdAt', '>=', moment(startDate).startOf('day').toDate())
        .where('createdAt', '<=', moment(endDate).endOf('day').toDate())
      totalQuery.where('createdAt', '>=', moment(startDate).startOf('day').toDate())
        .where('createdAt', '<=', moment(endDate).add(1, 'd').toDate())
    }

    if (workStartDate && workEndDate) {
      query.where('workTime', '>=', moment(workStartDate).toDate())
        .where('workTime', '<=', moment(workEndDate).add(1, 'd').toDate())
      totalQuery.where('workTime', '>=', moment(workStartDate).toDate())
        .where('workTime', '<=', moment(workEndDate).add(1, 'd').toDate())
    }

    if (finishStartDate && finishEndDate) {
      query.where('finishedAt', '>=', moment(finishStartDate).toDate())
        .where('finishedAt', '<=', moment(finishEndDate).add(1, 'd').toDate())
      totalQuery.where('finishedAt', '>=', moment(finishStartDate).toDate())
        .where('finishedAt', '<=', moment(finishEndDate).add(1, 'd').toDate())
    }

    if (offStartDate && offEndDate) {
      query.where('offTime', '>=', moment(offStartDate).toDate())
        .where('offTime', '<=', moment(offEndDate).add(1, 'd').toDate())
      totalQuery.where('offTime', '>=', moment(offStartDate).toDate())
        .where('offTime', '<=', moment(offEndDate).add(1, 'd').toDate())
    }

    if (province) {
      query.whereRaw('province like ?', [`%${province}%`])
      totalQuery.whereRaw('province like ?', [`%${province}%`])
    }

    if (city) {
      query.whereRaw('city like ?', [`%${city}%`])
      totalQuery.whereRaw('city like ?', [`%${city}%`])
    }

    if (workerName) {
      query.where('wokerName', 'LIKE', `%${workerName}%`)
      totalQuery.where('wokerName', 'LIKE', `%${workerName}%`)
    }

    if (orderNo) {
      query.whereRaw('orderNo like ?', [`%${orderNo}%`])
      totalQuery.whereRaw('orderNo like ?', [`%${orderNo}%`])
    }

    if (type) {
      query.whereRaw('type like ?', [`%${type}%`])
      totalQuery.whereRaw('type like ?', [`%${type}%`])
    }

    if (waste_1st_type) {
      query.where('waste_1st_ID', waste_1st_type)
      totalQuery.where('waste_1st_ID', waste_1st_type)
    }

    if (userMobile) {
      query.whereRaw('userMobile like ?', [`%${userMobile}%`])
      totalQuery.whereRaw('userMobile like ?', [`%${userMobile}%`])
    }

    if (userName) {
      query.whereRaw('userName like ?', [`%${userName}%`])
      totalQuery.whereRaw('userName like ?', [`%${userName}%`])
    }

    if (address) {
      query.whereRaw('address like ?', [`%${address}%`])
      totalQuery.whereRaw('address like ?', [`%${address}%`])
    }

    // 排序处理
    this.applySorting(query, { createdAt, workTime, finishedAt, offTime, address })

    let vo = await query.with('worker').paginate(current, pageSize)
    let totalInfoFee = await totalQuery.sum('infoFee as totalFee')

    response.json({
      ...vo.toJSON(),
      totalInfoFee: totalInfoFee[0].totalFee || 0
    })
  }

  /**
   * 应用排序
   */
  applySorting(query, { createdAt, workTime, finishedAt, offTime, address }) {
    const sortFields = { createdAt, workTime, finishedAt, offTime, address }

    Object.keys(sortFields).forEach(field => {
      if (sortFields[field] === 'descend') {
        query.orderBy(field, 'desc')
      } else if (sortFields[field] === 'ascend') {
        query.orderBy(field, 'asc')
      }
    })
  }

  /**
   * 获取单个嗨回收订单详情
   */
  async show({ request, params, response }) {
    let vo = await HiOrder.query()
      .where('id', params.id)
      .first()

    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }

    response.json(vo)
  }

  /**
   * 更新嗨回收订单
   */
  async update({ request, params, response }) {
    let vo = await HiOrder.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }

    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
  }


  /**
   * 批量删除嗨回收订单
   */
  async batchDestroy({ request, response }) {
    const { ids, id } = request.all()

    if (!ids || !Array.isArray(ids) || ids.length === 0 && !id) {
      return response.status(400).json({
        code: 400,
        message: '请提供有效的订单ID列表'
      })
    }
    if (id) {
      ids = [id]
    }
    try {
      // 批量删除订单
      for (const id of ids) {
        const order = await HiOrder.find(id)
        let worker = await Worker.query().where('mobile', order.countPhone).first()
        if (!worker) {
          let coWorker = await HiCOWorker.query()
            .where('workerName', order.wokerName)
            .where('mobile', order.workerPhone)
            .first()
          if (coWorker) {
            worker = await Worker.find(coWorker.managerID)
          }
        }
        const deductionAmount = order.infoFee ? parseInt(order.infoFee) * parseInt(1) : 0;
        worker.wallet += deductionAmount
        let log = await HiWorkerWalletLog.query().where('orderID', order.id).first()
        if (log) {
          await HiWorkerWalletLog.create({
            orderID: order.id, // 使用订单号关联，后续更新为订单ID
            sign: order.orderNo,
            workerID: worker.id,
            money: +(deductionAmount),
            remark: `${order.wokerName}嗨回收订单红充`
          })
        }
        if (order) {
          // 删除订单
          await order.delete()
          await worker.save()
        }
      }

      response.json({
        code: 200,
        message: `成功删除 ${ids.length} 条记录`,
        deletedCount: ids.length
      })
    } catch (error) {
      console.error('批量删除失败:', error)
      return response.status(500).json({
        code: 500,
        message: '批量删除失败',
        error: error.message
      })
    }
  }



  /**
   * 解析嗨回收订单行数据
   */
  parseHiOrderRow(row) {
    // 这里需要根据嗨回收的Excel格式来解析
    // 假设的字段映射，需要根据实际Excel格式调整
    const data = {
      orderNo: this.getCellValue(row, 1), // 订单号
      userName: this.getCellValue(row, 2), // 用户姓名
      userMobile: this.getCellValue(row, 3), // 用户电话
      address: this.getCellValue(row, 4), // 地址
      type: this.getCellValue(row, 5), // 品类
      infoFee: this.getCellValue(row, 6), // 信息费
      wokerName: this.getCellValue(row, 7), // 师傅姓名
      workerPhone: this.getCellValue(row, 8), // 师傅电话
      from: '嗨回收', // 来源
      status: '完成', // 默认状态
      companyID: 47, // 默认公司ID，需要根据实际情况调整
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // 验证必填字段
    if (!data.orderNo || !data.userName || !data.userMobile) {
      throw new Error('订单号、用户姓名、用户电话为必填字段');
    }

    return data;
  }

  /**
   * 获取单元格值
   */
  getCellValue(row, columnIndex) {
    const cell = row.getCell(columnIndex);
    return cell ? cell.value : '';
  }

  /**
   * 获取嗨回收待维护订单列表
   */
  async hiOrderMaintainList({ request, response }) {
    let { status = "待维护", orderNo, current = 1, pageSize = 10, remark } = request.all()
    let vo = HiOrderMaintain.query().where('status', status)

    if (orderNo) {
      vo.where('orderNo', 'LIKE', `%${orderNo}%`)
    }
    if (remark) {
      vo.where('remark', 'LIKE', `%${remark}%`)
    }

    vo = await vo.paginate(current, pageSize)
    response.json(vo)
  }

  /**
   * 更新嗨回收订单维护状态
   */
  async hiOrderMaintainUpdate({ request, params, response }) {
    let vo = await HiOrderMaintain.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }

    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
  }

  /**
   * 导出嗨回收订单数据
   */
  async exportXLS({ request, response }) {
    let {
      createdAt, workTime, status = "完成", orderNo,
      waste_1st_type, userMobile, userName, workerName, from,
      startDate, endDate, workStartDate, workEndDate,
      orderType, type, countName, countPhone,
      finishStartDate, finishEndDate,
      offStartDate, offEndDate, lowPrice, hightPrice,
      address, price, city, source, province
    } = request.all()
    console.log('request.all(): ', request.all())

    // 创建Excel工作簿和工作表
    let workbook = new Excel.Workbook()
    let worksheet = workbook.addWorksheet('嗨回收订单数据')

    // 设置列头和列宽
    let font = { name: 'Times New Roman', size: 12 }
    worksheet.columns = [
      { header: '订单号', key: 'orderNo', width: 18, style: { font } },
      { header: '结算人', key: 'countName', width: 15, style: { font } },
      { header: '四级分类', key: 'type', width: 15, style: { font } },
      { header: '来源', key: 'from', width: 12, style: { font } },
      { header: '客户姓名', key: 'userName', width: 15, style: { font } },
      { header: '联系电话', key: 'userMobile', width: 15, style: { font } },
      { header: '信息费', key: 'infoFee', width: 12, style: { font } },
      { header: '地址', key: 'address', width: 35, style: { font } },
      { header: '省', key: 'province', width: 15, style: { font } },
      { header: '市', key: 'city', width: 15, style: { font } },
      { header: '区', key: 'town', width: 15, style: { font } },
      { header: '县', key: 'county', width: 15, style: { font } },
      { header: '上门时间', key: 'workTime', width: 20, style: { font } },
      { header: '回收人员', key: 'wokerName', width: 15, style: { font } },
      { header: '工作电话', key: 'workerPhone', width: 15, style: { font } },
      { header: '完成时间', key: 'finishedAt', width: 20, style: { font } },
      { header: '揽收时间', key: 'offTime', width: 20, style: { font } },
      { header: '下单时间', key: 'createdAt', width: 20, style: { font } },
      { header: '同步类型', key: 'model', width: 20, style: { font } },
      { header: '备注', key: 'remark', width: 25, style: { font } },
    ]

    try {
      // 构建查询条件
      const query = this.buildExportQuery({
        orderType, type, status, startDate, endDate, workStartDate, workEndDate,
        province, lowPrice, hightPrice, orderNo, from, waste_1st_type,
        userMobile, userName, address, price, city, source, countPhone,
        offStartDate, offEndDate, finishStartDate, finishEndDate, workerName, countName
      });

      const countResult = await query.count('* as total');
      const totalCount = countResult[0].total;

      console.log(`开始导出嗨回收订单，共${totalCount}条记录`);

      if (totalCount === 0) {
        const fileName = `嗨回收订单数据${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`;
        await workbook.xlsx.writeFile(`./${fileName}`);
        return response.attachment(`./${fileName}`);
      }

      // 分批查询数据
      const batchSize = 2000;
      const totalBatches = Math.ceil(totalCount / batchSize);
      let rowCount = 1;

      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        const batchQuery = this.buildExportQuery({
          orderType, type, status, startDate, endDate, workStartDate, workEndDate,
          province, lowPrice, hightPrice, orderNo, from, waste_1st_type,
          userMobile, userName, address, price, city, source, countPhone,
          offStartDate, offEndDate, finishStartDate, finishEndDate, workerName, countName
        });

        const batchData = await batchQuery
          .with('worker')
          .offset(batchIndex * batchSize)
          .limit(batchSize)
          .fetch();

        // 添加数据到Excel
        for (const item of batchData.rows) {
          worksheet.addRow({
            orderNo: item.orderNo,
            from: item.from,
            userName: item.userName,
            countName: item.worker && item.worker.workerName || item.wokerName || '',
            type: item.type,
            userMobile: item.userMobile,
            infoFee: item.infoFee ? parseInt(item.infoFee) : 0,
            address: `${item.address}`,
            workTime: item.workTime ? moment(item.workTime).format('YYYY-MM-DD HH:mm') : '',
            workerPhone: item.workerPhone || '',
            wokerName: item.wokerName || '',
            province: item.province || '',
            city: item.city || '',
            town: item.town || '',
            county: item.county || '',
            finishedAt: item.finishedAt ? moment(item.finishedAt).format('YYYY-MM-DD HH:mm') : '',
            offTime: item.offTime ? moment(item.offTime).format('YYYY-MM-DD HH:mm') : '',
            createdAt: item.createdAt ? moment(item.createdAt).format('YYYY-MM-DD HH:mm') : '',
            model: item.model || '',
            remark: item.remark || '',
          });
          rowCount++;
        }

        console.log(`已处理${rowCount - 1}/${totalCount}条记录，完成度: ${Math.round(((rowCount - 1) / totalCount) * 100)}%`);
      }

      const fileName = `嗨回收订单数据${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`;
      await workbook.xlsx.writeFile(`./${fileName}`);
      console.log(`导出完成，文件名: ${fileName}`);

      return response.attachment(`./${fileName}`);
    } catch (error) {
      console.error('导出过程中发生错误:', error);
      return response.status(500).json({
        code: 500,
        message: '导出失败',
        error: error.message
      });
    }
  }

  /**
   * 构建导出查询条件
   */
  buildExportQuery(params) {
    const {
      orderType, type, status, startDate, endDate, workStartDate, workEndDate,
      province, lowPrice, hightPrice, orderNo, from, waste_1st_type,
      userMobile, userName, address, price, city, source, countPhone,
      offStartDate, offEndDate, finishStartDate, finishEndDate, workerName, countName
    } = params;

    let query = HiOrder.query();

    // 默认查询嗨回收订单

    if (status) query.where('status', status);
    if (orderType) query.where('orderType', orderType);
    if (type) query.whereRaw('type like ?', [`%${type}%`]);
    if (orderNo) query.whereRaw('orderNo like ?', [`%${orderNo}%`]);
    if (userMobile) query.whereRaw('userMobile like ?', [`%${userMobile}%`]);
    if (userName) query.whereRaw('userName like ?', [`%${userName}%`]);
    if (address) query.whereRaw('address like ?', [`%${address}%`]);
    if (province) query.whereRaw('province like ?', [`%${province}%`]);
    if (city) query.whereRaw('city like ?', [`%${city}%`]);
    if (workerName) query.where('wokerName', 'LIKE', `%${workerName}%`);
    if (countPhone) query.where('countPhone', 'LIKE', `%${countPhone}%`);
    if (waste_1st_type) query.where('waste_1st_ID', waste_1st_type);

    // 日期范围查询
    if (startDate && endDate) {
      query.where('createdAt', '>=', moment(startDate).toDate())
        .where('createdAt', '<=', moment(endDate).add(1, 'd').toDate());
    }
    if (workStartDate && workEndDate) {
      query.where('workTime', '>=', moment(workStartDate).toDate())
        .where('workTime', '<=', moment(workEndDate).add(1, 'd').toDate());
    }
    if (finishStartDate && finishEndDate) {
      query.where('finishedAt', '>=', moment(finishStartDate).toDate())
        .where('finishedAt', '<=', moment(finishEndDate).add(1, 'd').toDate());
    }
    if (offStartDate && offEndDate) {
      query.where('offTime', '>=', moment(offStartDate).toDate())
        .where('offTime', '<=', moment(offEndDate).add(1, 'd').toDate());
    }

    return query;
  }

  /**
   * 导出嗨回收待维护订单
   */
  async exportHiOrderMaintain({ request, response }) {
    try {
      let { status = "待维护", orderNo, remark } = request.all()
      let query = HiOrderMaintain.query().where('status', status)

      if (orderNo) {
        query.where('orderNo', 'LIKE', `%${orderNo}%`)
      }
      if (remark) {
        query.where('remark', 'LIKE', `%${remark}%`)
      }

      let vo = await query.fetch()
      let data = []

      vo.rows.forEach((value, index) => {
        let arrInner = []
        arrInner.push(value.id)
        arrInner.push(value.orderNo)
        arrInner.push(value.status)
        arrInner.push(value.remark || '')
        arrInner.push(moment(value.createdAt).format('YYYY-MM-DD HH:mm:ss'))
        arrInner.push(moment(value.updatedAt).format('YYYY-MM-DD HH:mm:ss'))
        data.push(arrInner)
      })

      let theTitle = [
        'ID',
        '订单号',
        '状态',
        '备注',
        '创建时间',
        '更新时间'
      ]

      let theWidth = [
        { wch: 8 },
        { wch: 20 },
        { wch: 10 },
        { wch: 30 },
        { wch: 22 },
        { wch: 22 }
      ]

      let fileTitle = '嗨回收待维护订单-' + `${moment().format('YYYY-MM-DD-HH-mm')}`
      await ExcelService.generateExcel(fileTitle, theTitle, data, response, { '!cols': theWidth })
    } catch (error) {
      console.error('导出过程中发生错误:', error)
      return response.status(500).json({
        code: 500,
        message: '导出失败',
        error: error.message
      })
    }
  }
  async exportHiOrder2({ request, response }) {
    // console.log('request.all(): ', request.all())

    let { status = "完成", orderNo, remark } = request.all()
    let query = HiOrder.query().where('status', status)
    let vo = await query.fetch()
    let data = []
    vo.rows.forEach((value, index) => {
      let arrInner = []
      arrInner.push(value.orderNo)
      arrInner.push(value.userName)
      arrInner.push(value.userMobile)
      arrInner.push(value.address)
      arrInner.push(value.type)
      arrInner.push(value.infoFee)
      arrInner.push(value.wokerName)

      arrInner.push(value.workerPhone)
      arrInner.push(value.from)
      arrInner.push(value.status)
      arrInner.push(value.remark)
      data.push(arrInner)
    })
    let theTitle = [
      '订单号',
      '用户姓名',
      '用户电话',
      '地址',
      '废品类型',
      '信息费',
      '师傅姓名',
      '师傅电话',
      '来源',
      '状态',
      '备注'
    ]
    let theWidth = [
      { wch: 20 },
      { wch: 15 },
      { wch: 15 },
      { wch: 30 },
      { wch: 15 },
      { wch: 15 },
      { wch: 15 },
      { wch: 15 },
      { wch: 15 },
      { wch: 15 },
      { wch: 30 },
    ]
    let fileTitle = '嗨回收订单-' + `${moment().format('YYYY-MM-DD-HH-mm')}`
    await ExcelService.generateExcel(fileTitle, theTitle, data, response, { '!cols': theWidth })
    return response.attachment(`./${fileTitle}`);
  }

  async exportMaintainXLS({ request, response }) {
    let { status = "待维护", orderNo, remark } = request.all()
    let query = HiOrderMaintain.query().where('status', status)
    let vo = await query.fetch()
    let data = []
    vo.rows.forEach((value, index) => {
      let arrInner = []
      arrInner.push(value.id)
      arrInner.push(value.orderNo)
      arrInner.push(value.status)
      arrInner.push(moment(value.createdAt).format('YYYY-MM-DD HH:mm:ss'))
      arrInner.push(moment(value.updatedAt).format('YYYY-MM-DD HH:mm:ss'))
      data.push(arrInner)
    })
    let theTitle = [
      'ID',
      '订单号',
      '状态',
      '创建时间',
      '更新时间'
    ]
    let theWidth = [
      { wch: 8 },
      { wch: 20 },
      { wch: 10 },
      { wch: 22 },
      { wch: 22 }
    ]
    let fileTitle = '嗨回收待维护订单-' + `${moment().format('YYYY-MM-DD-HH-mm')}`
    await ExcelService.generateExcel(fileTitle, theTitle, data, response, { '!cols': theWidth })
    return response.attachment(`./${fileTitle}`);

  }
}

module.exports = HiOrderController 