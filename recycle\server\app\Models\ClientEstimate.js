'use strict'

const Model = use('Model')


class ClientEstimate extends Model {
  static get table() {
    return 'client_estimate'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return "createdAt"
  }
  static get updatedAtColumn() {
    return "updatedAt"
  }
  imgs() {
    return this.belongsTo('App/Models/Waste', 'subType', 'name')
  }
}

module.exports = ClientEstimate
