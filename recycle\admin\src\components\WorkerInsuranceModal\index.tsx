import { Modal, Button, message, Upload, DatePicker } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { effect } from 'dva17';
import { NWorker, EPostWorkerInsure } from '../../common/action';
import { get } from 'lodash';
import dayjs from 'dayjs';
import type { UploadProps } from 'antd';
import styles from './index.module.less';
import { SERVER_HOME } from '../../common/config';

const DatePick: any = DatePicker;

interface WorkerInsuranceModalProps {
  visible: boolean;
  worker: any;
  onClose: () => void;
  onSuccess: () => void;
}

const WorkerInsuranceModal = ({ visible, worker, onClose, onSuccess }: WorkerInsuranceModalProps) => {
  const [fileList, setFileList] = useState<any>([]);
  const [overTime, setOverTime] = useState<any>(null);

  useEffect(() => {
    if (worker && worker.insure) {
      setFileList(worker.insure?.files || []);
      if (worker.insure?.overTime) {
        setOverTime(worker.insure.overTime);
      }
    } else {
      setFileList([]);
      setOverTime(null);
    }
  }, [worker, visible]);

  const handleSave = async () => {
    await effect(NWorker, EPostWorkerInsure, {
      workerID: worker.id,
      companyID: worker.companyID,
      files: fileList,
      overTime
    });
    onSuccess();
    onClose();
  };

  const uploadProps: UploadProps = {
    action: `${SERVER_HOME}file`,
    listType: 'picture',
    onChange: (info) => {
      const { status } = info.file;
      if (status !== "uploading") {
        const newArray = Array.from(info.fileList, item => ({
          name: item.response?.fileName || item.name,
          url: item.response?.url || item.url,
        }));
        setFileList(newArray);
      }
      if (status === "done") {
        if (get(info, "file.response.msg", "") === "error") {
          message.warn(`文件上传失败`);
        } else if (get(info, "file.response", "")) {
          message.success(`${info.file.name} 文件上传成功`);
        }
      } else if (status === "error") {
        message.error(`${info.file.name} 文件上传失败`);
      }
    }
  };

  const handleChange = (date: any, dateString: string) => {
    setOverTime(dateString);
  };

  return (
    <Modal
      title="师傅保险管理"
      open={visible}
      onCancel={() => {
        setFileList([]);
        onClose();
      }}
      onOk={handleSave}
      width={800}
    >
      <div className={styles.container}>
        <div className={styles.formItem}>
          <div className={styles.label}>原到期时间:</div>
          <div className={styles.value}>
            {worker?.insure?.overTime 
              ? dayjs(worker.insure.overTime).format('YYYY-MM-DD') 
              : '无'}
          </div>
        </div>

        <div className={styles.formItem}>
          <div className={styles.label}>到期时间：</div>
          <DatePick onChange={handleChange} />
        </div>

        {fileList && fileList.length > 0 && (
          <div className={styles.fileListContainer}>
            <h4>已上传保险文件：</h4>
            <ul className={styles.fileList}>
              {fileList.map((file: any, index: number) => (
                <li key={index} className={styles.fileItem}>
                  <div>
                    <a href={file.url} target="_blank" rel="noopener noreferrer">
                      {file.name || `保险文件${index + 1}`}
                    </a>
                  </div>
                  <Button
                    danger
                    size="small"
                    onClick={() => {
                      const newFileList = [...fileList];
                      newFileList.splice(index, 1);
                      setFileList(newFileList);
                    }}
                  >
                    删除
                  </Button>
                </li>
              ))}
            </ul>
          </div>
        )}

        <div className={styles.uploadContainer}>
          <span className={styles.label}>凭证：</span>
          <Upload
            {...uploadProps}
            maxCount={5}
            fileList={[]}
            multiple={true}
            name="file"
          >
            <Button icon={<UploadOutlined />}>上传保险凭证(可多选)</Button>
          </Upload>
        </div>
      </div>
    </Modal>
  );
};

export default WorkerInsuranceModal; 