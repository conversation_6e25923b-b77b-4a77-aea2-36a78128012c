@import '../../../styles/variables.less';

.order-success {
  background: @background-light;
  min-height: 100vh;
  width: 100%;
  padding: @spacing-lg;
}

.success-container {
  background: @background;
  border-radius: @radius-lg;
  padding: @spacing-xl @spacing-lg;
  box-shadow: @shadow-light;
  text-align: center;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: @gradient-primary;
    border-radius: @radius-lg @radius-lg 0 0;
  }
}

.success-icon {
  width: 160rpx;
  height: 160rpx;
  border-radius: @radius-round;
  background: @gradient-primary;
  color: @text-white;
  font-size: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto @spacing-lg;
  box-shadow: @shadow-medium;
  animation: scaleIn 0.8s ease forwards;
}

.success-title {
  font-size: @font-title;
  font-weight: 600;
  color: @primary-color;
  margin-bottom: @spacing-lg;
  animation: fadeInUp 0.8s ease 0.2s forwards;
  opacity: 0;
  animation-fill-mode: forwards;
}

.success-desc {
  font-size: @font-md;
  color: @text-secondary;
  margin-bottom: @spacing-xl;
  padding: 0 @spacing-md;
  line-height: 1.6;
  animation: fadeInUp 0.8s ease 0.4s forwards;
  opacity: 0;
  animation-fill-mode: forwards;
}

.process-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: @spacing-xl 0;
  padding: 0 @spacing-sm;
  position: relative;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 22%;
  z-index: 2;
}

.step-icon {
  font-size: @font-title;
  margin-bottom: @spacing-sm;
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
  
  &:nth-child(1) { animation-delay: 0.6s; }
  &:nth-child(2) { animation-delay: 0.7s; }
  &:nth-child(3) { animation-delay: 0.8s; }
  &:nth-child(4) { animation-delay: 0.9s; }
}

.step-title {
  font-size: @font-sm;
  color: @text-secondary;
  text-align: center;
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
  
  &:nth-child(1) { animation-delay: 0.8s; }
  &:nth-child(2) { animation-delay: 0.9s; }
  &:nth-child(3) { animation-delay: 1.0s; }
  &:nth-child(4) { animation-delay: 1.1s; }
}

.step-divider {
  flex-grow: 1;
  height: 2rpx;
  background: linear-gradient(90deg, @primary-lighter, @primary-color, @primary-lighter);
  margin: 0 @spacing-xs;
  position: relative;
  top: -60rpx;
  z-index: 1;
  animation: slideInRight 0.8s ease 1.2s forwards;
  opacity: 0;
  animation-fill-mode: forwards;
}

.tips {
  background: linear-gradient(135deg, @secondary-color, rgba(240, 249, 244, 0.8));
  padding: @spacing-lg;
  border-radius: @radius-md;
  border-left: 8rpx solid @success-color;
  text-align: left;
  margin: @spacing-lg 0 @spacing-xl;
  box-shadow: @shadow-light;
  animation: fadeInUp 0.8s ease 1.4s forwards;
  opacity: 0;
  animation-fill-mode: forwards;
}

.tips-title {
  display: block;
  font-weight: 600;
  color: @primary-dark;
  margin-bottom: @spacing-sm;
  font-size: @font-md;
}

.tips-content {
  display: block;
  font-size: @font-sm;
  color: @text-secondary;
  line-height: 1.6;
}

.action-buttons {
  display: flex;
  gap: @spacing-md;
  margin-top: @spacing-xl;
  animation: fadeInUp 0.8s ease 1.6s forwards;
  opacity: 0;
  animation-fill-mode: forwards;
}

.action-btn {
  flex: 1;
  border-radius: 50rpx;
  font-size: @font-md;
  font-weight: 500;
  padding: @spacing-lg;
  transition: all @transition-normal;
  
  &.primary {
    background: @gradient-primary;
    color: @text-white;
    border: none;
    box-shadow: @shadow-medium;
    
    &:active {
      transform: translateY(2rpx);
      box-shadow: @shadow-light;
    }
  }
  
  &.secondary {
    background: @background;
    color: @primary-color;
    border: 2rpx solid @primary-color;
    
    &:active {
      background: @secondary-color;
    }
  }
}

// 动画定义
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 为步骤添加动画延迟
.step:nth-child(1) .step-icon { animation-delay: 0.6s; }
.step:nth-child(1) .step-title { animation-delay: 0.8s; }

.step:nth-child(3) .step-icon { animation-delay: 0.7s; }
.step:nth-child(3) .step-title { animation-delay: 0.9s; }

.step:nth-child(5) .step-icon { animation-delay: 0.8s; }
.step:nth-child(5) .step-title { animation-delay: 1.0s; }

.step:nth-child(7) .step-icon { animation-delay: 0.9s; }
.step:nth-child(7) .step-title { animation-delay: 1.1s; }