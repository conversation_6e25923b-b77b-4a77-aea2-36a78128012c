.administration {
  width: 100vw;
  min-height: 100vh;
  background-color: #f3f3f3;
  padding-bottom: 13.1vw;

  .administration_input {
    border: 1px soild red;
    width: 100vw;
    height: 18.4vw;
    background-color: rgba(255, 255, 255, 1);
    display: flex;
    justify-content: center;
    align-items: center;

    .input_ {
      width: 87vw;
      height: 10vw;
      border-radius: 5vw;
      color: rgba(136, 136, 136, 1);
      font-size: 3.7vw;
      text-align: left;
      padding-left: 5vw;
      background: #f5f6f8;
    }
  }

  .address_box {
    width: 92vw;
    margin: 4.5vw auto;
    background-color: #fff;
    padding: 3.7vw 4.5vw;

    .address_name_phone {
      width: 100%;
      pointer-events: none;
      //    border:1px solid red;
      .address_name {
        display: inline-block;
        padding-right: 6.7vw;
        color: rgba(51, 51, 51, 1);
        font-size: 4.2vw;
        font-family: PingFangSC-bold;
      }

      .address_phone {
        color: rgba(102, 102, 102, 1);
        font-size: 4.2vw;
        font-family: PingFangSC-regular;
      }
    }

    .address_information {
      pointer-events: none;
      width: 100%;
      padding: 3vw 0;
      border-bottom: 1px solid rgba(243, 243, 243, 1);

      .information {
        color: rgba(102, 102, 102, 1);
        font-size: 3.5vw;
        text-align: left;
        font-family: PingFangSC-regular;
      }
    }

    .address_edit {
      margin-top: 3.7vw;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .address_Radio_text {
        height: 70px;
        color: rgba(102, 102, 102, 1);
        font-size: 3.2vw;
        text-align: left;
        font-family: PingFangSC-regular;
        display: flex;
        align-items: center;
      }

      //    未选中的 背景样式
      .wx-radio-input {
        border-radius: 50%;
        width: 20px;
        height: 20px;
      }

      // 选中后的 背景样式 （红色背景 无边框 可根据UI需求自己修改）
      .wx-radio-input.wx-radio-input-checked {
        border-color: #15b381 !important;
        background: #15b381 !important;
      }

      // 选中后的 对勾样式 （白色对勾 可根据UI需求自己修改）
      .wx-radio-input.wx-radio-input-checked::before {
        border-radius: 50%;
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        font-size: 15px;
        color: #fff;
        background: transparent;
        transform: translate(-50%, -50%) scale(1);
        -webkit-transform: translate(-50%, -50%) scale(1);
      }

      .address_edit_del {
        width: 27.5vw;
        height: 4.5vw;
        display: flex;
        justify-content: space-between;
        align-items: center;
        // border:1px solid red;
      }

      .addressEditDel {
        display: flex;
        height: 70px;
        justify-items: space-between;
        align-items: center;
        // border:1px solid red;
      }

      .address_Icon {
        // border:1px solid red;
        display: inline-block;
        width: 4vw;
        height: 4vw;
      }

      .address_text {
        // border:1px solid red;
        display: inline-block;
        color: rgba(102, 102, 102, 1);
        font-size: 3.2vw;
        text-align: left;
        font-family: PingFangSC-regular;
        margin-left: 1vw;
      }
    }
  }

  .administration_add_address {
    position: fixed;
    left: 50px;
    bottom: 30px;
    width: 650px;
    height: 90px;
    line-height: 90px;
    border-radius: 45px;
    background: #15b381;
    color: rgba(255, 255, 255, 1);
    font-size: 30px;
    .text_wrapper {
      display: flex;
      width: 100%;
      justify-content: center;
      align-items: center;
    }
    .add_item {
      height: 32px;
      width: 100%;
    }
  }
}
