import { Button, notification, Space, Table, Form, Card, Select, InputNumber, Modal } from 'antd'
import { SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import ProCard from '@ant-design/pro-card'
import { effect, useConnect } from 'dva17'
import { useEffect, useRef, useState } from 'react'
import { NCategoryPrice } from '../../common/action'
import type { ActionType, ProColumns } from '@ant-design/pro-table'
import ProTable from '@ant-design/pro-table'
import ProForm, { ProFormInstance, ProFormSelect, ProFormText, ProFormDigit } from '@ant-design/pro-form'

// 定义表格数据类型
interface EstimateItem {
  id: number;
  type: string;
  subType: string;
  coldType: string;
  price: number;
  years: number;
  functions: string;
  outside: string;
  spu: string;
}

export default () => {
  const [visible, setVisible] = useState<boolean>(false);
  const [editingRecord, setEditingRecord] = useState<EstimateItem | null>(null);
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  // 获取数据
  const getEstimateList = async (params: any): Promise<{
    data: EstimateItem[];
    success: boolean;
    total: number;
  }> => {
    try {
      const response = await effect(NCategoryPrice, 'EGetClientEstimate', { ...params });
      return {
        data: (response as any)?.data || [],
        success: true,
        total: (response as any)?.total || 0
      };
    } catch (error: any) {
      notification.error({
        message: '获取数据失败',
        description: error.message,
      });
      return { data: [], success: false, total: 0 };
    }
  };

  // 创建记录
  const createEstimate = async (values: any) => {
    try {
      await effect(NCategoryPrice, 'EPostClientEstimate', values);
      notification.success({
        message: '创建成功',
        description: '新记录已成功创建',
        duration: 2,
      });
      actionRef.current?.reload();
      return true;
    } catch (error: any) {
      notification.error({
        message: '创建失败',
        description: error.message,
      });
      return false;
    }
  };

  // 更新记录
  const updateEstimate = async (id: number, values: any) => {
    try {
      await effect(NCategoryPrice, 'EPutClientEstimate', { id, ...values });
      notification.success({
        message: '更新成功',
        description: '记录已成功更新',
        duration: 2,
      });
      actionRef.current?.reload();
      return true;
    } catch (error: any) {
      notification.error({
        message: '更新失败',
        description: error.message,
      });
      return false;
    }
  };

  // 删除记录
  const deleteEstimate = async (id: number) => {
    try {
      await effect(NCategoryPrice, 'EDeleteClientEstimate', { id });
      notification.success({
        message: '删除成功',
        description: '记录已成功删除',
        duration: 2,
      });
      actionRef.current?.reload();
      return true;
    } catch (error: any) {
      notification.error({
        message: '删除失败',
        description: error.message,
      });
      return false;
    }
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await formRef.current?.validateFields();
      if (!values) return;

      let success;
      if (editingRecord) {
        success = await updateEstimate(editingRecord.id, values);
      } else {
        success = await createEstimate(values);
      }

      if (success) {
        setVisible(false);
        setEditingRecord(null);
        formRef.current?.resetFields();
      }
    } catch (error: any) {
      console.error('表单验证失败:', error);
    }
  };

  // 打开编辑模态框
  const handleEdit = (record: EstimateItem) => {
    setEditingRecord(record);
    setVisible(true);
    // 等待Modal渲染后设置表单值
    setTimeout(() => {
      formRef.current?.setFieldsValue(record);
    }, 100);
  };

  // 打开新建模态框
  const handleAdd = () => {
    setEditingRecord(null);
    setVisible(true);
    formRef.current?.resetFields();
  };

  // 处理删除
  const handleDelete = (record: EstimateItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除这条记录吗？此操作不可恢复。`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        await deleteEstimate(record.id);
      },
    });
  };

  // 定义表格列
  const columns: ProColumns<EstimateItem>[] = [
    {
      title: '品类',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      valueType: 'select',
      fieldProps: {
        options: [
          { value: '电视', label: '电视' },
          { value: '冰箱', label: '冰箱' },
          { value: '洗衣机', label: '洗衣机' },
          { value: '空调', label: '空调' },
          { value: '热水器', label: '热水器' },
        ],
      },
    },
    {
      title: '子类型',
      dataIndex: 'subType',
      key: 'subType',
      width: 120,
    },

    {
      title: '价格(元)',
      dataIndex: 'price',
      key: 'price',
      sorter: true,
      hideInSearch: true,
      width: 100,
      render: (text) => `¥${text}`,
    },
    {
      title: '使用年限',
      dataIndex: 'years',
      key: 'years',
      width: 100,
    },
    {
      title: '功能',
      dataIndex: 'functions',
      key: 'functions',
      width: 150,
    },
    {
      title: '外观',
      dataIndex: 'outside',
      key: 'outside',
      width: 150,
    },
    {
      title: 'SPU',
      dataIndex: 'spu',
      key: 'spu',
      width: 150,
    },
    {
      title: '制冷类型',
      dataIndex: 'coldType',
      key: 'coldType',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<EstimateItem>
        headerTitle="价格管理"
        actionRef={actionRef}
        rowKey="id"
        request={async (
          params,
          sort,
          filter
        ) => {
          // 处理排序参数
          const sortParams: Record<string, string> = {};
          if (sort) {
            Object.keys(sort).forEach(key => {
              sortParams[key] = sort[key] === 'ascend' ? 'asc' : 'desc';
            });
          }

          // 构建查询参数
          const queryParams = {
            ...params,
            ...sortParams,
          };

          // 删除不需要的参数
          if (queryParams._timestamp) {
            delete queryParams._timestamp;
          }

          return await getEstimateList(queryParams);
        }}
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            type="primary"
            onClick={handleAdd}
          >
            新建
          </Button>,
        ]}
        columns={columns}
        pagination={{
          showSizeChanger: true,
        }}
      />

      <Modal
        title={editingRecord ? '编辑价格' : '新建价格'}
        open={visible}
        onCancel={() => {
          setVisible(false);
          setEditingRecord(null);
        }}
        onOk={handleSubmit}
        width={600}
        destroyOnClose
      >
        <ProForm
          formRef={formRef}
          submitter={false}
          layout="horizontal"
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 16 }}
        >
          <ProFormSelect
            name="type"
            label="品类"
            placeholder="请选择品类"
            rules={[{ required: true, message: '请选择品类' }]}
            options={[
              { value: '电视', label: '电视' },
              { value: '冰箱', label: '冰箱' },
              { value: '洗衣机', label: '洗衣机' },
              { value: '空调', label: '空调' },
              { value: '热水器', label: '热水器' },
            ]}
          />
          <ProFormText
            name="subType"
            label="子类型"
            placeholder="请输入子类型"
            rules={[{ required: true, message: '请输入子类型' }]}
          />
          <ProFormText
            name="coldType"
            label="制冷类型"
            placeholder="请输入制冷类型"
          />
          <ProFormDigit
            name="price"
            label="价格"
            placeholder="请输入价格"
            min={0}
            rules={[{ required: true, message: '请输入价格' }]}
            fieldProps={{
              precision: 2,
              addonAfter: '元',
            }}
          />
          <ProFormText
            name="years"
            label="使用年限"
            placeholder="请输入使用年限"
            rules={[{ required: true, message: '请输入使用年限' }]}
          />
          <ProFormText
            name="functions"
            label="功能"
            placeholder="请输入功能"
            rules={[{ required: true, message: '请输入功能' }]}
          />
          <ProFormText
            name="outside"
            label="外观"
            placeholder="请输入外观"
        
          />
          <ProFormText
            name="spu"
            label="SPU"
            placeholder="请输入SPU"
          />
        </ProForm>
      </Modal>
    </>
  );
};
