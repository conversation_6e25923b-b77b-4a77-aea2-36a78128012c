'use strict'

const _ = require('lodash')
const moment = require('moment')
const axios = require('axios')

//废品属性
class AddressController {
  async index ({request, response}) {
    let { code, codeArr } = request.all()
    const res = await axios.get('https://api.ouhe-tech.com/tongyi/master/v1/address', {
      params: {
        code: code,
        codeArr: codeArr
      }
    }, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
    response.json(res.data)
  }
  async show({request, params, response}) {

  }
  async store ({request, response}) {

  }
  async update ({request, params, response}) {

  }
  async destroy ({request, params, response}) {

  }
}

module.exports = AddressController
