import { Modal, Badge } from 'antd'
import { useState, useEffect } from 'react'
import { uniq } from 'lodash'
import styles from './index.module.less'

interface WorkerAddressModalProps {
  visible: boolean;
  workerSelected: any[];
  onClose: () => void;
}

const WorkerAddressModal = ({ visible, workerSelected, onClose }: WorkerAddressModalProps) => {
  // 区域显示相关
  const [currentIndex, setCurrentIndex] = useState<any>(null);
  const [currentArea, setCurrentArea] = useState<any>([]);
  const [currentTown, setCurrentTown] = useState<any>([]);
  const [currentTownIndex, setCurrentTownIndex] = useState<any>(null);
  const [currentType, setCurrentType] = useState<any>([]);
  const [currentTypeApp, setCurrentTypeApp] = useState<any>([]);

  // 设置区域数据
  useEffect(() => {
    let areas: any = [];
    workerSelected.forEach((area: any) => {
      if (areas.indexOf(area.area) < 0) {
        areas.push(area.area);
      }
    });
    setCurrentArea(areas);
  }, [workerSelected]);

  // 获取区域下的镇街数量
  const getCount = (area: any) => {
    let towns: any = [];
    workerSelected.forEach((value: any) => {
      if (value.area === area && towns.indexOf(value.name) < 0) {
        towns.push(value.name);
      }
    });
    return towns.length;
  };

  // 获取区域下的镇街
  const getTown = (area: any, index: any) => {
    let towns: any = [];
    workerSelected.forEach((value: any) => {
      if (value.area === area && towns.indexOf(value.name) < 0) {
        towns.push(value.name);
      }
    });
    setCurrentIndex(index);
    setCurrentTown(towns);
    setCurrentTownIndex(null);
    setCurrentType([]);
    setCurrentTypeApp([]);
  };

  // 获取镇街下的回收类型
  const getKind = (town: any, index: any) => {
    let types: any = [];
    let typeApps: any = [];
    workerSelected.forEach((value: any) => {
      if (value.name === town) {
        types.push(value.typeName);
        if (value.detailName) {
          typeApps.push(value.detailName);
        }
      }
    });
    setCurrentTypeApp(typeApps);
    setCurrentType(uniq(types));
    setCurrentTownIndex(index);
  };

  return (
    <Modal
      title="师傅服务区域查看"
      open={visible}
      onOk={onClose}
      onCancel={onClose}
      width={800}>
      <div className={styles.item_wrapper}>
        <div className={styles.item}>
          <span className={styles.item_title}>区域选择：</span>
          <div className={styles.item_content}>
            {currentArea &&
              currentArea.map((area: any, index: number) => (
                <Badge count={getCount(area)} style={{ right: 20 }} key={index + area}>
                  <div
                    className={`${styles.area_item} ${currentIndex === index ? styles.selected : null}`}
                    onClick={() => getTown(area, index)}>
                    {area}
                  </div>
                </Badge>
              ))}
          </div>
        </div>
        <div className={styles.item}>
          <span className={styles.item_title}>街道/镇：</span>
          <div className={styles.item_content}>
            {currentTown &&
              currentTown.map((value: any, index: any) => (
                <div
                  className={`${styles.area_item} ${currentTownIndex === index ? styles.selected : null}`}
                  key={index + value}
                  onClick={() => getKind(value, index)}>
                  {value}
                </div>
              ))}
          </div>
        </div>
        <div className={styles.item}>
          <span className={styles.item_title}>回收类型：</span>
          <div className={styles.item_content}>
            {currentType &&
              currentType.map((value: any, index: any) => (
                <div className={styles.area_item} key={index + value} style={{ cursor: 'auto' }}>
                  {value}
                </div>
              ))}
          </div>
        </div>
        {currentTypeApp?.length > 0 && (
          <div className={styles.item}>
          <span className={styles.item_title}>家电类型：</span>
          <div className={styles.item_content}>
              {currentTypeApp.map((value: any, index: any) => (
                  <div className={styles.area_item} key={index} style={{ cursor: 'auto' }}>
                    {value}
                  </div>
              ))}
          </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default WorkerAddressModal; 