import { Input, message, Modal, notification, Select } from 'antd'
import styles from './index.module.less'
import { useState, useEffect } from 'react'
import dayjs from 'dayjs'
import { changeOrderStatus, postNewOrder } from '../../services/order'
import { effect, useConnect } from 'dva17'
import { ECityAddress, EProvinceAddress, NCompany } from '../../common/action'
import { getAddress } from '../../services/company'
import { pageStatus, recycleTypeEnum, SourceLevels, typeList } from '../../common/enum'

const { Option } = Select

export default (props: any) => {
  let { visible, onChange, data } = props
  const { provinceList, cityList } = useConnect(NCompany)
  const [newOrder, setNewOrder] = useState<any>({})
  const [districtList, setDistrictList] = useState<any>([])
  const [subDistrictList, setSubDistrictList] = useState<any>([])
  const [inputValue, setInputValue] = useState<any>(null)
  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    if (visible) {
      getStreet()
      effect(NCompany, EProvinceAddress, { code: '' })
      effect(NCompany, ECityAddress, { code: '' })
      // effect(NCompany, ECityAddress, { code: 51 })
    }
    return () => { }
  }, [visible])
  useEffect(() => {
    if (data) {
      setNewOrder(data)
      setInputValue(dayjs(data.workTime).format('YYYY-MM-DD'))
    } else {
      setNewOrder({})
      setInputValue(dayjs().format('YYYY-MM-DD'))
    }
    return () => { }
  }, [data])

  const orderCancel = () => {
    onChange({ visible: false, refresh: false })
  }
  const orderOk = async () => {
    if (!dayjs(inputValue).isValid()) {
      message.error({ content: '日期格式错误，请按照"年-月-日"格式', duration: 3000 })
    } else {
      newOrder.workTime = dayjs(inputValue).format('YYYY-MM-DD') + dayjs(newOrder.workTime).format(' HH:mm:ss')
      console.log(newOrder)
      if (data) {
        delete newOrder.sms
        delete newOrder.company
        delete newOrder.cancel
        delete newOrder.worker
        delete newOrder.transfer
        delete newOrder.rating
        await changeOrderStatus(newOrder).then(async () => {
          notification.success({
            message: '成功！',
            description: '修改成功',
            duration: 2,
          })
          onChange({ visible: false, refresh: true })
        })
      } else {
        await postNewOrder(newOrder).then(async () => {
          notification.success({
            message: '成功！',
            description: '创建成功',
            duration: 2,
          })
          onChange({ visible: false, refresh: true })
        })
      }
    }
  }
  const getStreet = async () => {
    let res = await getAddress({ code: data.userAddress?.districtCode })
    // console.log(res, '>>>')
    setSubDistrictList(res)
  }

  const changProvince = async (e: any) => {
    effect(NCompany, ECityAddress, { code: provinceList[e].code })
    setNewOrder({ ...newOrder, province: provinceList[e].name })
  }
  const changCity = async (e: any) => {
    let res = await getAddress({ code: cityList[e].code })
    setDistrictList(res)
    setNewOrder({ ...newOrder, city: cityList[e].name })
    // setcityCode(cityList[e].code)
  }
  const changDistrit = async (e: any) => {
    let res = await getAddress({ code: districtList[e].code })
    setSubDistrictList(res)
    setNewOrder({ ...newOrder, countyCode: districtList[e].code, county: districtList[e].name })
  }
  const changSubDistrit = async (e: any) => {
    console.log(subDistrictList[e])
    setNewOrder({ ...newOrder, receiverTown: subDistrictList[e].name })
  }
  const changeValue = (vo: any) => {
    if (vo.workTime) {
      vo.workTime = newOrder.workTime && dayjs(newOrder.workTime).format('YYYY-MM-DD ') + vo.workTime
    }
    if (vo.dateTime) {
      setInputValue(vo.dateTime)
      delete vo.dateTime
    }
    if (vo.address) {
      newOrder.countyCode = 1
      delete newOrder.doneInfo
      delete newOrder.sales
    }
    setNewOrder({ ...newOrder, ...vo })
  }

  /*--------------------- 响应 ---------------------*/
  /*--------------------- 渲染 ---------------------*/
  return (
    <Modal
      open={visible}
      onOk={() => {
        orderOk()
      }}
      onCancel={() => {
        orderCancel()
      }}
      width={600}
      okText={data ? '确认修改' : '确认新建'}
      cancelText={'退出'}>
      <h4 className={styles.modalTitle}>{data ? '编辑订单' : '新增订单'}</h4>
      <div className={styles.newOrder}>
        <div className={styles.newOrder_item}>
          <span className={styles.newOrder_item_title}>订单号：</span>
          <Input disabled={true} style={{ width: 250 }} value={newOrder.orderNo} type="text" />
        </div>
        <div className={styles.newOrder_item}>
          <span className={styles.newOrder_item_title}>客户姓名：</span>
          <Input
            placeholder="请输入客户姓名"
            onChange={e => {
              changeValue({ userName: e?.target?.value })
            }}
            style={{ width: 250 }}
            value={newOrder.userName}
            type="text"
          />
        </div>
        <div className={styles.newOrder_item}>
          <span className={styles.newOrder_item_title}>联系电话：</span>
          <Input
            placeholder="请输入联系电话"
            onChange={e => {
              changeValue({ userMobile: e?.target?.value })
            }}
            style={{ width: 250 }}
            value={newOrder.userMobile}
            maxLength={11}
            type="text"
          />
        </div>
        <div className={styles.newOrder_item}>
          <span className={styles.newOrder_item_title}>预约时间：</span>
          <div style={{ width: '80%' }}>
            <Input
              type={'date'}
              style={{ width: '30%' }}
              value={inputValue}
              onChange={(e: any) => {
                changeValue({ dateTime: e.target.value })
              }}
            />
            <Select
              value={newOrder.workTime && dayjs(newOrder.workTime).format('HH:mm:ss')}
              style={{ width: 100 }}
              onChange={(e: any) => {
                changeValue({ workTime: e })
              }}>
              <Option value="09:00:00">上午</Option>
              <Option value="13:00:00">下午</Option>
            </Select>
          </div>
        </div>

        <div className={styles.newOrder_item}>
          <span className={styles.newOrder_item_title}>省：</span>
          <Select
            // disabled={(newOrder.status !== pageStatus.SystemReturn) && (data)}
            style={{ width: 250 }}
            value={newOrder.province}
            onChange={e => {
              changProvince(e)
            }}>
            {provinceList && provinceList.length > 0 ? provinceList.map((v: any, i: number) => <Option key={i}>{v.name}</Option>) : null}
          </Select>
        </div>
        <div className={styles.newOrder_item}>
          <span className={styles.newOrder_item_title}>市：</span>
          <Select
            style={{ width: 250 }}
            // disabled={(newOrder.status !== pageStatus.SystemReturn) && (data)}
            value={newOrder.city}
            onChange={e => {
              changCity(e)
            }}>
            {cityList && cityList.length > 0 ? cityList.map((v: any, i: number) => <Option key={i}>{v.name}</Option>) : null}
          </Select>
        </div>
        <div className={styles.newOrder_item}>
          <span className={styles.newOrder_item_title}>区：</span>
          <Select
            style={{ width: 250 }}
            // disabled={(newOrder.status !== pageStatus.SystemReturn) && (data)}
            value={newOrder.county}
            onChange={e => {
              changDistrit(e)
            }}>
            {districtList && districtList.length > 0 ? districtList.map((v: any, i: number) => <Option key={i}>{v.name}</Option>) : null}
          </Select>
        </div>
        <div className={styles.newOrder_item}>
          <span className={styles.newOrder_item_title}>街道：</span>
          <Select
            style={{ width: 250 }}
            value={newOrder.receiverTown}
            // disabled={(newOrder.status !== pageStatus.SystemReturn) && (data)}
            onChange={e => {
              changSubDistrit(e)
            }}>
            {subDistrictList && subDistrictList.length > 0 ? subDistrictList.map((v: any, i: number) => <Option key={i}>{v.name}</Option>) : null}
          </Select>
        </div>
        <div className={styles.newOrder_item}>
          <span className={styles.newOrder_item_title}>家电品类：</span>
          <Select
            style={{ width: 250 }}
            value={newOrder.type}
            // disabled={(newOrder.status !== pageStatus.SystemReturn) && (data)}
            onChange={e => {
              changeValue({ type: e })
            }}>
            {typeList.map((v: any, i: number) => <Option key={v} >{v}</Option>)}
          </Select>
        </div>
        <div className={styles.newOrder_item}>
          <span className={styles.newOrder_item_title}>平台：</span>
          <Select
            style={{ width: 250 }}
            value={newOrder.from}
            disabled={data}
            onChange={e => {
              changeValue({ from: e })
            }}>
            {SourceLevels.map((v: any, i: number) => <Option key={v} >{v}</Option>)}
          </Select>
        </div>
        <div className={styles.newOrder_item}>
          <span className={styles.newOrder_item_title}>信息费：</span>
          <Input
            placeholder="请输入信息费"
            onChange={e => {
              changeValue({ infoFee: e?.target?.value })
            }}
            style={{ width: 250 }}
            value={newOrder.infoFee}
            type="text"
          />
        </div>
        <div className={styles.newOrder_item}>
          <span className={styles.newOrder_item_title}>详细地址：</span>
          <Input
            placeholder="请输入详细地址，具体到门牌号"
            // disabled={(newOrder.status !== pageStatus.SystemReturn) && (data)}
            onChange={e => {
              changeValue({ address: e?.target?.value })
            }}
            style={{ width: 400 }}
            value={newOrder.address}
            type="text"
          />
        </div>
        <div className={styles.newOrder_item}>
          <span className={styles.newOrder_item_title}>备注：</span>
          <Input
            placeholder="请输入详细备注"
            onChange={e => {
              changeValue({ remark: e?.target?.value })
            }}
            style={{ width: 400 }}
            value={newOrder?.remark}
            type="text"
          />
        </div>
      </div>
    </Modal>
  )
}
