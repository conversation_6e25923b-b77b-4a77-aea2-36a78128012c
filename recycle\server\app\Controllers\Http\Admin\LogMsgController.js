'use strict'

const _ = require('lodash')
const moment = require('moment')

const { LogMsg, } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { CryptUtil, Configs } = require('../../../Util')
const axios = require("axios");
const Env = use('Env')
class LogMsgController {
  async index({ request, response }) {
    let { current = 1, pageSize = 20, openid, sort = 'desc', createdAt, keyword, startDate, endDate } = request.all()
    let query = LogMsg.query()
    if (keyword) {
      query.where('msgBody', 'like', `%${keyword}%`)
    }
    if (openid) {
      query.where('openid', openid)
    }
    switch (createdAt) {
      case 'descend':
        query.orderBy('createdAt', 'desc')
        break
      case 'ascend':
        query.orderBy('createdAt', 'asc')
        break
      default:
        break
    }
    if (startDate && endDate) {
      query.where('createdAt', '>=', moment(startDate).toDate()).where('createdAt', '<=', moment(endDate).add(1, 'd').toDate())
    }
    let vo = await query.orderBy('id', sort).paginate(current, pageSize)
    let res = vo.toJSON()
    let TotalPay
    try {
      const pay = await axios.get(
        'https://api.ouhe-tech.com/tongyi/admin/v1/smsQuery'
      )
      let datajson = pay.data.data
      let DbPAY = _.filter(datajson, { id: 1 })[0]
      TotalPay = parseInt(DbPAY.totalPay)
    } catch (error) {
      console.log(error);
      TotalPay = Configs.totalPay
    }
    res.totalPay = TotalPay
    response.json(res)
  }

  async store({ request, response }) {
    response.json({ msg: 'ok' })
  }
  async update({ request, params, response }) {
    response.json({ msg: 'ok' })
  }

  async destroy({ request, params, response }) {
    return ({ msg: 'delete ok', code: 200 })
  }
}

module.exports = LogMsgController
