import { DownloadOutlined, PlusOutlined, UserAddOutlined } from '@ant-design/icons'
import { notification, Table, Badge, Modal, Button, Switch, InputNumber, Select, Input, message, Space, Form } from 'antd'
import type { ActionType, ProColumns } from '@ant-design/pro-table'
import ProTable from '@ant-design/pro-table'
import ProCard from '@ant-design/pro-card'
import { sumBy } from 'lodash'
import { useEffect, useState, useRef } from 'react'
import { masterStatusEnum } from '../../common/enum'
import styles from './index.module.less'
import { SERVER_HOME } from '../../common/config'
import { effect, useConnect } from 'dva17'
import {
  NCompany, EGet, NWorker, EChangeWorkerUse, EGetWorkerReview, NUser,
  EGetWorkerPaylist,
  EGetWorkerWalletlog,
  EWorkerSelected,
  EAddWorkerAssistant,
} from '../../common/action'
import { useNavigate, useParams } from 'react-router-dom'
import dayjs from 'dayjs'
import { computeAuthority } from '../../utils/Authorized/authority'
import WorkerDetailModal from '../../components/WorkerDetailModal'
import WorkerInsuranceModal from '../../components/WorkerInsuranceModal'
import WorkerAreaEdit from '../../components/WorkerAreaEdit'
import WorkerChargeModal from '../../components/WorkerChargeModal'
import WorkerChargeLogModal from '../../components/WorkerChargeLogModal'
import WorkerWalletLogModal from '../../components/WorkerWalletLogModal'
import WorkerReviewModal from '../../components/WorkerReviewModal'
import WorkerAddressModal from '../../components/WorkerAddressModal'
import WorkerDataModal from '../../components/WorkerDataModal'

const { Option } = Select

// 编辑单元格组件
interface EditableCellProps extends React.HTMLAttributes<HTMLElement> {
  editing: boolean;
  dataIndex: string;
  title: string;
  inputType: 'text' | 'number';
  record: any;
  index: number;
  children: React.ReactNode;
}

const EditableCell: React.FC<EditableCellProps> = ({
  editing,
  dataIndex,
  title,
  inputType,
  record,
  index,
  children,
  ...restProps
}) => {
  const inputNode = inputType === 'number' ? <InputNumber /> : <Input />;
  return (
    <td {...restProps}>
      {editing ? (
        <Form.Item
          name={dataIndex}
          style={{ margin: 0 }}
          rules={[
            {
              required: true,
              message: `请输入${title}!`,
            },
          ]}
        >
          {inputNode}
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );
};

export default () => {
  const any: any = null
  const { workerReviewList, workerPaylist } = useConnect(NWorker)
  const { currentUser } = useConnect(NUser)
  const { workerSelected, companyList } = useConnect(NCompany)
  const actionRef = useRef<ActionType>()
  const params = useParams()
  const navigate = useNavigate()
  // 各种弹窗状态管理
  const [visibleLog, setVisibleLog] = useState(false);
  const [visibleWalletLog, setWalletVisibleLog] = useState(false);
  const [visibleCharge, setVisibleCharge] = useState(false);
  const [visibleInsure, setVisibleInsure] = useState(false);
  const [visibleReview, setVisibleReview] = useState(false);
  const [visibleAreaEdit, setVisibleAreaEdit] = useState(false);
  const [visibleDetail, setVisibleDetail] = useState(false);
  const [visibleAddress, setVisibleAddress] = useState(false);
  const [visibleData, setVisibleData] = useState(false);

  // 当前操作的师傅
  const [currentWorker, setCurrentWorker] = useState<any>(null);
  const [workerID, setworkerID] = useState<number>(0);
  const [companyID, setCompanyID] = useState<number | null>(null);
  const [visible, setVisible] = useState(any)

  const [assistantVisible, setAssistantVisible] = useState(false)
  const [assistantName, setAssistantName] = useState('')
  const [assistantMobile, setAssistantMobile] = useState('')
  const [assistantWorker, setAssistantWorker] = useState<any>(null)
  const [assistantListVisible, setAssistantListVisible] = useState(false)
  const [assistantList, setAssistantList] = useState<any>([])
  const [form] = Form.useForm();
  const [editingKey, setEditingKey] = useState('');

  // 判断一行是否处于编辑状态
  const isEditing = (record: any) => record.id.toString() === editingKey;

  // 进入编辑状态
  const edit = (record: any) => {
    form.setFieldsValue({
      workerName: '',
      mobile: '',
      ...record,
    });
    setEditingKey(record.id.toString());
  };

  // 取消编辑
  const cancel = () => {
    setEditingKey('');
  };

  // 保存编辑后的数据
  const save = async (key: string) => {
    try {
      const row = await form.validateFields();

      // 调用API更新小工信息
      await effect(NWorker, EChangeWorkerUse, {
        id: key,
        workerName: row.workerName,
        mobile: row.mobile,
      });

      notification.success({
        message: '成功！',
        description: '信息更新成功',
        duration: 2,
      });

      setEditingKey('');
      refreshPage();
    } catch (errInfo: any) {
      message.error('保存失败: ' + errInfo.message);
    }
  };

  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    let id = params.id;
    let companyID;
    effect(NWorker, EGetWorkerReview, { current: 1, pageSize: 10 });
    if (id && id.indexOf('id') > 0) {
      companyID = currentUser.companyID;
    } else {
      companyID = id;
    }
    setCompanyID(companyID);
  }, [params.id, currentUser]);

  // 刷新页面数据
  const refreshPage = () => {
    if (actionRef.current) {
      actionRef.current.reload();
    }
  };

  // 导出师傅数据
  const exportExcel = () => {
    window.open(`${SERVER_HOME}exportMasterXLS`);
  };

  // 师傅详情
  const showWorkerDetail = (worker: any) => {
    setCurrentWorker(worker);
    setVisibleDetail(true);
  };

  // 充值
  const showChargeModal = (worker: any) => {
    setCurrentWorker(worker);
    setVisibleCharge(true);
  };

  // 查看服务区域
  const showAddressModal = (worker: any) => {
    effect(NCompany, EWorkerSelected, { workerID: worker.id });
    setVisibleAddress(true);
  };
   // 查看师傅数据
   const showDataModal = (worker: any) => {
    setCurrentWorker(worker);
    setVisibleData(true);
  };

  // 表格列配置
  const columns: ProColumns<any>[] = [
    { title: '姓名', dataIndex: 'workerName', copyable: false, ellipsis: true },
    { title: '联系电话', dataIndex: 'mobile', copyable: false, ellipsis: true },
    {
      title: '隶属公司',
      dataIndex: 'companyNameID',
      copyable: false,
      ellipsis: true,
      hideInTable: true,
      valueType: "select",
      fieldProps: { allowClear: false },
      request: async () => {
        let options: any = [];
        await effect(NCompany, EGet, { pageSize: 50, forbidden: 1 })
          .then((response: any) => {
            response.data.map((vo: any) => {
              options.push({ label: vo.companyName, value: vo.id });
            });
          });
        return options;
      }
    },
    {
      title: '隶属公司',
      dataIndex: ['company', 'companyName'],
      copyable: false,
      search: false,
      render: (_, row) => (
        <Select
          value={row.company && row.company?.companyName}
          style={{ width: 100 }}
          disabled={!computeAuthority('回收人员审核') || currentUser.level == '服务商'}
          onChange={(e: any) => {
            effect(NWorker, EChangeWorkerUse, {
              id: row.id,
              companyID: e,
            }).then(() => {
              notification.success({
                message: '成功！',
                description: '更改成功',
                duration: 2,
              });
              refreshPage();
            });
          }}
        >
          {companyList && companyList.map((row: any, index: number) => (
            <Option key={index} value={row.id}>{row.companyName}</Option>
          ))}
        </Select>
      ),
    },
    {
      title: '回收行政区',
      dataIndex: 'district',
      copyable: false,
      ellipsis: true,
      search: false,
      render: (_, row) => (
        <a onClick={() => showAddressModal(row)}>查看详情</a>
      ),
    },
    {
      title: '订单数',
      dataIndex: 'count',
      search: false,
      render: (_, row: any) => (
        <>{(row.order?.length > 0 && sumBy(row.order, 'count')) || 0}</>
      )
    },
    {
      title: '保险',
      dataIndex: 'count',
      search: false,
      render: (_, row: any) => (
        <>
          <a onClick={() => {
            setCurrentWorker(row);
            setVisibleInsure(true);
          }}>保险管理</a>
          {row.insure ? (
            dayjs(row.insure.overTime, 'YYYY-MM-DD').isBefore(dayjs()) ?
              <div style={{ color: 'red' }}>已过期</div> :
              <div style={{ color: 'green' }}>在期内</div>
          ) : <div style={{ color: 'red' }}>未提交</div>}
        </>
      )
    },
    {
      title: '钱包余额',
      dataIndex: 'wallet',
      search: false,
      copyable: false,
      ellipsis: true,
      sorter: (a, b) => a.wallet - b.wallet,
      render: (_, row: any) => (
        <>{row?.wallet < 500 ? <div style={{ color: 'red' }}>{row?.wallet}</div> : row?.wallet}</>
      )
    },
    {
      title: '钱包余额',
      dataIndex: 'wallet',
      copyable: false,
      ellipsis: true,
      valueType: 'digitRange',
      hideInTable: true,
      search: {
        transform: value => ({
          lowWallet: value[0],
          hightWallet: value[1],
        }),
      },
    },
    {
      title: '充值记录',
      dataIndex: 'recor',
      search: false,
      render: (_, row: any) => (
        <Button onClick={() => {
          setworkerID(row?.id);
          effect(NWorker, EGetWorkerPaylist, { status: '完成', current: 1, pageSize: 100, workerID: row?.id });
          setVisibleLog(true);
        }}>查看</Button>
      )
    },
    {
      title: '钱包消费记录',
      dataIndex: 'recor',
      search: false,
      render: (_, row: any) => (
        <Button onClick={() => {
          setworkerID(row?.id);
          effect(NWorker, EGetWorkerWalletlog, { current: 1, pageSize: 100, workerID: row?.id });
          setWalletVisibleLog(true);
        }}>查看</Button>
      )
    },
    {
      title: '启用状态',
      dataIndex: 'forbidden',
      copyable: false,
      ellipsis: true,
      valueEnum: masterStatusEnum,
      render: (_, row) => (
        <Switch
          checkedChildren="启动"
          unCheckedChildren="停用"
          disabled={!computeAuthority('回收人员编辑')}
          checked={row.forbidden === 0 ? false : true}
          onChange={e => {
            effect(NWorker, EChangeWorkerUse, {
              id: row.id,
              forbidden: e ? 1 : 0,
            }).then(() => {
              notification.success({
                message: '成功！',
                description: '更改成功',
                duration: 2,
              });
              refreshPage();
            });
          }}
        />
      ),
    },
    {
      title: '操作',
      width: '18%',
      copyable: false,
      ellipsis: true,
      search: false,
      render: (_, row: any) => (
        <div>
          <Space direction='vertical'>
            <Button
              onClick={() => {
                showWorkerDetail(row)
              }}>
              个人信息
            </Button>
            <Button
              disabled={currentUser.name !== "兰姐" && currentUser.name !== "系统"}
              onClick={() => {
                showChargeModal(row)
              }}>
              充值
            </Button>
            <Button
              disabled={!computeAuthority('回收人员编辑')}
              onClick={() => {
                setCurrentWorker(row);
                setVisibleAreaEdit(true);
              }}>
              区域编辑 区域编辑
            </Button>
            <Button
              onClick={() => {
                showDataModal(row)
              }}>
              数据统计
            </Button>
          </Space>
          <Space direction='vertical'>
            <Button
              key="2"
              type="primary"
              style={{ marginLeft: 8 }}
              onClick={() => {
                setAssistantVisible(true)
                setAssistantWorker(row.id)
              }}>
              <UserAddOutlined />
              添加小工
            </Button>
            <Button
              key="1"
              onClick={() => {
                viewAssistantList(row)
              }}>
              查看小工{row?.assistants?.length}
            </Button>
          </Space>
        </div>
      ),
    },
  ]

  const addAssistant = () => {
    if (!assistantName) {
      message.error('请输入小工姓名')
      return
    }
    if (!assistantMobile || !/^1\d{10}$/.test(assistantMobile)) {
      message.error('请输入正确的手机号')
      return
    }
    if (!assistantWorker) {
      message.error('请选择所属师傅')
      return
    }

    effect(NWorker, EAddWorkerAssistant, {
      assistantName,
      mobile: assistantMobile,
      workerID: assistantWorker,
      password: assistantMobile.slice(-6),
    }).then(() => {
      notification.success({
        message: '成功！',
        description: '添加小工成功',
        duration: 2,
      })
      setAssistantVisible(false)
      setAssistantName('')
      setAssistantMobile('')
      setAssistantWorker(null)
    }).catch((err) => {
      message.error(err.message || '添加小工失败')
    })
  }

  const viewAssistantList = (row: any) => {
    setAssistantWorker(row.id)
    setAssistantList(row.assistants || [])
    setAssistantListVisible(true)
  }

  const assistantColumns = [
    {
      title: '小工姓名',
      dataIndex: 'workerName',
      editable: true,
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
      editable: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: (createdAt: any) => <span>{createdAt ? dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss') : '-'}</span>,
    },
    {
      title: '启用状态',
      dataIndex: 'forbidden',
      copyable: false,
      ellipsis: true,
      valueEnum: masterStatusEnum,
      render: (_: any, row: any) => (
        <Switch
          checkedChildren="启动"
          unCheckedChildren="停用"
          disabled={!computeAuthority('回收人员编辑')}
          checked={row.forbidden === 0 ? false : true}
          onChange={e => {
            effect(NWorker, EChangeWorkerUse, {
              id: row.id,
              forbidden: e ? 1 : 0,
            }).then(() => {
              setAssistantListVisible(false)
              notification.success({
                message: '成功！',
                description: '更改成功',
                duration: 2,
              })
              refreshPage()
            })
          }}
        />
      ),
    },
    {
      title: '操作',
      dataIndex: 'operation',
      render: (_: any, record: any) => {
        const editable = isEditing(record);
        return editable ? (
          <Space>
            <Button
              onClick={() => save(record.id)}
              type="primary"
              size="small"
            >
              保存
            </Button>
            <Button
              onClick={cancel}
              size="small"
            >
              取消
            </Button>
          </Space>
        ) : (
          <Button
            disabled={editingKey !== ''}
            onClick={() => edit(record)}
            size="small"
            type="link"
          >
            编辑
          </Button>
        );
      },
    },
  ];

  // 合并可编辑功能
  const mergedAssistantColumns = assistantColumns.map(col => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: any) => ({
        record,
        inputType: col.dataIndex === 'mobile' ? 'text' : 'text',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  const renderAssistantListModal = () => (
    <Modal
      title="小工列表"
      open={assistantListVisible}
      onOk={() => setAssistantListVisible(false)}
      onCancel={() => setAssistantListVisible(false)}
      destroyOnClose
      footer={null}
      width={800}>
      <Form form={form} component={false}>
        <Table
          components={{
            body: {
              cell: EditableCell,
            },
          }}
          columns={mergedAssistantColumns}
          loading={!assistantList}
          dataSource={assistantList}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </Form>
    </Modal>
  );

  return (
    <div>
      <ProCard>
        <ProTable
          actionRef={actionRef}
          columns={columns}
          request={async (params = {}, sorter) => (
            await effect(NWorker, EGet, { ...params, ...sorter, companyID })
          ) as any}
          rowKey="id"
          dateFormatter="string"
          toolBarRender={() => [
            <Badge count={workerReviewList?.total || 0} key="review-badge">
              <Button
                key="export"
                disabled={!computeAuthority('回收人员审核') || currentUser.level == '服务商'}
                onClick={exportExcel}>
                <DownloadOutlined />
                导出
              </Button>
              <Button
                disabled={!computeAuthority('回收人员审核') || currentUser.level == '服务商'}
                key="review"
                type="primary"
                onClick={() => setVisibleReview(true)}>
                <PlusOutlined />
                人员申请列表
              </Button>
            </Badge>,
          ]}
        />
      </ProCard>

      {/* 服务区域查看弹窗 */}
      <WorkerAddressModal
        visible={visibleAddress}
        workerSelected={workerSelected}
        onClose={() => setVisibleAddress(false)}
      />

      {/* 各种功能组件弹窗 */}
      <WorkerDetailModal
        visible={visibleDetail}
        worker={currentWorker}
        onClose={() => setVisibleDetail(false)}
        onRefresh={refreshPage}
      />

      <WorkerInsuranceModal
        visible={visibleInsure}
        worker={currentWorker}
        onClose={() => setVisibleInsure(false)}
        onSuccess={refreshPage}
      />

      <WorkerAreaEdit
        visible={visibleAreaEdit}
        workerId={currentWorker?.id}
        workerDetail={currentWorker}
        onClose={() => setVisibleAreaEdit(false)}
        onSuccess={refreshPage}
      />

      <WorkerChargeModal
        visible={visibleCharge}
        worker={currentWorker}
        onClose={() => setVisibleCharge(false)}
        onSuccess={refreshPage}
      />

      <WorkerChargeLogModal
        visible={visibleLog}
        workerID={workerID}
        onClose={() => setVisibleLog(false)}
      />

      <WorkerWalletLogModal
        visible={visibleWalletLog}
        workerID={workerID}
        onClose={() => setWalletVisibleLog(false)}
      />

      <WorkerReviewModal
        visible={visibleReview}
        onClose={() => setVisibleReview(false)}
        companyID={companyID}
      />

      <WorkerDataModal
        visible={visibleData}
        worker={currentWorker}
        onClose={() => setVisibleData(false)}
      />

      <Modal
        title="添加小工"
        open={assistantVisible}
        onOk={addAssistant}
        onCancel={() => setAssistantVisible(false)}
        destroyOnClose
        width={500}>
        <div className={styles.item_wrapper}>
          <div className={styles.item} style={{ marginTop: 30, paddingBottom: 10 }}>
            <span className={styles.item_title}>小工姓名：</span>
            <div className={styles.item_content}>
              <Input
                placeholder="请输入小工姓名"
                value={assistantName}
                onChange={(e) => setAssistantName(e.target.value)}
              />
            </div>
          </div>
          <div className={styles.item}>
            <span className={styles.item_title}>手机号：</span>
            <div className={styles.item_content}>
              <Input
                placeholder="请输入手机号"
                value={assistantMobile}
                onChange={(e) => setAssistantMobile(e.target.value)}
              />
            </div>
          </div>
          <div className={styles.item} style={{ color: '#888', fontSize: '12px', marginTop: 10 }}>
            注：小工的密码默认为手机号后6位，所属公司默认为师傅所属公司
          </div>
        </div>
      </Modal>
      {renderAssistantListModal()}
    </div>
  )
}
