.main {
  display: flex;
  flex-direction: column;
  background: #ffffff;
  padding: 30px 20px;
  :global {
    .ant-modal-content {
      .ant-btn {
        display: none;
      }
      .ant-btn-primary {
        display: inline-block;
      }
    }
  }
}
.item_wrapper {
  display: flex;
  flex-direction: column;
  padding: 20px;
  background: #fafafa;
  border-radius: 6px;
}
.item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}
.item_title {
  font-weight: 600;
  color: #333;
  min-width: 120px;
  margin-right: 12px;
}
.item_content {
  color: #666;
  flex: 1;
  word-break: break-all;
}
.operate_wrapper {
  display: flex;
  gap: 8px;
}
.charge_form {
  padding: 20px 0;
  
  .form_item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    
    .form_label {
      font-weight: 500;
      min-width: 80px;
      margin-right: 12px;
    }
  }
}
.area_item {
  width: 120px;
  height: 34px;
  border: 1px solid #1890ff;
  border-radius: 4px;
  line-height: 34px;
  text-align: center;
  color: #1890ff;
  flex-shrink: 0;
  margin-right: 20px;
  margin-bottom: 10px;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.selected {
  background: #1890ff;
  color: #ffffff;
}
