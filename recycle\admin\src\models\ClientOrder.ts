import { } from 'dva17'
import {
  ECancelOrderStatus, EChangeOrderStatus, EChangeOrderWorker,
  EDevideOrderAgain, EGetNotice, EGetOrderDetail,
  EGetOrderList, EGetOrderNumberArray, EConfirmOrder,
  EGetWhichWorkers, EOrderBack,  EPut, ESeeComplaint,
  ESeeRating, RSetState, NClientOrder
} from '../common/action'
import { adapterPaginationResult } from '../common/utils'
import {
  getOrderList,
  changeOrderStatus,
  getWhichWorkers,
  getWorkerDetail,
  orderBack,
  seeComplaint,
  devideOrder,
  seeRating,
  getOrderDetail,
  getOrderCount,
  getNotice,
  postConfirmOrder,
  orderSend,
  changeWorker,
} from '../services/clientOrder'
import { message } from 'antd'

export default {
  namespace: NClientOrder,
  state: {
    visibleLog: false,
    logData: null,
    orderNumberArray: null,
    orderList: null,
    isStatusChange: false,
    isCancelOrder: false,
    whichWorkers: null,
    complaintList: null,
    ratingList: null,
    searchQuery: {},
    notices: null
  },
  reducers: {
    [RSetState](state: any, payload: any) {
      return { ...state, ...payload }
    },
  },
  effects: {
    //获取各种状态的订单的列表
    async [EGetOrderList]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { orderList: null })
      const response = await getOrderList(payload)
      // console.log('result: ', response)
      reducer(RSetState, { orderList: response, searchQuery: payload })
      return adapterPaginationResult(response)
    },
    //获取各种状态订单的数量
    async [EGetOrderNumberArray]({ payload }: any, { reducer }: any) {
      let res = await getOrderCount(payload)
      reducer(RSetState, {
        orderNumberArray: res
      })
    },
    async [EPut]({ payload }: any, { reducer }: any) {
      const hide = message.loading('正在添加')
      reducer(RSetState, { isStatusChange: false })
      const response = await orderSend(payload)
      reducer(RSetState, { isStatusChange: true })
      hide()
    },
    //修改订单的状态
    async [EChangeOrderStatus]({ payload }: any, { reducer }: any) {
      const hide = message.loading('正在添加')
      reducer(RSetState, { isStatusChange: false })
      if (payload.isTransferID) {
        const response = await changeOrderStatus({
          id: payload?.id, isTransferID: payload?.isTransferID
        })
        const worker = await getWorkerDetail({
          id: payload.isTransferID
        })
        // const message = await sendSMS, {
        //   mobile: worker.mobile,
        //   smsCode: 'SMS_184825898',
        //   smsParam: {
        //     orderNo: response.orderNo,
        //     wasteType: payload.waste_1st_ID === 3 ? '大家电' : payload.waste_1st_ID === 2 ? '生活废品' :payload.waste_1st_ID === 5 ? '海尔电器' : '大家具'
        //   }
        // })
      } else {
        const response = await changeOrderStatus(payload)
      }
      reducer(RSetState, { isStatusChange: true })
      hide()
    },
    async [EConfirmOrder]({ payload }: any, { reducer }: any) {
      const hide = message.loading('正在确认订单')
      reducer(RSetState, { isStatusChange: false })
      const response = await postConfirmOrder(payload)
      reducer(RSetState, { isStatusChange: true })
      hide()
    },
    // 改派
    async [EChangeOrderWorker]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isStatusChange: false })
      const response = await changeWorker(payload)
      reducer(RSetState, { isStatusChange: true })
    },
    // 获取订单详情
    async [EGetOrderDetail]({ payload }: any, { reducer }: any) {
      const response = await getOrderDetail(payload)
      return response
    },
    //订单撤回
    async [EOrderBack]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isStatusChange: false })
      const response = await orderBack(payload)
      reducer(RSetState, { isStatusChange: true })
    },
    async [ECancelOrderStatus]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isCancelOrder: false })
      const response = await changeOrderStatus(payload)
      reducer(RSetState, { isCancelOrder: true })
    },
    //获取可接单人员
    async [EGetWhichWorkers]({ payload }: any, { reducer }: any) {
      const response = await getWhichWorkers(payload)
      reducer(RSetState, { whichWorkers: response })
    },
    //获取客诉订单
    async [ESeeComplaint]({ payload }: any, { reducer }: any) {
      const response = await seeComplaint(payload)
      return adapterPaginationResult(response)
    },
    //订单重分配
    async [EDevideOrderAgain]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isStatusChange: false })
      const response = await devideOrder(payload)
      reducer(RSetState, { isStatusChange: true })
    },
    //获取订单评价
    async [ESeeRating]({ payload }: any, { reducer }: any) {
      const response = await seeRating(payload)
      return adapterPaginationResult(response)
    },
    //获取订单评价
    async [EGetNotice]({ payload }: any, { reducer }: any) {
      const response = await getNotice(payload)
      reducer(RSetState, { notices: response })
    },
  }
}
