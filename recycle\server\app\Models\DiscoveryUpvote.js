'use strict'

const Model = use('Model')

//订单评价
class DiscoveryUpvote extends Model {
  static get table() {
    return 'discovery_upvote'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return 'updatedAt'
  }
  // userInfo() {
  //   return this.belongsTo('App/Models/User', ['userID', 'toUserID'], 'id')
  // }
}

module.exports = DiscoveryUpvote
