---
description: 代码质量和最佳实践规范
alwaysApply: true
---

# 代码质量和最佳实践规范

## 代码风格统一

### JavaScript/TypeScript 规范
```javascript
// 1. 命名规范
// 变量和函数：小驼峰命名
const userName = 'zhang_san';
const getUserInfo = async (userId) => { /* ... */ };

// 常量：大写下划线分隔
const API_BASE_URL = 'https://api.example.com';
const ORDER_STATUS = {
  PENDING: 'pending',
  COMPLETED: 'completed'
};

// 类名：大驼峰命名
class OrderService {
  constructor() { /* ... */ }
}

// 接口/类型：大驼峰命名，接口以 I 开头
interface IOrderData {
  id: number;
  status: string;
}

type OrderStatus = 'pending' | 'completed' | 'cancelled';

// 2. 函数设计原则
// 单一职责原则
const calculateOrderTotal = (items) => {
  return items.reduce((total, item) => total + item.price, 0);
};

const formatCurrency = (amount) => {
  return `¥${amount.toFixed(2)}`;
};

// 参数对象模式 (RORO)
const createOrder = ({
  userId,
  items,
  address,
  appointmentTime
}) => {
  return Order.create({
    user_id: userId,
    items: JSON.stringify(items),
    address,
    appointment_time: appointmentTime,
    status: 'pending'
  });
};

// 3. 错误处理最佳实践
// 早期返回模式
const processOrder = async (orderId) => {
  const order = await Order.find(orderId);
  if (!order) {
    throw new Error('订单不存在');
  }
  
  if (order.status !== 'pending') {
    throw new Error('订单状态不允许此操作');
  }
  
  // 主要业务逻辑
  return await order.process();
};

// 自定义错误类型
class ValidationError extends Error {
  constructor(message, field) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
  }
}

class BusinessError extends Error {
  constructor(message, code) {
    super(message);
    this.name = 'BusinessError';
    this.code = code;
  }
}
```

### React 组件最佳实践
```typescript
// 1. 组件设计原则
interface OrderCardProps {
  order: Order;
  onUpdate?: (orderId: number) => void;
  onDelete?: (orderId: number) => void;
  showActions?: boolean;
}

// 使用 memo 优化性能
const OrderCard = memo<OrderCardProps>(({
  order,
  onUpdate,
  onDelete,
  showActions = true
}) => {
  // 提取自定义 hooks
  const { formatTime } = useTimeFormatter();
  const { formatPrice } = usePriceFormatter();
  
  // 使用 useCallback 优化事件处理
  const handleUpdate = useCallback(() => {
    onUpdate?.(order.id);
  }, [order.id, onUpdate]);
  
  const handleDelete = useCallback(() => {
    onDelete?.(order.id);
  }, [order.id, onDelete]);
  
  // 条件渲染优化
  if (!order) {
    return <Empty description="暂无订单数据" />;
  }
  
  return (
    <Card className="order-card">
      <div className="order-header">
        <Text strong>{order.order_no}</Text>
        <Tag color={getStatusColor(order.status)}>
          {getStatusText(order.status)}
        </Tag>
      </div>
      
      <div className="order-content">
        <Text>{formatTime(order.created_at)}</Text>
        <Text type="success">{formatPrice(order.total_price)}</Text>
      </div>
      
      {showActions && (
        <div className="order-actions">
          <Button size="small" onClick={handleUpdate}>
            更新
          </Button>
          <Button size="small" danger onClick={handleDelete}>
            删除
          </Button>
        </div>
      )}
    </Card>
  );
});

// 2. 自定义 Hooks 设计
const useOrderData = (filters: OrderFilters) => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const fetchOrders = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await orderService.getOrders(filters);
      setOrders(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取订单失败');
    } finally {
      setLoading(false);
    }
  }, [filters]);
  
  useEffect(() => {
    fetchOrders();
  }, [fetchOrders]);
  
  const refresh = useCallback(() => {
    fetchOrders();
  }, [fetchOrders]);
  
  return { orders, loading, error, refresh };
};

// 3. 高阶组件模式
const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return (props: P) => (
    <ErrorBoundary>
      <Component {...props} />
    </ErrorBoundary>
  );
};

const withLoading = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return (props: P & { loading?: boolean }) => {
    if (props.loading) {
      return <Spin size="large" />;
    }
    return <Component {...props} />;
  };
};
```

## 代码审查标准

### 审查清单
```markdown
## 功能性审查
- [ ] 代码实现是否符合需求
- [ ] 边界条件处理是否完整
- [ ] 错误处理是否恰当
- [ ] 性能是否满足要求

## 代码质量审查
- [ ] 命名是否清晰有意义
- [ ] 函数是否遵循单一职责
- [ ] 代码复用性是否良好
- [ ] 注释是否充分且准确

## 安全性审查
- [ ] 输入验证是否充分
- [ ] 敏感信息是否泄露
- [ ] SQL注入防护是否到位
- [ ] XSS防护是否完善

## 测试审查
- [ ] 单元测试覆盖率
- [ ] 集成测试是否完整
- [ ] 测试用例是否合理

## 文档审查
- [ ] API文档是否更新
- [ ] 代码注释是否完善
- [ ] 变更日志是否记录
```

### 常见问题和解决方案
```javascript
// ❌ 问题：魔法数字
const discount = price * 0.8;

// ✅ 解决：使用常量
const DISCOUNT_RATE = 0.8;
const discount = price * DISCOUNT_RATE;

// ❌ 问题：深层嵌套
if (user) {
  if (user.isActive) {
    if (user.hasPermission) {
      // 执行操作
    }
  }
}

// ✅ 解决：早期返回
if (!user) return;
if (!user.isActive) return;
if (!user.hasPermission) return;
// 执行操作

// ❌ 问题：长参数列表
const createUser = (name, email, phone, address, age, gender) => {
  // ...
};

// ✅ 解决：参数对象
const createUser = ({ name, email, phone, address, age, gender }) => {
  // ...
};

// ❌ 问题：重复代码
const formatUserName = (user) => {
  return user.firstName + ' ' + user.lastName;
};
const formatWorkerName = (worker) => {
  return worker.firstName + ' ' + worker.lastName;
};

// ✅ 解决：提取公共函数
const formatFullName = (person) => {
  return `${person.firstName} ${person.lastName}`;
};
```

## 文档规范

### 代码注释规范
```javascript
/**
 * 计算订单总价
 * @param {Array<Object>} items - 订单项目列表
 * @param {number} items[].price - 单项价格
 * @param {number} items[].quantity - 数量
 * @param {Object} options - 计算选项
 * @param {boolean} options.includeTax - 是否包含税费
 * @param {number} options.discountRate - 折扣率 (0-1)
 * @returns {number} 订单总价
 * @throws {ValidationError} 当参数无效时抛出
 * @example
 * const total = calculateOrderTotal(
 *   [{ price: 100, quantity: 2 }],
 *   { includeTax: true, discountRate: 0.1 }
 * );
 */
const calculateOrderTotal = (items, options = {}) => {
  // 参数验证
  if (!Array.isArray(items) || items.length === 0) {
    throw new ValidationError('订单项目不能为空');
  }
  
  const { includeTax = false, discountRate = 0 } = options;
  
  // 计算基础总价
  const baseTotal = items.reduce((total, item) => {
    return total + (item.price * item.quantity);
  }, 0);
  
  // 应用折扣
  const discountedTotal = baseTotal * (1 - discountRate);
  
  // 计算税费
  if (includeTax) {
    const TAX_RATE = 0.13; // 13% 税率
    return discountedTotal * (1 + TAX_RATE);
  }
  
  return discountedTotal;
};

// React 组件注释
/**
 * 订单列表组件
 * 
 * 功能：
 * - 展示订单列表
 * - 支持筛选和排序
 * - 支持批量操作
 * 
 * @component
 * @param {Object} props
 * @param {Array<Order>} props.orders - 订单数据
 * @param {Function} props.onUpdate - 更新订单回调
 * @param {boolean} props.loading - 加载状态
 */
const OrderList = ({ orders, onUpdate, loading }) => {
  // 组件实现
};
```

### README 文档模板
```markdown
# 家电回收项目

## 项目简介
家电回收平台，包含用户端、师傅端小程序和管理后台系统。

## 技术栈
- 前端：Taro.js, React.js, TypeScript, Ant Design
- 后端：Node.js, Adonis.js, MySQL
- 部署：Docker, Nginx

## 项目结构
```
recycle/
├── client/     # 用户端小程序
├── master/     # 师傅端小程序  
├── admin/      # 管理后台
└── server/     # API服务
```

## 快速开始

### 环境要求
- Node.js >= 16.0.0
- MySQL >= 8.0
- Redis >= 6.0

### 安装依赖
```bash
# 后端
cd server && npm install

# 管理后台
cd admin && npm install

# 用户端
cd client && npm install
```

### 开发命令
```bash
# 启动后端服务
npm run dev:server

# 启动管理后台
npm run dev:admin

# 启动小程序开发
npm run dev:client
```

## API 文档
详见 [API文档](./docs/api.md)

## 部署说明
详见 [部署文档](./docs/deployment.md)

## 贡献指南
1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request

## 许可证
MIT License
```

## 代码质量工具

### ESLint 配置
```javascript
// .eslintrc.js
module.exports = {
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended'
  ],
  rules: {
    // 强制使用中文注释
    'spaced-comment': ['error', 'always', {
      'line': { 'markers': ['/', '#', '//'] },
      'block': { 'markers': ['*', '!'], 'exceptions': ['*'] }
    }],
    
    // 函数复杂度限制
    'complexity': ['warn', 10],
    
    // 函数最大行数
    'max-lines-per-function': ['warn', 50],
    
    // 禁用 console（生产环境）
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    
    // 强制使用 === 
    'eqeqeq': 'error',
    
    // 禁用未使用变量
    '@typescript-eslint/no-unused-vars': 'error',
    
    // React 相关
    'react/prop-types': 'off', // 使用 TypeScript 替代
    'react-hooks/exhaustive-deps': 'warn'
  }
};
```

### Prettier 配置
```javascript
// .prettierrc.js
module.exports = {
  semi: true,
  singleQuote: true,
  tabWidth: 2,
  trailingComma: 'es5',
  printWidth: 80,
  bracketSpacing: true,
  arrowParens: 'avoid'
};
```

### 提交规范
```bash
# 提交消息格式
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建工具、辅助工具的变动

# 示例
feat: 添加订单状态筛选功能
fix: 修复用户地址选择bug
docs: 更新API文档
```