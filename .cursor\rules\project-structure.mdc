---
alwaysApply: true
description: 家电回收项目结构和架构指导
---

# 家电回收项目结构指导

## 项目概述
家电回收平台，用户可以通过平台进行家电回收，包含评估、回收、处理、销售、售后等完整流程。

## 项目架构
- `recycle/client/` - Taro.js + React.js 开发的微信小程序/H5（家电回收用户端）
- `recycle/admin/` - React.js + Ant Design + Vite + TypeScript + DVA 开发的后台管理系统
- `recycle/server/` - Node.js + Adonis.js + MySQL 开发的API服务

## 技术栈
- 前端：Taro.js, React.js, TypeScript, Ant Design, Vite
- 后端：Node.js, Adonis.js, MySQL
- 移动端：微信小程序，H5
- 认证：JWT
- 版本控制：Git

## 关键文件
- 服务端入口：[recycle/server/start/routes.js](mdc:recycle/server/start/routes.js)
- 管理后台入口：[recycle/admin/src/pages/App.tsx](mdc:recycle/admin/src/pages/App.tsx)
- 用户端配置：[recycle/client/src/app.config.js](mdc:recycle/client/src/app.config.js)
- 师傅端配置：类似用户端结构
- 数据模型：[recycle/admin/src/models/](mdc:recycle/admin/src/models/)
- API服务：[recycle/server/app/Controllers/](mdc:recycle/server/app/Controllers/)

## 开发规范
- 使用中文注释，保持代码清晰易懂
- 代码简洁，可复用性强，易维护
- 统一的错误处理和日志记录
- 响应式设计，移动端优先
- 基于冬瓜回收官网风格，采用绿色环保主题 (#15b381)

## 常用路径模式
- 页面组件：`/pages/*/index.jsx|tsx`
- 业务组件：`/components/*/index.jsx|tsx`
- 服务接口：`/services/*.js|tsx`
- 数据模型：`/models/*.js|ts`
- 样式文件：`*.less`
- 工具函数：`/utils/*.js`

## 调试和开发
- 使用 firecrawl-mcp 进行调试
- 自动解决调试过程中的错误、bug、性能和安全问题
- 严格遵循用户需求和游戏规则