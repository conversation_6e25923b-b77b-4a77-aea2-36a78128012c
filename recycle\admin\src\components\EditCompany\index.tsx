import React, { useCallback } from 'react'
import { Avatar, Input, Modal, notification, Select, Spin } from 'antd'
import { NUser, NCompany, RSetState, ECityAddress, EProvinceAddress, EGet } from '../../common/action'
import styles from './index.module.less'
import { effect, useConnect } from 'dva17'
import { Fragment, useState, useEffect } from 'react';
import {
  getCompanylist, getAddress, creatCompany,
  editCompany
} from '../../services/companyAPI'

const { Option } = Select

export default (props: any) => {
  let { visible, onChange, data } = props
  const { provinceList, cityList } = useConnect(NCompany)
  const [newCompany, setNewCompany] = useState<any>({})
  const [districtList, setDistrictList] = useState<any>([])
  const [districtCode, setdistrictCode] = useState<any>(null)
  const [provinceCode, setprovinceCode] = useState<any>(null)
  const [cityCode, setcityCode] = useState<any>(null)
  const [userName, setuserName] = useState<any>(null)
  const [password, setpassword] = useState<any>(null)
  const [repeatPassword, setrepeatPassword] = useState<any>('')
  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    if (visible) {
      effect(NCompany, EProvinceAddress, { code: '' })
      // effect(NCompany, ECityAddress, { code: 51 })
    }
    return () => {
    }
  }, [visible])
  useEffect(() => {
    if (data) {
      setNewCompany(data)
    } else {
      setNewCompany({})
    }
    return () => {
    }
  }, [data])

  const cancelCompanyCancel = () => {
    onChange({ visible: false, refresh: false })
    setNewCompany({})
  }
  const cancelCompanyOk = async () => {
    if (!newCompany.companyName) {
      openNotificationWithIcon('请输入服务商名称！')
      return
    }
    if (!newCompany.contactPerson) {
      openNotificationWithIcon('请输入联系人姓名！')
      return
    }
    if (!newCompany.mobile) {
      openNotificationWithIcon('请输入联系电话！')
      return
    }
    if (!newCompany.email) {
      openNotificationWithIcon('请输入邮箱！')
      return
    }
    if (!data && !userName) {
      openNotificationWithIcon('请输入登录账号！')
      return
    }
    if (!data && !password) {
      openNotificationWithIcon('请输入登录密码！')
      return
    }
    if (!data && !repeatPassword) {
      openNotificationWithIcon('请再次输入登录密码！')
      return
    }
    if (!data && password !== repeatPassword) {
      openNotificationWithIcon('两次密码输入不一样！')
      return
    }
    if (!newCompany.province) {
      openNotificationWithIcon('请输入所在省份！')
      return
    }
    if (!newCompany.city) {
      openNotificationWithIcon('请输入所在城市！')
      return
    }
    if (!newCompany.district) {
      openNotificationWithIcon('请输入所在区！')
      return
    }
    if (!newCompany.address) {
      openNotificationWithIcon('请输入详细地址！')
      return
    }
    if (data) {
      let vo = await editCompany({
        payload: {
          id: newCompany.id,
          addressCode: districtCode ? JSON.stringify([provinceCode, cityCode, districtCode]) : newCompany.addressCode,
          companyName: newCompany.companyName,
          mobile: newCompany.mobile,
          contactPerson: newCompany.contactPerson,
          email: newCompany.email,
          province: newCompany.province,
          city: newCompany.city,
          district: newCompany.district,
          address: newCompany.address
        }
      })
      notification.success({
        message: '成功！',
        description: '修改成功',
        duration: 2
      })
      onChange({ visible: false, refresh: true })
      setrepeatPassword('')
    } else {
      let data = await creatCompany({
        payload: {
          ...newCompany,
          addressCode: JSON.stringify([provinceCode, cityCode, districtCode]),
          userName,
          password
        }
      })
      notification.success({
        message: '成功！',
        description: '创建成功',
        duration: 2
      })
      onChange({ visible: false, refresh: true })
      setrepeatPassword('')
    }
  }

  const openNotificationWithIcon = (msg: any) => {
    notification.warning({
      message: '提示！',
      description: msg,
      duration: 2
    })
  }

  const changeValue = (vo: any) => {
    console.log(vo, { ...newCompany, ...vo }, "更新");
    setNewCompany({ ...newCompany, ...vo })
  }
  const changProvince = async (e: any) => {
    effect(NCompany, ECityAddress, { code: provinceList[e].code })
    setNewCompany({ ...newCompany, province: provinceList[e].name })
    setprovinceCode(provinceList[e].code)
  }
  const changCity = async (e: any) => {
    let res = await getAddress({ payload: { code: cityList[e].code } })
    setDistrictList(res)
    setNewCompany({ ...newCompany, city: cityList[e].name })
    setcityCode(cityList[e].code)
  }
  const changDistrit = async (e: any) => {
    setNewCompany({ ...newCompany, district: districtList[e].name })
    setdistrictCode(districtList[e].code)
  }

  /*--------------------- 响应 ---------------------*/
  /*--------------------- 渲染 ---------------------*/
  return (
    <Modal
      open={visible}
      onOk={() => {
        cancelCompanyOk()
      }}
      onCancel={() => {
        cancelCompanyCancel()
      }}
      width={600}
      okText={data ? '确认修改' : '确认新建'}
      cancelText={'退出'}>
      <h4 className={styles.modalTitle}>{data ? '编辑服务商' : '新增服务商'}</h4>
      <div className={styles.newCompany}>
        <div className={styles.newCompany_item}>
          <span className={styles.newCompany_item_title}>服务商名称：</span>
          <Input
            placeholder="请输入服务商名称"
            onChange={e => {
              console.log(e?.target?.value);
              changeValue({ companyName: e?.target?.value })
            }}
            style={{ width: 250 }}
            value={newCompany.companyName}
            type="text"
          />
        </div>
        <div className={styles.newCompany_item}>
          <span className={styles.newCompany_item_title}>联系人姓名：</span>
          <Input
            placeholder="请输入联系人姓名"
            onChange={e => {
              console.log(e?.target?.value);
              changeValue({ contactPerson: e?.target?.value })
            }}
            style={{ width: 250 }}
            value={newCompany.contactPerson}
            type="text"
          />
        </div>
        <div className={styles.newCompany_item}>
          <span className={styles.newCompany_item_title}>联系电话：</span>
          <Input
            placeholder="请输入联系电话"
            onChange={e => {
              console.log(e?.target?.value);
              changeValue({ mobile: e?.target?.value })
            }}
            style={{ width: 250 }}
            value={newCompany.mobile}
            maxLength={11}
            type="text"
          />
        </div>
        <div className={styles.newCompany_item}>
          <span className={styles.newCompany_item_title}>邮箱：</span>
          <Input
            placeholder="请输入邮箱"
            onChange={e => {
              console.log(e?.target?.value);
              changeValue({ email: e?.target?.value })
            }}
            style={{ width: 250, color: `${'#606266'}` }}
            value={newCompany.email}
            type="text"
          />
        </div>
        {!data ? (
          <Fragment>
            <div className={styles.newCompany_item}>
              <span className={styles.newCompany_item_title}>登录账号：</span>
              <Input
                placeholder="请输入登录账号"
                onChange={e => {
                  console.log(e?.target?.value);
                  setuserName(e?.target?.value)
                }}
                style={{ width: 250 }}
                value={userName}
                type="text"
              />
            </div>
            <div className={styles.newCompany_item}>
              <span className={styles.newCompany_item_title}>密码：</span>
              <Input
                placeholder="请输入登录密码"
                onChange={e => {
                  console.log(e?.target?.value);
                  setpassword(e?.target?.value)
                }}
                style={{ width: 250 }}
                value={password}
                type="password"
              />
            </div>
            <div className={styles.newCompany_item}>
              <span className={styles.newCompany_item_title}>确认密码：</span>
              <Input
                placeholder="请再次输入登录密码"
                onChange={e => {
                  console.log(e?.target?.value);
                  setrepeatPassword(e?.target?.value)
                }}
                style={{ width: 250 }}
                value={repeatPassword}
                type="password"
              />
            </div>
          </Fragment>
        ) : null}

        <div className={styles.newCompany_item}>
          <span className={styles.newCompany_item_title}>省：</span>
          <Select
            style={{ width: 250 }}
            value={newCompany.province}
            onChange={e => {
              console.log(e);
              changProvince(e)
            }}>
            {provinceList && provinceList.length > 0 ? provinceList.map((v: any, i: number) => <Option key={i}>{v.name}</Option>) : null}
          </Select>
        </div>
        <div className={styles.newCompany_item}>
          <span className={styles.newCompany_item_title}>市：</span>
          <Select
            style={{ width: 250 }}
            value={newCompany.city}
            onChange={e => {
              console.log(e);
              changCity(e)
            }}>
            {cityList && cityList.length > 0 ? cityList.map((v: any, i: number) => <Option key={i}>{v.name}</Option>) : null}
          </Select>
        </div>
        <div className={styles.newCompany_item}>
          <span className={styles.newCompany_item_title}>区：</span>
          <Select
            style={{ width: 250 }}
            value={newCompany.district}
            onChange={e => {
              console.log(e);
              changDistrit(e)
            }}>
            {districtList && districtList.length > 0 ? districtList.map((v: any, i: number) => <Option key={i}>{v.name}</Option>) : null}
          </Select>
        </div>
        <div className={styles.newCompany_item}>
          <span className={styles.newCompany_item_title}>详细地址：</span>
          <Input
            placeholder="请输入详细地址，具体到门牌号"
            onChange={e => {
              console.log(e?.target?.value);
              changeValue({ address: e?.target?.value })
            }}
            style={{ width: 400 }}
            value={newCompany?.address}
            type="text"
          />
        </div>
      </div>
    </Modal>
  )
}
