import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'

export async function getScoreList(payload:any) {
  return requestGet('/scoreRecord', payload)
}

export async function getScoreDetail(payload:any) {
  return requestGet(`/detailScore/${payload}`)
}

export async function changeStatus(payload:any) {
  return requestPut(`/updateStatus/${payload.id}`, {status: payload.status})
}

export async function changeItemStatus(payload:any) {
  return requestPut(`/updateItemStatus/${payload.id}/`, {status: payload.status, projectId: payload.projectId, refuse: payload.refuse})
}

export async function exportExcel(payload:any) {
  return requestGet(`/exportExcel`, payload)
}

export async function exportExcel2021(payload:any) {
  return requestGet(`/exportExcel2021`, payload)
}

export async function getScoreProjectList(payload:any) {
  return requestGet('/scoreProject', payload)
}

export async function getScoreProjectDetail(payload:any) {
  return requestGet(`/scoreProjectDetail/${payload}`)
}

