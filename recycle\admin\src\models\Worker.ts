import { requestGet } from 'dva17'
import {
  EAddWorkerAssistant,
  EDelete, EGet, EGetAddress, EGetAssistantOrders, EGetDetailWorker, EGetDiscoveryList,
  EGetFullTimeWorker, EGetList, EGetTownAddress, EGetTownList,
  EGetWorkerAssistants,
  EGetWorkerPaylist, EGetWorkerReview, EGetWorkerWalletlog, EPost,
  EPostDiscoveryList, EPostWorkerWallet, EPut, EPutDiscoveryList,
  EWorkerSelected, NWorker, RAdd, RSetState,
  EGetWorkerOrderData,
} from '../common/action'
import { adapterPaginationResult } from '../common/utils'
import {
  getWorkerReview,
  getFullTimeWorker,
  getDetailWorker,
  changeWorkerUse,
  addWorkAddress,
  removeWorkAddress,
  getTownList,
  getTheWorkerList,
  getWorkerPaylist,
  getWorkerArealist,
  postWorkerWallet,
  getWorkerWalletLog,
  postWorkerInsure,
  addWorkerAssistant,
  getWorkerAssistants,
  getAssistantOrders,
  getWorkerOrderData,
} from '../services/worker'
import { getDiscoveryList, editDiscovery, createDiscovery } from '../services/discovery'

import { EChangeWorkerUse, EAddWorkAddress, ERemoveWorkAddress, EGetTheWorkerList , EPostWorkerInsure} from '../common/action'

export default {
  namespace: NWorker,
  state: {
    workerReviewList: [],
    lastSearch: {},
    workerList: null,
    workerDetail: null,
    addressList: null,
    townAddressList: null,
    isGetDetail: false,
    isGetTown: false,
    isUseChange: false,
    townList: null,
    theWorkerList: null,
    workerPaylist: [],
    assistantList: [], // 小工列表
    assistantOrders: [], // 小工订单
  },
  reducers: {
    [RSetState](state: any, payload: any) {
      return { ...state, ...payload }
    },
    [RAdd](state: { count: any }, payload: any) {
      return { ...state, count: state.count + payload }
    },
  },
  effects: {
    //  标准CURD示例
    async [EGet]({ payload }: any, { reducer }: any) {
      let result = await requestGet('worker', { ...payload, isUse: '同意' })
      await reducer(RSetState, { workerList: result.data })
      return adapterPaginationResult(result)
    },
    // 充值
    async [EPostWorkerWallet]({ payload }: any, { reducer }: any) {
      const response = await postWorkerWallet(payload)
      await reducer(RSetState, { workerWallet: response })
    },
    //钱包消费记录
    async [EGetWorkerWalletlog]({ payload }: any, { reducer }: any) {
      const response = await getWorkerWalletLog(payload)
      reducer(RSetState, { workerPaylist: response })
      return adapterPaginationResult(response)
    },

    // 获取待审核人员
    async [EGetWorkerReview]({ payload }: any, { reducer }: any) {
      const response = await getWorkerReview(payload)
      reducer(RSetState, { workerReviewList: response })
      return response
    },
    // 获取已审核人员列表
    async [EGetFullTimeWorker]({ payload }: any, { reducer }: any) {
      const response = await getFullTimeWorker(payload)
      reducer(RSetState, { workerList: response })
    },
    //获取人员详情
    async [EGetDetailWorker]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isGetDetail: false })
      const response = await getDetailWorker(payload)
      reducer(RSetState, { isGetDetail: true, workerDetail: response })
      return response
    },
    //获取区地址
    async [EGetAddress]({ payload }: any, { reducer }: any) {
      let res = await requestGet('address', payload)
      reducer(RSetState, { addressList: res })
    },
    //获取街道地址
    async [EGetTownAddress]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isGetTown: false })
      let res = await requestGet('address', payload)
      reducer(RSetState, { townAddressList: res, isGetTown: true })
    },
    //worker信息更新
    async [EPut]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isUseChange: false })
      const response = await changeWorkerUse(payload)
      reducer(RSetState, { isUseChange: true })
    },
    async [EWorkerSelected]({ payload }: any, { reducer }: any) {
      const response = await getWorkerArealist(payload)
      reducer(RSetState, { theWorkerList: response })
      return adapterPaginationResult(response)
    },
    //添加工作区域
    async [EPost]({ payload }: any, { reducer }: any) {
      const response = await addWorkAddress(payload)
    },
    //删除工作区域
    async [EDelete]({ payload }: any, { reducer }: any) {
      const response = await removeWorkAddress(payload)
    },
    //街道列表
    async [EGetTownList]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { townList: null })
      const response = await getTownList(payload)
      reducer(RSetState, { townList: response })
    },
    //数据总览中，获取回收人员列表
    async [EGetList]({ payload }: any, { reducer }: any) {
      const response = await getTheWorkerList(payload)
      reducer(RSetState, { theWorkerList: response })
    },
    async [EPostWorkerInsure]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isUseChange: false })
      const response = await postWorkerInsure(payload)
      reducer(RSetState, { isUseChange: true })
    },
    async [EChangeWorkerUse]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isUseChange: false })
      const response = await changeWorkerUse(payload)
      reducer(RSetState, { isUseChange: true })
    },
    async [EAddWorkAddress]({ payload }: any, { reducer }: any) {
      const response = await addWorkAddress(payload)
    },

    async [EGetTheWorkerList]({ payload }: any, { reducer }: any) {
      const response = await getTheWorkerList(payload)
      reducer(RSetState, { theWorkerList: response })
    },
    //支付记录
    async [EGetWorkerPaylist]({ payload }: any, { reducer }: any) {
      const response = await getWorkerPaylist(payload)
      reducer(RSetState, { lastSearch: payload })
      reducer(RSetState, { workerPaylist: response })
      return adapterPaginationResult(response)
    },
    async [EGetDiscoveryList]({ payload }: any, { reducer }: any) {
      const response = await getDiscoveryList(payload)
      return adapterPaginationResult(response)
    },
    async [EPostDiscoveryList]({ payload }: any, { reducer }: any) {
      const response = await createDiscovery(payload)
    },
    async [EPutDiscoveryList]({ payload }: any, { reducer }: any) {
      const response = await editDiscovery(payload)
    },
      // 小工相关Effects
      async [EAddWorkerAssistant]({ payload }: any, { reducer }: any) {
        const response = await addWorkerAssistant(payload)
        return response
      },
  
      async [EGetWorkerAssistants]({ payload }: any, { reducer }: any) {
        const response = await getWorkerAssistants(payload)
        reducer(RSetState, { assistantList: response })
        return adapterPaginationResult(response)
      },
      async [EGetAssistantOrders]({ payload }: any, { reducer }: any) {
        const response = await getAssistantOrders(payload)
        reducer(RSetState, { assistantOrders: response })
        return adapterPaginationResult(response)
      },
      async [EGetWorkerOrderData]({ payload }: any, { reducer }: any) {
        const response = await getWorkerOrderData(payload)
        reducer(RSetState, { workerOrderData: response })
        return adapterPaginationResult(response)
      },
  },
}
