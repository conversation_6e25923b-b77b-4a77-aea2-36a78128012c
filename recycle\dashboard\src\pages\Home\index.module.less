.container {
  background-color: #0a0c0f;
  min-height: 100vh;
  overflow: auto;
  padding: 10px 15px;
  .head {
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    color: white;
    .title {
      width: 214px;
      object-fit: contain;
    }
  }

  .card {
    width: 100%;
    min-height: 106px;
    border-radius: 8px;
    background: #171923;
    margin-bottom: 12px;
    padding: 20px 16px;
    .headCount {
      display: flex;
      justify-content: space-between;
      width: 100%;
      .visitors {
        width: 33%;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 15px;
        height: 100px;
        position: relative;
        .num {
          font-size: 28px;
          font-weight: bold;
          background: linear-gradient(181deg, #ffffff 1%, #4dffff 193%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-fill-color: transparent;
          text-shadow: 0px 4px 9px 0px rgba(39, 51, 52, 0.57);
        }
        .visitors_title {
          margin-top: 5px;
          font-size: 12px;
          font-weight: 500;
          color: #def0ff;
          z-index: 9;
        }
        .icon {
          z-index: 1;
          bottom: 0;
          position: absolute;
          width: 68px;
          height: 49px;
        }
      }
    }
  }

  .card_title {
    font-size: 14px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0px 4px 7px 0px rgba(0, 0, 0, 0.26);
    padding-left: 12px;
    position: relative;
    margin-bottom: 12px;
    &::before {
      content: ' ';
      width: 3px;
      height: 12px;
      background: #74eec1;
      border-radius: 126px;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .columns_box {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    column-gap: 12px;
  }
  .card_bottom {
    margin-top: 12px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    .card_icon {
      width: 49px;
      height: 49px;
      margin-right: 16px;
    }
    .number_car {
      span {
        &:first-child {
          font-size: 24px;
          font-weight: bold;
          background: linear-gradient(180deg, #ffffff 2%, #48e5e5 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-fill-color: transparent;
          text-shadow: 0px 4px 7px 0px rgba(0, 0, 0, 0.26);
        }
      }
    }
    .number_police {
      span {
        &:first-child {
          font-size: 24px;
          font-weight: bold;
          line-height: 28px;
          background: linear-gradient(180deg, #ffffff 2%, #ffda61 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-fill-color: transparent;
          text-shadow: 0px 4px 7px 0px rgba(0, 0, 0, 0.26);
        }
      }
    }
    .unit {
      margin-left: 4px;
      font-size: 14px;
      font-weight: 500;
      line-height: 28px;
      color: rgba(255, 255, 255, 0.9);
      text-shadow: 0px 4px 7px 0px rgba(0, 0, 0, 0.26);
    }
  }
  .current_number {
    .current_number_title {
      font-size: 14px;
      font-weight: 500;
      color: #dbfdf1;
      text-shadow: 0px 4px 7px 0px rgba(0, 0, 0, 0.26);
      white-space: nowrap;
    }
    .people_num {
      margin-top: 8px;
      font-size: 24px;
      font-weight: bold;
      background: linear-gradient(180deg, #ffffff 2%, #48e5e5 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
      text-shadow: 0px 4px 7px 0px rgba(0, 0, 0, 0.26);
    }
  }
  .first_card {
    position: relative;
    overflow: hidden;
    .circular {
      width: 333px;
      height: 333px;
      position: absolute;
      right: -111px;
      top: -111px;
      z-index: 0;
    }
  }
  .car_list {
    .car_list_item {
      .list_item_header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        color: #ffffff;
        font-size: 12px;
        margin-top: 10px;
        margin-bottom: 6px;
        .gray_title {
          color: rgba(230, 247, 255, 0.5);
          zoom: 0.9;
          margin-left: 3px;
          display: inline-block;
        }
      }
      .list_item_speed {
        background: rgba(255, 255, 255, 0.1);
        height: 6px;
        .speed {
          height: 6px;
          background: linear-gradient(270deg, #18ffbe 0%, #1ee7e7 99%);
        }
      }
    }
  }
  .police_table {
    font-size: 12px;
    .table_body_contain {
      overflow: auto;
    }
    .table_body {
      color: #86fce2;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      .table_body_item {
        display: flex;
        flex: 1;
        padding: 8px 0;
        white-space: nowrap;

        &:first-child {
          min-width: 22%;
        }
      }
    }
    .table_body_content {
      color: #def0ff;
      .table_body_item {
        margin-right: 12px;
      }
    }
  }
  .statics_item {
    background: #08080d;
    min-height: 129px;
    border-radius: 4px;
    padding: 13px 15px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }
  .row_span {
    display: grid;
    grid-template-rows: repeat(3, 1fr);
    row-gap: 4px;
    .row_span_item {
      background: #08080d;
      border-radius: 4px;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      padding: 8px 12px;
      .iconStop {
        min-width: 23px;
        min-height: 23px;
        width: 23px;
        height: 23px;
      }
    }
  }
  .bold_title {
    font-size: 14px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0px 4px 7px 0px rgba(0, 0, 0, 0.26);
  }
  .statics_item_line {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: 500;
    .number {
      color: #00d1d1;
    }
    .number_active {
      color: #fcb763;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
  .right_content {
    margin-left: 6px;
    width: 100%;
    .right_content_item {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2px;
      &:last-child {
        margin-bottom: 0;
      }
      .number {
        color: #00ffff;
      }
    }
  }
}
