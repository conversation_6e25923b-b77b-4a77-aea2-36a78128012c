'use strict'

const Model = use('Model')

//订单
class JDOrder extends Model {
  static get table() {
    return 'jd_order'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return null
  }
  static get updatedAtColumn() {
    return null
  }
  worker() {
    return this.belongsTo('App/Models/JDWorker', 'workerID', 'id')
  }
  company() {
    return this.belongsTo('App/Models/Company', 'companyID', 'id')
  }
  sms() {
    return this.belongsTo('App/Models/LogMsg', 'id', 'orderID')
  }
}

module.exports = JDOrder
