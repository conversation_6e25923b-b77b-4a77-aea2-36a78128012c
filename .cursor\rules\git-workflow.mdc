---
description: Git 工作流规范
---

# Git 工作流规范

## 分支管理策略

### 分支命名规范
```
master        # 主分支，稳定版本发布
develop       # 开发分支，最新开发版本
feature/xxx   # 功能分支，用于开发新功能
bugfix/xxx    # 修复分支，用于修复非紧急 bug
hotfix/xxx    # 热修复分支，用于紧急 bug 修复
release/x.x.x # 发布分支，用于版本发布准备
```

### 分支工作流程
```mermaid
graph TB
  master((master)) --> hotfix(hotfix/xxx)
  master --> release(release/x.x.x)
  develop((develop)) --> feature(feature/xxx)
  develop --> bugfix(bugfix/xxx)
  hotfix --> master
  hotfix --> develop
  release --> master
  release --> develop
  feature --> develop
  bugfix --> develop
```

## 提交规范

### 提交消息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 提交类型说明
```
feat:     新功能
fix:      修复 bug
docs:     文档变更
style:    代码格式调整（不影响代码运行）
refactor: 代码重构（不新增功能或修复 bug）
perf:     性能优化
test:     添加或修改测试用例
chore:    构建过程或工具变动
```

### 提交示例
```bash
# 添加新功能
git commit -m "feat(order): 添加订单状态筛选功能"

# 修复 bug
git commit -m "fix(user): 修复用户地址无法保存的问题"

# 文档更新
git commit -m "docs(api): 更新订单 API 文档"

# 性能优化
git commit -m "perf(list): 优化列表渲染性能"
```

## 工作流程指南

### 功能开发流程
```bash
# 1. 确保本地 develop 分支最新
git checkout develop
git pull origin develop

# 2. 创建功能分支
git checkout -b feature/order-filter

# 3. 开发功能并提交
git add .
git commit -m "feat(order): 添加订单状态筛选功能"

# 4. 定期拉取 develop 分支最新代码并合并
git checkout develop
git pull origin develop
git checkout feature/order-filter
git merge develop

# 5. 功能开发完成，推送到远程
git push origin feature/order-filter

# 6. 创建 Pull Request 到 develop 分支
# (在 Git 平台上操作)

# 7. Code Review 通过后合并
# (在 Git 平台上操作)

# 8. 删除本地和远程功能分支
git branch -d feature/order-filter
git push origin --delete feature/order-filter
```

### Bug 修复流程
```bash
# 1. 创建修复分支
git checkout develop
git pull origin develop
git checkout -b bugfix/address-save

# 2. 修复 bug 并提交
git add .
git commit -m "fix(user): 修复用户地址无法保存的问题"

# 3. 推送到远程并创建 Pull Request
git push origin bugfix/address-save

# 4. Code Review 通过后合并
# (在 Git 平台上操作)
```

### 紧急修复流程
```bash
# 1. 从主分支创建热修复分支
git checkout master
git pull origin master
git checkout -b hotfix/critical-login-issue

# 2. 修复 bug 并提交
git add .
git commit -m "fix(auth): 修复严重登录问题"

# 3. 推送到远程
git push origin hotfix/critical-login-issue

# 4. 创建 Pull Request 到 master 分支
# (在 Git 平台上操作)

# 5. 合并到 master 后，同时合并到 develop
git checkout develop
git pull origin develop
git merge hotfix/critical-login-issue
git push origin develop
```

### 版本发布流程
```bash
# 1. 创建发布分支
git checkout develop
git pull origin develop
git checkout -b release/1.0.0

# 2. 版本准备工作（版本号更新、文档完善等）
git add .
git commit -m "chore(release): 准备发布 1.0.0 版本"

# 3. 测试和修复问题
git add .
git commit -m "fix(release): 修复发布前测试发现的问题"

# 4. 发布完成后，合并到 master
git checkout master
git pull origin master
git merge release/1.0.0
git tag -a v1.0.0 -m "版本 1.0.0 发布"
git push origin master --tags

# 5. 同时合并到 develop
git checkout develop
git pull origin develop
git merge release/1.0.0
git push origin develop
```

## 代码审查指南

### 审查清单
```markdown
## 功能性
- [ ] 代码是否符合需求
- [ ] 是否包含所有必要的测试
- [ ] 边界情况是否处理

## 代码质量
- [ ] 代码是否符合项目规范
- [ ] 变量和函数命名是否合理
- [ ] 是否有重复代码可以优化
- [ ] 注释是否清晰准确

## 性能和安全
- [ ] 是否有潜在的性能问题
- [ ] 是否有安全风险
- [ ] 错误处理是否完善

## 其他
- [ ] 文档是否更新
- [ ] 依赖管理是否合理
```

### 审查示例评论
```
- 这个函数看起来有点复杂，建议拆分成多个小函数
- 这里缺少边界条件处理，当输入为空时会出错
- 建议添加单元测试覆盖这个新功能
- 这个变量名不够清晰，建议改为更具描述性的名称
```

## 常用 Git 命令

### 基础操作
```bash
# 克隆仓库
git clone <repository-url>

# 查看状态
git status

# 添加文件
git add <file-path>
git add .  # 添加所有变更

# 提交
git commit -m "提交信息"

# 查看日志
git log
git log --oneline --graph  # 简洁图形化日志
```

### 分支操作
```bash
# 查看分支
git branch            # 本地分支
git branch -r         # 远程分支
git branch -a         # 所有分支

# 创建分支
git branch <branch-name>
git checkout -b <branch-name>  # 创建并切换

# 切换分支
git checkout <branch-name>
git switch <branch-name>  # Git 2.23+ 新命令

# 删除分支
git branch -d <branch-name>    # 安全删除
git branch -D <branch-name>    # 强制删除
```

### 合并和变基
```bash
# 合并
git merge <branch-name>

# 变基
git rebase <branch-name>

# 解决冲突后继续变基
git rebase --continue

# 中止变基
git rebase --abort
```

### 远程操作
```bash
# 拉取更新
git fetch             # 获取远程更新但不合并
git pull              # 获取并合并
git pull --rebase     # 获取并变基

# 推送
git push origin <branch-name>
git push -u origin <branch-name>  # 设置上游分支

# 删除远程分支
git push origin --delete <branch-name>
```

### 高级操作
```bash
# 暂存工作区
git stash
git stash save "工作进度描述"
git stash list
git stash pop        # 应用并删除暂存
git stash apply      # 应用但不删除

# 撤销提交
git reset --soft HEAD~1   # 撤销上一次提交，保留变更
git reset --hard HEAD~1   # 撤销上一次提交，丢弃变更

# 修改最后一次提交
git commit --amend

# 查找引入 bug 的提交
git bisect start
git bisect bad       # 当前版本有 bug
git bisect good <commit-id>  # 指定一个正常的版本
# Git 会自动二分查找，每次测试后标记
git bisect good      # 当前检出版本正常
git bisect bad       # 当前检出版本有 bug
git bisect reset     # 完成查找
```

## 冲突解决策略

### 预防冲突
```bash
# 定期拉取主分支变更
git checkout develop
git pull origin develop
git checkout feature/your-feature
git merge develop
```

### 解决合并冲突
```bash
# 使用工具解决冲突
git mergetool

# 手动编辑冲突文件
# 冲突区域会被标记为：
# <<<<<<< HEAD
# 当前分支的内容
# =======
# 被合并分支的内容
# >>>>>>> feature/branch

# 编辑后标记为已解决
git add <resolved-file>

# 继续合并过程
git merge --continue
```

### 解决变基冲突
```bash
# 变基过程中遇到冲突
# 手动解决冲突后
git add <resolved-file>
git rebase --continue

# 如果情况复杂，可以中止变基
git rebase --abort
```

## 最佳实践

### 提交建议
- 每个提交只做一件事情
- 提交前确保代码能正确运行
- 经常提交，避免大型提交
- 提交信息要清晰描述变更内容

### 分支管理建议
- 定期更新本地分支
- 功能分支生命周期不要太长
- 功能开发完成后及时删除分支
- 避免直接在主分支上工作

### 团队协作建议
- 提交前进行代码自查
- 创建有意义的 Pull Request
- 积极参与代码审查
- 避免强制推送到共享分支