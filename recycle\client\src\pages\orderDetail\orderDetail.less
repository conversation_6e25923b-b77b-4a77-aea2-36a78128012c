.orderDetail {
  width: 100%;
  min-height: 100vh;
  background: #f5f6f8;

  .orderHead {
    width: 100vw;
    padding: 32px 32px 54px;
    background: linear-gradient(90deg, rgb(151, 247, 146) 0%, rgb(18, 198, 84, 1) 100%);
    color: #fff;
    display: flex;

    .top_wrapper {
      flex: 1;
      margin-left: 32px;
      height: 90px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .manage_title {
        display: flex;
        justify-content: space-between;
        flex: 1;
        .title_right {
          font-size: 24px;
          text-decoration: underline;
        }
      }
      .work_time {
        color: #fff;
        font-size: 24px;
      }
      .devide {
        display: none;
      }
    }
  }
  .wrapperBox {
    transform: translateY(-30px);
    > .manage_title.format {
      color: rgba(18, 198, 84, 1);
      align-items: center;
      font-size: 28px;
      padding-left: 30px;
      display: flex;
      > view:first-of-type {
        height: 24px;
        width: 6px;
        background: rgba(18, 198, 84, 1);
        margin-right: 18px;
      }
    }
    .format_wrapper {
      border-radius: 15px;
      background: #f5f5f5;
      border-top: 6px solid #f5f6f8;
      width: 690px;
      margin: 0 30px 30px;

      > .top_wrapper {
        background-color: #fff;
        width: 100%;
        padding: 20px 48px;
        .manage_title {
          font-size: 28px;
          color: #464646;
          font-weight: 700;
          letter-spacing: 1px;
          position: relative;
          display: flex;
          justify-content: space-between;
          .title_left {
          }
          .title_right {
            margin-top: -5px;
            font-size: 24px;
            display: flex;
            justify-content: center;

            text-decoration: underline;
            font-weight: 400;
          }
        }
        .describe_content {
          margin-top: 24px;
          font-size: 26px;
          line-height: 32px;
          letter-spacing: 2px;
          word-break: break-all;
        }
        .work_time {
          margin-top: 24px;
          font-size: 24px;
          color: #556073;
        }
      }
    }
  }
}
.format {
  margin-bottom: 24px;
}
.information_wrapper {
  > Text {
    font-size: 32px;
    color: #333333;
    &:first-child {
      margin-right: 50px;
      font-weight: 600;
    }
  }
}
.kind_price {
  width: 100%;
  border-top: 2px solid #f3f3f3;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  > View {
    display: flex;
    justify-content: space-between;
    > Text {
      font-size: 30px;
      color: #666666;
      &:first-child {
        margin-left: 10px;
        margin-right: 30px;
      }
    }
  }
  > Text {
    font-size: 30px;
    color: #333333;
    margin-right: 20px;
  }
}
.image_wrapper {
  display: flex;
  flex-wrap: wrap;
  .image_item {
    width: 198px;
    height: 198px;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    margin-right: 10px;
    margin-left: 10px;
  }
}
.remark_wrapper {
  width: 100%;
  font-size: 24px;
  line-height: 36px;
  margin-top: 10px;
  color: #556073;
}
.recycler_wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .recycler {
    display: flex;
    align-items: center;
    .recycler_avatar {
      height: 100px;
      width: 100px;
      line-height: 100px;
      text-align: center;
      border-radius: 50%;
      margin-right: 50px;
      background: #f5f6f8;
      font-weight: 800;
      font-size: 48px;
      color: #333333;
    }
    .recycler_operate {
      Text {
        font-size: 28px;
        color: #333333;
        font-weight: 600;
      }
      View {
        font-size: 24px;
        color: #556073;
        margin-top: 16px;
      }
    }
  }
  .recycler_phone {
    display: flex;
    flex-direction: column;
    align-items: center;
    image {
      width: 40px;
      height: 40px;
      margin-bottom: 8px;
    }
    Text {
      color: #556073;
      font-size: 24px;
    }
  }
}
.close_button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 110px;
  font-size: 26px;
  font-weight: 400;
  > Text {
    color: #666666;
  }
  image {
    width: 32px;
    height: 32px;
  }
}
.total_price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100px;
  border-top: 2px solid #f3f3f3;
  Text {
    font-size: 30px;
    font-weight: 600;
    &:last-child {
      margin-right: 20px;
    }
  }
}
.how_are_you {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .pay_button {
    display: inline-block;
    padding: 5px 25px;
    font-weight: 600;
    border: 1px solid #15b381;
    color: #15b381;
  }
}
