import { RouteType } from './type';
import React from 'react';
import { Navigate, Route } from 'react-router-dom';
import Authorized from './Authorized';
import type { IAuthorityType } from './CheckPermissions';

type AuthorizedRouteProps = {
  routes: RouteType[];
  currentAuthority: string;
  component: React.ComponentClass<any, any>;
  render: (props: any) => React.ReactNode;
  redirectPath: string;
  authority: IAuthorityType;
};

const AuthorizedRoute: React.FunctionComponent<AuthorizedRouteProps> = ({
  routes,
  component: Component,
  render,
  authority,
  redirectPath,
  ...rest
}: any) => (
  <Authorized
    routes={routes}
    authority={authority}
    noMatch={
      <Route
        {...rest}
        element={() => <Navigate to={{ pathname: redirectPath }} />}
      />
    }
  >
    <Route
      {...rest}
      element={(props: any) =>
        Component ? <Component {...props} /> : render(props)
      }
    />
  </Authorized>
);

export default AuthorizedRoute;
