'use strict'

const Suite = use('Test/Suite')('Admin User')
const { before, beforeEach, after, afterEach, test, trait } = Suite
const { E } = require('../../../../constants')

trait('Test/ApiClient')

const Database = use('Database')

const UserAuthorization = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySUQiOjE5LCJpYXQiOjE1Nzk0OTQ3MTl9.kOM-6zJ0Fvg-jScKIuDBlQoL2d7njx4BxW2vO7E2f1Y'
let AdminAuthorization = null
const MasterAuthorization = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySUQiOjMsImlhdCI6MTU4MTA0OTM4MH0.W9_etereYwSvnC425j91fJ2uaaXqOz3DXKPZeBDsk4Y'
let orderID = null
let companyID = null

before(async () => {
  await Database.truncate('order')
  await Database.truncate('order_cancel')
  await Database.truncate('order_waste')
  console.log('before')
})

beforeEach(async () => {
  console.log('beforeEach')
  // executed before each test inside a given suite
})

test('后台管理员登录', async ({ client }) => {
  let res = await client.post('admin/v1/user/login').send({ username: '小张', password: '21' }).end()
  res.assertStatus(200)
  AdminAuthorization = `Bearer ${res.body.token}`
})

test('创建服务商',async ({client})=>{
  let addressCode = JSON.stringify(['3101'])
  let res = await client.post('admin/v1/company')
    .send({
      companyName: '单元测试',
      mobile: '110',
      contactPerson: 'linglinglu',
      email: '<EMAIL>',
      province: '上海市',
      city: '市辖区',
      district: '静安区',
      address: '50号',
      userName: 'unit',
      password: '21',
      addressCode
    })
    .header('Authorization', AdminAuthorization)
    .end()
  companyID = res.body.id
  res.assertStatus(200)
  res.assertJSONSubset({companyName: '单元测试'})
})

test('服务商登录',async ({client})=>{
  let res = await client.post('admin/v1/user/login').send({ username: 'unit', password: '21' }).end()
  res.assertStatus(200)
})

test('服务商修改',async  ({client})=>{
  let res = await client.put(`admin/v1/company/${companyID}`).send({companyName: 'unit test'}).header('Authorization', AdminAuthorization).end()
  res.assertJSONSubset({companyName: 'unit test'})
  let res2 = await client.put(`admin/v1/company/${companyID}`).send({password: '22'}).header('Authorization', AdminAuthorization).end()
  res.assertStatus(200)
})

test('服务商登录',async ({client})=>{
  let res = await client.post('admin/v1/user/login').send({username: 'unit',password: '22'}).end()
  res.assertStatus(200)
})


after(async () => {
  let company = await Database.table('company').where('companyName','unit test').first()
  console.log('company',company.companyName)
  // await company.delete()
  console.log('after', AdminAuthorization)
  // executed after all the tests for a given suite
})

afterEach(async () => {
  console.log('afterEach')
  // executed after each test inside a given suite
})
