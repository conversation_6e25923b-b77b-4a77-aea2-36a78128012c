/**
 * Created by <PERSON>(qq:24242811) on 2018/8/14.
 */
const moment = require('moment')
const _ = require('lodash')
const Env = use('Env')

const { ShopUserWallet, ShopLogScoreUser } = require('../Models')
const { ERR, E } = require('../../../../constants')
// const SettingService = require('./SettingService')

let levelRules = null

const UserWallet = {
  async addScore(userID, score, reason, reasonID, wallet = null, mobile) {
    if (userID == null) {
      wallet = await this.modifyScore(userID = null, score, reason, reasonID, wallet, mobile)
    } else {
      wallet = await this.modifyScore(userID, score, reason, reasonID, wallet, mobile)
      // await this.updateLevel(wallet)
    }
    return wallet
  },
  async modifyScore(userID, score, reason, reasonID, wallet = null, mobile) {
    if (userID == null) {
      await ShopLogScoreUser.create({
        userID: userID,
        mobile: mobile,
        reason: reason,
        reasonID: reasonID,
        score: score,
        prevScore: 0,
        nextScore: 0
      })
      return null
    } else {
      if (!wallet) {
        wallet = await ShopUserWallet.find(userID)
      }
      let prevScore = wallet.score
      wallet.score = Math.max(0, wallet.score + score)
      let nextScore = wallet.score
      if (score > 0 && reason !== E.ScoreReason.GainByOrderCancel) {
        wallet.levelScore += score
        wallet.dailyScore += score
      }
      await wallet.save()
      await ShopLogScoreUser.create({
        userID,
        mobile,
        reason,
        reasonID,
        score,
        prevScore,
        nextScore
      })
      return wallet
    }
  },

  async updateLevel(wallet) {
    // let newLevel = await SettingService.getNextLevel(wallet)

    // // 升级计算
    // if (newLevel) {
    //   wallet.levelID = newLevel.id
    //   wallet.levelAt = new Date()
    //   await wallet.save()
    // }
  }
}

module.exports = UserWallet
