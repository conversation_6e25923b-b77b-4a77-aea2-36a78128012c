@import '../../../styles/variables.less';

.electronics-page {
  background: @background-light;
  min-height: 100vh;
  width: 100%;
}

.content {
  min-height: 100vh;
  padding-bottom: @spacing-lg;
}

.category-section {
  padding: @spacing-lg;
}

.section-title {
  font-size: @font-xl;
  font-weight: 600;
  margin-bottom: @spacing-lg;
  display: flex;
  align-items: center;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -@spacing-xs;
    left: 0;
    width: 60rpx;
    height: 6rpx;
    border-radius: @radius-xs;
  }
}

.title-icon {
  margin-right: @spacing-sm;
  font-size: @font-lg;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: @spacing-md;
  margin-bottom: @spacing-xl;
}

.category-item {
  background: @background;
  border-radius: @radius-lg;
  padding: @spacing-lg;
  text-align: center;
  transition: all @transition-normal;
  box-shadow: @shadow-light;
  border: 2rpx solid transparent;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: @gradient-primary;
  }
  
  &:active {
    transform: scale(0.96);
  }
  
  &.selected {
    border-color: @primary-color;
    background: linear-gradient(135deg, @secondary-color, rgba(255, 255, 255, 0.9));
    box-shadow: @shadow-medium;
  }
}

.category-icon {
  margin-bottom: @spacing-sm;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120rpx;
  
  image {
    width: 100rpx;
    height: 100rpx;
    border-radius: @radius-md;
    object-fit: cover;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2rpx solid @border-color;
  }
}

.category-name {
  font-size: @font-md;
  font-weight: 600;
  color: @primary-color;
}

.form-section {
  background: @background;
  margin: 0 @spacing-lg @spacing-lg;
  border-radius: @radius-lg;
  padding: @spacing-lg;
  box-shadow: @shadow-light;
}

.form-group {
  margin-bottom: @spacing-lg;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: block;
  margin-bottom: @spacing-sm;
  font-weight: 600;
  color: @text-primary;
  font-size: @font-md;
}

.form-select {
  width: 100%;
  padding: @spacing-md @spacing-lg;
  border: 2rpx solid @border-color;
  border-radius: @radius-md;
  font-size: @font-md;
  background: @background;
  color: @text-primary;
  transition: all @transition-normal;
  
  &:focus {
    border-color: @primary-color;
    box-shadow: 0 0 0 4rpx rgba(21, 179, 129, 0.1);
  }
}

.condition-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: @spacing-sm;
}

.condition-item {
  padding: @spacing-md;
  text-align: center;
  border: 2rpx solid @border-color;
  border-radius: @radius-md;
  transition: all @transition-normal;
  background: @background;
  
  &:active {
    background: @secondary-color;
  }
  
  &.selected {
    border-color: @primary-color;
    background: @secondary-color;
    color: @primary-color;
    font-weight: 600;
    box-shadow: @shadow-light;
  }
}

.condition-desc {
  font-size: @font-xs;
  color: @text-light;
  margin-top: 4rpx;
}

// 图片上传区域样式
.image-upload-area {
  display: flex;
  flex-wrap: wrap;
  gap: @spacing-md;
  margin-bottom: @spacing-sm;
  padding: @spacing-md;
  background: rgba(240, 240, 240, 0.3);
  border-radius: @radius-lg;
  position: relative;
}

.uploaded-images {
  display: flex;
  flex-wrap: wrap;
  gap: @spacing-md;
  width: 100%;
}

.image-item {
  width: 160rpx;
  height: 160rpx;
  position: relative;
  border-radius: @radius-md;
  overflow: hidden;
  box-shadow: @shadow-light;
  transition: all 0.2s ease;
  border: 2rpx solid @border-color;
  
  &:active {
    transform: scale(0.95);
  }
  
  &::after {
    content: '';
    position: absolute;
    inset: 0;
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    border-radius: @radius-md;
    pointer-events: none;
  }
  
  &.uploading {
    border-color: @primary-color;
    animation: pulse 1.2s infinite;
  }
}

@keyframes pulse {
  0% { 
    box-shadow: 0 0 0 0 rgba(21, 179, 129, 0.4);
  }
  70% { 
    box-shadow: 0 0 0 6px rgba(21, 179, 129, 0);
  }
  100% { 
    box-shadow: 0 0 0 0 rgba(21, 179, 129, 0);
  }
}

.uploading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  
  &.failed {
    background-color: rgba(220, 0, 0, 0.5);
  }
}

.uploading-text {
  color: white;
  font-size: 24rpx;
  background: rgba(0, 0, 0, 0.6);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-icon {
  position: absolute;
  top: 0;
  right: 0;
  width: 44rpx;
  height: 44rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: @font-xl;
  line-height: 1;
  border-radius: 0 0 0 @radius-md;
  transition: all 0.2s ease;
  
  &:active {
    background: rgba(220, 0, 0, 0.8);
    transform: scale(1.1);
  }
}

.add-image-btn {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed @border-color;
  border-radius: @radius-md;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: @text-light;
  background-color: rgba(255, 255, 255, 0.5);
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.95);
    background-color: rgba(240, 240, 240, 0.8);
  }
}

.add-icon {
  font-size: 52rpx;
  line-height: 1;
  margin-bottom: @spacing-xs;
  color: @primary-color;
}

.add-text {
  font-size: @font-xs;
  color: @primary-color;
  font-weight: 500;
}

.image-tips {
  font-size: @font-xs;
  color: @text-light;
  margin-top: @spacing-sm;
  padding-left: @spacing-xs;
  position: relative;
  
  &::before {
    position: absolute;
    left: -4rpx;
    opacity: 0.7;
    font-size: @font-xs;
  }
}

.price-display {
  background: @gradient-primary;
  color: @text-white;
  padding: @spacing-xl;
  border-radius: @radius-lg;
  text-align: center;
  margin: @spacing-lg;
  position: relative;
  overflow: hidden;
  box-shadow: @shadow-medium;
  
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 2rpx, transparent 2rpx);
    background-size: 40rpx 40rpx;
    animation: sparkle 20s infinite linear;
  }
}

@keyframes sparkle {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.price-label {
  font-size: @font-md;
  opacity: 0.9;
  margin-bottom: @spacing-sm;
  position: relative;
  z-index: 1;
}

.price-value {
  font-size: 64rpx;
  font-weight: bold;
  margin-bottom: @spacing-sm;
  position: relative;
  z-index: 1;
}

.price-note {
  font-size: @font-sm;
  opacity: 0.8;
  position: relative;
  z-index: 1;
}

.next-btn {
  background: @gradient-primary;
  color: @text-white;
  padding: @spacing-lg;
  border: none;
  border-radius: 50rpx;
  width: calc(100% - @spacing-xl);
  margin: 0 @spacing-lg @spacing-lg;
  font-size: @font-lg;
  font-weight: 600;
  transition: all @transition-normal;
  box-shadow: @shadow-medium;
  
  &:active {
    transform: translateY(2rpx);
    box-shadow: @shadow-light;
  }
  
  &[disabled] {
    background: @background-gray;
    color: @text-light;
    transform: none;
    box-shadow: none;
  }
}

.tips {
  background: linear-gradient(135deg, @secondary-color, rgba(240, 249, 244, 0.8));
  margin: 0 @spacing-lg @spacing-lg;
  padding: @spacing-lg;
  border-radius: @radius-md;
  border-left: 8rpx solid @success-color;
  box-shadow: @shadow-light;
}

.tips-title {
  font-weight: 600;
  color: @primary-dark;
  margin-bottom: @spacing-sm;
  font-size: @font-md;
}

.tips-content {
  font-size: @font-sm;
  color: @text-secondary;
  line-height: 1.6;
  margin-bottom: @spacing-md;
}

.feature-tags {
  display: flex;
  gap: @spacing-sm;
  flex-wrap: wrap;
}

.feature-tag {
  background: @gradient-primary;
  color: @text-white;
  padding: @spacing-xs @spacing-md;
  border-radius: 30rpx;
  font-size: @font-xs;
  font-weight: 600;
  box-shadow: @shadow-light;
}

// 页面加载动画
.category-item,
.form-section,
.price-display,
.tips {
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 为不同元素添加延迟动画
.category-item:nth-child(1) { animation-delay: 0.1s; }
.category-item:nth-child(2) { animation-delay: 0.15s; }
.category-item:nth-child(3) { animation-delay: 0.2s; }
.category-item:nth-child(4) { animation-delay: 0.25s; }
.category-item:nth-child(5) { animation-delay: 0.3s; }
.category-item:nth-child(6) { animation-delay: 0.35s; }

.form-section { animation-delay: 0.4s; }
.price-display { animation-delay: 0.5s; }
.tips { animation-delay: 0.6s; }