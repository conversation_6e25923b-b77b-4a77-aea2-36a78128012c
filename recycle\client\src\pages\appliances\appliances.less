.appliances {
  width: 100vw;
  height: 100vh;
  display: flex;
  background: #f5f6f8;

  .left_wrapper {
    width: 20vw;
    height: 100%;
    background: #ffffff;

    .value_item {
      width: 100%;
      height: 94px;
      line-height: 94px;
      text-align: center;
      font-size: 28px;
      font-weight: 700;
      position: relative;
      color: rgba(0, 0, 0, 0.6);

      .mark {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        // height: 85%;
        height: 40px;
        width: 8px;
        border-radius: 4px;
        // background: #15b381;
        background: linear-gradient(to bottom, rgba(119, 220, 143, 1) 0% , rgba(21, 179, 129, 1) 100%);
      }
    }

    .value_selected {
      color: rgba(0, 0, 0);
    }
  }

  .right_wrapper {
    flex: 1;

    .top_title {
      padding-top: 24px;
      padding-right: 32px;
      width: 100%;
      text-align: right;
    }

    .item_wrapper {
      display: flex;
      flex-wrap: wrap;
      padding-top: 40px;

      .item {
        width: 33.3%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 40px;

        .image_wrapper {
          width: 140px;
          height: 110px;
          display: flex;
          justify-content: center;
          align-items: center;
          overflow: hidden;

          >image {
            height: 100%;
            background-size: contain;
          }
        }
      }

      Text {
        font-size: 24px;
        margin-top: 20px;
      }
    }
  }
}

.remind_wrapper {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  letter-spacing: 1.2px;

  .message_wrapper {
    height: 896px;
    width: 600px;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .remindTitle {
      font-size: 30px;
      color: #333333;
      font-weight: 700;
      margin-top: 34px;
      margin-bottom: 50px;
      display: inline-block;
    }

    .content_wrapper {
      width: 500px;
      height: 520px;
      overflow-y: auto;
      display: flex;
      flex-direction: column;

      .remind_text {
        font-size: 24px;
        color: #7c8696;
        line-height: 36px;
        margin-bottom: 30px;
      }

      .remind_title {
        font-size: 28px;
        color: #444444;
        margin-bottom: 20px;
      }
    }

    .know_button_wrapper {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 40px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .know_button {
        height: 88px;
        width: 500px;
        background: linear-gradient(to right, rgba(119, 220, 143, 1) 0% , rgba(21, 179, 129, 1) 100%);
        line-height: 88px;
        text-align: center;
        border-radius: 44px;
        color: #ffffff;
        font-size: 30px;
        font-weight: 700;
      }

      .radio {
        font-size: 20px;
        color: #7c8696;
        margin-bottom: 20px;

        //    未选中的 背景样式
        .wx-radio-input {
          width: 20px;
          height: 20px;
          margin-right: 16px;
        }

        // 选中后的 背景样式 （红色背景 无边框 可根据UI需求自己修改）
        .wx-radio-input.wx-radio-input-checked {
          // border-color: #15b381 !important;
          background: linear-gradient(to right, rgba(119, 220, 143, 1) 0% , rgba(21, 179, 129, 1) 100%);
        }

        .wx-radio-input.wx-radio-input-checked::before {
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          font-size: 15px;
          color: #fff;
          background: transparent;
          transform: translate(-50%, -50%) scale(1);
          -webkit-transform: translate(-50%, -50%) scale(1);
        }
      }
    }
  }
}