/**
 * Created by <PERSON>(qq:24242811) on 2019/1/10.
 */
const Route = use('Route')
const Env = use('Env')

// 无需登录
Route.group(() => {
  Route.post('worker/login', 'WorkerController.login')
  Route.post('worker/register', 'WorkerController.register')
  Route.post('worker/loginBypasswd', 'WorkerController.loginBypasswd')
  Route.resource('debug', 'DebugController') //调试
  Route.any('hook/pay', 'PayController.hookWechatPay') //微信支付回调
  Route.any('hook/orderPay', 'PayController.hookOrderWechatPay') //微信支付回调
  Route.any('hook/refund', 'PayController.hookRefund')
  Route.post('checkCode', 'TrackingController.check')
  Route.post('postCode', 'TrackingController.store')
  Route.get('worker', 'WorkerController.index') //上门师傅信息
})
  .prefix('master/v1')
  .namespace('Master')
// 需要登录
Route.group(() => {
  Route.resource('waste1stCategory', 'Waste1stCategoryController') //废品一级分类
  Route.put('worker/:id', 'WorkerController.update') //上门师傅信息
  Route.post('worker', 'WorkerController.store') //上门师傅信息
  Route.get('worker/:id', 'WorkerController.show') //上门师傅信息
  Route.resource('orderWaste', 'OrderWasteController') //订单废品关联表
  Route.resource('waste2ndCategory', 'Waste2ndCategoryController') //废品二级分类
  Route.resource('waste', 'WasteController') //废品
  Route.resource('orderRating', 'OrderRatingController') //订单评价
  Route.resource('company', 'CompanyController') //公司信息
  Route.post('workerPay', 'PayController.workerPay') //师傅支付
  Route.get('walletlog', 'PayController.walletlog') //walletlog  
  Route.post('subMessage', 'WorkerController.getMessage') //订阅消息  
  // Route.resource('pay', 'PayController') //支付
  Route.get('chargePrice', 'ChargePriceController.index')
  Route.post('webPay', 'PayController.webPay') //web端充值

  Route.resource('order', 'OrderController').middleware(new Map([[['store'], ['postLock']]])) //订单
  Route.post('order/chart', 'OrderController.chart') //获取饼状图信息
  Route.post('order/orderBack', 'OrderController.orderBack') //订单撤回
  Route.post('comfirmOrder', 'OrderController.comfirmOrder') //订单确认
  Route.get('orderPrice', 'OrderController.orderPrice')
  Route.get('revenue', 'OrderController.getRevenue') //营收  
  Route.get('manager/order', 'OrderController.managerOrder') //订单管理  
  Route.post('manager/orderChange', 'OrderController.orderChange') //订单管理  
  Route.post('orderRemark', 'OrderController.orderRemark')
  Route.post('orderCall', 'OrderController.orderCall')
  Route.get('orderLog/:id', 'OrderController.getOrderLog') // 订单记录

  Route.resource('clientOrder', 'ClientOrderController').middleware(new Map([[['store'], ['postLock']]])) //订单
  Route.post('clientOrder/chart', 'ClientOrderController.chart') //获取饼状图信息
  Route.post('clientOrder/orderBack', 'ClientOrderController.orderBack') //订单撤回
  Route.post('clientOrder/comfirmOrder', 'ClientOrderController.comfirmOrder') //订单确认
  Route.get('clientOrder/orderPrice', 'ClientOrderController.orderPrice')
  Route.get('clientOrder/revenue', 'ClientOrderController.getRevenue') //营收  
  Route.get('clientOrder/manager/order', 'ClientOrderController.managerOrder') //订单管理  
  Route.post('clientOrder/manager/orderChange', 'ClientOrderController.orderChange') //订单管理  
  Route.post('clientOrder/orderRemark', 'ClientOrderController.orderRemark')
  Route.post('clientOrder/orderCall', 'ClientOrderController.orderCall')
  Route.get('clientOrder/orderLog/:id', 'ClientOrderController.getOrderLog') // 订单记录
  Route.get('clientOrderLog/:id', 'ClientOrderController.getOrderLog') // 订单记录
  Route.resource('jdOrder', 'JdOrderController').middleware(new Map([[['store'], ['postLock']]])) //订单
})
  .prefix('master/v1')
  .namespace('Master')
  .middleware('masterAuth')

// 文件上传接口
Route.group(() => {
  Route.post('file', 'FileController.file')
  Route.post('sms', 'SendSmsController.smsSend') //短信提醒
  Route.resource('address', 'AddressController') //获取街道
}).prefix('master/v1')

// 菜鸟回收  relaese
Route.group(() => {
  Route.post('create/order', 'DebugController.createCNOrder')//下单
  Route.post('update/order', 'DebugController.updateCNOrder')//更新
}).prefix('proxy/v1')

// 菜鸟回收  debug
Route.group(() => {
  Route.post('create/order', 'DebugController.createCNOrder')//下单
  Route.post('update/order', 'DebugController.updateCNOrder')//更新
}).prefix('debug/v1')