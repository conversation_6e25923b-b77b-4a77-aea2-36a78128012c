'use strict'

const _ = require('lodash')
const moment = require('moment')
const {
  JDOrder,
} = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//订单
class JdOrderController {
  async index({ request, response }) {
    let { page = 1, perPage = 20, workDate, status, orderNo, sort = 'desc', date, keyword } = request.all()
    let workerID = request.worker && request.worker.id
    workDate = parseInt(workDate)
    if (!workerID) {
      return ERR.USER_NOT_EXISTS
    }
    let query = JDOrder.query().where('workerID', workerID)
    if (status) {
      if (status === E.OrderStatus.Pending) {
        query.where('status', E.OrderStatus.Reservation)
      } else if (status === E.OrderStatus.Dispatched) {
        throw ERR.INVALID_PARAMS
      } else if (status === E.OrderStatus.TransferOrder) {
        throw ERR.INVALID_PARAMS
      } else {
        query.where('status', status)
      }
    } else if (orderNo) {
      query.where('orderNo', orderNo)
    } else {
      query.orderBy('id', 'desc')
    }
    if (keyword) {
      query.whereRaw('keywords like ?', [`%${keyword}%`])
    }
    if (date) {
      query.whereBetween('workTime', [moment(date).format('YYYY-MM-DD'), moment(date).add(1, 'days').format('YYYY-MM-DD')])
    }
    if (status === E.OrderStatus.InProgress) {
      switch (workDate) {
        case 0:
          query.where('workTime ', '<=', moment().add(1, 'days').format('YYYY-MM-DD'))
          break;
        case 1:
          query.whereBetween('workTime', [moment().add(1, 'days').format('YYYY-MM-DD'), moment().add(2, 'days').format('YYYY-MM-DD')])
          break;
        case 2:
          query.whereBetween('workTime', [moment().add(2, 'days').format('YYYY-MM-DD'), moment().add(60, 'days').format('YYYY-MM-DD')])
          break;
        default:
          break;
      }
      query.orderBy('workTime', 'asc')
    } else if (status === E.OrderStatus.Completed) {
      query.orderBy('finishedAt', 'desc')
    } else {
      query.orderBy('id', sort)
    }
    let vo = await query.with('worker')
      .paginate(page, perPage)
    vo.rows = vo.rows.map(item => {
      item.orderNo = item.orderNo
      item.type = item.type
      item.infoFee = item.infoFee
      item.address = item.address
      return item
    })
    response.json(vo)
  }

  async show({ request, params, response }) {
    let { worker } = request
    let { companyID, type } = worker
    let query = JDOrder.query().where('id', params.id)
    if (!query) {
      throw ERR.RESTFUL_GET_ID
    }
    let vo = await query
      .with('worker')
      .first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    vo = vo.toJSON()
    vo.wasteInfo = {}
    vo.wasteInfo.id = 0
    vo.wasteInfo.subType = vo.model
    response.json(vo)
  }

}

module.exports = JdOrderController
