'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Waste1stCategory } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//废品一级分类
class Waste1stCategoryController {
  async index({ request, response }) {
    let { page = 1, perPage = 10 } = request.all()
    let query = Waste1stCategory.query()
    let vo = await query.paginate(page, perPage)
    response.json(vo)
  }
  async show({ request, params, response }) {
    let vo = await Waste1stCategory.query()
      .where('id', params.id)
      .first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
}

module.exports = Waste1stCategoryController
