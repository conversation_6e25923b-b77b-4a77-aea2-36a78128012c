'use strict'

const Model = use('Model')

/**
 * 嗨回收价格配置模型
 * 管理嗨回收平台的各种价格设置
 */
class HiPrice extends Model {
  static get table() {
    return 'hi_price'
  }
  
  static get primaryKey() {
    return 'id'
  }
  
  static get createdAtColumn() {
    return 'createdAt'
  }
  
  static get updatedAtColumn() {
    return 'updatedAt'
  }

  /**
   * 关联公司信息
   */
  company() {
    return this.belongsTo('App/Models/Company', 'companyID', 'id')
  }

  /**
   * 获取价格类型文本
   */
  static getTypeText(type) {
    const typeMap = {
      '1': '基础回收价',
      '2': '运费价格',
      '3': '服务费价格',
      '4': '上门费价格',
      '5': '特殊处理费'
    }
    return typeMap[type] || '其他'
  }

  /**
   * 获取适用区域类型文本
   */
  static getAreaTypeText(areaType) {
    const areaTypeMap = {
      '1': '全国通用',
      '2': '省级配置',
      '3': '市级配置',
      '4': '区县配置'
    }
    return areaTypeMap[areaType] || '未知'
  }

  /**
   * 获取状态文本
   */
  static getStatusText(status) {
    const statusMap = {
      '0': '禁用',
      '1': '启用'
    }
    return statusMap[status] || '未知'
  }

  /**
   * 根据区域和商品类型获取价格
   */
  static async getPriceByArea(goodsType, province = '', city = '', district = '') {
    let query = this.query()
      .where('goodsType', goodsType)
      .where('status', '1')

    // 优先级：区县 > 市级 > 省级 > 全国
    if (district) {
      query.where('area', district).where('areaType', '4')
    } else if (city) {
      query.where('area', city).where('areaType', '3')
    } else if (province) {
      query.where('area', province).where('areaType', '2')
    } else {
      query.where('areaType', '1')
    }

    const price = await query.orderBy('areaType', 'desc').first()
    return price ? price.price : 0
  }
}

module.exports = HiPrice 