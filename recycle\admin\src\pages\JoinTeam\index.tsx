import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons'
import { Button, message, Modal, Form, Input, Select, Space, Popconfirm, Drawer, Descriptions, Image, Tag } from 'antd'
import type { ActionType, ProColumns } from '@ant-design/pro-table'
import ProTable from '@ant-design/pro-table'
import ProCard from '@ant-design/pro-card'
import { effect, useConnect } from 'dva17'
import { useEffect, useRef, useState } from 'react'
import { 
  EGetJoinTeamList, 
  ECreateJoinTeam, 
  EUpdateJoinTeam, 
  EDeleteJoinTeam, 
  NJoinTeam 
} from '../../common/action'
import { computeAuthority } from '../../utils/Authorized/authority'
import { TEAM_TYPE, CAR_TYPE } from '../../constants'
import dayjs from 'dayjs'
import './index.less'

const { Option } = Select

type JoinTeamItem = {
  id: number
  name: string
  company: string
  phone: string
  forbidden: number
  createdAt: string
  updatedAt?: string
  address: string
  carType: string[]
  cert: Array<{ url: string }>
  masterNum: string
  teamType: string
}

export default () => {
  const actionRef = useRef<ActionType>()
  const [form] = Form.useForm()
  const [visible, setVisible] = useState<boolean>(false)
  const [detailVisible, setDetailVisible] = useState<boolean>(false)
  const [editingRecord, setEditingRecord] = useState<JoinTeamItem | null>(null)
  const [detailRecord, setDetailRecord] = useState<JoinTeamItem | null>(null)
  const { joinTeamList, isLoading } = useConnect(NJoinTeam)

  /*--------------------- 列定义 ---------------------*/
  const columns: ProColumns<JoinTeamItem>[] = [

    {
      title: '姓名',
      dataIndex: 'name',
      copyable: true,
      ellipsis: true,
    },
    {
      title: '公司',
      dataIndex: 'company',
      copyable: true,
      ellipsis: true,
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      copyable: true,
      ellipsis: true,
    },
    {
      title: '地址',
      dataIndex: 'address',
      copyable: true,
      ellipsis: true,
    },
    {
      title: '师傅数量',
      dataIndex: 'masterNum',
      search: false,
      render: (_, record) => <span>{record.masterNum || '-'}</span>,
    },
    {
      title: '团队类型',
      dataIndex: 'teamType',
      valueType: 'select',
      valueEnum: {
        person: { text: TEAM_TYPE.person },
        team: { text: TEAM_TYPE.team },
      },
    },
    {
      title: '车辆类型',
      dataIndex: 'carType',
      search: false,
      render: (_, record) => (
        <Space>
          {record.carType && record.carType.length > 0 ? (
            record.carType.map((type, index) => {
              const carTypeItem = CAR_TYPE.find(item => item.value === type)
              return (
                <Tag key={index} color="blue">
                  {carTypeItem?.icon} {carTypeItem?.label || type}
                </Tag>
              )
            })
          ) : (
            <span>-</span>
          )}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'forbidden',
      valueType: 'select',
      valueEnum: {
        0: { text: '正常', status: 'Success' },
        1: { text: '禁用', status: 'Error' },
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      search: false,
      render: (_, record) => (
        <span>{record.createdAt ? dayjs(record.createdAt).format('YYYY-MM-DD HH:mm:ss') : '-'}</span>
      ),
    },
 
    {
      title: '操作',
      search: false,
      width: "20%",
      render: (_, record) => (
        <Space>
          <Button
            type="default"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleDetail(record)}
          >
            查看详情
          </Button>
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="primary"
              danger
              size="small"
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  /*--------------------- 事件处理 ---------------------*/
  const handleAdd = () => {
    setEditingRecord(null)
    setVisible(true)
    form.resetFields()
  }

  const handleDetail = (record: JoinTeamItem) => {
    setDetailRecord(record)
    setDetailVisible(true)
  }

  const handleEdit = (record: JoinTeamItem) => {
    setEditingRecord(record)
    setVisible(true)
    form.setFieldsValue({
      name: record.name,
      company: record.company,
      phone: record.phone,
      address: record.address,
      masterNum: record.masterNum,
      teamType: record.teamType,
      carType: record.carType,
      forbidden: record.forbidden,
    })
  }

  const handleDelete = async (record: JoinTeamItem) => {
    try {
      await effect(NJoinTeam, EDeleteJoinTeam, { id: record.id })
      message.success('删除成功')
      refreshTable()
    } catch (error) {
      message.error('删除失败')
    }
  }

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      
      if (editingRecord) {
        // 更新
        await effect(NJoinTeam, EUpdateJoinTeam, { ...values, id: editingRecord.id })
        message.success('更新成功')
      } else {
        // 新增
        await effect(NJoinTeam, ECreateJoinTeam, values)
        message.success('创建成功')
      }
      
      setVisible(false)
      form.resetFields()
      refreshTable()
    } catch (error) {
      message.error(editingRecord ? '更新失败' : '创建失败')
    }
  }

  const refreshTable = () => {
    if (actionRef.current) {
      actionRef.current.reload()
    }
  }

  /*--------------------- 渲染 ---------------------*/
  return (
    <ProCard className="joinTeam-container">
      <ProTable<JoinTeamItem>
        actionRef={actionRef}
        columns={columns}
        request={async (params = {}) => {
          const { current = 1, pageSize = 10, ...searchParams } = params
          return (await effect(NJoinTeam, EGetJoinTeamList, {
            current,
            pageSize,
            ...searchParams,
          })) as any
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        rowKey="id"
        dateFormatter="string"
        headerTitle="加入团队管理"
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={handleAdd}
            icon={<PlusOutlined />}
          >
            新增
          </Button>,
        ]}
        search={{
          labelWidth: 'auto',
        }}
      />

      {/* 新增/编辑弹窗 */}
      <Modal
        title={editingRecord ? '编辑加入团队' : '新增加入团队'}
        open={visible}
        onCancel={() => {
          setVisible(false)
          form.resetFields()
        }}
        onOk={handleSubmit}
        confirmLoading={isLoading}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            forbidden: 0,
            teamType: 'team',
          }}
        >
          <Form.Item
            name="name"
            label="姓名"
            rules={[
              { required: true, message: '请输入姓名' },
              { max: 50, message: '姓名不能超过50个字符' },
            ]}
          >
            <Input placeholder="请输入姓名" />
          </Form.Item>

          <Form.Item
            name="company"
            label="公司"
            rules={[
              { max: 100, message: '公司名称不能超过100个字符' },
            ]}
          >
            <Input placeholder="请输入公司名称" />
          </Form.Item>

          <Form.Item
            name="phone"
            label="联系电话"
            rules={[
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' },
            ]}
          >
            <Input placeholder="请输入联系电话" />
          </Form.Item>

          <Form.Item
            name="address"
            label="地址"
            rules={[
              { max: 200, message: '地址不能超过200个字符' },
            ]}
          >
            <Input placeholder="请输入地址" />
          </Form.Item>

          <Form.Item
            name="masterNum"
            label="师傅数量"
            rules={[
              { pattern: /^\d+$/, message: '请输入正确的数字' },
            ]}
          >
            <Input placeholder="请输入师傅数量" />
          </Form.Item>

          <Form.Item
            name="teamType"
            label="团队类型"
            rules={[{ required: true, message: '请选择团队类型' }]}
          >
            <Select placeholder="请选择团队类型">
              {Object.entries(TEAM_TYPE).map(([key, value]) => (
                <Option key={key} value={key}>
                  {value}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="carType"
            label="车辆类型"
          >
            <Select
              mode="multiple"
              placeholder="请选择车辆类型"
              allowClear
            >
              {CAR_TYPE.map((item) => (
                <Option key={item.value} value={item.value}>
                  {item.icon} {item.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="forbidden"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select placeholder="请选择状态">
              <Option value={0}>正常</Option>
              <Option value={1}>禁用</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 查看详情抽屉 */}
      <Drawer
        title="查看详情"
        placement="right"
        onClose={() => setDetailVisible(false)}
        open={detailVisible}
        width={600}
      >
        {detailRecord && (
          <Descriptions bordered column={1}>
            <Descriptions.Item label="序号">
              {detailRecord.id}
            </Descriptions.Item>
            <Descriptions.Item label="姓名">
              {detailRecord.name}
            </Descriptions.Item>
            <Descriptions.Item label="公司">
              {detailRecord.company}
            </Descriptions.Item>
            <Descriptions.Item label="联系电话">
              {detailRecord.phone}
            </Descriptions.Item>
            <Descriptions.Item label="地址">
              {detailRecord.address}
            </Descriptions.Item>
            <Descriptions.Item label="师傅数量">
              {detailRecord.masterNum || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="团队类型">
              {TEAM_TYPE[detailRecord.teamType as keyof typeof TEAM_TYPE] || detailRecord.teamType}
            </Descriptions.Item>
            <Descriptions.Item label="车辆类型">
              {detailRecord.carType && detailRecord.carType.length > 0 ? (
                <Space>
                  {detailRecord.carType.map((type, index) => {
                    const carTypeItem = CAR_TYPE.find(item => item.value === type)
                    return (
                      <Tag key={index} color="blue">
                        {carTypeItem?.icon} {carTypeItem?.label || type}
                      </Tag>
                    )
                  })}
                </Space>
              ) : (
                '-'
              )}
            </Descriptions.Item>
            <Descriptions.Item label="证书/证件">
              {detailRecord.cert && detailRecord.cert.length > 0 ? (
                <Space>
                  {detailRecord.cert.map((item, index) => (
                    <Image
                      key={index}
                      width={100}
                      src={item.url}
                      placeholder="loading..."
                      fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1xkE8C+8zz+b9+b9tb8J"
                    />
                  ))}
                </Space>
              ) : (
                '-'
              )}
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              <Tag color={detailRecord.forbidden === 0 ? 'green' : 'red'}>
                {detailRecord.forbidden === 0 ? '正常' : '禁用'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {detailRecord.createdAt ? dayjs(detailRecord.createdAt).format('YYYY-MM-DD HH:mm:ss') : '-'}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Drawer>
    </ProCard>
  )
} 