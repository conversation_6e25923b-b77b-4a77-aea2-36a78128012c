---
globs: *.js,*.jsx,*.ts,*.tsx,*.less
description: <PERSON><PERSON> + React 小程序开发规范（用户端和师傅端）
---

# Taro + React 小程序开发规范

## 适用范围
- `recycle/client/` - 用户端小程序/H5
- `recycle/master/` - 师傅端小程序

## Taro 开发规范
- 遵循 Taro 官方开发规范和最佳实践
- 使用 React Hooks 进行状态管理
- 统一使用 Taro.xxx API 访问小程序能力
- 页面配置使用 `*.config.js` 文件

## 页面和组件结构
- 页面入口：`pages/*/index.jsx`
- 组件文件：`components/*/index.jsx`
- 样式文件：使用 `.less` 文件
- 配置文件：`app.config.js`, `*.config.js`

## 小程序特定功能
- 导航：使用 `Taro.navigateTo`, `Taro.redirectTo`
- 存储：使用 `Taro.setStorageSync`, `Taro.getStorageSync`
- 网络请求：统一封装 `Taro.request`
- 用户授权：处理微信登录和授权流程
- 地理位置：集成地图选择和定位功能

## 页面生命周期
```javascript
import { useEffect } from 'react';
import Taro, { useDidShow, useDidHide } from '@tarojs/taro';

function HomePage() {
  // 页面显示时触发
  useDidShow(() => {
    console.log('页面显示');
  });

  // 页面隐藏时触发
  useDidHide(() => {
    console.log('页面隐藏');
  });

  return <View>页面内容</View>;
}
```

## 样式开发
- 使用 rpx 单位适配不同屏幕
- 遵循移动端设计规范
- 基于冬瓜回收风格，使用绿色环保主题
- 主色调：#15b381

## 数据管理
- 页面状态使用 useState
- 全局状态使用 Taro.getGlobalData
- API 调用封装在 services 目录
- 模型定义在 models 目录

## 关键配置文件
- 应用配置：[recycle/client/src/app.config.js](mdc:recycle/client/src/app.config.js)
- 构建配置：[recycle/client/config/index.js](mdc:recycle/client/config/index.js)
- 包配置：[recycle/client/package.json](mdc:recycle/client/package.json)

## 常用组件引用
```javascript
import { View, Text, Button, Image } from '@tarojs/components';
import Taro from '@tarojs/taro';
```

## 网络请求示例
```javascript
import Taro from '@tarojs/taro';

export const request = (url, options = {}) => {
  return Taro.request({
    url: `${API_BASE}${url}`,
    method: 'GET',
    header: {
      'content-type': 'application/json',
      'Authorization': `Bearer ${getToken()}`
    },
    ...options
  });
};
```