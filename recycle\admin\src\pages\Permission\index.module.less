.main {
  display: flex;
  flex-direction: column;
  background: #ffffff;
  padding: 30px 20px;
}

// :global {
//   .ant-modal-footer {
//     display: none;
//   }
// }

.content_wrapper {
  margin-bottom: 20px;
  &:last-child {
    margin: 0;
  }
  .title {
    display: inline-block;
    width: 100px;
    text-align: right;
  }
}
.button_format {
  background: #1890ff;
  color: #ffffff;
}
th.columnBasic,
td.columnBasic {
  text-align: center !important;
}
