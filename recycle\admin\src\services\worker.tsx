import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'

export async function getWorkerReview(payload: any) {
  return requestPost('/worker/workerReview', payload)
}
export async function getFullTimeWorker(payload: any) {
  return requestGet('/worker', payload)
}
export async function postWorkerWallet(payload: any) {
  return requestPost('/workerCharge', payload)
}
export async function getWorkerWalletLog(payload: any) {
  return requestGet('/walletlog', payload)
}
export async function getDetailWorker(payload: any) {
  return requestGet(`/worker/${payload.id}`)
}
export async function getAddress(payload: any) {
  return requestGet('/address', payload)
}
export async function changeWorkerUse(payload: any) {
  return requestPut(`/worker/${payload.id}`, payload)
}
export async function addWorkAddress(payload: any) {
  return requestPost('worker/addWorkAddress', payload)
}
export async function removeWorkAddress(payload: any) {
  return requestPost('worker/removeWorkAddress', payload)
}
export async function getTownList(payload: any) {
  return requestPost('worker/getTown', payload)
}
export async function getWorkerArealist(payload: any) {
  return requestGet(`getWorkerArea/${payload.id}`, payload)
}
export async function getTheWorkerList(payload: any) {
  return requestPost('worker', payload)
}
export async function postWorkerInsure(payload: any) {
  return requestPost('workerInsure', payload)
}
export async function getWorkerInsure(payload: any) {
  return requestGet('workerInsure', payload)
}
export async function putWorkerInsure(payload: any) {
  return requestPut(`workerInsure/${payload.id}`, payload)
}
export async function delWorkerInsure(payload: any) {
  return requestDelete(`workerInsure/${payload.id}`, payload)
}
export async function getWorkerPaylist(payload: any) {
  return requestGet('/workerPaylist', payload)
}

export async function postWorkerRefund(payload: any) {
  return requestPost('masterPayRefund', payload)
}
export async function workerAreaEdit(payload: any) {
  return requestPost('workerArea', payload)
}
export async function getWorkerSelected(payload: any) {
  return requestGet('workerArea', payload)
}
export async function workerDeleteArea(payload: any) {
  return requestDelete(`workerArea/${payload.workerID}`, payload)
}

// 小工相关API
export async function addWorkerAssistant(payload: any) {
  return requestPost('worker/addAssistant', payload)
}

export async function getWorkerAssistants(payload: any) {
  return requestGet('worker/getAssistantsList', payload)
}
export async function getAssistantOrders(payload: any) {
  return requestGet('worker/assistantOrders', payload)
}
export async function getWorkerOrderData(payload: any) {
  return requestGet('getWorkerOrderData', payload)
} 