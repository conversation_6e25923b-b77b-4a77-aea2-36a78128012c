'use strict'

const _ = require('lodash')
const moment = require('moment')
const { Order, User, Company, OrderCancel, Worker } = require('../../../Models')
const E = require('../../../../../../constants/E')
const { ExcelService } = require('../../../Services')
const { ERR } = require('../../../../../../constants')

//统计数据
class CollectionController {
  //完成订单数量及预估的总金额
  async index({ request, response }) {
    let { startDate, endDate, current, pageSize, status, workerID, companyNameID } = request.all()
    let { adminUser } = request
    let { companyID, level } = adminUser
    let query = Order.query().with('sms')
    companyID = parseInt(companyID)
    if (companyID && companyID !== 36) {
      query.where('companyID', companyID)
    }
    if (companyNameID) {
      query.where('companyID', companyNameID)
    }
    if (status) {
      query.where('status', status)
    }
    if (startDate && endDate) {
      query
        .where(`${status === E.OrderStatus.Reservation ? 'createdAt' : 'finishedAt'}`, '>=', moment(startDate).toDate())
        .where(`${status === E.OrderStatus.Reservation ? 'createdAt' : 'finishedAt'}`, '<=', moment(endDate).add(1, 'd').toDate())
    }
    let vo = await query.fetch()
    let date = []
    let countArray = []
    let amountArray = []
    if (startDate !== endDate) {
      let diff = moment(endDate).diff(moment(startDate), 'days')
      for (let i = 0; i <= diff; i++) {
        let day = moment(startDate).add(i, 'days').format('MM-DD')
        date.push(day)
      }
      date.forEach((value, index) => {
        let count = 0
        let amount = 0
        vo.rows.forEach((order, mark) => {
          if (moment(order.finishedAt).format('MM-DD') === value) {
            count += 1

            let b = order.commission ? order.commission : 0
            let money = 0
            if (order.waste_1st_ID === 4) {
              money = b
            } else {
              money = b
            }
            amount += money
          }
        })
        countArray.push(count)
        amountArray.push(amount / 100)
      })
    }
    let total = 0
    vo.rows.forEach((order, index) => {
      let b = order.commission ? order.commission : 0
      let money = 0
      if (order.waste_1st_ID === 4) {
        money = b
      } else {
        money = b
      }
      total += money
    })
    response.json({ totalComplete: vo.rows.length, totalEstimatedMoney: total, date, countArray, amountArray })
  }
  //订单列表
  async store({ request, response }) {
    let {
      cancelStartDate,
      cancelEndDate,
      workTimeStartDate,
      workTimeEndDate,
      createdAt,
      city,
      startDate,
      endDate,
      current = 1,
      pageSize = 5,
      status,
      workerName,
      type,
      orderNo,
      from,
      userName,
      userMobile,
      address,
      companyNameID, remindCount,
      finishStartDate, finishEndDate,
      takeTime, takeStartDate, takeEndDate,
      waste_1st_ID,
      sort = 'desc',
      isTrack,
      workerID,
    } = request.all()
    let { adminUser } = request
    let { companyID, level } = adminUser
    companyID = parseInt(companyID)
    let query = Order.query().with('sms')
      .with('company')
      .with('doneInfo',
        builder => {
          builder.select('orderID', 'sign', 'remark', 'imageUrl')
          return builder
        })
      .with('cancel', (b) =>
        b.with('worker')
      )
    if (type) {
      query.whereRaw('type like ?', [`%${type}%`])
    }
    if (address) {
      query.whereRaw('address like ?', [`%${address}%`])
    }
    if (userMobile) {
      query.whereRaw('userMobile like ?', [`%${userMobile}%`])
    }
    if (userName) {
      query.whereRaw('userName like ?', [`%${userName}%`])
    }
    if (isTrack) {
      query.where('isTrack', isTrack)
    }
    if (cancelStartDate && cancelEndDate) {
      let cancels = await OrderCancel.query()
        .where('createdAt', '>=', moment(cancelStartDate).toDate())
        .where('createdAt', '<=', moment(cancelEndDate).add(1, 'd').toDate())
        .select('orderID', 'createdAt')
        .fetch()
      let cancelIds = cancels.rows.map((vo) => vo.orderID)
      query.where('id', 'IN', _.uniq(cancelIds))
    }
    if (companyID && companyID !== 36) {
      query.where('companyID', companyID)
    }
    if (from) {
      query.where('from', from)
    }
    if (orderNo) {
      query.where('orderNo', 'like', `%${orderNo}%`)
    }
    if (startDate && endDate) {
      query.where('createdAt', '>=', moment(startDate).toDate()).where('createdAt', '<=', moment(endDate).add(1, 'd').toDate())
    }
    if (workTimeStartDate && workTimeEndDate) {
      query.where('workTime', '>=', moment(workTimeStartDate).toDate()).where('workTime', '<=', moment(workTimeEndDate).add(1, 'd').toDate())
    }
    if (city) {
      query.whereRaw('address like ?', [`%${city}%`])
    }
    if (finishStartDate && finishEndDate) {
      query.where('finishedAt', '>=', moment(finishStartDate).toDate()).where('finishedAt', '<=', moment(finishEndDate).add(1, 'd').toDate())
    }
    if (takeStartDate && takeEndDate) {
      query.where('takeTime', '>=', moment(takeStartDate).toDate()).where('takeTime', '<=', moment(takeEndDate).add(1, 'd').toDate())
    }
    if (workerID) {
      query.where('workerID', workerID)
    }
    if (companyNameID) {
      query.where('companyID', companyNameID)
    }
    if (workerName) {
      let workerInfo = await Worker.query().where('workerName', 'LIKE', `%${workerName}%`).select('id', 'workerName').fetch()
      let workerIds = workerInfo.rows.map((vo) => vo.id)
      query.where('workerID', 'IN', _.uniq(workerIds))
    }
    if (status) {
      if (status === E.OrderStatus.Pending) {
        query.where('status', E.OrderStatus.Reservation).whereNull('workerID')
      } else if (status === E.OrderStatus.Dispatched) {
        query.where('status', E.OrderStatus.Reservation).whereNotNull('workerID')
      } else {
        query.where('status', status)
      }
    }
    if (waste_1st_ID) {
      query.where('waste_1st_ID', waste_1st_ID)
    }
    switch (createdAt) {
      case 'descend':
        query.orderBy('createdAt', 'desc')
        break
      case 'ascend':
        query.orderBy('createdAt', 'asc')
        break
      default:
        break
    }
    switch (takeTime) {
      case 'descend':
        query.orderBy('takeTime', 'desc')
        break
      case 'ascend':
        query.orderBy('takeTime', 'asc')
        break
      default:
        break
    }
    switch (remindCount) {
      case 'descend':
        query.orderBy('remindCount', 'desc')
        break;
      case 'ascend':
        query.orderBy('remindCount', 'asc')
        break;
      default:
        break;
    }

    let vo = await query.with('worker').paginate(current, pageSize)

    response.json(vo)
  }
  //订单数据导出接口
  async exportXLS({ request, params, response }) {
    let { cancelStartDate, cancelEndDate, startDate, endDate, from,
      address, workTimeStartDate,takeStartDate, takeEndDate,
      workTimeEndDate, userName, userMobile,
      city,
      finishStartDate, finishEndDate, companyID, companyNameID, orderNo, workerName, type, status, waste_1st_ID, sort = 'desc', workerID } = request.all()
    let query = Order.query().with('company').with('doneInfo')
    companyID = parseInt(companyID)
    if (companyID && companyID !== 36) {
      query.where('companyID', companyID)
    }
    if (type) {
      query.whereRaw('type like ?', [`%${type}%`])
    }
    if (address) {
      query.whereRaw('address like ?', [`%${address}%`])
    }
    if (userMobile) {
      query.whereRaw('userMobile like ?', [`%${userMobile}%`])
    }
    if (userName) {
      query.whereRaw('userName like ?', [`%${userName}%`])
    }
    if (companyNameID) {
      query.where('companyID', companyNameID)
    }
    if (isTrack) {
      query.where('isTrack', isTrack)
    }
    if (cancelStartDate && cancelEndDate) {
      let cancels = await OrderCancel.query()
        .where('createdAt', '>=', moment(cancelStartDate).toDate())
        .where('createdAt', '<=', moment(cancelEndDate).add(1, 'd').toDate())
        .select('orderID', 'createdAt')
        .fetch()
      let cancelIds = cancels.rows.map((vo) => vo.orderID)
      query.where('id', 'IN', _.uniq(cancelIds))
    }

    if (from) {
      query.where('from', from)
    }
    if (orderNo) {
      query.where('orderNo', 'like', `%${orderNo}%`)
    }
    if (startDate && endDate) {
      query.where('createdAt', '>=', moment(startDate).toDate()).where('createdAt', '<=', moment(endDate).add(1, 'd').toDate())
    }
    if (takeStartDate && takeEndDate) {
      query.where('takeTime', '>=', moment(takeStartDate).toDate()).where('takeTime', '<=', moment(takeEndDate).add(1, 'd').toDate())
    }
    if (finishStartDate && finishEndDate) {
      query.where('finishedAt', '>=', moment(finishStartDate).toDate()).where('finishedAt', '<=', moment(finishEndDate).add(1, 'd').toDate())
    }
    if (workerID) {
      query.where('workerID', workerID)
    }
    if (workerName) {
      let workerInfo = await Worker.query().where('workerName', 'LIKE', `%${workerName}%`).select('id', 'workerName').fetch()
      let workerIds = workerInfo.rows.map((vo) => vo.id)
      query.where('workerID', 'IN', _.uniq(workerIds))
    }
    if (status) {
      if (status === E.OrderStatus.Pending) {
        query.where('status', E.OrderStatus.Reservation).whereNull('workerID')
      } else if (status === E.OrderStatus.Dispatched) {
        query.where('status', E.OrderStatus.Reservation).whereNotNull('workerID')
      } else {
        query.where('status', status)
      }
    }
    if (workTimeStartDate && workTimeEndDate) {
      query.where('workTime', '>=', moment(workTimeStartDate).toDate()).where('workTime', '<=', moment(workTimeEndDate).add(1, 'd').toDate())
    }
    if (city) {
      query.whereRaw('address like ?', [`%${city}%`])
    }
    if (waste_1st_ID) {
      query.where('waste_1st_ID', waste_1st_ID)
    }
    let vo = await query.orderBy('id', sort).with('worker').fetch()
    // console.log(vo, "voovov");
    let data = []

    // return vo
    vo.rows.forEach((value, index) => {
      let arrInner = []
      arrInner.push(value.orderNo)
      arrInner.push(value.from)
      arrInner.push(value.status)
      arrInner.push(value ? value.province : null)
      arrInner.push(value ? value.city : null)
      arrInner.push(value ? value.county : null)
      arrInner.push(value ? value.receiverTown : null)
      arrInner.push(value ? value.address : null)
      arrInner.push(value.type)
      arrInner.push(moment(value.createdAt).format('YYYY-MM-DD HH:mm'))
      arrInner.push(value.$relations.company ? value.$relations.company.companyName : value.mailNo ? '德邦' : '无')
      arrInner.push(value.mailNo ? value.mailNo : '')
      arrInner.push(value.infoFee ? value.infoFee : '')
      let finishTime = value.$relations.doneInfo
        ? moment(value.$relations.doneInfo.createdAt).format('YYYY-MM-DD HH:mm')
        : null
      arrInner.push(finishTime)
      let workTime = moment(value.workTime).format('YYYY-MM-DD HH:mm')
      arrInner.push(workTime)
      arrInner.push(value.$relations.worker ? value.$relations.worker.workerName : null)
      arrInner.push(value.userName || null)
      arrInner.push(value.userMobile || null)
      arrInner.push(value.remindCount || null)
      data.push(arrInner)
      // console.log('>>>>>>>>>>>>>>>>', arrInner, ">>>>>>>>>>>>>>>>>>>>");
    })
    // return data[0][12]
    let theTitle = [
      '订单号',
      '来源',
      '状态',
      '省',
      '市',
      '区',
      '街道',
      '回收地址',
      '类目',
      '创建时间',
      '服务商名称',
      '物流单号',
      '信息费',
      '完成时间',
      '上门时间',
      '回收人员',
      '用户',
      '用户电话',
      '催单次数',
    ]
    let theWidth = [
      { wch: 14 },
      { wch: 16 },
      { wch: 14 },
      { wch: 16 },
      { wch: 12 },
      { wch: 12 },
      { wch: 22 },
      { wch: 22 },
      { wch: 12 },
      { wch: 18 },
      { wch: 22 },
      { wch: 16 },
      { wch: 8 },
      { wch: 18 },
      { wch: 18 },
      { wch: 10 },
      { wch: 18 },
      { wch: 18 },
      { wch: 15 },
    ]
    let fileTitle = '订单统计-' + `${moment().format('YYYYMMDDHH')}`
    await ExcelService.generateExcel(fileTitle, theTitle, data, response, { '!cols': theWidth })
    // return vo
  }
  //回收人员或公司订单数据
  async workerCollection({ request, response }) {
    let { startDate, endDate, companyID, current, type, pageSize, status, waste_1st_ID, sort = 'desc', workerID } = request.all()
    let query = Order.query().with('company')
    if (companyID) {
      query.where('companyID', companyID)
    }
    if (type) {
      query.whereRaw('type like ?', [`%${type}%`])
    }
    if (workerID) {
      query.where('workerID', workerID)
    }
    if (startDate && endDate) {
      query.where('createdAt', '>=', moment(startDate).toDate()).where('createdAt', '<=', moment(endDate).add(1, 'd').toDate())
    }
    if (status) {
      if (status === E.OrderStatus.Pending) {
        query.where('status', E.OrderStatus.Reservation).whereNull('workerID')
      } else if (status === E.OrderStatus.Dispatched) {
        query.where('status', E.OrderStatus.Reservation).whereNotNull('workerID')
      } else {
        query.where('status', status)
      }
    }
    if (waste_1st_ID) {
      query.where('waste_1st_ID', waste_1st_ID)
    }
    let vo = await query.orderBy('id', sort).paginate(current, pageSize)

    response.json(vo)
  }
  //回收人员或公司订单数据导出
  async workerCollectionExportExcel({ request, response }) {
    let { startDate, endDate, companyID, type, current, pageSize, status, waste_1st_ID, sort = 'desc', workerID } = request.all()
    let query = Order.query().with('company')
    if (companyID) {
      query.where('companyID', companyID)
    }
    if (type) {
      query.whereRaw('type like ?', [`%${type}%`])
    }
    if (workerID) {
      query.where('workerID', workerID)
    }
    if (startDate && endDate) {
      query.where('createdAt', '>=', moment(startDate).toDate()).where('createdAt', '<=', moment(endDate).add(1, 'd').toDate())
    }
    if (status) {
      if (status === E.OrderStatus.Pending) {
        query.where('status', E.OrderStatus.Reservation).whereNull('workerID')
      } else if (status === E.OrderStatus.Dispatched) {
        query.where('status', E.OrderStatus.Reservation).whereNotNull('workerID')
      } else {
        query.where('status', status)
      }
    }
    if (waste_1st_ID) {
      query.where('waste_1st_ID', waste_1st_ID)
    }
    let vo = await query.orderBy('id', sort).fetch()

    let data = []
    vo.rows.forEach((value, index) => {
      let arrInner = []
      arrInner.push(value.orderNo)
      arrInner.push(moment(value.createdAt).format('YYYY-MM-DD HH:mm:ss'))
      let day = moment(value.workerTime).format('YYYY-MM-DD')
      let hour = moment(value.workerTime).format('HH:mm') === '09:00' ? '上午' : '下午'
      let time = day + ' ' + hour
      arrInner.push(time)
      let type =
        value.waste_1st_ID === 1 ? '旧衣旧书' : value.waste_1st_ID === 2 ? '生活废品' : value.waste_1st_ID === 3 ? '家电类' : '家具类'
      arrInner.push(type)
      arrInner.push(value.status)
      arrInner.push(value.userName)
      arrInner.push(value.userMobile)
      data.push(arrInner)
    })
    // console.log('data', data)
    await ExcelService.generateExcel(
      `个人数据${startDate ? startDate + '_' + endDate : '总计'}`,
      ['订单号', '下单时间', '上门时间', '回收类型', '状态', '客户姓名', '联系电话'],
      data,
      response,
      { '!cols': [{ wch: 14 }, { wch: 20 }, { wch: 16 }, { wch: 10 }, { wch: 10 }, { wch: 12 }, { wch: 14 }] }
    )
  }
  // 催单
  async remind({ request, params, response }) {
    let vo = await Order.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    vo.remindCount = vo.remindCount + 1
    await vo.save()
    response.json(vo)
  }
}

module.exports = CollectionController
