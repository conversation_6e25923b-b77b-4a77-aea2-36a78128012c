'use strict'

const _ = require('lodash')
const Env = use('Env')

const { Worker,
  YCOrder, ReqLog, WorkerPay,
  YCWorkerMaintain,
  YCWorkerWalletLog } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { CryptUtil } = require('../../../Util')

class YCWorkerController {
  async index({ request, response }) {
    const { adminUser } = request
    let { current = 1, pageSize = 10, lowWallet, companyNameID, hightWallet, leadID, wallet, forbidden, level, isUse, sort = 'desc', mobile, workerName } = request.all()
    let query = Worker.query().where('isyc', 1)
      .orderBy('id', sort).with('company', (b) => b.select('id', 'companyName', 'province'))
    if (level) {
      query.where('level', level)
    } else if (leadID) {
      query.where('leadID', leadID)
    }
    if (lowWallet && hightWallet) {
      query.whereBetween('wallet', [lowWallet, hightWallet])
    }
    if (companyNameID) {
      query.where('companyID', companyNameID)
    }
    if (forbidden) {
      query.where('forbidden', forbidden)
    }
    if (wallet === 'descend') {
      query.orderBy('wallet', 'desc')
    } else if (wallet === 'ascend') {
      query.orderBy('wallet', 'asc')
    } 
    if (isUse) {
      query.where('isUse', isUse)
    }
    if (mobile) {
      query.whereRaw('mobile like ?', [`%${mobile}%`])
    }
    if (workerName) {
      query.whereRaw('workerName like ?', [`%${workerName}%`])
    }
    let vo = await query.with('countUser').paginate(current, pageSize)
    response.json(vo)
  }
  async walletlog({ request, response, params }) {
    let { workerID, current = 1, pageSize = 10, } = request.all()
    // 合并消费记录
    let query = YCWorkerWalletLog.query()
      .where('workerID', workerID)
      .select('workerID', 'id', 'createdAt', 'money', 'remark', 'orderID')
      .with('order').orderBy('createdAt', 'desc')
    let vo = await query.paginate(current, pageSize)
    response.json(vo)
  }

  async workerReview({ request, response }) {
    let { companyID = 0, sort = 'desc', workerName } = request.all()
    companyID = parseInt(companyID)
    let query = Worker.query().with('company')
      .whereNotNull('idCardNo')
      .where('isUse', E.WorkerAccountStatus.Init)
    if (companyID && companyID !== 36) {
      query.where('companyID', companyID)
    }
    if (workerName) {
      query.whereRaw('workerName like ?', [`%${workerName}%`])
    }
    let vo = await query.orderBy('id', sort).fetch()
    response.json(vo)
  }
  async workerMaintain({ request, response }) {
    let { sort = 'desc', status } = request.all()
    let query = YCWorkerMaintain.query()
    if (status) {
      query.where('status', status)
    }
    let vo = await query.orderBy('id', sort).fetch()
    response.json(vo)
  }
  async putWorkerMaintain({ request, response, params }) {
    let { status } = request.all()
    let vo = await YCWorkerMaintain.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    vo.status = status
    await vo.save()
    response.json(vo)
  }
  async show({ request, params, response }) {
    let vo = await Worker.query()
      .where('id', params.id)
      .with('company')
      .first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }

  async store({ request, response }) {
    let { companyID, search } = request.all()
    let query = Worker.query().where('isUse', E.WorkerAccountStatus.Agree)
    if (companyID) {
      query.where('companyID', companyID)
    }
    if (search) {
      query.whereRaw('workerName like ?', [`%${search}%`])
    }
    let vo = await query
      .orderBy('id', 'desc')
      .select('id', 'workerName')
      .fetch()
    response.json(vo)
  }
  async update({ request, params, response }) {
    let { type, companyID, mobile } = request.all()
    let { adminUser } = request
    let vo = await Worker.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    if (mobile) {
      let passwd = mobile.slice(-6)
      let enPw = CryptUtil.md5(CryptUtil.encryptData256(passwd, Env.get('APP_KEY')))
      _.assign(vo, request.all(), { password: enPw })
    } else {
      _.assign(vo, request.all())
    }
    await vo.save()
    response.json(vo)
    if (companyID) {
      await YCWorkerWalletLog.query().where('workerID', params.id).update({ companyID: companyID })
      await YCOrder.query().where('workerID', params.id).update({ companyID: companyID })
    }
  }
  async destroy({ request, params, response }) {
    let vo = await Worker.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_DELETE_ID
    }
    throw ERR.USER_EXISTS
    // await vo.delete()
  }
  async chargeWallet({ request, params, response }) {
    let { chargeNum, workerID } = request.all()
    let vo = await Worker.find(workerID)
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    vo.wallet += chargeNum
    await vo.save()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台充值' })
    await WorkerPay.create({ workerID: workerID, totalPay: chargeNum * 100, status: '完成', finishAt: new Date(), transactionID: 1, type: '系统' })
    return { chargeNum, workerID }
  }
}

module.exports = YCWorkerController
