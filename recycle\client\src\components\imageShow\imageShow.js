import Taro, { Component, offDeviceMotionChange } from '@tarojs/taro'

import { View, Picker,Image } from '@tarojs/components'
import './imageShow.less'

const ImageShow = props => {
  const { data } = props
  function operatePhote(list, index) {
    // wx.previewImage({
    //   current: index, // 当前显示图片的http链接
    //   urls: list, // 需要预览的图片http链接列表
    // })
  }
  return (
    <View className="image_wrapper">
      {data &&
        data.map((img, mark) => (
          <View
            key={img}
            className="image"
            onClick={() => {
              operatePhote(data, img)
            }}
          >
            <Image src={img} className="img" mode="aspectFit" />
          </View>
        ))}
    </View>
  )
}
export default ImageShow
