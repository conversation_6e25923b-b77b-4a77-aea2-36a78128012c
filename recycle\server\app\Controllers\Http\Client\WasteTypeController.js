'use strict'

const _ = require('lodash')
const moment = require('moment')

const { WasteType } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//废品类型
class WasteTypeController {
  async index({ request, response }) {
    let { page = 1, perPage = 10 } = request.all()
    let query = WasteType.query().where('deletedAt', 0)
    let vo = await query.paginate(page, perPage)
    response.json(vo)
  }
  async show({ request, params, response }) {
    let vo = await WasteType.query()
      .where('deletedAt', 0)
      .where('id', params.id)
      .first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
}

module.exports = WasteTypeController
