/**
 * Created by <PERSON>(qq:24242811) on 2018/11/5.
 */
const moment = require('moment')
const _ = require('lodash')
const Env = use('Env')

const { ERR, E } = require('../../../../constants')

const UserService = {
  async judgeAuthority(adminUser, need) {
    const adminUserJSON = adminUser.toJSON()
    let result = false
    let key = adminUserJSON.authority.key
    if (key) {
      key = JSON.parse(key)
      if (key.indexOf(need) >= 0) {
        result = true
      }
    }
    if (!result) {
      throw ERR.USER_ROLE_NO_PRIVILEGE
    }
  }
}

module.exports = UserService
