'use strict'

const _ = require('lodash')
const { ReqLog, ChargePrice, Order } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

class ChargePriceController {
  async index({ request, response }) {
    let { current = 1, pageSize = 10, area, sort = 'desc' } = request.all()
    let workerID = request.worker && request.worker.id
    let query = ChargePrice.query()
    if (area) {
      query.whereRaw('area like ?', [`%${area}%`])
    }
    let vo = await query.orderBy('id', sort).paginate(current, pageSize)
    let inprocessWallets = 0
    // inprocessWallets = await Order.query()
    //   .where('workerID', workerID)
    //   .where('status', E.OrderStatus.InProgress)
    //   .getSum('infoFee')
    // console.log(inprocessWallets);
    response.json({ ...vo.toJSON(), inprocessWallets })
  }
}

module.exports = ChargePriceController
