import { useEffect, useMemo, useState } from 'react'
import images from '../../assets/images'
import styles from './index.module.less'
import { TinyLine, Column, Area, Pie } from '@ant-design/plots'
import io, { Socket } from 'socket.io-client'
import { sumBy, orderBy } from 'lodash'
import dayjs from 'dayjs'
import { useNavigate } from 'react-router-dom'
import { message, Spin } from 'antd'
import Config from '../../config/Config'
import { EGetCharts, EGetData, EGetLineCharts, NUser } from '../../config/Constants'
import { effect, useConnect } from '../../utils/dva17'

export default () => {
  const navigate = useNavigate()
  const {
    dashData,
    chartData,
    lineData,
  } = useConnect(NUser)
  const [loading, setLoading] = useState(true)
  const [currentOrder, setCurrentOrder] = useState(0) //实时订单
  const [orderTpye, setOrderTpye] = useState([]) //停车分析
  const [orderTrash, setOrderTrash] = useState([]) //客流趋势
  let user = JSON.parse(localStorage.getItem('userInfo'))

  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    effect(NUser, EGetData, {})
    effect(NUser, EGetCharts, {})
    effect(NUser, EGetLineCharts, {})
  }, [])

  useEffect(() => {
    if (dashData) {
      setCurrentOrder(dashData.todayCount)
      setLoading(false)
    }
    if (chartData) {
      setOrderTpye(chartData)
    }
    if (lineData) {
      setOrderTrash(lineData)
    }
  }, [dashData, chartData, lineData])

  /*--------------------- 响应 ---------------------*/
  //实时在园人数
  const renderCurrentPeople = () => {
    const data = [264, 417, 438, 887, 309, 397, 550, 575, 563, 430, 525, 592, 492, 467, 513, 546, 983, 340, 539, 243, 226, 192]
    const config = {
      height: 60,
      autoFit: false,
      data,
      smooth: true,
      color: '#5CF6F9',
    }
    return <TinyLine {...config} />
  }
  //车型占比
  const renderCarType = () => {
    const [config, setConfig] = useState({
      data: [],
      appendPadding: 10,
      angleField: 'count',
      colorField: 'vehicleType',
      radius: 1,
      innerRadius: 0.6,
      label: {
        type: 'inner',
        offset: '-50%',
        content: '{value}',
        style: {
          textAlign: 'center',
          fontSize: 14,
        },
      },
      interactions: [
        {
          type: 'element-selected',
        },
        {
          type: 'element-active',
        },
      ],
      statistic: {
        title: false,
        content: {
          style: {
            whiteSpace: 'pre-wrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            color: '#fff',
            fontSize: '24px',
          },
          content: '',
        },
      },
    })
    return <Pie {...config} />
  }

  const renderStopCar = () => {
    const [config, setConfig] = useState({
      data: [],
      appendPadding: 10,
      angleField: 'count',
      colorField: 'type',
      radius: 0.9,
      legend: {
        position: 'right',
        //每个后面加百分比
      },
      label: {
        type: 'inner',
        offset: '-30%',
        content: ({ type, count }) => {
          return type
        },
        style: {
          fontSize: 11,
          textAlign: 'center',
        },
      },
      interactions: [
        {
          type: 'element-active',
        },
      ],
    })
    useEffect(() => {
      let arr = []
      //计算总和
      let total = 0
      if (orderTpye.length > 0) {
        orderTpye.forEach(vo => {
          total += vo.count
        })
        orderTpye.forEach(vo => {
          arr.push({
            type: vo.type + `${((vo.count / total) * 100).toFixed(2)}%`,
            count: vo.count,
          })
        })
        config.data = arr
        setConfig(Object.assign({}, config))
      }
    }, [orderTpye])
    return (
      <div>
        <div style={{ height: '240px' }}>
          <Pie {...config} />
        </div>
      </div>
    )
  }

  const renderPassengerFlow = () => {
    const [config, setConfig] = useState({
      data: [],
      isGroup: true,
      //颜色
      color: ['#E2364A', '#02F9FC', '#8C55E2', '#8DCB7C', '#FFC667', '#4B73C1', '#66C1DC'],
      xField: 'date',
      yField: 'count',
      seriesField: 'type',
      legend: {
        position: 'bottom',
      },
      slider: {
        start: 0.6,
        end: 1,
      },
      columnStyle: {
        radius: [30, 30, 0, 0],
      },
    })
    useEffect(() => {
      let orderTrashArr = []
      orderTrash?.forEach(vo => {
        orderTrashArr.push({
          date: vo.date,
          count: vo.count,
          type: vo.type,
        })
      })
      const index = orderTrashArr.findIndex(item => item.name == '上野')
      // 如果找到了“上野”数据，则将其移动到第一位
      if (index != -1) {
        const item = orderTrashArr.splice(index, 1)[0]
        orderTrashArr.unshift(item)
      }
      config.data = orderTrashArr
      setConfig(Object.assign({}, config))
    }, [orderTrash])
    return <Column {...config} />
  }


  function onOut() {
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    //页面重载
    navigate('/user/login', { replace: true })
  }
  /*--------------------- 渲染 ---------------------*/
  return (
    <Spin spinning={loading}>
      <div className={styles.container}>
        <div className={styles.head}>
          <img src={'https://oss.evergreenrecycle.cn/yshs/LOGO2.png'} className={styles.title} />
          <span onClick={onOut}>退出</span>
        </div>
        <div className={[styles.card, styles.columns_box, styles.first_card].join(' ')}>
          <div className={styles.card_bottom} style={{ marginTop: 0 }}>
            <img className={styles.card_icon} src={images.icon1} />
            <div className={styles.current_number}>
              <div className={styles.current_number_title}>今日实时订单量</div>
              <div className={styles.people_num}>{currentOrder}</div>
            </div>
          </div>
          <div>{renderCurrentPeople()}</div>
          <img src={images.circular} className={styles.circular} />
        </div>
        <div className={styles.card}>
          <div className={styles.card_title}>类型分析</div>
          {renderStopCar()}
        </div>
        <div className={styles.columns_box}>
          <div className={styles.card}>
            <div className={styles.card_title}>今日充值</div>
            <div className={styles.card_bottom}>
              <img className={styles.card_icon} src={images.icon2} />
              <div className={styles.number_car}>
                <span>{(dashData?.todayCharge || 0) / 100}</span>
                <span className={styles.unit}>元</span>
              </div>
            </div>
          </div>
          <div className={styles.card}>
            <div className={styles.card_title}>待处理</div>
            <div className={styles.card_bottom}>
              <img className={styles.card_icon} src={images.icon3} />
              <div className={styles.number_police}>
                <span>{dashData?.PendingCount}</span>
                <span className={styles.unit}>条</span>
              </div>
            </div>
          </div>
        </div>
        {/* <div className={styles.card}>
          <div className={styles.card_title}>展区车流</div>
          {renderCarNumber()}
        </div> */}
        <div className={styles.card}>
          <div className={styles.card_title}>订单趋势</div>
          <div style={{ height: '289px' }}>{renderPassengerFlow()}</div>
        </div>
        <div className={styles.card}>
          <div className={styles.card_title}>订单展示</div>
          <div className={styles.headCount}>
            <div className={styles.visitors}>
              <div className={styles.num}>{dashData?.InProgressCount || 0}</div>
              <span className={styles.visitors_title}>进行中订单</span>
              <img src={images.attendance_icon} className={styles.icon}></img>
            </div>
            <div className={styles.visitors}>
              <div className={styles.num}>{dashData?.yedCompletedCount || 0}</div>
              <span className={styles.visitors_title}>昨日完单</span>
              <img src={images.attendance_icon} className={styles.icon}></img>
            </div>
            <div className={styles.visitors}>
              <div className={styles.num}>{dashData?.yedCancelledCount || 0}</div>
              <span className={styles.visitors_title}>昨日取消</span>
              <img src={images.attendance_icon} className={styles.icon}></img>
            </div>
          </div>
        </div>
      </div>
    </Spin>
  )
}
