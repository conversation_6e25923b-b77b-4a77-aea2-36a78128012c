import { CopyOutlined, DownloadOutlined, FormOutlined, PoweroffOutlined } from '@ant-design/icons'
import { Badge, Button, Image, Input, Modal, Upload, notification } from 'antd'
import type { ActionType, ProColumns } from '@ant-design/pro-table'
import ProTable, { } from '@ant-design/pro-table'
import ProCard from '@ant-design/pro-card'
import { effect, reducer, useConnect } from 'dva17'
import { useEffect, useRef, useState } from 'react'
import {
  ECancelOrderStatus,
  EChangeOrderStatus,
  EChangeOrderWorker,
  EGetOrderList,
  EGetOrderNumberArray,
  EGetWhichWorkers,
  EOrderBack, EConfirmOrder,
  EPut,
  NOrder,
  NUser,
  RSetState,
} from '../../common/action'
import dayjs from 'dayjs'
import copy from 'copy-to-clipboard'
import qs from 'qs'
import styles from './index.module.less'
import { fromEnum, pageStatus } from '../../common/enum'
import { NCompany, EGet } from '../../common/action'
import EditOrder from '../../components/EditOrder'
import { SERVER_HOME, SERVER_HOME_File } from '../../common/config'
import OrderExpande from '../../components/OrderExpande'
import { includes, sum } from 'lodash'
import { getOrderLog, remindOrder } from '../../services/order'
const { confirm } = Modal
const { TextArea, Search } = Input

type Item = {
  id: number
  remindCount: number
  orderNo: string
  from: string
  status: string
  createdAt: any
  finishedAt: any
  realname: string
  workTime: any
  estimatedMoney: number
  wasteType: string
  company: {
    companyName: string
  }
  cancel: {
    createdAt: any
  }
  mobile: string
  province: string
  takeTime: any
  city: string
  address: string
}

export default ({ type = 1, storeOrder = false }) => {
  const { orderNumberArray, whichWorkers, visibleLog, logData, searchQuery } = useConnect(NOrder)
  const { currentUser } = useConnect(NUser)
  const { companyList } = useConnect(NCompany)
  const [ComEnum, setComEnum] = useState<any>({})
  const [whichOrder, setWhichOrder] = useState<any>(pageStatus.Pending)
  const [visible, setVisible] = useState<boolean>(false)
  const [visibleSend, setVisibleSend] = useState<boolean>(false)
  const [visibleEdit, setVisibleEdit] = useState<boolean>(false)
  const [cancelOrderVisible, setCancelOrderVisible] = useState<boolean>(false)
  const [isTrans, setIsTrans] = useState<boolean>(false)
  const [orderData, setOrderData] = useState<any>(null)
  const [operateWhichOrder, setOperateWhichOrder] = useState<any>(null)
  const [textAreaValue, setTextAreaValue] = useState<any>(null)
  const [mySelectedRowKeys, setMySelectedRowKeys] = useState([]);
  const [selectedWorker, setSelectedWorker] = useState<any>(null)
  const actionRef = useRef<ActionType>()
  const reasonList = ['重复下单', '用户取消', '用户投诉', '回收人员不上门', '机子被他人取走', '实物与图片不符']
  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    getCounts()
    effect(NCompany, EGet, {})
  }, [type])

  useEffect(() => {
    const enumObject: Record<string, { text: string; status: string }> = {};
    companyList.forEach((item: any) => {
      enumObject[item.id] = {
        text: item.companyName,
        status: 'Processing',
      };
    });
    setComEnum(enumObject);
  }, [companyList])

  let show = (currentUser.level == '服务商' ? true : false)

  /*--------------------- 响应 ---------------------*/
  const rowSelectionChange = (selectedRowKeys: any, selectedRows: any) => {
    setMySelectedRowKeys(selectedRowKeys)
  }
  const stylea = { display: 'block', padding: '0px 0px 8px 0px' };

  const refreshPage = async () => {
    if (actionRef.current) {
      actionRef.current.reload()
    }
    getCounts()
  }
  const getCounts = async () => {
    await effect(NOrder, EGetOrderNumberArray, {
      waste_1st_type: 3,
      companyID: currentUser?.companyID,
      isStore: storeOrder ? 1 : 0,
    })
  }
  const changeOrder = async (order: any) => {
    if (order === "全部") {
      setWhichOrder(null)
    } else {
      setWhichOrder(order)
    }
    await refreshPage()
  }
  const showWorkers = async (orders: any[]) => {
    console.log(orders);
    await effect(NOrder, EGetWhichWorkers, {
      province: currentUser?.level === "总部" ? "广东省" : null,
      type: type,
    })
    setVisibleSend(true)
  }
  const showModal = async (text: any) => {
    await effect(NOrder, EGetWhichWorkers, {
      subDistrct: text.receiverTown,
      townID: text.townID,
      province: text.province,
      county: text.county,
      companyID: text.companyID,
      userMobile: text.userMobile,
      orderID: text.id,
      typeName: text.type,
      type: type,
    })
    setVisible(true)
    setOperateWhichOrder(text)
  }
  const editOrder = (text: any) => {
    setVisibleEdit(true)
    setOrderData(text)
  }
  const cancelOrder = (text: any) => {
    setOrderData(text)
    setCancelOrderVisible(true)
  }
  const cancelOrderCancel = () => {
    setCancelOrderVisible(false)
    setOrderData(null)
    setTextAreaValue(null)
  }
  const handleOk = async () => {
    if (selectedWorker === 0 || selectedWorker) {
      setVisible(false)
      setSelectedWorker(null)
      if (isTrans) {
        await effect(NOrder, EChangeOrderWorker, {
          id: operateWhichOrder?.id,
          workerID: whichWorkers[selectedWorker].id,
          companyID: whichWorkers[selectedWorker].companyID,
          status: operateWhichOrder?.status,
        })
        setIsTrans(!isTrans)
      } else {
        await effect(NOrder, EChangeOrderStatus, {
          id: operateWhichOrder?.id,
          workerID: whichWorkers[selectedWorker].id,
          companyID: whichWorkers[selectedWorker].companyID,
          waste_1st_ID: operateWhichOrder?.waste_1st_ID,
        })
      }
      await refreshPage()
    }
  }
  const handleConfirm = async (text: any) => {
    Modal.confirm({
      title: '确认完成该订单',
      content: <div>确认完单！</div>,
      okText: '确认完单',
      cancelText: '退出',
      onOk: async () => {
        await effect(NOrder, EConfirmOrder, {
          orderID: text?.id,
        })
        notification.success({
          message: '成功！',
          description: '完单成功',
          duration: 2,
        })
        await refreshPage()
      },
      width: 700,
    })
  }
  const handleSend = async () => {
    if (mySelectedRowKeys.length > 0 && (selectedWorker === 0 || selectedWorker)) {
      await effect(NOrder, EPut, {
        orderIDList: mySelectedRowKeys,
        workerID: whichWorkers[selectedWorker].id,
        companyID: whichWorkers[selectedWorker].companyID,
      })
      await refreshPage()
    }
    setVisibleSend(false)
    setSelectedWorker(null)
  }
  const handleCancel = () => {
    setVisible(false)
    setSelectedWorker(null)
  }
  const exportExcel = () => {
    let qsQuery = qs.stringify(searchQuery)
    window.open(
      `${SERVER_HOME_File}DGlogDataByExcelFile?${qsQuery}`
    )
  }
  const cancelOrderOk = async () => {
    if (textAreaValue) {
      await effect(NOrder, ECancelOrderStatus, {
        id: orderData?.id,
        // isTransferID: null,
        status: pageStatus.Cancelled,
        cancelReason: textAreaValue,
      })
    } else {
      notification.warn({
        message: '失败！',
        description: '请填写内容',
        duration: 2,
      })
    }
    setCancelOrderVisible(false)
    setTextAreaValue(null)
    setOrderData(null)
    await refreshPage()
  }
  const backOrder = (ids: any) => {
    confirm({
      title: '确认撤回此组订单吗?',
      onOk: async () => {
        await effect(NOrder, EOrderBack, { orderIDList: ids }).then(() => {
          refreshPage()
          notification.success({
            message: '成功！',
            description: '撤回成功',
            duration: 2,
          })
        })
      },
      onCancel() { },
      okText: '确认',
      cancelText: '取消',
    })
  }
  const onSearch = async (row: any) => {
    effect(NOrder, EGetWhichWorkers, {
      name: row,
      type: type,
    })
  }
  const copyText = async (row: any) => {
    let copyContent: string =
      row.orderNo +
      row.userName +
      ' (' +
      row.userMobile +
      ') 【' +
      row.city +
      row.province +
      '】' +
      row.address +
      '\n旧机信息:' +
      row.type +
      '-' +
      '\n预约时间:' +
      dayjs(row.workTime).format('YYYY-MM-DD A') +
      '\n信息费 ￥:' +
      row.infoFee
    copy(copyContent)
    notification.success({
      message: '成功！',
      description: '复制成功',
      duration: 2,
    })
  }
  const rowSelection = {
    columnWidth: '2vw',
    selections: [
      {
        key: 'odd',
        text: '批量派单',
        onSelect: (changeableRowKeys: any) => {
          showWorkers(mySelectedRowKeys)
        },
      },
      {
        key: 'recall',
        text: '批量撤回',
        onSelect: (changeableRowKeys: any) => {
          backOrder(mySelectedRowKeys)
        },
      },
    ],
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      rowSelectionChange(selectedRowKeys, selectedRows)
    },
  };
  const columns: ProColumns<Item>[] = [
    {
      title: '订单号',
      dataIndex: 'orderNo',
      width: '5%',
      copyable: false,
      render: (_, row: any) => (
        <>
          <div style={{ textAlign: 'center' }}>{row.orderNo}</div>
          <a
            style={{ marginLeft: 15 }}
            onClick={() => {
              copyText(row)
            }}
            key="3">
            <CopyOutlined />
            复制
          </a>
        </>
      ),
    },
    {
      title: '催单次数',
      dataIndex: 'remindCount',
      copyable: false,
      ellipsis: true,
      hideInSearch: true,
      sorter: (a, b) => a.remindCount - b.remindCount,
      render: (_, row: any) => {
        return <div>
          <div style={{ fontWeight: 'bolder', fontSize: 20 }}>{row.remindCount}</div>
          <Button type='primary' onClick={async () => {
            await remindOrder(row).then((res) => {
              if (res) {
                refreshPage()
              }
            })
          }} >
            催单
          </Button>
        </div >
      },
    },
    { title: '品类', dataIndex: 'type', copyable: false },
    {
      title: '服务商', hideInTable: true, dataIndex: 'companyNameID',
      hideInSearch: show,
      copyable: false, valueEnum: ComEnum,
    },
    {
      title: '回收人员',
      dataIndex: 'workerName',
      hideInTable: true,
    },
    {
      title: '地址',
      dataIndex: 'address',
      copyable: false,
      width: '15%',
      render: (_, row: any) => (
        <div style={{ wordWrap: 'break-word', wordBreak: 'break-word' }}>{row.province}{row.city}{row.receiverTown}{row.address}</div>
      ),
    },
    {
      title: '下单时间',
      dataIndex: 'createdAt',
      copyable: false,
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            startDate: value[0],
            endDate: value[1],
          }
        },
      },
    },
    {
      title: '下单时间',
      dataIndex: 'createdAt',
      copyable: false,
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: (a, b) => a.createdAt - b.createdAt,
      render: (_, row: any) => (
        <div >
          <div >  {row.createdAt}</div>
          <div style={{
            width: 'fit-content',
            padding: '2px',
            boxShadow: '0 2px 8px #1b56c0',
            borderRadius: '3px',
            color: 'red'
          }}> 已下单：{dayjs().diff(dayjs(row.createdAt), 'hours')}小时</div>
        </div>
      ),
    },

    { title: '客户姓名', dataIndex: 'userName', copyable: false },
    { title: '联系电话', dataIndex: 'userMobile', copyable: false, search: false },
    { title: '联系电话', dataIndex: 'userMobile', hideInTable: true },
    {
      title: '师傅', dataIndex: 'workerName', copyable: false, search: false,
      render: (_, row: any) => (<div>
        {row?.cancel && row?.cancel.slice(-1)[0]?.worker?.workerName || row?.worker?.workerName}
      </div>)
    },
    {
      title: '上门时间',
      dataIndex: 'workTime',
      copyable: false,
      sorter: (a, b) => a.workTime - b.workTime,
      search: false,
      renderText: (_, row) => dayjs(row.workTime).format('YYYY-MM-DD A'),
    },
    {
      title: '上门时间',
      dataIndex: 'workTime',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            workStartDate: value[0],
            workEndDate: value[1],
          }
        },
      },
    },
    {
      title: '接单时间',
      dataIndex: 'takeTime',
      copyable: false,
      sorter: (a, b) => a.takeTime - b.takeTime,
      search: false,
      renderText: (_, row) => { return (row.takeTime ? dayjs(row.takeTime).format('YYYY-MM-DD HH:mm:ss') : '-') },
    },
    {
      title: '接单时间',
      dataIndex: 'takeTime',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            takeStartDate: value[0],
            takeEndDate: value[1],
          }
        },
      },
    },
    { title: '订单来源', dataIndex: 'from', copyable: false, valueEnum: fromEnum },
    { title: '信息费', dataIndex: 'infoFee', copyable: false, search: false },
    {
      title: '取消时间',
      dataIndex: ['cancel', 'createdAt'],
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            cancelStartDate: value[0],
            cancelEndDate: value[1],
          }
        },
      },
    },
    {
      title: '完成时间',
      dataIndex: 'finishedAt',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            finishStartDate: value[0],
            finishEndDate: value[1],
          }
        },
      },
    },
    { title: '服务商', dataIndex: ['company', 'companyName'], copyable: false, search: false },
    {
      title: '状态',
      dataIndex: 'status',
      copyable: false,
      search: false,
      render: (_, row: any) => (
        <>
          <div>{row.status}</div>
          {whichOrder === pageStatus.Cancelled && <div>{row.cancel?.createdAt}</div>}
          {whichOrder === pageStatus.Completed && <div>{dayjs(row.finishedAt).format('YYYY-MM-DD HH:mm:ss')}</div>}
        </>
      ),
    },
    {
      title: '操作',
      copyable: false,
      fixed: 'right',
      search: false,
      // hideInTable: includes([pageStatus.Cancelled, pageStatus.Completed, null], whichOrder) ? true : false,
      render: (_, row: any) => (
        <>
          <a
            key="5"
            style={stylea}
            onClick={async () => {
              let data = await getOrderLog({ id: row.id })
              reducer(NOrder, RSetState, { logData: data, visibleLog: true })
            }}
          >
            订单记录
          </a>
          {(whichOrder === pageStatus.Pending || whichOrder === pageStatus.SystemReturn || whichOrder === pageStatus.MasterReturn) ? (
            <a
              key="0"
              style={stylea}
              onClick={() => {
                showModal(row)
              }}>
              派单
            </a>
          ) : null}
          {['已派单', '进行中'].indexOf(whichOrder) >= 0 ? (
            <a
              key="1"
              style={stylea}
              onClick={() => {
                backOrder([row.id])
              }}>
              撤回订单
            </a>
          ) : null}
          {['已派单', '进行中'].indexOf(whichOrder) >= 0 ? (
            <a
              style={{ marginRight: 10 }}
              onClick={() => {
                showModal(row)
                setIsTrans(true)
              }}>
              改派
            </a>
          ) : null}
          {['已派单', '进行中'].indexOf(whichOrder) >= 0 ? (
            <a
              key="2"
              style={stylea}
              onClick={() => {
                handleConfirm(row)
              }}>
              后台完单
            </a>
          ) : null}
          {row.status == pageStatus.Pending || row.status == pageStatus.Reservation || row.status == pageStatus.Dispatched || row.status == pageStatus.SystemReturn || row.status == pageStatus.InProgress || row.status == pageStatus.MasterReturn ? (
            <a
              style={{ ...stylea, color: 'red' }}
              key="3"
              onClick={() => {
                cancelOrder(row)
              }}>
              取消订单
            </a>
          ) : null}
          {[pageStatus.Pending, pageStatus.Reservation, pageStatus.InProgress, pageStatus.SystemReturn, pageStatus.MasterReturn].includes(row.status) && (
            <a
              key="4"
              style={stylea}
              onClick={() => {
                editOrder(row)
              }}
            >
              <FormOutlined />
              编辑
            </a>
          )}
        </>
      ),
    },
  ]
  /*--------------------- 渲染 ---------------------*/
  return (
    <ProCard>
      <Modal
        zIndex={1100}
        open={visibleLog}
        title="订单记录"
        width={850}
        footer={[
          <Button type="primary" onClick={() => { reducer(NOrder, RSetState, { visibleLog: false }) }}>
            知道了
          </Button>,
        ]}
        onCancel={() => {
          reducer(NOrder, RSetState, { visibleLog: false })
        }}>
        <>
          {logData &&
            logData.map((vo: any, index: number) => {
              return (
                <div
                  key={index}
                  style={{
                    display: 'flex',
                    margin: 10,
                    flexWrap: 'wrap',
                    justifyContent: 'space-around',
                    borderBottom: '1px solid grey',
                  }}>
                  <div style={{ width: '22%' }}>{vo.createdAt}</div> -
                  <div style={{ width: '22%' }}>
                    {(vo.user?.realName && '用户:' + vo.user?.realName) ||
                      (vo.worker?.workerName && '师傅:' + vo.worker?.workerName + vo.worker?.mobile) ||
                      (vo.creater?.name && '后台:' + vo.creater?.name)}
                  </div>
                  -<div style={{ width: '22%' }}>{vo.status}</div>-<div style={{ width: '22%' }}>备注:{vo.content}</div>
                  <div style={{ textDecoration: 'underline', width: '100%' }} />
                </div>
              )
            })}
        </>
      </Modal>
      <div className={styles.order_status_wrapper}>
        {[
          { title: '待处理', status: pageStatus.Pending },
          { title: '师傅撤回', status: pageStatus.MasterReturn },
          { title: '系统撤回', status: pageStatus.SystemReturn },
          { title: '已派单', status: pageStatus.Dispatched },
          { title: '进行中', status: pageStatus.InProgress },
          { title: '已完成', status: pageStatus.Completed },
          { title: '已取消', status: pageStatus.Cancelled },
          { title: '全部', status: pageStatus.ALL },
        ].map((value: any, index: number) => (
          <div
            key={index + value}
            onClick={() => {
              changeOrder(value.status)
            }}
            className={`${styles.order_status} ${value.status === whichOrder ? styles.order_status_active : ((value.status === "全部" && whichOrder == null) ? styles.order_status_active : '')}`}>
            <span>{value.title}</span>
            <span>{index === 7 ? sum(orderNumberArray) : (orderNumberArray ? orderNumberArray[index] : 0)}</span>
          </div>
        ))}
      </div>
      <Modal
        title="取消订单"
        open={cancelOrderVisible}
        onOk={() => {
          cancelOrderOk()
        }}
        onCancel={() => {
          cancelOrderCancel()
        }}
        width={800}
        okText={'确认取消'}
        cancelText={'退出'}>
        <div className={styles.item_wrapper}>
          <div className={styles.item_wrapper_reasonList}>
            {reasonList.map((v: string, i: number) => (
              <div
                key={i + v}
                className={styles.reasonList_item}
                onClick={() => {
                  setTextAreaValue(v)
                }}>
                {v}
              </div>
            ))}
          </div>
          <TextArea
            rows={5}
            value={textAreaValue}
            placeholder="请输入原因"
            style={{ margin: '15px 0' }}
            onChange={(e: any) => {
              setTextAreaValue(e.target.value)
            }}
          />
        </div>
      </Modal>
      <Modal
        width={'70vw'}
        destroyOnClose={true}
        title="批量派单确认" open={visibleSend} onOk={handleSend}
        onCancel={() => { setVisibleSend(false) }}
      >
        <p>人员选择：
          <Search placeholder="筛选师傅名称" onSearch={onSearch} style={{ width: 200 }} />
        </p>
        <div className={styles.each}>
          {whichWorkers && whichWorkers.length > 0
            ? whichWorkers.map((value: any, index: number) => (
              <div
                className={`${styles.each_one} ${selectedWorker === index ? styles.each_selected : null}`}
                key={index}
                style={{ color: ((value?.orderID || parseInt(value?.id) === -1 || parseInt(value?.id) === -2) ? 'green' : 'black') }}
                onClick={() => {
                  setSelectedWorker(index)
                }}>
                {value?.orderID ? "合并单-" : null}{value.workerName}{value.order && value.order[0] && ("-进行中" + value.order[0].count)}
              </div>
            ))
            : null}
        </div>
      </Modal>
      <Modal
        width={'70vw'}
        destroyOnClose={true}
        title="派单确认" open={visible} onOk={handleOk} onCancel={handleCancel}>
        {operateWhichOrder ? (
          <div>
            <p>
              客户地址信息：
              {operateWhichOrder?.province}
              {operateWhichOrder?.city}
              {operateWhichOrder?.address}
            </p>
            <p>人员选择：
              <Search placeholder="筛选师傅名称" onSearch={onSearch} style={{ width: 200 }} />
            </p>
            <div className={styles.each}>
              {whichWorkers && whichWorkers.length > 0
                ? whichWorkers.map((value: any, index: number) => (
                  <div
                    className={`${styles.each_one} ${selectedWorker === index ? styles.each_selected : null}`}
                    key={index}
                    style={{ color: ((value?.orderID || parseInt(value?.id) === -1 || parseInt(value?.id) === -2) ? 'green' : 'black') }}
                    onClick={() => {
                      setSelectedWorker(index)
                    }}>
                    {value?.orderID ? "合并单-" : null}{value.workerName}{value.order && value.order[0] && ("-进行中" + value.order[0].count)}
                  </div>
                ))
                : null}
            </div>
          </div>
        ) : null}
      </Modal>
      <EditOrder
        data={orderData}
        visible={visibleEdit}
        onChange={({ visible, refresh }: any) => {
          setVisibleEdit(visible)
          if (refresh) {
            refreshPage()
          }
        }}
      />
      <ProTable<Item>
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        columns={columns}
        rowSelection={rowSelection}
        expandable={{
          expandedRowRender: (record) => { return (<OrderExpande row={record} whichOrder={whichOrder} />) },
        }}
        request={async (params = {}, sorter) => {
          return (await effect(NOrder, EGetOrderList, {
            ...params,
            status: whichOrder,
            companyID: currentUser?.companyID,
            isStore: storeOrder ? 1 : 0,
            waste_1st_type: 3,
            ...sorter,
          })) as any
        }}
        pagination={{}}
        rowKey="id"
        dateFormatter="string"
        headerTitle=""
        toolBarRender={() => [
          whichOrder == pageStatus.Pending ? (<Button
            style={{ marginRight: 20 }}
            type="primary"
            disabled={currentUser.level == '服务商'}
            onClick={() => {
              setVisibleEdit(true)
              setOrderData(null)
            }}>
            手动建档
          </Button>) : null
          ,
          <Button icon={<DownloadOutlined />} onClick={() => {
            exportExcel()
          }} type='primary'>导出</Button>
        ]}
      />
    </ProCard>
  )
}
