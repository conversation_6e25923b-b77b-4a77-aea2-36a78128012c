'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Waste2ndCategory } = require('../../../Models')
const { Waste } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//废品二级分类
class Waste2ndCategoryController {
  async show({ request, params, response }) {
    let { source = E.OrderSource.CaiNiao, type = E.Permission.Primary, area = E.AreaList[0] } = request.all()
    let query = Waste.query().where('source', source).where('type', type).where('area', area)
    let vo = await query.where('secondCateID', params.id)
      .fetch()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
}

module.exports = Waste2ndCategoryController
