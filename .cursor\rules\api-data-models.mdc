---
description: API 接口和数据模型规范
---

# API 接口和数据模型规范

## API 设计原则
- RESTful API 设计风格
- 统一的请求和响应格式
- 版本控制和向后兼容
- 完善的错误处理和状态码

## API 路由规范
```javascript
// 资源型路由
GET    /api/orders          // 获取订单列表
POST   /api/orders          // 创建订单
GET    /api/orders/:id      // 获取单个订单
PUT    /api/orders/:id      // 更新订单
DELETE /api/orders/:id      // 删除订单

// 功能型路由
POST   /api/orders/:id/assign    // 分配订单
POST   /api/orders/:id/complete  // 完成订单
POST   /api/orders/:id/cancel    // 取消订单
```

## 请求格式规范
```javascript
// GET 请求 - 查询参数
GET /api/orders?page=1&limit=20&status=pending&keyword=家电

// POST 请求 - JSON 格式
POST /api/orders
Content-Type: application/json
{
  "category_id": 1,
  "user_address": "北京市朝阳区",
  "appointment_time": "2024-01-15 14:00:00",
  "items": [
    {
      "name": "洗衣机",
      "brand": "海尔",
      "model": "XQG80-B1226"
    }
  ]
}
```

## 响应格式规范
```javascript
// 成功响应
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "order_no": "ORD20240115001",
    "status": "pending",
    "created_at": "2024-01-15T10:00:00Z"
  }
}

// 分页响应
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [...],
    "pagination": {
      "current": 1,
      "pageSize": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}

// 错误响应
{
  "code": 400,
  "message": "参数错误",
  "error": "validation_failed",
  "details": {
    "field": "category_id",
    "message": "分类ID不能为空"
  }
}
```

## 核心数据模型

### 订单模型 (Order)
```javascript
{
  id: number,                    // 订单ID
  order_no: string,              // 订单编号
  user_id: number,               // 用户ID
  category_id: number,           // 分类ID
  status: string,                // 订单状态
  appointment_time: datetime,    // 预约时间
  address: string,               // 回收地址
  contact_name: string,          // 联系人
  contact_phone: string,         // 联系电话
  total_price: decimal,          // 总价格
  worker_id: number,             // 师傅ID
  company_id: number,            // 公司ID
  images: array,                 // 图片列表
  remark: string,                // 备注
  created_at: datetime,          // 创建时间
  updated_at: datetime           // 更新时间
}
```

### 用户模型 (User)
```javascript
{
  id: number,
  username: string,
  phone: string,
  avatar: string,
  real_name: string,
  address: array,               // 地址列表
  status: string,               // 用户状态
  created_at: datetime,
  updated_at: datetime
}
```

### 师傅模型 (Worker)
```javascript
{
  id: number,
  name: string,
  phone: string,
  avatar: string,
  company_id: number,
  area_ids: array,              // 服务区域
  status: string,               // 师傅状态
  rating: decimal,              // 评分
  order_count: number,          // 订单数量
  created_at: datetime,
  updated_at: datetime
}
```

## 数据验证规则
```javascript
// 订单创建验证
const orderValidation = {
  category_id: 'required|integer',
  user_address: 'required|string|max:200',
  contact_name: 'required|string|max:50',
  contact_phone: 'required|regex:/^1[3-9]\d{9}$/',
  appointment_time: 'required|date|after:now',
  items: 'required|array|min:1'
};
```

## 状态枚举定义
```javascript
// 订单状态
const ORDER_STATUS = {
  PENDING: 'pending',           // 待分配
  ASSIGNED: 'assigned',         // 已分配
  IN_PROGRESS: 'in_progress',   // 进行中
  COMPLETED: 'completed',       // 已完成
  CANCELLED: 'cancelled'        // 已取消
};

// 用户状态
const USER_STATUS = {
  ACTIVE: 'active',            // 正常
  INACTIVE: 'inactive',        // 停用
  BANNED: 'banned'             // 封禁
};
```

## 关键文件路径
- 后端模型：[recycle/server/app/Models/](mdc:recycle/server/app/Models/)
- 前端模型：[recycle/admin/src/models/](mdc:recycle/admin/src/models/)
- API 服务：[recycle/admin/src/services/](mdc:recycle/admin/src/services/)
- 控制器：[recycle/server/app/Controllers/](mdc:recycle/server/app/Controllers/)

## API 权限控制
- JWT Token 认证
- 角色和权限验证
- API 访问频率限制
- 敏感操作审计日志