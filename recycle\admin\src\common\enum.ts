export enum UserState {
  loading = 'loading',
  login = 'login',
  autherized = 'autherized',
}

export const WorkerTypes = ['初级', '中级', '高级', '管理', '超级管理员']
export const Permission = {
  Primary: '初级', //可管理 订单
  Intermediate: '中级', //可管理 订单 价格 佣金
  Senior: '高级', //可管理 回收人员 服务商 订单 价格 佣金
}
export const WorkerLevels = ['初级', '中级', '高级']
export const TypeLevels = ['电视', '冰箱', '洗衣机', '空调柜机', '空调挂机', '燃气热水器', '电热水器', '空气热泵热水器']
export const ClientTypeLevels = ['电视', '冰箱', '洗衣机', '窗机', '柜机', '挂机', '燃气热水器', '电热水器', '空气热泵热水器']

export const Areas = ['浙江省', '江苏省', '安徽省', '合肥市', '广东省', '甘肃省','内蒙古自治区','宁夏回族自治区']
export const AreasEnum = {
  浙江省: { text: '浙江省', status: 'Processing' },
  江苏省: { text: '江苏省', status: 'Processing' },
  安徽省: { text: '安徽省', status: 'Processing' },
  广东省: { text: '广东省', status: 'Processing' },
  揭阳市: { text: '揭阳市', status: 'Processing' },
  韶关市: { text: '韶关市', status: 'Processing' },
  珠海市: { text: '珠海市', status: 'Processing' },
  汕头市: { text: '汕头市', status: 'Processing' },
  佛山市: { text: '佛山市', status: 'Processing' },
  湛江市: { text: '湛江市', status: 'Processing' },
  茂名市: { text: '茂名市', status: 'Processing' },
  肇庆市: { text: '肇庆市', status: 'Processing' },
  惠州市: { text: '惠州市', status: 'Processing' },
  梅州市: { text: '梅州市', status: 'Processing' },
  汕尾市: { text: '汕尾市', status: 'Processing' },
  河源市: { text: '河源市', status: 'Processing' },
  阳江市: { text: '阳江市', status: 'Processing' },
  清远市: { text: '清远市', status: 'Processing' },
  云浮市: { text: '云浮市', status: 'Processing' },
  广州市: { text: '广州市', status: 'Processing' },
  东莞市: { text: '东莞市', status: 'Processing' },
  深圳市: { text: '深圳市', status: 'Processing' },
  中山市: { text: '中山市', status: 'Processing' },
  合肥市: { text: '合肥市', status: 'Default' },
  内蒙古自治区: { text: '内蒙古自治区', status: 'Default' },
  宁夏回族自治区: { text: '宁夏回族自治区', status: 'Default' },
  甘肃省: { text: '甘肃省', status: 'Default' },
}
export const SourceLevels = ['菜鸟回收', '京东回收', '嗨回收', '冬瓜回收', '其他']
export const AreaList = ['浙江省', '江苏省', '北京市', '上海市', '安徽省', '甘肃省', '广东省','内蒙古自治区','宁夏回族自治区']
export const IsYes = ['是', '否']
export const DiscoveryType = {
  WELFARE: 'welfare',
  ACTIVITIES: 'activities',
  MEDIA: 'media',
}
export const CBannerType = {
  AD: 'ad',
  MARQUEE: 'marquee',
  CATEGORY: 'category',
}
export const BasicHave = [
  {
    title: '订单管理',
    key: '订单管理',
    children: [
      { title: '订单查看', key: '订单查看' },
      { title: '订单操作', key: '订单操作' },
    ],
  },
  {
    title: '账户与权限管理',
    key: '账户与权限管理',
    children: [
      { title: '账户的查看', key: '账户的查看' },
      { title: '账户新建与编辑', key: '账户新建与编辑' },
      { title: '权限编辑', key: '权限编辑' },
    ],
  },
  {
    title: '业务数据总览',
    key: '业务数据总览',
    children: [
      { title: '数据查询', key: '数据查询' },
      { title: '数据导出', key: '数据导出' },
    ],
  },
  {
    title: '短信管理',
    key: '短信管理',
    children: [
      { title: '数据查询', key: '数据查询' },
      { title: '数据导出', key: '数据导出' },
    ],
  },
  {
    title: '数据看板',
    key: '数据看板',
    children: [
      { title: '数据查询', key: '数据查询' },
      { title: '数据导出', key: '数据导出' },
    ],
  },
  {
    title: '佣金管理',
    key: '佣金管理',
    children: [
      { title: '佣金查看', key: '佣金查看' },
      { title: '佣金编辑', key: '佣金编辑' },
    ],
  },
  {
    title: '押金管理',
    key: '押金管理',
    children: [
      { title: '押金查看', key: '押金查看' },
      { title: '押金编辑', key: '押金编辑' },
    ],
  },
  {
    title: '招募管理',
    key: '招募管理',
    children: [
      { title: '招募查看', key: '招募查看' },
      { title: '招募新建与编辑', key: '招募新建与编辑' },
    ],
  },
  {
    title: '服务商管理',
    key: '服务商管理',
    children: [
      { title: '服务商查看', key: '服务商查看' },
      { title: '服务商新建与编辑', key: '服务商新建与编辑' },
    ],
  },
  {
    title: '回收人员管理',
    key: '回收人员管理',
    children: [
      { title: '回收人员查看', key: '回收人员查看' },
      { title: '回收人员编辑', key: '回收人员编辑' },
      { title: '回收人员审核', key: '回收人员审核' },
    ],
  },
  {
    title: '最低充值管理',
    key: '最低充值管理',
    children: [
      { title: '数据查询', key: '数据查询' },
      { title: '数据导出', key: '数据导出' },
    ],
  },
]
export const orderSources = ['菜鸟回收', '京东回收', '嗨回收']
export const OrderStatus = {
  Waiting: '待处理',
  Dispatched: '已派单',
  Processing: '进行中',
  Finish: '已完成',
  Cancel: '已取消',
}
export const WorkerAccountStatus = {
  Init: '初始',
  Agree: '同意',
  Reject: '拒绝',
}
export const AdminLevel = {
  总部: '总部',
  服务商: '服务商',
}
export const OrderFrom = {
  菜鸟回收: '菜鸟回收',
  京东回收: '京东回收',
  嗨回收: '嗨回收',
  电话下单: '电话下单',
  二维码跳转下单: '二维码跳转下单',
}
export const CancleOrderStatus = {
  Client: '客户取消',
  Master: '师傅取消',
  MasterRevoke: '师傅撤销',
  Admin: '后台取消',
}
export const pageStatus = {
  ALL: '全部',
  Init: '初始化',
  Reservation: '预订',
  Pending: '待处理',
  MasterReturn: '师傅撤回',
  SystemReturn: '系统撤回',
  TransferOrder: '转派',
  Dispatched: '已派单',
  InProgress: '进行中',
  Cancelled: '取消',
  Hacker: '黑客攻击',
  Completed: '完成',
}
export const PageName = {
  TableList: '/TableList',
  CRUD: '/CRUD',
  Welcome: '/Welcome',
  Commission: '/Commission',
  StoreOrder: '/StoreOrder',
  Home: '/Home',
  Login: '/Login',
  OverView: '/OverView/:id',
  Analysis: '/Analysis',
  DashBorad: '/DashBorad',
  ProForm: '/ProForm',
  WorkersManageFolder: '/WorkersManage',
  AreaEdit: '/WorkersManage/AreaEdit/:id',
  WorkerArea: '/WorkersManage/WorkerArea/:id',
  WorkerManage: '/workerManage',

  OrderManageFolder: '/OrderManage',
  OrderManage: '/OrderManage/OrderManage',
  FiveWastes: '/OrderManage/FiveWastes',
  BigObject: '/OrderManage/BigObject',
  Furniture: '/OrderManage/Furniture',
  ClientOrder: '/ClientManage/ClientOrder/:id',
  ClientPrice: '/ClientManage/ClientPrice',
  AgentDistribution: '/ClientManage/AgentDistribution',
  CendInfo: '/ClientManage/CendInfo',
  PublicWelfare: '/ClientManage/PublicWelfare',
  CBanner: '/ClientManage/CBanner',
  ClientManage: '/ClientManage',
  Other: '/Other',
  Company: '/Other/Company',
  SmsManager: '/Other/SmsManager',
  PriceManage: '/Other/PriceManage',
  PayManage: '/Other/PayManage',
  OtherOrder: '/Other/OtherOrder',
  Permission: '/Other/Permission',
  CompanyArea: '/Other/CompanyArea/:id',
  CompanyEdit: '/Other/CompanyEditor/:id',
  YCManager: '/YCManager',
  YCinfofee: '/YCManager/YCinfofee',
  YCWorkersManage: '/YCManager/YCWorkersManage',
  YCOrderManage: '/YCManager/YCOrderManage',
  YCPriceManage: '/YCManager/YCPriceManage',
  JDManager: '/JDManager',
  JDinfofee: '/JDManager/JDinfofee',
  JDRecycleInfofee: '/JDManager/JDRecycleInfofee',
  JDOrderManage: '/JDManager/JDOrderManage',
  JDWorkersManage: '/JDManager/JDWorkersManage',
  JDRecycleOrderManage: '/JDManager/JDRecycleOrderManage',
  JDPriceManage: '/JDManager/JDPriceManage',
  HaiRecycleManager: '/HaiRecycleManager',
  HaiRecycleInfofee: '/HaiRecycleManager/HaiRecycleInfofee',
  HaiRecycleWorkersManage: '/HaiRecycleManager/HaiRecycleWorkersManage',
  HaiRecycleOrderManage: '/HaiRecycleManager/HaiRecycleOrderManage',
  HaiRecyclePriceManage: '/HaiRecycleManager/HaiRecyclePriceManage',
  JoinTeam: '/JoinTeam',
}
export const statusEnum = {
  all: { text: '全部', status: 'Default' },
  完成: { text: '完成', status: 'Success' },
  待支付: { text: '待支付', status: 'Processing' },
}
export const recycleType = [
  {
    type: 1,
    typeName: '旧衣旧书',
  },
  {
    type: 2,
    typeName: '生活五废',
  },
  {
    type: 3,
    typeName: '大家电',
  },
  {
    type: 4,
    typeName: '大家具',
  },
]
export const recycleTypeApp = [
  {
    type: 1,
    typeName: '旧衣旧书',
  },
  {
    type: 2,
    typeName: '生活五废',
  },
  {
    type: 4,
    typeName: '大家具',
  },
  {
    type: 3,
    typeName: '电视',
    id: '15',
  },
  {
    type: 3,
    typeName: '空调',
    id: '16',
  },
  {
    type: 3,
    typeName: '冰箱',
    id: '17',
  },
  {
    type: 3,
    typeName: '电脑',
    id: '18',
  },
  {
    type: 3,
    typeName: '洗衣机',
    id: '19',
  },
]
export const provinceEnum = {
  all: { text: '全部', status: 'Default' },
  '31': { text: '上海市' },
  '12': { text: '天津市' },
  '11': { text: '北京市' },
  '32': { text: '江苏省' },
  '33': { text: '浙江省' },
  '34': { text: '安徽省' },
  '44': { text: '广东省' },
  '65': { text: '内蒙古自治区' },
  '64': { text: '宁夏回族自治区' },
  '62': { text: '甘肃省' },
}
export const provinceList = [
  {
    "name": "北京市",
    "code": "11"
  },
  {
    "name": "天津市",
    "code": "12"
  },
  {
    "name": "上海市",
    "code": "31"
  },
  {
    "name": "江苏省",
    "code": "32"
  },
  {
    "name": "浙江省",
    "code": "33"
  },
  {
    "name": "安徽省",
    "code": "34"
  },
  {
    "name": "内蒙古自治区",
    "code": "65"
  },
  {
    "name": "宁夏回族自治区",
    "code": "64"
  },
  {
    "name": "甘肃省",
    "code": "62"
  },
  {
    "name": "福建省",
    "code": "35"
  },
  {
    "name": "四川省",
    "code": "51"
  },
  {
    "name": "山东省",
    "code": "37"
  },
  {
    "name": "河南省",
    "code": "41"
  },
  {
    "name": "湖北省",
    "code": "42"
  },
  {
    "name": "湖南省",
    "code": "43"
  },
  {
    "name": "广东省",
    "code": "44"
  },
  {
    "name": "广西壮族自治区",
    "code": "45"
  },
]

export const typeEnum = {
  all: { text: '全部', status: 'Default' },
  用户: { text: '用户', status: 'Success' },
  系统: { text: '系统', status: 'Processing' },
  师傅: { text: '师傅', status: 'Default' },
}
export const fromEnum = {
  菜鸟回收: { text: '菜鸟回收', status: 'Success' },
  京东回收: { text: '京东回收', status: 'Processing' },
  嗨回收: { text: '嗨回收', status: 'Processing' },
  冬瓜回收: { text: '冬瓜回收', status: 'Processing' },
}
export const priceTypeEnum = {
  rate: { text: '比例价', status: 'Success' },
  fixed: { text: '固定价', status: 'Processing' },
}

export const masterStatusEnum = {
  1: { text: '启用', status: 'Success' },
  0: { text: '禁用', status: 'Default' },
}
export const orderStatusEnum = {
  // 预订: { text: '预订', status: 'Default' },
  待处理: { text: '待处理', status: 'Processing' },
  师傅撤回: { text: '师傅撤回', status: 'Processing' },
  系统撤回: { text: '系统撤回', status: 'Processing' },
  已派单: { text: '已派单', status: 'Processing' },
  进行中: { text: '进行中', status: 'Default' },
  完成: { text: '完成', status: 'Success' },
  取消: { text: '取消', status: 'Success' },
}
// export const recycleTypeEnum = {
//   '1': { text: '旧衣旧书', status: 'Default' },
//   '2': { text: '生活废品', status: 'Success' },
//   '3': { text: '大家电', status: 'Processing' },
//   '4': { text: '大家具', status: 'Processing' },
// }

export const recycleTypeEnum = {
  '电视': { text: '电视', status: 'Default' },
  '空调柜机': { text: '空调柜机', status: 'Success' },
  '空调挂机': { text: '空调挂机', status: 'Processing' },
  '冰箱': { text: '冰箱', status: 'Processing' },
  '洗衣机': { text: '洗衣机', status: 'Processing' },
  '燃气热水器': { text: '燃气热水器', status: 'Processing' },
  '电热水器': { text: '电热水器', status: 'Processing' },
  '空气热泵热水器': { text: '空气热泵热水器', status: 'Processing' },
}
export const ClientTypeEnum = {
  '电视': { text: '电视', status: 'Default' },
  '冰箱': { text: '冰箱', status: 'Processing' },
  '洗衣机': { text: '洗衣机', status: 'Processing' },
  '窗机': { text: '窗机', status: 'Processing' },
  '柜机': { text: '柜机', status: 'Processing' },
  '挂机': { text: '挂机', status: 'Processing' },
  '燃气热水器': { text: '燃气热水器', status: 'Processing' },
  '电热水器': { text: '电热水器', status: 'Processing' },
  '空气热泵热水器': { text: '空气热泵热水器', status: 'Processing' },
}


export const typeList = ['冰箱', '电视', '洗衣机', '空调', '空调柜机', '空调挂机', '空气热泵热水器', '燃气热水器', '电热水器', '壁挂炉', '洗碗机', '烟灶']


export const PriceTypeEnum = {
  纯回收: { text: '纯回收', status: 'Success' },
  不同步: { text: '不同步', status: 'Default' },
}

