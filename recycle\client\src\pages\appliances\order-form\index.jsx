import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { View, Text, Button, Input, Picker, Map } from '@tarojs/components'
import Taro from '@tarojs/taro'
import './index.less'
import { NPublics } from '../../../config/constants'
import { useDispatch } from 'react-redux'
import CitySelector from '../../../components/citySelector/index'

/**
 * 家电回收订单表单页面
 * 支持用户填写回收订单信息，包括联系方式、地址选择、预约时间等
 */
const OrderForm = () => {
  const dispatch = useDispatch()
  
  // 回收物品信息（从上一页传递过来）
  const [recycleData, setRecycleData] = useState({
    type: '',           // 回收类型
    category: '',       // 物品类别  
    subType: '',        // 机器类型
    spu: '',           // 规格型号
    estimatedPrice: '', // 预估价格
    brand: '',         // 品牌
    model: '',         // 型号
    condition: '',     // 物品状态
    specs: {}          // 物品规格参数
  })
  
  // 统一的订单表单数据结构
  const [orderForm, setOrderForm] = useState({
    // 联系信息
    contact: {
      name: '',           // 联系人姓名
      phone: '',          // 联系电话
      alternatePhone: ''  // 备用电话（可选）
    },
    
    // 地址信息
    address: {
      province: '',       // 省份
      city: '',          // 城市
      district: '',      // 区县
      street: '',        // 街道
      detail: '',        // 详细地址
      fullAddress: '',   // 完整地址显示
      latitude: '',      // 纬度
      longitude: '',     // 经度
    },
    
    // 预约信息
    appointment: {
      date: '',          // 预约日期 YYYY-MM-DD
      dateDisplay: '',   // 日期显示文本
      timeSlot: '',      // 预约时间段
      workTime: ''       // 后端期望的完整时间格式
    },
    
    // 补充信息
    additional: {
      remark: '',           // 备注信息
      needAssistance: false, // 是否需要协助搬运
      urgentLevel: 'normal',  // 紧急程度: normal, urgent, very_urgent
      floorCount: '',       // 楼层数
      needClimbing: false,  // 需要登高
      isUrgent: false       // 加急
    }
  })
  
  // 表单验证错误信息
  const [errors, setErrors] = useState({})
  
  // 控制城市选择器显示状态
  const [citySelectorVisible, setCitySelectorVisible] = useState(false)
  
  // 地图定位状态
  const [location, setLocation] = useState({
    latitude: 39.90469,
    longitude: 116.40717,
    markers: []
  })
  
  // 生成未来2周的日期选项（从明天开始）
  const dateOptions = useMemo(() => {
    const options = []
    const today = new Date()
    
    // 从明天开始，共14天（2周）
    for (let i = 1; i <= 14; i++) {
      const date = new Date(today)
      date.setDate(today.getDate() + i)
      
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const weekday = weekdays[date.getDay()]
      
      let label = `${month}月${day}日 ${weekday}`
      if (i === 1) label = '明天 ' + label
      else if (i === 2) label = '后天 ' + label
      
      options.push({
        label,
        value: `${year}-${month}-${day}`
      })
    }
    
    return options
  }, [])
  
  // 时间段选项
  const timeSlots = [
    { label: '上午 9:00-12:00', value: '09:00-12:00' },
    { label: '下午 13:00-16:00', value: '13:00-16:00' }, 
    { label: '晚上 17:00-20:00', value: '17:00-20:00' }
  ]

  // 初始化页面数据
  useEffect(() => {
    initializePageData()
  }, [])

  /**
   * 初始化页面数据
   */
  const initializePageData = async () => {
    try {
      // 读取回收表单数据
      const recycleFormData = Taro.getStorageSync('recycleForm')
      if (recycleFormData) {
        setRecycleData(JSON.parse(recycleFormData))
      }
      
      // 读取之前保存的订单信息
      const savedOrderInfo = Taro.getStorageSync('savedOrderInfo')
      if (savedOrderInfo) {
        const parsedOrderInfo = JSON.parse(savedOrderInfo)
        updateOrderFormFromSaved(parsedOrderInfo)
      }
    } catch (error) {
      console.error('初始化页面数据失败:', error)
      Taro.showToast({
        title: '数据加载失败',
        icon: 'none'
      })
    }
  }

  /**
   * 从保存的数据更新表单
   */
  const updateOrderFormFromSaved = (savedData) => {
    setOrderForm(prevForm => ({
      ...prevForm,
      contact: {
        name: savedData.name || '',
        phone: savedData.phone || '',
        alternatePhone: savedData.alternatePhone || ''
      },
      address: {
        ...prevForm.address,
        province: savedData.province || '',
        city: savedData.city || '',
        district: savedData.district || '',
        detail: savedData.useraddress || savedData.detail || '',
        latitude: savedData.latitude || '',
        longitude: savedData.longitude || ''
      },
      additional: {
        ...prevForm.additional,
        remark: savedData.remark || '',
        needAssistance: savedData.needAssistance || false,
        floorCount: savedData.floorCount || '',
        needClimbing: savedData.needClimbing || false,
        isUrgent: savedData.isUrgent || false
      }
    }))
  }
  
  /**
   * 处理城市选择
   */
  const handleCitySelect = useCallback((fullAddress) => {
    const addressParts = fullAddress.split(' ')
    
    setOrderForm(prevForm => ({
      ...prevForm,
      address: {
        ...prevForm.address,
        province: addressParts[0] || '',
        city: addressParts[1] || '',
        district: addressParts[2] || '',
        street: addressParts[3] || '',
        fullAddress: fullAddress
      }
    }))
    
    setCitySelectorVisible(false)
    
    // 清除地址相关错误
    setErrors(prevErrors => ({
      ...prevErrors,
      'address.city': '',
      'address.fullAddress': ''
    }))
  }, [])
  


  /**
   * 生成自动备注信息
   */
  const generateAutoRemark = useCallback((additionalData, manualRemark = '') => {
    const autoRemarks = []
    
    // 添加楼层信息
    if (additionalData.floorCount) {
      autoRemarks.push(`楼层数：${additionalData.floorCount}层`)
    }
    
    // 添加登高需求
    if (additionalData.needClimbing) {
      autoRemarks.push('需要登高：是')
    }
    
    // 添加加急处理
    if (additionalData.isUrgent) {
      autoRemarks.push('加急：是')
    }
    
    // 添加协助搬运
    if (additionalData.needAssistance) {
      autoRemarks.push('需要协助搬运')
    }
    
    // 组合自动备注和手动备注
    const combinedRemark = [...autoRemarks, manualRemark].filter(Boolean).join('；')
    return combinedRemark
  }, [])

  /**
   * 统一的表单输入处理
   */
  const handleFormInput = useCallback((field, value) => {
    const fieldPath = field.split('.')
    
    setOrderForm(prevForm => {
      const newForm = { ...prevForm }
      
      if (fieldPath.length === 2) {
        newForm[fieldPath[0]] = {
          ...newForm[fieldPath[0]],
          [fieldPath[1]]: value
        }
      } else {
        newForm[field] = value
      }
      
      // 如果修改的是additional相关字段（除了remark），自动更新备注
      if (fieldPath[0] === 'additional' && fieldPath[1] !== 'remark') {
        // 保留用户手动输入的备注部分
        const manualRemark = newForm.additional.remark.split('；').find(item => 
          !item.includes('楼层数：') && 
          !item.includes('需要登高：') && 
          !item.includes('加急：') && 
          !item.includes('需要协助搬运')
        ) || ''
        
        newForm.additional.remark = generateAutoRemark(newForm.additional, manualRemark)
      }
      
      return newForm
    })
    
    // 清除该字段的错误信息
    setErrors(prevErrors => ({
      ...prevErrors,
      [field]: ''
    }))
    
    // 实时保存表单数据
    saveOrderFormToLocal()
  }, [generateAutoRemark])
  
  /**
   * 保存订单表单到本地存储
   */
  const saveOrderFormToLocal = useCallback(() => {
    try {
      const dataToSave = {
        name: orderForm.contact.name,
        phone: orderForm.contact.phone,
        alternatePhone: orderForm.contact.alternatePhone,
        province: orderForm.address.province,
        city: orderForm.address.city,
        district: orderForm.address.district,
        street: orderForm.address.street,
        detail: orderForm.address.detail,
        latitude: orderForm.address.latitude,
        longitude: orderForm.address.longitude,
        remark: orderForm.additional.remark,
        needAssistance: orderForm.additional.needAssistance,
        floorCount: orderForm.additional.floorCount,
        needClimbing: orderForm.additional.needClimbing,
        isUrgent: orderForm.additional.isUrgent
      }
      Taro.setStorageSync('savedOrderInfo', JSON.stringify(dataToSave))
    } catch (error) {
      console.error('保存表单信息失败:', error)
    }
  }, [orderForm])

  /**
   * 处理日期选择
   */
  const handleDateChange = useCallback((e) => {
    const index = e.detail.value
    const selectedDate = dateOptions[index]
    
    setOrderForm(prevForm => ({
      ...prevForm,
      appointment: {
        ...prevForm.appointment,
        date: selectedDate.value,
        dateDisplay: selectedDate.label,
        workTime: `${selectedDate.value} ${prevForm.appointment.timeSlot}`
      }
    }))
    
    // 清除日期错误
    setErrors(prevErrors => ({
      ...prevErrors,
      'appointment.date': ''
    }))
  }, [dateOptions])

  /**
   * 处理时间选择
   */
  const handleTimeChange = useCallback((e) => {
    const index = e.detail.value
    const selectedTime = timeSlots[index]
    
    setOrderForm(prevForm => ({
      ...prevForm,
      appointment: {
        ...prevForm.appointment,
        timeSlot: selectedTime.label,
        workTime: `${prevForm.appointment.date} ${selectedTime.value}`
      }
    }))
    
    // 清除时间错误
    setErrors(prevErrors => ({
      ...prevErrors,
      'appointment.timeSlot': ''
    }))
  }, [])

  /**
   * 表单验证
   */
  const validateForm = () => {
    const newErrors = {}
    
    // 联系信息验证
    if (!orderForm.contact.name?.trim()) {
      newErrors['contact.name'] = '请输入联系人姓名'
    }
    
    if (!orderForm.contact.phone?.trim()) {
      newErrors['contact.phone'] = '请输入联系电话'
    } else if (!/^1[3-9]\d{9}$/.test(orderForm.contact.phone)) {
      newErrors['contact.phone'] = '请输入正确的手机号码'
    }
    
    // 地址信息验证
    if (!orderForm.address.city?.trim()) {
      newErrors['address.city'] = '请选择所在城市'
    }
    
    if (!orderForm.address.detail?.trim()) {
      newErrors['address.detail'] = '请输入详细地址'
    }
    
    // 预约信息验证
    if (!orderForm.appointment.date) {
      newErrors['appointment.date'] = '请选择预约日期'
    }
    
    if (!orderForm.appointment.timeSlot) {
      newErrors['appointment.timeSlot'] = '请选择预约时间段'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  /**
   * 提交订单
   */
  const submitOrder = async () => {
    // 表单验证
    if (!validateForm()) {
      Taro.showToast({
        title: '请完善必填信息',
        icon: 'none'
      })
      return
    }

    try {
      Taro.showLoading({ title: '提交中...' })
      
      // 组合订单数据
      const orderData = {
        // 回收物品信息
        ...recycleData,
        
        // 联系信息
        name: orderForm.contact.name,
        phone: orderForm.contact.phone,
        alternatePhone: orderForm.contact.alternatePhone,
        
        // 地址信息
        province: orderForm.address.province,
        city: orderForm.address.city,
        district: orderForm.address.district,
        street: orderForm.address.street,
        useraddress: orderForm.address.detail, // 兼容后端字段名
        fullAddress: orderForm.address.fullAddress,
        latitude: orderForm.address.latitude,
        longitude: orderForm.address.longitude,
        
        // 预约信息
        appointmentDate: orderForm.appointment.date,
        appointmentTime: orderForm.appointment.timeSlot,
        workTime: orderForm.appointment.workTime, // 后端期望字段
        
        // 补充信息
        remark: orderForm.additional.remark,
        needAssistance: orderForm.additional.needAssistance,
        urgentLevel: orderForm.additional.urgentLevel,
        floorCount: orderForm.additional.floorCount,
        needClimbing: orderForm.additional.needClimbing,
        isUrgent: orderForm.additional.isUrgent,
        
        // 系统信息
        orderTime: new Date().toISOString(),
        source: 'miniprogram' // 订单来源
      }

      console.log('提交订单数据:', orderData)
      
      // 调用API提交订单
      const response = await dispatch.NPublics[NPublics.EPostCreateOrder](orderData)
      console.log('订单创建响应:', response)
      
      Taro.hideLoading()
      
      // 保存表单信息供下次使用
      saveOrderFormToLocal()
      
      Taro.showToast({
        title: '订单提交成功',
        icon: 'success',
        duration: 2000,
        complete: () => {
          // 跳转到订单成功页面
          Taro.navigateTo({ 
            url: `/pages/appliances/order-success/index?orderId=${response.orderId || ''}` 
          })
        }
      })
    } catch (error) {
      Taro.hideLoading()
      console.error('订单提交失败:', error)
      Taro.showToast({
        title: error.message || '订单提交失败，请重试',
        icon: 'none'
      })
    }
  }

  return (
    <View className='order-form'>
      <View className='header'>
        <Button className='back-btn' onClick={() => Taro.navigateBack()}>←</Button>
        <View className='header-title'>填写订单信息</View>
      </View>

      <View className='content'>
        {/* 回收信息摘要 */}
        <View className='summary-card'>
          <View className='summary-title'>回收信息确认</View>
          <View className='summary-item'>
            <Text className='label'>回收类型</Text>
            <Text className='value'>{recycleData.type || '-'}</Text>
          </View>
          <View className='summary-item'>
            <Text className='label'>物品类别</Text>
            <Text className='value'>{recycleData.category || '-'}</Text>
          </View>
          <View className='summary-item'>
            <Text className='label'>机器类型</Text>
            <Text className='value'>{recycleData.subType || '-'}</Text>
          </View>
          <View className='summary-item'>
            <Text className='label'>规格型号</Text>
            <Text className='value'>{recycleData.spu || '-'}</Text>
          </View>
          <View className='summary-item'>
            <Text className='label'>预估价格</Text>
            <Text className='value price'>{recycleData.estimatedPrice || '¥ 0'}</Text>
          </View>
        </View>
        
        {/* 城市选择器组件 */}
        <CitySelector 
          visible={citySelectorVisible}
          onClose={() => setCitySelectorVisible(false)}
          onSelect={handleCitySelect}
          currentCity={orderForm.address.city}
        />

        {/* 联系信息 */}
        <View className='form-section'>
          <View className='section-title'>联系信息</View>
          
          <View className='form-group'>
            <Text className='form-label'>联系人姓名 <Text className='required'>*</Text></Text>
            <Input 
              className={`form-input ${errors['contact.name'] ? 'error' : ''}`}
              placeholder='请输入联系人姓名'
              value={orderForm.contact.name}
              onInput={e => handleFormInput('contact.name', e.detail.value)}
            />
            {errors['contact.name'] && (
              <Text className='error-text'>{errors['contact.name']}</Text>
            )}
          </View>
          
          <View className='form-group'>
            <Text className='form-label'>联系电话 <Text className='required'>*</Text></Text>
            <Input 
              className={`form-input ${errors['contact.phone'] ? 'error' : ''}`}
              type='number'
              maxLength={11}
              placeholder='请输入手机号码'
              value={orderForm.contact.phone}
              onInput={e => handleFormInput('contact.phone', e.detail.value)}
            />
            {errors['contact.phone'] && (
              <Text className='error-text'>{errors['contact.phone']}</Text>
            )}
          </View>
          
          <View className='form-group'>
            <Text className='form-label'>备用电话</Text>
            <Input 
              className='form-input'
              type='number'
              maxLength={11}
              placeholder='请输入备用联系电话（可选）'
              value={orderForm.contact.alternatePhone}
              onInput={e => handleFormInput('contact.alternatePhone', e.detail.value)}
            />
          </View>
        </View>

        {/* 地址信息 */}
        <View className='form-section'>
          <View className='section-title'>地址信息</View>
          
          <View className='form-group'>
            <Text className='form-label'>所在地区 <Text className='required'>*</Text></Text>
            <View 
              className={`location-selector ${errors['address.city'] ? 'error' : ''}`} 
              onClick={() => setCitySelectorVisible(true)}
            >
              <Text className='location-text'>
                {orderForm.address.fullAddress || '点击选择所在地区'}
              </Text>
              <Text className='location-arrow'>▼</Text>
            </View>
            {errors['address.city'] && (
              <Text className='error-text'>{errors['address.city']}</Text>
            )}
          </View>
          

          
          <View className='form-group'>
            <Text className='form-label'>详细地址 <Text className='required'>*</Text></Text>
            <Input 
              className={`form-input ${errors['address.detail'] ? 'error' : ''}`}
              placeholder='请输入详细地址，如：XX路XX号XX小区XX栋XX室'
              value={orderForm.address.detail}
              onInput={e => handleFormInput('address.detail', e.detail.value)}
            />
            {errors['address.detail'] && (
              <Text className='error-text'>{errors['address.detail']}</Text>
            )}
          </View>
        </View>

        {/* 预约信息 */}
        <View className='form-section'>
          <View className='section-title'>预约信息</View>
          
          <View className='form-group'>
            <Text className='form-label'>预约日期 <Text className='required'>*</Text></Text>
            <Picker
              mode='selector'
              range={dateOptions}
              rangeKey='label'
              onChange={handleDateChange}
            >
              <View className={`picker-view ${errors['appointment.date'] ? 'error' : ''}`}>
                {orderForm.appointment.dateDisplay || '请选择上门日期'}
                <View className='picker-arrow'>▼</View>
              </View>
            </Picker>
            {errors['appointment.date'] && (
              <Text className='error-text'>{errors['appointment.date']}</Text>
            )}
          </View>

          <View className='form-group'>
            <Text className='form-label'>预约时间 <Text className='required'>*</Text></Text>
            <Picker
              mode='selector'
              range={timeSlots}
              rangeKey='label'
              onChange={handleTimeChange}
            >
              <View className={`picker-view ${errors['appointment.timeSlot'] ? 'error' : ''}`}>
                {orderForm.appointment.timeSlot || '请选择上门时间段'}
                <View className='picker-arrow'>▼</View>
              </View>
            </Picker>
            {errors['appointment.timeSlot'] && (
              <Text className='error-text'>{errors['appointment.timeSlot']}</Text>
            )}
          </View>
        </View>

        {/* 补充信息 */}
        <View className='form-section'>
          <View className='section-title'>补充信息</View>
          
          <View className='form-group'>
            <Text className='form-label'>特殊需求</Text>
            <View className='checkbox-group'>
              <View 
                className={`checkbox-item ${orderForm.additional.needAssistance ? 'checked' : ''}`}
                onClick={() => handleFormInput('additional.needAssistance', !orderForm.additional.needAssistance)}
              >
                <Text>需要协助搬运</Text>
              </View>
              <View 
                className={`checkbox-item ${orderForm.additional.needClimbing ? 'checked' : ''}`}
                onClick={() => handleFormInput('additional.needClimbing', !orderForm.additional.needClimbing)}
              >
                <Text>需要登高</Text>
              </View>
              <View 
                className={`checkbox-item ${orderForm.additional.isUrgent ? 'checked' : ''}`}
                onClick={() => handleFormInput('additional.isUrgent', !orderForm.additional.isUrgent)}
              >
                <Text>加急处理</Text>
              </View>
            </View>
          </View>
          
          <View className='form-group'>
            <Text className='form-label'>楼层数</Text>
            <Input 
              className='form-input'
              type='number'
              placeholder='请输入楼层数（可选）'
              value={orderForm.additional.floorCount}
              onInput={e => handleFormInput('additional.floorCount', e.detail.value)}
            />
          </View>
          
          <View className='form-group'>
            <Text className='form-label'>备注信息</Text>
            <Input 
              className='form-input'
              placeholder='其他特殊需求或注意事项（上述选项会自动添加到备注中）'
              value={orderForm.additional.remark}
              onInput={e => {
                // 保留自动生成的备注部分，只更新手动输入部分
                const currentAutoRemarks = orderForm.additional.remark.split('；').filter(item => 
                  item.includes('楼层数：') || 
                  item.includes('需要登高：') || 
                  item.includes('加急：') || 
                  item.includes('需要协助搬运')
                )
                const newManualRemark = e.detail.value
                const combinedRemark = [...currentAutoRemarks, newManualRemark].filter(Boolean).join('；')
                handleFormInput('additional.remark', combinedRemark)
              }}
            />
            {orderForm.additional.remark && (
              <Text className='remark-preview'>当前备注：{orderForm.additional.remark}</Text>
            )}
          </View>
        </View>

        {/* 服务须知 */}
        <View className='tips'>
          <View className='tips-title'>💡 上门取件须知</View>
          <View className='tips-content'>
            • 预约时间最早为明天，最晚可预约2周内<br />
            • 工作人员上门后会重新评估确认价格<br />
            • 请保持回收物品周围环境通畅，便于搬运<br />
            • 如需上楼搬运，建议提前联系确认细节
          </View>
        </View>

        <Button 
          className='submit-btn' 
          onClick={submitOrder}
        >
          提交订单
        </Button>
      </View>
    </View>
  )
}

export default OrderForm
