'use strict'

const _ = require('lodash')
const moment = require('moment')
const Event = use('Event')
const Env = use('Env')

const { Pay, Order, LogExternalApiRequest, User,
  Company, WorkerPay, Worker,
  WorkerWalletLog, WorkerPayRefund, JDWorkerWalletLog,
  AppPay } = require('../../../Models')
const { CryptUtil, XMLUtil } = require('../../../Util')
const Config = require('../../../Util/Config')
const { WXService } = require('../../../Services')
const { ERR, E } = require('../../../../../constants')
const ORDER_ID_PREFIX = 'huge_master_pay_'
const PAY_REFUND_ID_PREFIX = 'huge_master_refund_'
const HOME_PATH = Env.get('HOME_PATH')


//支付记录
class PayController {
  //师傅支付，先支付给商户，再由商户支付给用户
  async index({ request, response }) {
    let { sort = 'desc' } = request.all()
    let vo = await Pay.query().orderBy('id', sort).fetch()
    return vo
  }
  async store({ request, response }) {
    let { worker } = request
    let { openid } = worker
    let { orderID, type = E.PayType.Wechat } = request.all()
    let notify_url = `${HOME_PATH}/master/v1/hook/orderPay`
    let order = await Order.query().where('id', orderID).where('workerID', worker.id).where('status', E.OrderStatus.InProgress).first()
    if (!order) {
      throw ERR.RESTFUL_GET_ID
    } else if (order.actualMoney < 0) {
      throw ERR.UNDEFINED
    }
    let { actualMoney, orderNO, commission, waste_1st_ID } = order
    console.log('orderNO: ', orderNO);
    // 师傅总支付金额为 给用户的金额 + 给商户的佣金
    let totalPay = (actualMoney >= 0 && commission >= 0) ? (actualMoney + commission) : 200
    let payObj = null
    let pay = await Pay.create({ type, workerID: worker.id, userID: order.userID, orderID, rmb: totalPay })
    if (totalPay > 0) {
      // 支付金额需要大于0
      payObj = await WXService.createPay(
        Config.WEAPP.AppID,
        openid,
        totalPay,
        `订单${orderNO}`,
        ORDER_ID_PREFIX + pay.id,
        notify_url
      )
    }
    let payType = ''
    if (totalPay == 0) {
      payType = 'changeSuccess'
    }
    response.json({
      pay,
      payObj,
      payType
    })
  }
  //支付订单回调
  async hookOrderWechatPay({ request, params, response }) {
    let body = await XMLUtil.parseXML(request.raw())
    let { appID } = params
    let { sign, out_trade_no, result_code, transaction_id, total_fee } = body
    delete body.sign
    let PaySecret = Env.get('APP_PARTNER_KEY')
    if (sign !== CryptUtil.wechatSign(body, PaySecret)) {
      console.warn('[hookWechatPay]', sign, CryptUtil.wechatSign(body, PaySecret))
      throw ERR.API_ERROR
    }
    out_trade_no = out_trade_no.substr(ORDER_ID_PREFIX.length)
    let return_code = 'FAIL'
    let pay = await Pay.find(out_trade_no)
    //支付成功
    if (pay && !pay.finishAt && result_code === 'SUCCESS') {
      let OGworker = await Worker.find(pay.toJSON().workerID)
      OGworker.wallet = parseFloat(OGworker.toJSON().wallet) + parseFloat(pay.toJSON().totalPay) / 100
      await OGworker.save()
      await WorkerWalletLog.create({
        workerID: OGworker.toJSON().id, money: parseFloat(pay.toJSON().totalPay) / 100, remark: '充值支付'
      })
      pay.finishAt = new Date()
      pay.transactionID = transaction_id
      await pay.save()
      return_code = 'SUCCESS'
    }
    response.json(XMLUtil.buildXML({ xml: { return_code } }))
    await LogExternalApiRequest.create({
      url: request.originalUrl(),
      headers: JSON.stringify(request.headers()),
      request: request.raw(),
      response: XMLUtil.buildXML({ xml: { return_code } })
    })
  }
  // 退款回调
  async hookRefund({ request, params, response }) {
    let body = await XMLUtil.parseXML(request.raw())
    let { req_info } = body
    let infoXML = CryptUtil.decryptData256(req_info, Env.get('APP_PARTNER_KEY'))
    body = await XMLUtil.parseXML(infoXML)
    let { refund_id, out_trade_no, out_refund_no } = body
    out_trade_no = out_trade_no.substr(ORDER_ID_PREFIX.length)
    out_refund_no = out_refund_no.substr(PAY_REFUND_ID_PREFIX.length)
    console.log(refund_id, out_trade_no, out_refund_no);
    let return_code = 'FAIL'
    let payRefund = await WorkerPayRefund.find(out_refund_no)
    if (payRefund && !payRefund.finishAt) {
      payRefund.transactionID = refund_id
      payRefund.finishAt = new Date()
      await payRefund.save()
      await WorkerPay.query().where('id', out_trade_no).update({ refundAt: new Date() })
      return_code = 'SUCCESS'
    }
    let ret = XMLUtil.buildXML({ xml: { return_code } })
    response.send(ret)
    await LogExternalApiRequest.create({
      url: request.originalUrl(),
      headers: JSON.stringify(request.headers()),
      request: infoXML,
      response: ret
    })
  }
  //支付回调
  async hookWechatPay({ request, params, response }) {
    let body = await XMLUtil.parseXML(request.raw())
    let { appID } = params
    let { sign, out_trade_no, result_code, transaction_id, total_fee } = body
    delete body.sign
    let PaySecret = Env.get('APP_PARTNER_KEY')
    if (sign !== CryptUtil.wechatSign(body, PaySecret)) {
      console.warn('[hookWechatPay]', sign, CryptUtil.wechatSign(body, PaySecret))
      throw ERR.API_ERROR
    }
    out_trade_no = out_trade_no.substr(ORDER_ID_PREFIX.length)
    let return_code = 'FAIL'
    console.log(out_trade_no);
    let pay = await WorkerPay.find(out_trade_no)
    //支付成功
    if (pay && !pay.finishAt && result_code === 'SUCCESS') {
      let OGworker = await Worker.find(pay.toJSON().workerID)
      OGworker.wallet = parseFloat(OGworker.toJSON().wallet) + parseFloat(pay.toJSON().totalPay) / 100
      await OGworker.save()
      pay.finishAt = new Date()
      pay.status = "完成"
      pay.transactionID = transaction_id
      await pay.save()
      return_code = 'SUCCESS'
    }
    response.json(XMLUtil.buildXML({ xml: { return_code } }))
    await LogExternalApiRequest.create({
      url: request.originalUrl(),
      headers: JSON.stringify(request.headers()),
      request: request.raw(),
      response: XMLUtil.buildXML({ xml: { return_code } })
    })
  }

  //判断支付金额是否被修改
  async _judgePay(orderID) {
    let order = await Order.find(orderID)
    let { actualMoney, orderNO, commission } = order
    let totalPay = actualMoney >= 0 && commission >= 0 ? actualMoney + commission : 200
    if (totalPay.toString() !== total_fee.toString()) {
      order.status = E.OrderStatus.Hacker
      await LogExternalApiRequest.create({
        url: request.originalUrl(),
        headers: JSON.stringify(request.headers()),
        request: request.raw(),
        response: 'hacker'
      })
      await order.save()
      throw ERR.API_ERROR
    }
  }

  //更新服务商回收数据
  async _updateCompany(order) {
    let a = order.actualMoney ? order.actualMoney : 0
    let b = order.commission ? order.commission : 0
    let money = 0
    if (order.waste_1st_ID === 4) {
      money = a
    } else {
      money = a + b
    }
    let company = await Company.find(order.companyID)
    company.estimateTotal += money
    company.completeTotal += 1
    await company.save()
  }

  //web端充值
  async webPay({ request, response }) {
    let { worker } = request
    let { openid } = worker
    let { totalPay, orderNo, workerID } = request.all()
    let notify_url = `${HOME_PATH}/master/v1/hook/pay`
    let appPay = await AppPay.create({
      orderNo,
      workerID,
      amount: totalPay,
      status: '待支付',
    })
    let pay = await WorkerPay.create({
      workerID: workerID ? workerID : worker.id,
      totalPay,
      status: '待支付',
    })
    appPay.status = '小程序充值'
    appPay.appID = pay.id || pay.toJSON().id
    await appPay.save()
    // 获取预支付交易信息
    let payObj = await WXService.createPay(
      Config.WEAPP.AppID,
      openid,
      totalPay,
      orderNo ? `订单${orderNo}充值` : `充值${pay.id}`,
      ORDER_ID_PREFIX + pay.id,
      notify_url
    )
    response.json({
      pay,
      payObj
    })
  }

  async workerPay({ request, response }) {
    let { worker } = request
    let { openid } = worker
    let { totalPay } = request.all()
    let notify_url = `${HOME_PATH}/master/v1/hook/pay`
    let pay = await WorkerPay.create({ workerID: worker.id, totalPay, status: '待支付' })
    // 获取预支付交易信息
    let payObj = await WXService.createPay(
      Config.WEAPP.AppID,
      openid,
      totalPay,
      `订单${pay.id}`,
      ORDER_ID_PREFIX + pay.id,
      notify_url
    )
    response.json({
      pay,
      payObj
    })
  }
  async walletlog({ request, response }) {
    let { current = 1, pageSize = 50 } = request.all()
    let { worker } = request
    let { id } = worker
    let data = await WorkerPay.query().where('workerID', id)
      .where('status', E.OrderStatus.Completed)
      .whereNotNull('finishAt')
      .orderBy('finishAt', 'desc')
      .fetch()
    // return data
    // 合并消费记录
    let orderLog = await WorkerWalletLog.query()
      .orderBy('createdAt', 'desc')
      .where('workerID', id)
      .select('workerID', 'id', 'createdAt', 'money', 'orderID', 'isClient')
      // .with('clientOrder',
      //   builder => {
      //     builder.select('id', 'orderNo', 'status',)
      //     return builder
      //   })
      .with('order',
        builder => {
          builder.select('id', 'orderNo', 'status', 'address', 'type')
          return builder
        })
      .paginate(current, pageSize)
    // 京东师傅记录
    let jdorderLog = await JDWorkerWalletLog.query()
      .orderBy('createdAt', 'desc')
      .where('workerID', id)
      .select('workerID', 'id', 'createdAt', 'money', 'orderID')
      .with('order',
        builder => {
          builder.select('id', 'orderNo', 'status', 'address')
          return builder
        })
      .paginate(current, pageSize)
    let resJson = orderLog.toJSON() && orderLog.toJSON().data && orderLog.toJSON().data.map((vo, index) => {
      vo.updatedAt = vo.createdAt
      vo.totalPay = parseInt(vo.money) * 100
      return vo
    })
    let jdresJson = jdorderLog.toJSON() && jdorderLog.toJSON().data && jdorderLog.toJSON().data.map((vo, index) => {
      vo.updatedAt = vo.createdAt
      vo.totalPay = parseInt(vo.money) * 100
      return vo
    })
    let res = resJson.concat(data.toJSON(), jdresJson)
    let returnVO = _.orderBy(res, ['updatedAt'], ['desc'])
    return returnVO
  }

}

module.exports = PayController
