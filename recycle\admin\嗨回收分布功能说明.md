# 嗨回收数据分布功能说明

## 功能概述

为嗨回收平台添加了省份分布、渠道分布和品类分布的数据可视化功能，帮助管理者更好地了解嗨回收业务的分布情况。

## 功能特性

### 1. 省份分布分析
- 展示嗨回收订单在各省份的分布情况
- 支持按时间范围筛选
- 饼图形式直观展示数据占比

### 2. 渠道分布分析  
- 分析不同渠道的订单分布
- 帮助了解各渠道的业务贡献
- 支持实时数据更新

### 3. 品类分布分析
- 展示各类家电回收的数量分布
- 支持业务策略决策
- 可按时间段进行对比分析

## 技术实现

### 前端架构
```
src/
├── common/action.ts          # 新增嗨回收分布相关的Action
├── models/Collection.ts      # 扩展数据模型和状态管理
├── pages/DashBorad/
│   ├── index.tsx            # 仪表板页面（新增分布模块）
│   └── index.module.less    # 样式文件（新增分布样式）
```

### API接口设计
```typescript
// 省份分布数据
GET /getHiProvinceCharts
Response: { province: string, count: number }[]

// 渠道分布数据  
GET /getHiChannelCharts
Response: { channel: string, count: number }[]

// 品类分布数据
GET /getHiCategoryCharts  
Response: { category: string, count: number }[]
```

### 支持的查询参数
- `startDate`: 开始日期（YYYY-MM-DD）
- `endDate`: 结束日期（YYYY-MM-DD）
- 其他业务相关筛选条件

## 使用方法

### 1. 查看分布数据
1. 登录管理后台
2. 进入仪表板页面
3. 滚动到"嗨回收数据分布分析"模块
4. 查看三个分布图表：省份分布、渠道分布、品类分布

### 2. 筛选数据
1. 点击任意图表右上角的日期选择器
2. 选择要分析的时间范围
3. 系统自动更新对应图表的数据

### 3. 数据解读
- **省份分布**: 了解业务地域覆盖情况，优化区域策略
- **渠道分布**: 分析各渠道效果，调整渠道资源配置
- **品类分布**: 掌握回收品类热度，制定品类运营策略

## 后端实现详情

### 1. API端点实现 ✅
已实现以下API端点：
- `GET /admin/v1/getHiProvinceCharts` - 获取省份分布
- `GET /admin/v1/getHiChannelCharts` - 获取渠道分布  
- `GET /admin/v1/getHiCategoryCharts` - 获取品类分布

### 2. 实现文件
- **路由配置**: `recycle/server/app/routes_admin.js`
- **控制器**: `recycle/server/app/Controllers/Http/Admin/DebugController.js`
- **数据模型**: `HiOrder` (嗨回收订单模型)

### 3. 数据格式
返回的数据格式符合前端期望：
```json
[
  { "province": "北京", "count": 150 },
  { "province": "上海", "count": 120 },
  { "province": "广东", "count": 200 }
]
```

### 4. 查询逻辑
```javascript
// 省份分布查询
HiOrder.query()
  .where('createdAt', '>=', startDate)
  .where('createdAt', '<=', endDate)
  .whereNotNull('province')
  .select(Database.raw("province, COUNT(*) AS count"))
  .groupBy('province')
  .orderBy('count', 'desc')

// 渠道分布查询  
HiOrder.query()
  .select(Database.raw("from AS channel, COUNT(*) AS count"))
  .groupBy('from')

// 品类分布查询
HiOrder.query()
  .select(Database.raw("type AS category, COUNT(*) AS count"))
  .groupBy('type')
```

### 5. 性能优化
- 支持日期范围筛选，默认查询最近30天
- 按统计数量降序排列
- 统一的错误处理和异常管理
- 空值过滤，避免无效数据干扰

## 样式定制

### 主题色彩
- 主色调：青色系 (#13c2c2)
- 卡片背景：渐变白色到淡蓝色
- 悬停效果：阴影和位移动画

### 响应式适配
- 大屏（>1400px）：完整显示所有功能
- 中屏（768-1400px）：自适应布局
- 小屏（<768px）：纵向排列图表

## 扩展建议

### 1. 新增分布维度
- 时间分布（按小时、周、月）
- 价格区间分布
- 订单状态分布

### 2. 交互增强
- 图表联动筛选
- 数据导出功能
- 趋势对比分析

### 3. 性能优化
- 图表数据懒加载
- 虚拟滚动支持
- 本地数据缓存

## 注意事项

1. **数据一致性**: 确保各分布图表的数据时间范围一致
2. **权限控制**: 根据用户权限显示对应的数据
3. **错误处理**: 添加数据加载失败的友好提示
4. **性能监控**: 关注大数据量时的页面性能表现

## 技术依赖

- React 18+
- Ant Design Charts
- TypeScript
- Less/CSS Modules
- DVA17 状态管理

---
*该功能基于现有仪表板架构，遵循项目的编码规范和设计模式* 