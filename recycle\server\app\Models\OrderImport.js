'use strict'

const Model = use('Model')

//订单
class OrderImport extends Model {
  static get table() {
    return 'order_import'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return 'updatedAt'
  }
  getImgJson(value) {
    return value && JSON.parse(value)
  }
  setImgJson(value) {
    return value && JSON.stringify(value)
  }
  worker() {
    return this.belongsTo('App/Models/Worker', 'workerID', 'id')
  }
  company() {
    return this.belongsTo('App/Models/Company', 'companyID', 'id')
  }
}

module.exports = OrderImport
