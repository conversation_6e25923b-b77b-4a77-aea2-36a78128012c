import Taro, { Component } from "@tarojs/taro";
import { connect } from 'react-redux'
import { View, Image, Button, Text } from "@tarojs/components";
import { AtModal } from "taro-ui";
import "./uploadChoose.less";
import { NOld<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, NUser } from '../../config/constants'
import { SERVER_HOME } from "../../config/config";
class UploadChoose extends Component {
  constructor() {
    super(...arguments);
    this.state = {
      show: true,
    };
  }
  config = {};

  //-----------------------生命周期-------------------------//
  componentWillMount() { }

  componentDidMount() { }

  componentWillUnmount() {
  }

  componentDidShow() { }

  componentDidHide() { }

  //-----------------------事件-------------------------//
  takePhoto = async () => {
    await Taro.navigateTo({ url: '/pages/cameraBox/cameraBox' })

    await this.props.dispatch.NOldGoods[NOldGoods.ESetState]({
      upload_choose: false
    })
  }
  // 本地相册与相机
  locationAlbum = async (sourceTypeStr) => {
    let newTakePhotoUrl = await this.props.oldGoods.takePhotoUrl
    await Taro.chooseImage({
      count: 9,
      sizeType: ['original', 'compressed'],
      sourceType: [`${sourceTypeStr}`],
      // sourceType: ['album','camera'],
      success(res) {
        for (let v of res.tempFilePaths) {
          newTakePhotoUrl.push(v)
          Taro.uploadFile({
            url: SERVER_HOME + 'file',
            filePath: v,
            name: 'file',
            success: (res) => {
              let data = res.data
            }
          })
        }
      }
    })
    await this.props.dispatch.NOldGoods[NOldGoods.ESetState]({
      takePhotoUrl: newTakePhotoUrl,
      upload_choose: false,
    })
    await Taro.navigateTo({ url: '/pages/uploadImg/index' })
  }
  //-----------------------渲染-------------------------//
  render() {
    // const {}=this.props
    const { show } = this.state
    return (
      <View className='uploadChoose'>
        <View className="uploadChoose_mask">
          {/* 压屏 */}
        </View>
        <View className="uploadChoose_modal_frame">

          <View className="uploadChoose_Photograph"
            onClick={() => {
              // this.takePhoto()
              this.locationAlbum('camera')
            }}
          >
            <Text>拍照</Text>
          </View>
          <View className="uploadChoose_Album"
            onClick={() => {
              this.locationAlbum('album')
            }}
          >
            <Text>从手机相册选择</Text>
          </View>
          <View className="uploadChoose_cancel"
            onClick={() => {
              this.props.dispatch.NOldGoods[NOldGoods.ESetState]({
                upload_choose: false
              })
            }}>
            <Text>取消</Text>
          </View>
        </View>
      </View>
    );
  }
}
export default connect(({ NOldGoods }) => ({ oldGoods: NOldGoods }))(UploadChoose);
