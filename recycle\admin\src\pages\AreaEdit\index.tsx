import { Fragment, useEffect, useState } from 'react'
import { notification, Select, Button, message } from 'antd'
import { useNavigate, useParams } from 'react-router-dom'
import styles from './index.module.less'
import { effect, reducer, useConnect } from 'dva17'
import {
  NCompany,
  EGetTownAddress,
  EWorkerSelected,
  NWorker,
  EGetDetailWorker,
  EGetAddress,
  EWorkerAreaEdit,
  ECityAddress,
  // ECompanyArea,
  RSetState,
} from '../../common/action'
import { getFirstKind, getSecondKind } from '../../services/price'
import { filter } from 'lodash'
import { LeftSquareOutlined } from '@ant-design/icons'
import { provinceList } from '../../common/enum'
const { Option } = Select

export default () => {
  /*--------------------- 常变量 ---------------------*/
  const params = useParams()
  const navigate = useNavigate()
  const [firstKind, setFirstKind] = useState<any>([])
  const [selectProvince, setSelectProvince] = useState<any>(null)
  const [addList, setAddList] = useState<any>([])
  const [details, setDetails] = useState<any>([])
  const [selectCity, setSelectCity] = useState<any>(null)
  const { workerDetail, addressList, townAddressList } = useConnect(NWorker)
  const { cityList } = useConnect(NCompany)

  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    getAddressData()
  }, [])

  useEffect(() => {
    if (workerDetail) {
      effect(NWorker, EGetAddress, {
        // code: JSON.parse(workerDetail?.company?.recycelCode)[1],
      })
      effect(NCompany, EWorkerSelected, {
        workerID: workerDetail.id,
      })
      setSelectProvince(workerDetail?.company?.recycelProvince ? workerDetail?.company?.recycelProvince : '')
      setSelectCity(workerDetail?.company?.recycelCity ? workerDetail?.company?.recycelCity : '')
    }
  }, [workerDetail])

  useEffect(() => {
    let objSelect = filter(addressList, { value: true })
    if (addressList && objSelect.length > 0) {
      effect(NWorker, EGetTownAddress, {
        codeArr: objSelect.map((vo: any) => vo.code),
      })
    }
  }, [addressList])

  useEffect(() => {
    let objAddress = filter(addressList, { value: true })
    let objTown = filter(townAddressList, { value: true })
    let objType = filter(firstKind, { value: true })
    let objDetail = filter(details, { value: true })
    if (objType.length > 0 && objTown.length > 0) {
      let arrobj: any = []
      for (let index = 0; index < objType.length; index++) {
        const element = objType[index];
        if (element.id === 3 && objDetail.length > 0) {
          for (let c = 0; c < objTown.length; c++) {
            for (let b = 0; b < objDetail.length; b++) {
              const elementDetail = objDetail[b];
              let townItem: any = {}
              townItem.type = element.id
              townItem.typeName = element.name
              townItem.detail = elementDetail.id
              townItem.detailName = elementDetail.name
              arrobj.push({ ...townItem, ...objTown[c] })
            }
          }
        } else {
          for (let a = 0; a < objTown.length; a++) {
            let townItem: any = {}
            townItem.type = element.id
            townItem.typeName = element.name
            arrobj.push({ ...townItem, ...objTown[a] })
          }
        }
      }
      arrobj.map((vo: any) => {
        let areaCode = vo.code?.substring(0, 6)
        vo.areaCode = areaCode
        vo.area = filter(objAddress, { code: areaCode })
          && filter(objAddress, { code: areaCode })[0]
          && filter(objAddress, { code: areaCode })[0].name
        delete vo.value
      })
      setAddList(arrobj)
    }
  }, [addressList, townAddressList, firstKind, details])

  useEffect(() => {
    if (firstKind) {
      let listApp = filter(firstKind, { id: 3, value: true })
      if (listApp.length <= 0) {
        setDetails([])
      }
    }
  }, [firstKind])
  /*--------------------- 函數 ---------------------*/
  // 省市选择
  function funSelectProvince(e: any) {
    let provinceCode
    setSelectProvince(e)
    provinceList.forEach((province: any, index: number) => {
      if (province.name === e) {
        provinceCode = province.code
        effect(NCompany, ECityAddress, {
          code: provinceCode,
        })
        return
      }
    })
  }
  // 城市选择
  const funSelectCity = (e: any) => {
    setSelectCity(e)
    let cityCode
    cityList.forEach((city: any, index: number) => {
      if (city.name === e) {
        cityCode = city.code
        effect(NWorker, EGetAddress, { code: cityCode })
        // effect(NCompany, ECompanyArea, {
        //   companyID: workerDetail.companyID,
        //   areaCode: cityCode
        // })
        return
      }
    })
  }
  async function getAddressData() {
    await effect(NWorker, EGetDetailWorker, { id: params.id })
    const firstKind: any = await getFirstKind(0)
    setFirstKind(firstKind.data)
  }
  // 区域选择
  const areaSelect = (address: any, index: number) => {
    addressList.map((vo: any) => {
      if (vo === address) {
        if (!vo.value) {
          vo.value = true
        } else {
          vo.value = false
        }
      }
    })
    reducer(NWorker, RSetState, { addressList: addressList.concat() })
  }
  // 街道选择
  const streetSelect = (address: any, index: number) => {
    townAddressList.map((vo: any) => {
      if (vo === address) {
        if (!vo.value) {
          vo.value = true
        } else {
          vo.value = false
        }
      }
    })
    reducer(NWorker, RSetState, { townAddressList: townAddressList.concat() })
  }
  // 区域全选
  const selectAllArea = () => {
    addressList.map((vo: any) => {
      if (!vo.value) {
        vo.value = true
      } else {
        vo.value = false
      }
    })
    reducer(NWorker, RSetState, { addressList: addressList.concat() })
  }
  // 街道全选
  const selectAllStreet = () => {
    townAddressList.map((vo: any) => {
      if (!vo.value) {
        vo.value = true
      } else {
        vo.value = false
      }
    })
    reducer(NWorker, RSetState, { townAddressList: townAddressList.concat() })
  }

  // 类型选择
  const setRecycelType = async (kind: any, theIndex: any) => {
    if (kind.id === 3 && !kind.value) {
      let resdata = await getSecondKind({ id: 3 })
      setDetails(resdata)
    }
    firstKind.map((vo: any) => {
      if (kind.id === vo.id) {
        if (!vo.value) {
          vo.value = true
        } else {
          vo.value = false
        }
      }
    })
    setFirstKind(firstKind.concat())
  }
    // 类型全选
    const selectAllKind = () => {
      details.map((vo: any) => {
        if (!vo.value) {
          vo.value = true
        } else {
          vo.value = false
        }
      })
    setDetails(details.concat())
  }
  const setDetailType = (item: any) => {
    details.map((vo: any) => {
      if (item.id === vo.id) {
        if (!vo.value) {
          vo.value = true
        } else {
          vo.value = false
        }
      }
    })
    setDetails(details.concat())
  }
  function saveRecycelType() {
    const id = params.id
    const hide = message.loading('正在添加')
    // console.log(addList);
    if (addList.length > 0) {
      effect(NCompany, EWorkerAreaEdit, {
        workerID: id,
        companyID: workerDetail.companyID,
        addList: addList,
        removeList: [],
      }).then(() => {
        notification.success({
          message: "成功",
          description: '保存成功！',
          duration: 2
        })
        hide()
        navigate(`/WorkersManage/WorkerArea/${id}`)
      })
    }
  }
  /*--------------------- 渲染 ---------------------*/
  return (
    <div className={styles.main}>
      <h2>
        <LeftSquareOutlined size={12} title='返回列表' onClick={() => { navigate(`/WorkersManage/WorkerArea/${params.id}`) }} />
        <span style={{ marginLeft: 10, fontSize: 16, fontWeight: 'bolder' }}>{workerDetail && workerDetail.workerName}-新增服务区域</span>
      </h2>
      <div className={styles.wrapper}>
        <span className={styles.title}>省份选择：</span>
        <div className={styles.right_wrapper}>
          <Select
            value={selectProvince}
            style={{ width: 200 }}
            onChange={e => {
              funSelectProvince(e)
            }}>
            {provinceList.map((province: any, index: any) => (
              <Option key={index} value={province.name}>{province.name}</Option>
            ))}
          </Select>
        </div>
      </div>
      <div className={styles.wrapper}>
        <span className={styles.title}>城市选择：</span>
        <div className={styles.right_wrapper}>
          <Select
            value={selectCity}
            style={{ width: 200 }}
            onChange={e => {
              funSelectCity(e)
            }}>
            {cityList && workerDetail && cityList.map((city: any, index: any) => <Option
              key={index}
              value={city.name}>
              {city.name}
            </Option>)}
          </Select>
        </div>
      </div>

      {addressList ? (
        <div className={styles.wrapper}>
          <span className={styles.title}>区域选择：<p style={{
            textAlign: "center", color: '#1890ff', cursor: 'pointer', textDecoration: 'underline'
          }}
            onClick={() => { selectAllArea() }}>全选</p></span>
          <div className={styles.right_wrapper}>
            {addressList.map((address: any, index: any) => (
              <div
                className={`${styles.area_item} ${address?.value ? styles.selected : null}`}
                key={address + index}
                onClick={() => {
                  areaSelect(address, index)
                }}>
                {address.name}
              </div>
            ))}
          </div>
        </div>
      ) : null}

      {townAddressList ? (
        <Fragment>
          <div className={styles.wrapper}>
            <span className={styles.title}>街道/镇：<p style={{
              textAlign: "center", color: '#1890ff', cursor: 'pointer', textDecoration: 'underline'
            }} onClick={() => { selectAllStreet() }}>全选</p></span>
            <div className={styles.right_wrapper}>
              {townAddressList.map((address: any, index: any) => (
                <div
                  key={address.code + index}
                  className={`${styles.area_item} ${address?.value ? styles.selected : null}`}
                  onClick={() => {
                    streetSelect(address, index)
                  }}>
                  {address.name}
                </div>
              ))}
            </div>
          </div>
          <div className={styles.wrapper}>
            <span className={styles.title}>回收类型：</span>
            <div className={styles.right_wrapper}>
              {firstKind
                ? firstKind.map((kind: any, index: any) => (
                  <div
                    className={`${styles.area_item} ${kind?.value ? styles.selected : null}`}
                    key={kind + index}
                    onClick={() => {
                      setRecycelType(kind, index)
                    }}>
                    {kind.name}
                  </div>
                ))
                : null}
            </div>
          </div>
          {details && details.length > 0 ? <div className={styles.wrapper}>
            <span className={styles.title}>家电类型：<p style={{
            textAlign: "center", color: '#1890ff', cursor: 'pointer', textDecoration: 'underline'
          }}
            onClick={() => { selectAllKind() }}>全选</p>
            </span>
            <div className={styles.right_wrapper}>
              {details.map((item: any, index: number) => (<div
                className={`${styles.area_item} ${item?.value ? styles.selected : null}`}
                key={item + index}
                onClick={() => {
                  setDetailType(item)
                }}>
                {item.name}
              </div>))}
            </div>
          </div> : null}
          <div className={styles.wrapper}>
            <span className={styles.title}>是否保存：</span>
            <div className={styles.right_wrapper}>
              <Button
                onClick={() => {
                  saveRecycelType()
                }}
                style={{ background: '#1890ff', color: '#ffffff' }}>
                保存
              </Button>
            </div>
          </div>
        </Fragment>
      ) : null}
    </div>
  )
}
