'use strict'

const _ = require('lodash')
const moment = require('moment')
const Event = use('Event')
const Env = use('Env')

const { Pay, Order, ThirdOrder, LogUserAction, Goods, LogExternalApiRequest, User, Worker, Company } = require('../../../Models')
const { CryptUtil, XMLUtil } = require('../../../Util')
const Config = require('../../../Util/Config')
const { WXService } = require('../../../Services')
const { ERR, E } = require('../../../../../../constants')
const APP_ID = Config.WEAPP.AppID

const ORDER_ID_PREFIX = 'donggua_client_pay_'

//支付记录
class PayController {
  //支付
  async store({ request, response }) {
    let { userID, user } = request
    let { orderID, type = E.PayType.Wechat } = request.all()
    let order = await Order.query()
      .where('id', orderID)
      .where('userID', userID)
      // .where("status", E.OrderStatus.InProgress)
      .first()
    let actualMoney = order.actualMoney ? order.actualMoney : order.payMoney
    if (!order) {
      throw ERR.RESTFUL_GET_ID
    } else if (actualMoney <= 0) {
      throw ERR.UNDEFINED
    }
    let { orderNO } = order
    // actualMoney = actualMoney * 100
    let { openid } = user
    let payObj = null
    let pay = await Pay.create({ type, userID, orderID, rmb: actualMoney, workerID: order.workerID })
    switch (type) {
      case E.PayType.Wechat:
        {
          payObj = await WXService.createPay(APP_ID, openid, actualMoney, `订单${orderNO}`, ORDER_ID_PREFIX + pay.id)
        }
        break
      case E.PayType.Debug:
        if (Env.get('NODE_ENV') == 'production') {
          throw ERR.UNDEFINED
        } else {
          pay.transactionID = pay.id
          pay.finishAt = new Date()
          await pay.save()
          // await this._finishOrder(order, openid)
        }
        break
    }
    response.json({
      pay,
      payObj
    })
  }
  //支付回调
  async hookWechatPay({ request, params, response }) {
    let body = await XMLUtil.parseXML(request.raw())
    let { appID } = params
    let { sign, out_trade_no, result_code, transaction_id, total_fee } = body
    delete body.sign
    let PaySecret = Env.get('APP_PARTNER_KEY')
    if (sign !== CryptUtil.wechatSign(body, PaySecret)) {
      console.warn('[hookWechatPay]', sign, CryptUtil.wechatSign(body, PaySecret))
      throw ERR.API_ERROR
    }
    out_trade_no = out_trade_no.substr(ORDER_ID_PREFIX.length)
    let return_code = 'FAIL'
    let pay = await Pay.find(out_trade_no)
    let order = await Order.find(pay.orderID)
    // console.log(out_trade_no, pay)
    if (pay && !pay.finishAt && result_code === 'SUCCESS') {
      if (order.actualMoney.toString() !== total_fee.toString()) {
        // order.status = E.OrderStatus.Hacker
        // await LogExternalApiRequest.create({
        //   url: request.originalUrl(),
        //   headers: JSON.stringify(request.headers()),
        //   request: request.raw(),
        //   response: 'hacker'
        // })
        // await order.save()
        // throw ERR.API_ERROR
      }
      this._finishOrder(order)
      pay.finishAt = new Date()
      pay.transactionID = transaction_id
      await pay.save()
      return_code = 'SUCCESS'
    }

    await LogExternalApiRequest.create({
      url: request.originalUrl(),
      headers: JSON.stringify(request.headers()),
      request: request.raw(),
      response: XMLUtil.buildXML({ xml: { return_code } })
    })
    response.json(XMLUtil.buildXML({ xml: { return_code } }))
  }

  async _finishOrder(order) {
    // TODO 业务
    order.status = E.OrderStatus.Completed
    order.finishedAt = new Date()
    await order.save()
    //支付给师傅
    let worker = await Worker.find(order.workerID)
    let title = `订单${order.orderNO}已完成`
    let pay = await Pay.create({ userID: order.userID, workerID: order.workerID, rmb: order.remuneration, orderID: order.id, type: '商户支付师傅' })
    let result = await WXService.businessPay(Config.WEAPP.AppID, worker.openid, order.remuneration, title, order.orderNO)
    if (result.return_code === 'SUCCESS') {
      pay.finishAt = new Date()
      pay.payment_no = result.payment_no
      await pay.save()
    }
    // console.log('result')
    this._updateCompany(order)
    await this._postMessage(order, E.OrderStatus.Completed)
  }
  //订阅消息
  async _postMessage(vo, status) {
    let user = await User.find(vo.userID)
    let page = `/pages/orderDetail/orderDetail?orderNumber=${vo.id}&manage=${status}`
    let messageID = Config.ClientMessageID1
    WXService.sendWechat( Config.Client.AppID,
      Config.Client.AppSecret, user.openid, vo.waste_1st_ID, vo.createdAt, vo.orderNO, status, page, messageID)
  }
  //更新服务商回收数据
  async _updateCompany(order) {
    let a = order.actualMoney ? order.actualMoney : 0
    let b = order.commission ? order.commission : 0
    let money = 0
    if (order.waste_1st_ID === 4) {
      money = a
    } else {
      money = a + b
    }
    let company = await Company.find(order.companyID)
    company.estimateTotal += money
    company.completeTotal += 1
    await company.save()
  }
}

module.exports = PayController
