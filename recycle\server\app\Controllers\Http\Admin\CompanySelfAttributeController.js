'use strict'

const _ = require('lodash')
const moment = require('moment')

const { CompanySelfWastePrice, CompanySelfAttribute } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//废品属性
class CompanySelfAttributeController {
  //获取服务商所设置的回收物属性
  async index({ request, response }) {
    let { companyID, typeID } = request.all()
    if (!companyID) {
      throw ERR.INVALID_PARAMS
    }
    let query = await CompanySelfAttribute.query()
      .where('companyID', companyID)
      .where('typeID', typeID)
      .fetch()
    response.json(query)
  }
  //编辑服务商所设置的回收物属性
  async store({ request, response }) {
    let { companyID, list } = request.all()
    _.forEach(list, async function(value) {
      let waste = await CompanySelfAttribute.query()
        .where('companyID', companyID)
        .where('attributeID', value.id)
        .first()
      if (!waste) {
        waste = await CompanySelfAttribute.create({
          companyID,
          typeID: value.typeID,
          attributeID: value.id,
          name: value.name,
          plusPrice: value.plusPrice,
          minusPrice: value.minusPrice
        })
      } else {
        _.assign(waste, { plusPrice: value.plusPrice, minusPrice: value.minusPrice })
        await waste.save()
      }
    })
    response.json({ result: 'ok' })
  }
}

module.exports = CompanySelfAttributeController
