'use strict'

const Model = use('Model')

//订单废品关联表
class WorkerDistrictV extends Model {
  static get table(){return 'v_worker_districtinfo'}
  static get primaryKey () { return 'id' }
  static get createdAtColumn (){return 'createdAt'}
  static get updatedAtColumn (){return 'updatedAt'}

  // order () {
  //   return this.belongsTo('App/Models/Order','orderID','id')
  // }
}

module.exports = WorkerDistrictV
