const E = {
  WasteType: {
    ClothingAndBook: '旧衣旧书',
    LivingWaste: '生活废品',
    Appliances: '大家电',
    Furniture: '大家具',
  },
  DiscoveryType: {
    WELFARE: 'welfare',
    ACTIVITIES: 'activities',
    MEDIA: 'media',
  },
  CoursesStatus: {
    SignUp: 'SignUp',
    Doing: 'Doing',
    Finish: 'Finish',
  },
  OrderFrom: {
    小程序: '小程序',
    冬瓜回收: '冬瓜回收',
    美团: '美团',
    电话下单: '电话下单',
    二维码跳转下单: '二维码跳转下单',
    定时定点: '定时定点',
  },
  OrderStatus: {
    Init: '初始化',
    Reservation: '预订',
    InProgress: '进行中',
    Completed: '完成',
    Cancelled: '取消',
    Hacker: '黑客攻击',
  },
  manageList: [
    {
      name: '已预约',
      url: 'record',
      status: '预订',
    },
    {
      name: '进行中',
      url: 'doing',
      status: '进行中',
    },
    {
      name: '已完成',
      url: 'success',
      status: '完成',
    },
    {
      name: '已取消',
      url: 'cancel',
      status: '取消',
    },
  ],
  OrderCanCle: ['重复下单', '回收人员不上门', '回收人员取消', '机子被他人取走', '已处理'],
  OrderCancel: [
    {
      zh_CN: '重复下单',
      en: 'Repeat Orders',
    },
    {
      zh_CN: '回收人员不上门',
      en: 'Staff do not come',
    },
    {
      zh_CN: '回收人员取消',
      en: 'Staff Cancel',
    },
    {
      zh_CN: '已处理',
      en: 'Processed',
    },
  ],
  CancleOrderStatus: {
    Client: '客户取消',
    Master: '师傅取消',
    MasterRevoke: '师傅撤销',
    Admin: '后台取消',
  },
  OrderComplaint: [
    {
      zh_CN: '乱开价',
      en: 'Random offer',
    },
    {
      zh_CN: '态度差',
      en: 'Bad attitude',
    },
    {
      zh_CN: '不准时',
      en: 'Not on time',
    },
    {
      zh_CN: '不专业',
      en: 'unprofessional',
    },
  ],
  OrderMedium: [
    {
      zh_CN: '服务态度一般',
      en: 'Punctual',
    },
    {
      zh_CN: '师傅操作不规范',
      en: 'Specialty',
    },
    {
      zh_CN: '衣着不整洁',
      en: 'Neatly Dressed',
    },
    {
      zh_CN: '反馈慢',
      en: 'Timely Feedback',
    },
  ],
  OrderRating: [
    {
      zh_CN: '很准时',
      en: 'Punctual',
    },
    {
      zh_CN: '特别专业',
      en: 'Specialty',
    },
    {
      zh_CN: '衣着整洁',
      en: 'Neatly Dressed',
    },
    {
      zh_CN: '态度很好',
      en: 'Good Attitude',
    },
    {
      zh_CN: '反馈很及时',
      en: 'Timely Feedback',
    },
  ],
  RecyclingInstructions: [
    {
      title: '旧衣说明',
      content: [
        '1、 您下单后，我们将安排快递人员上门回收，快递工作人员与您联系确认具体回收时间；',
        '2、 回收时间：9：00-18：00；',
        '3、 如果您对服务不满意，可以在“我的”个人中心“客服与投诉”予以投诉，您可以直接电话或留言投诉；',
        '4、 回收的物品我们将进行环保处理，避免二次污染和资源浪费；',
        '5、 我们提供专业化的废旧物品回收信息平台，您的支持是我们最大的动力；',
      ],
    },
    {
      title: '旧书说明',
      content: [
        '1、不接收高中以下教材、教辅、考试考证类辅导书、单独磁带光碟；不接收报刊、杂志类等时效性刊物；不接收非法出版物、盗版、严重污染、破损、影响阅读的书籍；',
        '2、 您下单后，我们将安排快递人员上门回收，快递工作人员与您联系确认具体回收时间；',
        '3、 回收时间：9：00-18：00；',
        '4、 如果您对服务不满意，可以在“我的”个人中心“客服与投诉”予以投诉，您可以直接电话或留言投诉；',
        '5、 回收的物品我们将进行环保处理，避免二次污染和资源浪费；',
        '6、 我们提供专业化的废旧物品回收信息平台，您的支持是我们最大的动力；',
      ],
    },
    {
      title: '其他',
      content: [
        '1、 您下单后，我们将安排回收人员上门回收，回收人员与您联系确认具体回收时间；',
        '2、 回收时间：9：00-18：00；',
        '3、 如果您对服务不满意，可以在“我的”个人中心“客服与投诉”予以投诉，您可以直接电话或留言投诉；',
        '4、 回收的物品我们将进行环保处理，避免二次污染和资源浪费；',
        '5、 我们提供专业化的废旧物品回收信息平台，您的支持是我们最大的动力；',
      ],
    },
  ],

  Questions: [
    {
      title: '关于公益回收、高价回收、有偿回收的说明？',
      enTitle: 'What about public welfare recycling, high-priced recycling, and paid recycling?',
      list: [
        '公益回收：旧衣、旧书、生活废品为公益回收项目，回收完成后给用户支付各种优惠卷权益；',
        '高价回收：大家电、智能数码为高价回收项目，我们会根据系统估价与实物评测，向用户支付物品残值的价格；',
        '有偿回收：大家具为有偿回收项目，因大家具运输与处置成本较高，故需要用户支付费用有偿回收；',
      ],
      enList: [
        'Public welfare recycling: used clothes, old books, and domestic waste are public welfare recycling projects. After the recycling is completed, users will be paid various discount coupons;',
        'High-priced recycling: Large home appliances and smart digital are high-priced recycling items. We will pay users the price of the residual value of the item based on system valuation and physical evaluation;',
        'Paid recycling: Large furniture is a paid recycling project. Because of the high transportation and disposal costs of large furniture, users need to pay for the paid recycling;',
      ],
    },
    {
      title: '你们什么时候可以给到我钱？',
      enTitle: 'When can you give me the money?',
      list: ['在订单完成之后的24小时内，资金由师傅线下转账，或者系统自动转到您的账号上，请您耐心等待。'],
      enList: [
        'The funds will be automatically transferred to your account within 24 hours after the order is completed, please be patient.',
      ],
    },
    {
      title: '你们什么时候上门回收？',
      enTitle: 'When will you come to recycle?',
      list: ['上门时间的话我们这边会在约定时间内上门，上门前半天之内会给您发送短信告知。'],
      enList: [
        'If the time to visit, we will visit the door within the agreed time, and we will send you a text message within half a day before the visit.',
      ],
    },
    {
      title: '怎么查看订单？',
      enTitle: 'How to check the order?',
      list: ['在冬瓜回收回收平台—个人中心--订单查看。'],
      enList: ['In the Mini Program-User-Order View'],
    },
    {
      title: '大件家具为什么要收费用啊？',
      enTitle: 'Why do you charge for large pieces of furniture?',
      list: ['大件家具回收因人工成本较高需要收取用户一定的服务费。显示的价格为平台估价，价格优惠问题可以等师傅上门看情况而定。'],
      enList: [
        'The recycling of large pieces of furniture requires a certain service fee to be charged to the user due to the high labor cost. The displayed price is estimated by the platform, and the price concessions can be determined by the master’s door-to-door visit.',
      ],
    },
    {
      title: '大家电回收怎么给的钱？',
      enTitle: 'How to pay for the recycling of large home appliances?',
      list: ['订单回收完成后微信到账，可能会有延迟，在微信账单处查看到账情况。'],
      enList: ['After the order collection is completed, the WeChat account may be delayed. Check the account status at the WeChat bill.'],
    },
    {
      title: '为什么你们衣服破的、坏的不收？',
      enTitle: 'Why don’t you accept your clothes that are broken or broken?',
      list: ['因为我们衣服都是做捐赠的，要经过消毒处理，所以都是需要干净整洁的。'],
      enList: ['Because our clothes are donated and need to be disinfected, they all need to be clean and tidy'],
    },
  ],

  Introduce: [
    '冬瓜回收:',
    '上海憬橙环保科技有限公司致力于引导全民环保而成立的一站式上门回收平台；',
    '用户通过平台预约、免费上门回收，打造“互联网+环保+再生”的绿色平台，为改善环境贡献自己一份微薄的力量；',
    '建立可回收物品循环利用与过程监管、追溯平台；',
  ],
  FlowText: [
    '选择您要回收的垃圾类型。',
    '拍照并填写提交您的预约。',
    '上门回收人员会根据您的预约时间上门回收，并与您当场确认订单完成情况。',
    '根据上门回收人员与您确认的订单，获得相应的权益。',
  ],
  RecycleRule: [
    '亲，每笔订单请积攒大概5公斤及以上的可回收物，再预约上门回收服务哦',
    '若每笔订单不足5公斤或与您上传的回收物照片不符，您的订单可能被取消',
    '回收小哥上门回收比较辛苦，谢谢您的体谅和对环保事业的支持',
  ],
  enRecycleRule: [
    'Dear, please accumulate about 5kg and above recyclables for each order, and then make an appointment for the on-site collection service.',
    'If each order is less than 5 kg or does not match the photo of the reclaimed item you uploaded, your order may be cancelled',
    'Recycling Staff is more difficult to come to recycle, thank you for your understanding and support to the environmental protection cause',
  ],
}

// 将空值用键名填充
function fillValue(dict) {
  for (var key in dict) {
    if (typeof dict[key] === 'object' && dict[key]) {
      fillValue(dict[key])
    } else if (null === dict[key]) {
      dict[key] = key
    }
  }
}
fillValue(E)

module.exports = E
