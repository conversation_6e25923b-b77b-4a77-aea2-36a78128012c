'use strict'

const _ = require('lodash')
const moment = require('moment')

const { CompanySelfWastePrice } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//废品属性
class CompanySelfWastePriceController {
  //获取服务商所设置的回收物
  async index({ request, response }) {
    let { companyID, priceType } = request.all()
    if (!companyID || !priceType) {
      throw ERR.INVALID_PARAMS
    }
    let query = await CompanySelfWastePrice.query()
      .where('companyID', companyID)
      .where('priceType', priceType)
      .fetch()
    response.json(query)
  }
  //编辑服务商所设置的回收物
  async store({ request, response }) {
    let { companyID, list, priceType } = request.all()
    _.forEach(list, async function(value) {
      let waste = await CompanySelfWastePrice.query()
        .where('companyID', companyID)
        .where('wasteID', value.wasteID)
        .where('priceType', priceType)
        .first()
      if (!waste) {
        waste = await CompanySelfWastePrice.create({ companyID, name: value.name, wasteID: value.wasteID, price: value.price, priceType })
      } else {
        _.assign(waste, value)
        await waste.save()
      }
    })
    response.json({ result: 'ok' })
  }
}

module.exports = CompanySelfWastePriceController
