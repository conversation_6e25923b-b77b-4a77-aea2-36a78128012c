import { DownOutlined, PlusOutlined, RightOutlined } from '@ant-design/icons'
import { Button, Badge, Space, Switch, Modal, notification, Input } from 'antd'
import type { ActionType, ProColumns } from '@ant-design/pro-table'
import ProTable from '@ant-design/pro-table'
import ProCard from '@ant-design/pro-card'
import { effect, reducer, useConnect } from 'dva17'
import { EGet, NCompany, NWorker, EGetFullTimeWorker, RSetState, ECompanyArea, NUser } from '../../common/action'
import { useEffect, useRef, useState } from 'react'
import styles from './index.module.less'
import { delCompanyItem, editCompany } from '../../services/companyAPI'
import EditCompany from '../../components/EditCompany'
import { useNavigate } from 'react-router-dom'
import Collapse from "@kunukn/react-collapse"
import { computeAuthority } from '../../utils/Authorized/authority'

type Item = {
  id: number
  companyName: string
  recycelCity: string
  recycelProvince: string
  title: string
  order: {
    companyID: number
    count: number
    totalMoney: number
  }[]
  forbidden: number
}

export default () => {
  const navigate = useNavigate()
  const { companyArea } = useConnect(NCompany)
  const { currentUser } = useConnect(NUser)
  const [seeDetail, setSeeDetail] = useState<any>(false)
  const [textDetail, setTextDetail] = useState<any>({})
  const [visibleEdit, setVisibleEdit] = useState<any>(false)
  const [visiblePassword, setVisiblePassword] = useState<any>(false)
  const [companyDetail, setCompanyDetail] = useState<any>(null)
  const [userNameEdit, setUserNameEdit] = useState<any>(null)
  const [passwordEdit, setPasswordEdit] = useState<any>(null)
  const [confirmPassword, setConfirmPassword] = useState<any>(null)
  const [editData, setEditData] = useState<any>(null)
  const [currentIndex, setCurrentIndex] = useState<any>(null)
  const [currentArea, setCurrentArea] = useState<any>(null)
  const [currentTown, setCurrentTown] = useState<any>(null)
  const [currentCity, setCurrentCity] = useState<any>(null)
  const [collapseVisible, setCollapseVisible] = useState<any>(true)
  const [currentType, setCurrentType] = useState<any>(null)
  const [currentTownIndex, setCurrentTownIndex] = useState<any>(null)
  const actionRef = useRef<ActionType>()
  const [visibleAddress, setVisibleAddress] = useState(false)
  let { workerList } = useConnect(NWorker)
  const columns: ProColumns<Item>[] = [
    {
      title: '服务商名称',
      dataIndex: 'companyName',
      copyable: false,
      ellipsis: true,
    },
    // {
    //   title: '回收区域',
    //   dataIndex: 'recycelProvince',
    //   copyable: false,
    //   ellipsis: true,
    //   search: false,
    //   render: (_, row) => (
    //     <Space>
    //       {row.recycelProvince}-{row.recycelCity}
    //     </Space>
    //   ),
    // },
    {
      title: '总订单数(单)',
      copyable: false,
      ellipsis: true,
      search: false,
      render: (_, row) => <Space>{row.order[0]?.count || 0}</Space>,
    },
    {
      title: '总流水(w)',
      copyable: false,
      ellipsis: true,
      search: false,
      render: (_, row) => <Space>{row.order[0]?.totalMoney || 0}</Space>,
    },
    {
      title: '服务商详情',
      copyable: false,
      ellipsis: true,
      search: false,
      render: (_, row) => (
        <Button
          onClick={() => {
            setTextDetail(row as any)
            setSeeDetail(true)
            effect(NWorker, EGetFullTimeWorker, { isUse: '同意', companyID: row?.id })
          }}>
          查看
        </Button>
      ),
    },
    {
      title: '启用状态',
      dataIndex: 'forbidden',
      copyable: false,
      ellipsis: true,
      search: false,
      render: (_, row) => (
        <Switch
          disabled={!computeAuthority('服务商新建与编辑') || currentUser.level == '服务商'}
          checkedChildren="启动"
          unCheckedChildren="停用"
          defaultChecked={Boolean(row.forbidden)}
          onChange={e => {
            changeForbidden(e, row)
          }}
        />
      ),
    },
    {
      title: '操作',
      width: '30%',
      copyable: false,
      ellipsis: true,
      search: false,
      render: (_, row) => (
        <>
          <Button
            disabled={!computeAuthority('服务商新建与编辑')}
            onClick={() => {
              setEditData(row)
              setVisibleEdit(true)
            }}>
            编辑
          </Button>
          <Button
            disabled={!computeAuthority('服务商新建与编辑')}
            onClick={() => {
              deleteCompanyItem(row)
            }}>
            删除
          </Button>
          <Button
            disabled={!computeAuthority('服务商新建与编辑')}
            onClick={() => {
              resetAccount(row)
            }}>
            账号密码
          </Button>
        </>
      ),
    },
  ]
  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    let currentArea: any = []
    companyArea.forEach((area: any, index: any) => {
      if (currentArea.indexOf(area.area) < 0) {
        currentArea.push(area.area)
      }
    })
    setCurrentArea(currentArea)
  }, [companyArea])
  /*--------------------- 响应 ---------------------*/
  const handleAddressOk = () => {
    setVisibleAddress(false)
    setCurrentIndex(null)
    setCurrentArea([])
    setCurrentTown([])
    setCurrentType([])
    effect(NWorker, RSetState, { townList: null })
  }
  const getCount = (area: any) => {
    let currentTown: any = []
    companyArea.forEach((value: any, index: any) => {
      if (value.area === area) {
        if (currentTown.indexOf(value.name) < 0) {
          currentTown.push(value.name)
        }
      }
    })
    return currentTown.length
  }

  const getKind = (town: any, index: any) => {
    let currentType: any = []
    companyArea.forEach((value: any) => {
      if (value.name === town && value.area === currentCity) {
        currentType.push(value.typeName)
      }
    })
    setCurrentType(currentType)
    setCurrentTownIndex(index)
  }
  const getTown = (area: any, index: any) => {
    setCurrentCity(area)
    let currentTown: any = []
    companyArea.forEach((value: any, index: any) => {
      if (value.area === area) {
        if (currentTown.indexOf(value.name) < 0) {
          currentTown.push(value.name)
        }
      }
    })
    setCurrentIndex(index)
    setCurrentTown(currentTown)
    setCurrentTownIndex(null)
    setCurrentType([])
  }
  const showAddressModal = (text: any) => {
    effect(NCompany, ECompanyArea, {
      companyID: text.id,
    })
    setVisibleAddress(true)
  }
  const refreshPage = () => {
    if (actionRef.current) {
      actionRef.current.reload()
    }
  }
  const deleteCompanyItem = (e: any) => {
    Modal.confirm({
      title: '确认删除该服务商',
      content: <div>删除后数据无法恢复！</div>,
      okText: '确认删除',
      cancelText: '退出',
      onOk: async () => {
        await delCompanyItem({ payload: { id: e.id } })
        refreshPage()
        notification.success({
          message: '成功！',
          description: '删除成功',
          duration: 2,
        })
      },
      width: 700,
    })
  }
  const resetAccount = (text: any) => {
    setVisiblePassword(true)
    setCompanyDetail(text)
    setUserNameEdit(text.userName)
  }

  const handleOkPassword = async (e: any) => {
    if (!userNameEdit) {
      notification.error({
        message: '提示！',
        description: '账号不能为空！',
        duration: 2,
      })
      return
    }
    if (!passwordEdit) {
      notification.error({
        message: '提示！',
        description: '密码不能为空！',
        duration: 2,
      })
      return
    }
    if (!confirmPassword) {
      notification.error({
        message: '提示！',
        description: '确认密码不能为空！',
        duration: 2,
      })
      return
    }
    if (passwordEdit !== confirmPassword) {
      notification.error({
        message: '提示！',
        description: '两次密码输入不一样！',
        duration: 2,
      })
      return
    }
    let data = await editCompany({
      payload: {
        id: companyDetail?.id,
        userName: userNameEdit,
        password: passwordEdit,
      },
    })
    refreshPage()
    notification.success({
      message: '成功！',
      description: '修改成功',
      duration: 2,
    })
    setConfirmPassword(null)
    setPasswordEdit(null)
    setUserNameEdit(null)
    setVisiblePassword(false)
  }

  const handleCancelPassword = (e: any) => {
    setConfirmPassword(null)
    setPasswordEdit(null)
    setUserNameEdit(null)
    setVisiblePassword(false)
  }

  const changeForbidden = async (e: any, record: any) => {
    let data = await editCompany({
      payload: {
        id: record.id,
        forbidden: e ? 1 : 0,
      },
    })
    notification.success({
      message: '成功！',
      description: '修改成功',
      duration: 2,
    })
  }
  /*--------------------- 渲染 ---------------------*/
  return (
    <ProCard>
      <Modal
        title="服务商详情"
        open={seeDetail}
        footer={null}
        onOk={() => {
          setSeeDetail(false)
        }}
        onCancel={() => {
          setSeeDetail(false)
        }}>
        <div className={styles.item_wrapper}>
          {[
            { title: '服务商名称：', content: textDetail?.companyName },
            { title: '联系人：', content: textDetail?.contactPerson },
            { title: '联系电话：', content: textDetail?.mobile },
            { title: '邮箱：', content: textDetail?.email },
            { title: '地址：', content: textDetail?.province + textDetail?.city + textDetail?.district + (textDetail?.address ? textDetail?.address : '') },
            // { title: '回收区域：', content: textDetail?.recycelProvince + '-' + textDetail?.recycelCity },
            // { title: '回收人员：', content: (workerList ? workerList.total : '0') + '人' },
          ].map((value: any, index: number) => (
            <div className={styles.item} key={index}>
              <span className={styles.item_title}>{value.title}</span>
              <div className={styles.item_content}>
                {value.content}
              </div>
            </div>
          ))}
        </div>
      </Modal>
      <Modal title="账号及密码设置" open={visiblePassword} onOk={handleOkPassword} onCancel={handleCancelPassword}>
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <div className={styles.newCompany_item_password} style={{ marginTop: 10 }}>
            <span className={styles.newCompany_item_title}>登录账号：</span>
            <Input
              placeholder="请输入登录账号"
              onChange={e => {
                setUserNameEdit(e?.target?.value)
              }}
              style={{ width: 250 }}
              value={userNameEdit}
              type="text"
            />
          </div>
          <div className={styles.newCompany_item_password}>
            <span className={styles.newCompany_item_title}>密码：</span>
            <Input
              placeholder="请输入登录密码"
              onChange={e => {
                setPasswordEdit(e?.target?.value)
              }}
              style={{ width: 250 }}
              value={passwordEdit}
              type="password"
            />
          </div>
          <div className={styles.newCompany_item_password}>
            <span className={styles.newCompany_item_title}>确认密码：</span>
            <Input
              style={{ width: 250 }}
              placeholder="请再次输入登录密码"
              onChange={e => {
                setConfirmPassword(e?.target?.value)
              }}
              value={confirmPassword}
              type="password"
            />
          </div>
        </div>
      </Modal>
      <EditCompany
        data={editData ? editData : null}
        visible={visibleEdit}
        onChange={({ visible, refresh }: any) => {
          setVisibleEdit(visible)
          setEditData(null)
          if (refresh) {
            refreshPage()
          }
        }}
      />
      <ProTable<Item>
        actionRef={actionRef}
        columns={columns}
        request={async (params = {}) => (await effect(NCompany, EGet, params)) as any}
        pagination={{}}
        rowKey="id"
        dateFormatter="string"
        headerTitle=""
        toolBarRender={() => [
          <Button
            disabled={!computeAuthority('服务商新建与编辑')}
            key="3"
            type="primary"
            onClick={() => {
              setVisibleEdit(true)
            }}>
            <PlusOutlined />
            新建
          </Button>,
        ]}
      />

      <Modal
        title="已分配区域"
        open={visibleAddress}
        onOk={handleAddressOk}
        onCancel={handleAddressOk}
        width={800}>
        <div className={styles.item_wrapper}>
          <div className={styles.item}>
            <span className={styles.item_title}>区域选择：</span>
            <Collapse isOpen={collapseVisible}>
              <div className={styles.item_content}
                style={{ marginTop: 10 }}
              >
                {currentArea &&
                  currentArea.map((area: any, index: number) => (
                    <Badge count={getCount(area)} style={{ right: 20 }} key={index + area}>
                      <div
                        className={`${styles.area_item} ${currentIndex === index ? styles.selected : null}`}
                        onClick={() => {
                          getTown(area, index)
                        }}>
                        {area}
                      </div>
                    </Badge>
                  ))}
              </div>
            </Collapse>
            <a onClick={() => setCollapseVisible(!collapseVisible)}>{collapseVisible ? <DownOutlined /> : <RightOutlined />}</a>
          </div>
          <div className={styles.item}>
            <span className={styles.item_title}>街道/镇：</span>
            <div className={styles.item_content}>
              {currentTown &&
                currentTown.map((value: any, index: any) => (
                  <div
                    className={`${styles.area_item} ${currentTownIndex === index ? styles.selected : null}`}
                    key={index + value}
                    onClick={() => {
                      getKind(value, index)
                    }}>
                    {value}
                  </div>
                ))}
            </div>
          </div>
          <div className={styles.item}>
            <span className={styles.item_title}>回收类型：</span>
            <div className={styles.item_content}>
              {currentType &&
                currentType.map((value: any, index: any) => (
                  <div className={styles.area_item} key={index + value} style={{ cursor: 'auto' }}>
                    {value}
                  </div>
                ))}
            </div>
          </div>
        </div>
      </Modal>
    </ProCard >
  )
}
