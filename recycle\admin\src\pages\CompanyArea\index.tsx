import { DownOutlined, PlusOutlined, RightOutlined, LeftSquareOutlined } from '@ant-design/icons'
import { Button, Badge, Space, Switch, Modal, notification, Input, Tag, Select } from 'antd'
import type { ActionType, ProColumns } from '@ant-design/pro-table'
import ProTable from '@ant-design/pro-table'
import ProCard from '@ant-design/pro-card'
import { effect, useConnect } from 'dva17'
import { EGet, NCompany, NWorker, EGetFullTimeWorker, RSetState, ECompanyArea, EGetList, ECompanyEdit } from '../../common/action'
import { useEffect, useRef, useState } from 'react'
import styles from './index.module.less'
import { useNavigate, useParams } from 'react-router-dom'
import { computeAuthority } from '../../utils/Authorized/authority'
import { provinceEnum, recycleType } from '../../common/enum'
import { CompanyDeleteArea } from '../../services/company'
import { filter, isEqualWith } from 'lodash'

type Item = {
  id: number
  companyName: string
  recycelCity: string
  recycelProvince: string
  title: string
  address: {
    id: number
    name: string
    province: {
      name: string
    }
    city: {
      name: string
    }
  }[]
  forbidden: number
}
export default () => {
  const navigate = useNavigate()
  const companyparams = useParams()
  const { companyTemp, tempName } = useConnect(NCompany)
  const [visibleEdit, setVisibleEdit] = useState<any>(false)
  const [editData, setEditData] = useState<any>(null)
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>();
  const [selectedRow, setSelectedRow] = useState<any[]>();
  const [removeList, setRemoveList] = useState<any>([])
  const [addList, setAddList] = useState<any>([])
  const [typeList, setTypeList] = useState<any>([])
  const actionRef = useRef<ActionType>()
  const columns: ProColumns<Item>[] = [
    {
      title: '省份',
      copyable: false,
      dataIndex: ['address', 'province', 'name'],
      search: false,
    },

    {
      title: '省份',
      copyable: false,
      dataIndex: 'province',
      hideInTable: true,
      valueEnum: provinceEnum
    },
    {
      title: '关键词',
      copyable: false,
      dataIndex: 'keyWord',
      hideInTable: true,
    },
    {
      title: '城市',
      search: false,
      copyable: false,
      dataIndex: ['address', 'city', 'name'],
    },
    {
      title: '区县',
      dataIndex: 'area',
      copyable: false,
      search: false,
    },
    {
      title: '街道',
      dataIndex: 'name',
      copyable: false,
      search: false,
    },
    {
      title: '操作',
      width: '20%',
      copyable: false,
      ellipsis: true,
      search: false,
      render: (_, row) => (
        <>
          <Button
            disabled={!computeAuthority('服务商新建与编辑')}
            onClick={() => {
              setEditData(row)
              setVisibleEdit(true)
            }}>
            编辑
          </Button>
          <Button
            disabled={!computeAuthority('服务商新建与编辑')}
            onClick={() => {
              deleteCompanyItem(row)
            }}>
            删除
          </Button>
        </>
      ),
    },
  ]
  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    if (editData) {
      console.log(editData, 'editData>')
      setTypeList(editData.typeInfo)
    }
  }, [editData])
  /*--------------------- 响应 ---------------------*/
  const refreshPage = () => {
    if (actionRef.current) {
      actionRef.current.reload()
    }
  }
  const deleteCompanyItem = (e: any) => {
    console.log(e);
    Modal.confirm({
      title: '确认删除该区域',
      content: <div>删除后数据无法恢复！</div>,
      okText: '确认删除',
      cancelText: '退出',
      onOk: async () => {
        if (e && e.length > 0) {
          await CompanyDeleteArea({ arrIds: e }).then(() => {
            refreshPage()
            notification.success({
              message: '成功！',
              description: '删除成功',
              duration: 2,
            })
          })
        } else {
          await CompanyDeleteArea({ code: e.code }).then(() => {
            refreshPage()
            notification.success({
              message: '成功！',
              description: '删除成功',
              duration: 2,
            })
          })
        }
      },
      width: 700,
    })
  }
  // 计算回收区域在原来的基础上是增加了，还是减少了，item表示传入的某一项，which表示其为哪种add or remove
  const computeType = (e: any) => {
    // 选择后list
    let listItem: any = []
    e.forEach((item: any, index: number) => {
      let vo = {
        areaCode: editData.areaCode,
        code: editData.code,
        area: editData.area,
        name: editData.name,
        type: filter(recycleType, { typeName: item })[0].type,
        typeName: item
      }
      listItem.push(vo)
    })
    let equal = isEqualWith(typeList, listItem)
    // console.log('computeType', "原來类型数", typeList, "变化后", listItem, equal);
    if (equal) {
      return
    } else {
      setAddList(listItem)
      setRemoveList(typeList)
    }
    // console.log('remove------', removeList, typeList)
    // console.log('add--------', addList, listItem)
  }


  const handleOK = () => {
    const id = companyparams.id
    if (addList.length > 0 || removeList.length > 0) {
      effect(NCompany, ECompanyEdit, {
        companyID: id,
        addList: addList,
        removeList: removeList,
      }).then(() => {
        setVisibleEdit(false)
        setEditData(null)
        refreshPage()
      })
    }
  }
  const onSelectChange = (keys: any, selectedRows: any) => {
    console.log('selectedRowKeys changed: ', selectedRows.map((vo: any) => vo.code));
    setSelectedRowKeys(keys);
    setSelectedRow(selectedRows.map((vo: any) => vo.code));
  };
  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    // 自定义选择项参考: https://ant.design/components/table-cn/#components-table-demo-row-selection-custom
    // 注释该行则默认不显示下拉选项
    onChange: onSelectChange
    // selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
  };
  /*--------------------- 渲染 ---------------------*/
  return (
    <ProCard>
      <Modal open={visibleEdit} title="服务类型选择"
        destroyOnClose
        onOk={() => {
          handleOK()
        }}
        onCancel={() => {
          setVisibleEdit(false)
          setEditData(null)
        }}>
        <>
          <Select
            mode="multiple"
            placeholder="选择回收类型!"
            style={{ width: '100%' }}
            defaultValue={editData?.typeInfo?.map((vo: any) => vo.typeName)}
            onChange={(e: any) => {
              computeType(e)
            }}
          >
            {recycleType.map((vo: any, index: number) => {
              return (<Select.Option
                label={vo.typeName}
                key={index} value={vo.typeName}> {vo.typeName}</Select.Option>
              )
            })
            }
          </Select>
        </>
      </Modal>
      <ProTable<Item>
        actionRef={actionRef}
        columns={columns}
        request={async (params = {}) => (await effect(NCompany, EGetList, { ...params, id: companyparams.id })) as any}
        pagination={{
          showSizeChanger: true,
        }}
        rowSelection={{
          type: 'checkbox',
          ...rowSelection,
        }}
        tableAlertRender={({ selectedRowKeys, selectedRows, onCleanSelected }) => (
          <Space size={24}>
            <span>
              已选 {selectedRowKeys.length} 项
              <a style={{ marginLeft: 8 }} onClick={onCleanSelected}>
                取消选择
              </a>
            </span>
          </Space>
        )}
        tableAlertOptionRender={() => {
          return (
            <Space size={16}>
              <a onClick={() => {
                deleteCompanyItem(selectedRow)
              }}>批量删除</a>
            </Space>
          );
        }}
        rowKey="id"
        dateFormatter="string"
        headerTitle={(tempName || '0') + "-服务区域"}
        toolBarRender={() => [
          <LeftSquareOutlined size={25} title='返回列表' onClick={() => { navigate('/Other/Company') }} />,
          <Button
            disabled={!computeAuthority('服务商新建与编辑')}
            key="3"
            type="primary"
            onClick={() => {
              navigate(`/Other/CompanyEditor/${companyparams.id}`)
            }}>
            <PlusOutlined />
            新建服务区域
          </Button>
        ]}
      />
    </ProCard >
  )
}
