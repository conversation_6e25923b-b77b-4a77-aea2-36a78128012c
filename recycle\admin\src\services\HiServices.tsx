import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'

export async function getHiOrderList(payload:any) {
  return requestGet('/hiOrder', payload)
}
export async function putHiOrder(payload:any) {
  return requestPut(`/hiOrder/${payload.id}`, payload)
}
export async function postHiOrder(payload:any) {
  return requestPost(`/hiOrder`, payload)
}
export async function putHiOrderImport(payload:any) {
  return requestPut(`/hiOrderImport/${payload.id}`, payload)
}

export async function getHiOrderMaintainList(payload:any) {
  return requestGet('/hiOrderMaintain', payload)
}
export async function putHiWorkerMaintain(payload:any) {
  return requestPut(`/hiWorkerMaintain/${payload.id}`, payload)
}
export async function getHiWorkerMaintainList(payload:any) {
  return requestGet('/hiWorkerMaintain', payload)
}
  
export async function getWhichWorkers(payload:any) {
  return requestPost('/hiWorker/whichWorkers', payload)
}
export async function sendSMS(payload:any) {
  return requestPost('sms', payload)
}
export async function getWorkerDetail(payload:any) {
  return requestGet(`hiWorker/${payload.id}`)
}
export async function postHiOrderBack(payload:any) {
  return requestPost('hiOrder/orderBack', payload)
}
export async function postHiOrderSend(payload:any) {
  return requestPost('hiOrder/orderSend', payload)
}
export async function postHiOrderNew(payload:any) {
  return requestPost('hiOrderNew', payload)
}
export async function postHiOrderConfirm(payload:any) {
  return requestPost('hiOrder/comfirmOrder', payload)
}
export async function seeComplaint(payload:any) {
  return requestPost('hiOrder/complaints', payload)
}
export async function devideOrder(payload:any) {
  return requestPost('hiOrder/devideOrder', payload)
}
export async function seeRating(payload:any) {
  return requestPost('hiOrder/rating', payload)
}
export async function getOrderDetail(payload:any) {
  return requestGet(`hiOrder/${payload.id}`, payload)
}
export async function getOrderCount(payload:any) {
  return requestGet('hiOrderCount', payload)
}
export async function getOrderLog(payload:any) {
  return requestGet(`hiOrderLog/${payload.id}`, payload)
}

export async function getNotice(payload:any) {
  return requestGet(`hiNoticeOrder`, payload)
}

export async function remindOrder(payload:any) {
  return requestPut(`/hiRemind/${payload.id}`, payload)
}




export async function getPrice(payload:any) {
  return requestGet(`HiPrice`, payload)
}

export async function putPrice(payload:any) {
  return requestPut(`/HiPrice/${payload.id}`, payload)
}

export async function postPrice(payload:any) {
  return requestPost(`HiPrice`, payload)
}
export async function getCommissionLogs(payload: any) {
  return requestGet(`HiPrice/${payload.id}/logs`, payload)
}

export async function delPrice(payload: any) {
  return requestDelete(`HiPrice/${payload.id}`, payload)
}