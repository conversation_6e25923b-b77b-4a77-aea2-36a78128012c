'use strict'

const _ = require('lodash')
const moment = require('moment')
const { ReqLog, Company, PriceSet, Order, OrderLog, OrderCancel, WorkerArea, Worker } = require('../../Models')
const { E } = require('../../../../../constants')
const { regReturn, getValueByKey, callBack } = require('../../Util/DebugUtil')
const { orderUpdates, OrderStatus, actions } = require('../../../../constants/E')
const { ASKey, companySelfName } = require('../../Util/Config')
const { WXService, AliYunService } = require('../../Services')
const Config = require('../../Util/Config')
const { yunqixunSms } = require('../../Services/AliYunService')
const Env = use('Env')
//debug
class DebugController {
  async createCNOrder({ request, response }) {
    let { msg_type, logistics_interface, source = E.OrderSource.CaiNiao
    } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: msg_type })
    let content = JSON.parse(logistics_interface)
    let orderUni = await Order.findBy('orderNo', content && content.logisticsId)
    if (orderUni) {
      response.json({
        "success": "true",
        "errorCode": " ",
        "errorMsg": " "
      })
    }
    let type = regReturn(content.packageInfo.itemList[0].extendFields, content.packageInfo.itemList[0].extendFields)
    let company
    let infoPrice
    let autoSend = true
    if (content.sender.city === '合肥市') {
      // company = await Company.findBy("province", '合肥市')
      infoPrice = await PriceSet.query()
        .where("area", '合肥市')
        .where("source", '菜鸟回收')
        .where("type", type)
        .first()
    } else if (content.sender.province === '广东省') {
      // company = await Company.findBy("province", content.sender.province)
      infoPrice = await PriceSet.query()
        .where("area", content.sender.county)
        .where("companyID", 36)
        .where("source", '菜鸟回收')
        .where("type", type)
        .first()
      if (!infoPrice) {
        infoPrice = await PriceSet.query()
          .where("companyID", 36)
          .where("area", content.sender.city)
          .where("source", '菜鸟回收')
          .where("type", type)
          .first()
      }
      autoSend = false
    } else {
      // company = await Company.findBy("province", content.sender.province)
      infoPrice = await PriceSet.query()
        .where("area", content.sender.province)
        .where("source", '菜鸟回收')
        .where("type", type)
        .first()
    }
    if (!infoPrice) {
      infoPrice = await PriceSet.query()
        .where("source", '菜鸟回收')
        .where("companyID", 36)
        .where("type", type)
        .first()
    }
    const createData = {
      orderNo: content.logisticsId || 0,
      mailNo: content.mailNo || 0,
      cpCode: content.cpCode,
      apprizeAmount: getValueByKey("apprizeAmount", content.orderExtendFields) || 10,
      companyID: (company && company.id) || 36,
      workTime: getValueByKey("reserveTime", content.orderExtendFields) || moment().format('YYYY-MM-DD HH:mm:ss'),
      remark: content.remark || null,
      commission: (infoPrice && infoPrice.price) || 80,
      infoFee: (infoPrice && infoPrice.price) || 80,
      type: type,
      userName: content.sender.name,
      userMobile: content.sender.mobile || content.sender.phone,
      province: content.sender.province,
      county: content.sender.county,
      receiverTown: content.sender.senderTown,
      townID: content.sender.senderDivisionId,
      city: content.sender.city,
      address: content.sender.address,
      from: source,
      keywords: content.sender.name + (content.sender.mobile || content.sender.phone) + content.sender.address + content.logisticsId,
      orderType: content.orderBizType || '',
      status: E.OrderStatus.Reservation,
      extendFields: content.packageInfo.itemList[0].extendFields,
    }
    // console.log(createData);
    // 创建预约订单
    let orderVo = await Order.create(createData)
    if (!orderVo) {
      response.json({
        "success": "false",
        "errorCode": "101",
        "errorMsg": "订单创建失败了"
      })
    }
    let data = await OrderLog.create({
      createrID: 118,
      status: E.OrderLogStatus.Create,
      orderID: orderVo.id,
      content: content.remark ? `菜鸟推送订单,${content.remark}` : "菜鸟推送订单",
    })
    let vo = {
      "success": true,
      "errorCode": "",
      "errorMsg": ""
    }
    response.json(vo)
    if (autoSend) {
      let uniworker = await this.sendWorkers(content.sender.senderDivisionId, content.sender.county, content.sender.senderTown, type)
      // console.log(uniworker)
      if (uniworker) {
        let workerInfo = await Worker.find(uniworker)
        orderVo.companyID = workerInfo.companyID
        orderVo.workerID = uniworker
        // console.log(workerInfo)
        await OrderLog.create({
          orderID: orderVo.id,
          content: "系统自动派单",
          status: E.OrderLogStatus.makeOrder,
          createrID: 118
        })
        await orderVo.save()
        if (content.sender.mobile && content.sender.name && workerInfo.toJSON().workerName && workerInfo.toJSON().mobile) {
          await yunqixunSms({ clientName: content.sender.name, clientPhone: content.sender.mobile, masterName: workerInfo.toJSON().workerName, masterPhone: workerInfo.toJSON().mobile, orderID: orderVo.toJSON().id })
        }
        this._remindMaster(workerInfo, orderVo.toJSON(), "自动派单")
      }
    }
  }
  //模板消息 师傅端订单提示
  async _remindMaster(worker, vo, status) {
    let page = `/pages/index/index`
    let messageID = Config.MasterMessageID
    WXService.sendWechat(
      Config.WEAPP.AppID,
      Config.WEAPP.AppSecret,
      worker.openid,
      vo.waste_1st_ID,
      vo.createdAt,
      vo.orderNo,
      status,
      page,
      messageID
    )
  }
  async sendWorkers(code, area, street, typeName) {
    // console.log(code, area, street, typeName, "code, area, street, typeName");
    let workerIDs = []
    if (code) {
      let area = await WorkerArea.query()
        .select('id', 'workerID', "companyID", "name", "code", "detailName").where('code', code)
        .where('detailName', typeName).fetch()
      workerIDs = area.rows.map((vo) => vo.workerID)
    }
    if (((workerIDs && workerIDs.length === 0) || !workerIDs) && street && typeName) {
      let prearea = await WorkerArea.query()
        .select('id', 'workerID', "companyID", "name", "code", "detailName")
        .where('area', area)
        .where('name', street)
        .where('detailName', typeName).fetch()
      workerIDs = prearea.rows.map((vo) => vo.workerID)
    }
    // console.log(workerIDs, "workerIDs>>>");
    let worker = _.uniq(workerIDs)
    // console.log(worker, "worker>>>");
    if (worker && worker.length === 1) {
      // console.log(worker[0], "worker[0]>>>");
      return worker[0]
    } else {
      return false
    }
  }
  async updateCNOrder({ request, response }) {
    let { msg_type, logistics_interface
    } = request.all()
    let content = JSON.parse(logistics_interface)
    await ReqLog.create({ req: JSON.stringify(request.all()), source: msg_type })
    let orderVo = await Order.query().where('orderNo', content.logisticsId).where('mailNo', content.mailNo).with('worker').first()
    if (!orderVo) {
      return {
        "cpCode": content.cpCode,
        "success": "false",
        "errorCode": "SYSTEM_ERROR",
        "logisticID": content.logisticsId,
        "errorMsg": "订单未找到"
      }
    }
    // 菜鳥取消
    if ((content && content.status === orderUpdates.TMS_CANCEL.name) || (content && content.status === orderUpdates.TMS_REJECT.name) || (content && content.status === orderUpdates.TMS_UNCANVASS.name)) {
      orderVo.status = OrderStatus.Cancelled
      orderVo.remark = content.remark || null
      await OrderCancel.create({
        orderID: orderVo.id,
        cancelReason: (content && content.remark) || (content && content.status),
        whoCancel: E.CancleOrderStatus.System,
      })
      let cvo = {
        "tracesList": [
          {
            "logisticProviderID": orderVo.cpCode,
            "extendFields": "",
            "traces": [
              {
                "country": "China",
                "city": orderVo.city,
                "tz": "+8",
                "remark": "系统取消回执",
                "province": orderVo.province,
                "extendFields": [
                  {
                    "value": 1,
                    "key": "cnRecycleType",
                    "desc": "回收类型"
                  }
                ],
                "action": "ORDER_CLOSED",
                "facilityName": companySelfName,
                "facilityType": "1",
                "contacter": orderVo.$relations.worker ? orderVo.$relations.worker.workerName : null,
                "outBizCode": moment().format('x'),
                "time": moment().format('YYYY-MM-DD HH:mm:ss'),
                "contactPhone": orderVo.$relations.worker ? orderVo.$relations.worker.mobile : null,
                "desc": "系统取消回执"
              }
            ],
            "txLogisticID": orderVo.orderNo,
            "mailNos": orderVo.mailNo
          }
        ]
      }
      await callBack('TRACEPUSH', orderVo.cpCode, cvo)
    } else if ((content && content.status === orderUpdates.TMS_MODIFY_ADDRESS.name)) {
      orderVo.remark = content.remark || content.status
      orderVo.address = content.receiver && content.receiver.detailAddress
      orderVo.city = content.receiver && content.receiver.city
      orderVo.province = content.receiver && content.receiver.province
      if (content.receiver && content.receiver.town) {
        orderVo.receiverTown = content.receiver && content.receiver.town
      }
      let tvo = {
        "mailNo": orderVo.toJSON().mailNo, "eventCode": "MODIFY_ADDRESS_SUCCESS",
        "logisticProviderID": content && content.cpCode,
        "occurTime": moment().format('YYYY-MM-DD HH:mm:ss'),
        "extendFields": [],
        "txLogisticID": orderVo.toJSON().orderNo
      }
      await callBack('TMS_EVENT_CALLBACK', content && content.cpCode, tvo)
      //  完成訂單
    } else if (content && content.status === orderUpdates.TMS_COMPLETE.name) {
      let data = await OrderLog.create({
        createrID: 118,
        status: E.OrderLogStatus.Over,
        orderID: orderVo.id,
        content: E.OrderLogStatus.Over,
      })
      orderVo.status = OrderStatus.Completed
      orderVo.cFinishedAt = moment().format('YYYY-MM-DD HH:mm:ss')
    }
    await orderVo.save()
    let data = await OrderLog.create({
      createrID: 118,
      status: E.OrderLogStatus.Updates,
      orderID: orderVo.id,
      content: content.remark ? `菜鸟修改订单,${content && content.remark},${content && content.status}` : "菜鸟更新订单",
    })

    let vo = {
      "cpCode": content.cpCode || "OTHER_31424531",
      "success": true,
      "errorCode": "",
      "logisticID": content.logisticsId || 0,
      "errorMsg": ""
    }
    response.json(vo)
  }
}

module.exports = DebugController
