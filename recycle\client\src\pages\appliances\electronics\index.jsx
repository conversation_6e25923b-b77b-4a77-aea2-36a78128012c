import React, { useState, useEffect } from 'react'
import { View, Text, But<PERSON>, Picker, Image } from '@tarojs/components'
import Taro, { useDidShow } from '@tarojs/taro'
import './index.less'
import { useSelector } from 'react-redux'
import { useStore } from 'react-redux'
import { NPublics } from '../../../config/constants'
import { SERVER_HOME } from '../../../config/config'

const Electronics = () => {
    const { dispatch } = useStore()
    // 状态管理
    const { indexPage, priceInfo } = useSelector(state => state.NPublics)

    const [selectedCategory, setSelectedCategory] = useState(null)
    const [selectedCondition, setSelectedCondition] = useState(null)
    const [selectedWorking, setSelectedWorking] = useState(null)
    const [subType, setSubType] = useState('')
    const [years, setYears] = useState('')
    const [estimatedPrice, setEstimatedPrice] = useState(0)
    const [selectedColdType, setSelectedColdType] = useState(null)
    const [selectedSpu, setSelectedSpu] = useState(null)
    const [images, setImages] = useState([]) // 存储上传的图片列表

    // 选项数据
    const [categories, setCategories] = useState([])
    const [subTypeOptions, setSubTypeOptions] = useState([])
    const [yearsOptions, setYearsOptions] = useState([])
    const [spuOptions, setSpuOptions] = useState([])
    const [outsideOptions, setOutsideOptions] = useState([])
    const [functionsOptions, setFunctionsOptions] = useState([])
    const [coldTypeOptions, setColdTypeOptions] = useState([])

    // 页面显示时触发
    useDidShow(() => {
        // 可以在这里添加页面显示时的逻辑
        setCategories(indexPage.Paths)
    })

    // 更新价格计算
    useEffect(() => {
        updatePrice()
    }, [selectedCategory, years, selectedCondition, selectedWorking, selectedColdType, selectedSpu])

    useEffect(() => {
        if (selectedCategory) {
            getThePrice({ level: 1, name: selectedCategory })
            setSubTypeOptions([])
            setYearsOptions([])
            setOutsideOptions([])
            setFunctionsOptions([])
            setColdTypeOptions([])
            setSpuOptions([])
            setSelectedCondition(null)
            setSelectedWorking(null)
            setSelectedColdType(null)
            setSelectedSpu(null)
        }
    }, [selectedCategory])
    // 获取当前选中类型的子类型选项
    const getTypeOptions = () => {
        let options = []
        priceInfo?.subType?.forEach((item, index) => {
            options.push(item.subType)
        })
        return options
    }
    // 获取当前选中类型的子类型选项
    const getYearsOptions = () => {
        let options = []
        priceInfo?.years?.forEach((item, index) => {
            options.push({ value: index, label: item.years })
        })
        return options
    }
    // 获取当前选中类型的子类型选项
    const getOutsideOptions = () => {
        let options = []
        priceInfo?.outside?.forEach((item, index) => {
            options.push({ value: index, label: item.outside, desc: item.outside })
        })
        return options
    }
    // 获取当前选中类型的子类型选项
    const getFunctionsOptions = () => {
        let options = []
        priceInfo?.functions?.forEach((item, index) => {
            options.push({ value: index, label: item.functions })
        })
        return options
    }
    // 获取当前选中类型的子类型选项
    const getColdTypeOptions = () => {
        let options = []
        priceInfo?.coldType?.forEach((item, index) => {
            if (item.coldType) {
                options.push({ value: index, label: item.coldType })
            }
        })
        return options
    }
    // 获取当前选中类型的子类型选项
    const getSpuOptions = () => {
        let options = []
        priceInfo?.spu?.forEach((item, index) => {
            if (item.spu) {
                options.push({ value: index, label: item.spu })
            }
        })
        return options
    }
    useEffect(() => {
        if (priceInfo && !priceInfo?.price) {
            if (priceInfo?.subType?.length > 0) {
                setSubTypeOptions(getTypeOptions())
            }
            if (priceInfo?.years?.length > 0) {
                setYearsOptions(getYearsOptions())
            }
            if (priceInfo?.outside?.length > 0) {
                setOutsideOptions(getOutsideOptions())
            }
            if (priceInfo?.functions?.length > 0) {
                setFunctionsOptions(getFunctionsOptions())
            }
            if (priceInfo?.coldType?.length > 0) {
                setColdTypeOptions(getColdTypeOptions())
            }
            if (priceInfo?.spu?.length > 0) {
                setSpuOptions(getSpuOptions())
            }
        } else {
            setEstimatedPrice(priceInfo?.price)
        }
    }, [priceInfo])

    // 价格计算逻辑
    const updatePrice = async () => {
        if (!selectedCategory || !subType || !years || !selectedCondition || !selectedWorking || !selectedSpu || (!selectedColdType && selectedCategory == '空调')) {
            setEstimatedPrice(0)
            return
        }
        await getThePrice({
            level: 2,
            name: selectedCategory,
            subType,
            years,
            outside: selectedCondition,
            functions: selectedWorking,
            coldType: selectedColdType,
            spu: selectedSpu
        })
    }

    // 下一步
    const nextStep = () => {
        const formData = {
            category: selectedCategory,
            subType,
            years,
            outside: selectedCondition,
            functions: selectedWorking,
            coldType: selectedColdType,
            spu: selectedSpu,
            estimatedPrice: estimatedPrice,
            type: '旧家电回收',
            images: images // 添加图片数据
        }

        Taro.setStorageSync('recycleForm', JSON.stringify(formData))
        Taro.navigateTo({ url: '/pages/appliances/order-form/index' })
    }
    async function getThePrice(payload) {
        await dispatch.NPublics[NPublics.EGetPrice](payload)
    }

    // 选择子类型
    const handleSubChange = (e) => {
        const options = getTypeOptions();
        setSubType(options[e.detail.value])
    }

    // 选择年限
    const handleYearsChange = (e) => {
        setYears(yearsOptions[e.detail.value].label)
    }

    // 图片选择函数
    const chooseImage = () => {
        Taro.chooseImage({
            count: 4 - images.length, // 最多可选择的图片数量
            sizeType: ['compressed'], // 压缩图
            sourceType: ['album', 'camera'], // 来源
            success(res) {
                // 显示上传中提示
                Taro.showLoading({
                    title: '上传中...',
                    mask: true
                })

                const newImages = [...images]
                const promises = []

                // 处理每张选中的图片
                res.tempFilePaths.forEach((path, index) => {
                    // 如果总数超过4张，不处理
                    if (newImages.length + index >= 4) return

                    // 先添加本地路径用于预览
                    newImages.push({
                        url: path,
                        path: path,
                        uploading: true
                    })

                    // 创建上传任务
                    const uploadPromise = new Promise((resolve, reject) => {
                        Taro.uploadFile({
                            url: SERVER_HOME + 'file',
                            filePath: path,
                            name: 'file',
                            success: (uploadRes) => {
                                try {
                                    // 假设服务器返回JSON格式的数据
                                    const data = typeof uploadRes.data === 'string' ?
                                        JSON.parse(uploadRes.data) : uploadRes.data

                                    // 更新图片状态为已上传，保存服务器返回的URL
                                    const imageIndex = newImages.findIndex(img => img.path === path)
                                    if (imageIndex !== -1) {
                                        newImages[imageIndex] = {
                                            url: path, // 使用本地路径显示
                                            path: path,
                                            serverUrl: data.url || data.path || path, // 服务器路径
                                            uploading: false
                                        }
                                        // 立即更新状态，使UI反映当前图片已上传完成
                                        setImages([...newImages])
                                    }
                                    resolve()
                                } catch (e) {
                                    reject(e)
                                }
                            },
                            fail: (error) => {
                                console.error('上传图片失败:', error)
                                // 当上传失败时修改图片状态
                                const imageIndex = newImages.findIndex(img => img.path === path)
                                if (imageIndex !== -1) {
                                    newImages[imageIndex] = {
                                        ...newImages[imageIndex],
                                        uploading: false,
                                        uploadFailed: true
                                    }
                                    // 更新UI显示上传失败状态
                                    setImages([...newImages])
                                }
                                reject(error)
                            }
                        })
                    })

                    promises.push(uploadPromise)
                })
                // 更新状态，显示上传中的图片
                setImages(newImages)

                // 等待所有上传完成
                Promise.all(promises)
                    .then(() => {
                        // 更新图片状态，确保UI显示正确
                        setImages([...newImages])
                        Taro.hideLoading()
                        Taro.showToast({
                            title: '上传成功',
                            icon: 'success',
                            duration: 1500
                        })
                    })
                    .catch((error) => {
                        console.error('上传失败:', error)
                        // 即使失败也更新状态，确保UI正确显示
                        setImages([...newImages])
                        Taro.hideLoading()
                        Taro.showToast({
                            title: '部分图片上传失败',
                            icon: 'none',
                            duration: 2000
                        })
                    })
            },
            fail() {
                Taro.showToast({
                    title: '选择图片失败',
                    icon: 'none'
                })
            }
        })
    }

    // 删除图片
    const removeImage = (index) => {
        const newImages = [...images]
        newImages.splice(index, 1)
        setImages(newImages)
    }

    // 预览图片
    const previewImage = (current) => {
        const urls = images.map(img => typeof img === 'string' ? img : img.serverUrl)
        if (urls.length > 0) {
            Taro.previewImage({
                current,
                urls
            })
        }
    }

    // 判断下一步按钮是否可用
    const isNextButtonDisabled = !selectedCategory || !subType || !years || !selectedCondition || !selectedWorking || !selectedSpu

    return (
        <View className='electronics-page'>
            <View className='content'>
                <View className='category-section'>
                    <View className='section-title'>
                        <Text className='title-icon'>📱</Text>
                        <Text>选择家电类型</Text>
                    </View>

                    <View className='category-grid'>
                        {categories.map(item => (
                            <View
                                className={`category-item ${selectedCategory === item.name ? 'selected' : ''}`}
                                key={item.type}
                                onClick={() => setSelectedCategory(item.name)}
                            >
                                <View className='category-icon'>
                                    <Image src={item.img} />
                                </View>
                                <View className='category-name'>{item.name}</View>
                            </View>
                        ))}
                    </View>
                </View>

                <View className='form-section'>
                    <View className='form-group'>
                        <Text className='form-label'>机器类型</Text>
                        <Picker mode='selector' range={subTypeOptions} onChange={handleSubChange}>
                            <View className='form-select'>
                                {subType || '请选择类型'}
                            </View>
                        </Picker>
                    </View>

                    <View className='form-group'>
                        <Text className='form-label'>使用年限</Text>
                        <Picker mode='selector' range={yearsOptions} rangeKey='label' onChange={handleYearsChange}>
                            <View className='form-select'>
                                {years || '请选择使用年限'}
                            </View>
                        </Picker>
                    </View>

                    <View className='form-group'>
                        <Text className='form-label'>规格型号</Text>
                        <View className='condition-grid'>
                            {spuOptions.map(item => (
                                <View
                                    className={`condition-item ${selectedSpu === item.label ? 'selected' : ''}`}
                                    key={item.value}
                                    onClick={() => setSelectedSpu(item.label)}
                                >
                                    <View>{item.label}</View>
                                </View>
                            ))}
                        </View>
                    </View>

                    <View className='form-group'>
                        <Text className='form-label'>外观成色</Text>
                        <View className='condition-grid'>
                            {outsideOptions.map(item => (
                                <View
                                    className={`condition-item ${selectedCondition === item.label ? 'selected' : ''}`}
                                    key={item.value}
                                    onClick={() => setSelectedCondition(item.label)}
                                >
                                    <View>{item.label}</View>
                                </View>
                            ))}
                        </View>
                    </View>

                    <View className='form-group'>
                        <Text className='form-label'>功能状态</Text>
                        <View className='condition-grid'>
                            {functionsOptions.map(item => (
                                <View
                                    className={`condition-item ${selectedWorking === item.label ? 'selected' : ''}`}
                                    key={item.value}
                                    onClick={() => setSelectedWorking(item.label)}
                                >
                                    <View>{item.label}</View>
                                </View>
                            ))}
                        </View>
                    </View>

                    {coldTypeOptions.length > 0 && <View className='form-group'>
                        <Text className='form-label'>制冷类型</Text>
                        <View className='condition-grid'>
                            {coldTypeOptions.map(item => (
                                <View
                                    className={`condition-item ${selectedColdType === item.label ? 'selected' : ''}`}
                                    key={item.value}
                                    onClick={() => setSelectedColdType(item.label)}
                                >
                                    <View>{item.label}</View>
                                </View>
                            ))}
                        </View>
                    </View>}
                    {/* 添加实物图片上传区域 */}
                    <View className='form-group'>
                        <Text className='form-label'>实物照片 {images.length > 0 ? `(${images.length}/4)` : '(最多4张)'}</Text>
                        <View className='image-upload-area'>
                            {/* 已上传的图片展示 */}
                            <View className='uploaded-images'>
                                {images.map((image, index) => (
                                    <View
                                        className={`image-item ${image.uploading ? 'uploading' : ''}`}
                                        key={index}
                                        onClick={() => previewImage(typeof image === 'string' ? image : image.serverUrl)}
                                    >
                                        <Image
                                            src={typeof image === 'string' ? image : image.serverUrl}
                                            className='preview-image'
                                            mode='aspectFill'
                                            lazyLoad
                                        />
                                        {image.uploading && (
                                            <View className='uploading-overlay'>
                                                <Text className='uploading-text'>上传中...</Text>
                                            </View>
                                        )}
                                        {image.uploadFailed && (
                                            <View className='uploading-overlay failed'>
                                                <Text className='uploading-text'>上传失败</Text>
                                            </View>
                                        )}
                                        <View
                                            className='delete-icon'
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                removeImage(index);
                                            }}
                                        >
                                            ×
                                        </View>
                                    </View>
                                ))}

                                {/* 添加图片按钮，只有当图片数量少于4张时才显示 */}
                                {images.length < 4 && (
                                    <View
                                        className='add-image-btn'
                                        onClick={chooseImage}
                                    >
                                        <Text className='add-icon'>+</Text>
                                        <Text className='add-text'>添加照片</Text>
                                    </View>
                                )}
                            </View>
                        </View>
                        <Text className='image-tips'>点击图片可预览，添加家电实物照片有助于师傅更准确估价</Text>
                    </View>
                </View>

                <View className='price-display'>
                    <View className='price-label'>预估回收价格</View>
                    <View className='price-value'>¥ {estimatedPrice}</View>
                    <View className='price-note'>最终价格以师傅上门确认为准</View>
                </View>

                <View className='tips'>
                    <View className='tips-title'>💡 回收须知</View>
                    <View className='tips-content'>
                        • 大型家电提供免费上门拆卸<br />
                        • 支持同时回收多件家电
                    </View>
                    <View className='feature-tags'>
                        <Text className='feature-tag'>免费上门</Text>
                        <Text className='feature-tag'>当场估价</Text>
                        <Text className='feature-tag'>当场付款</Text>
                    </View>
                </View>

                <Button
                    className='next-btn'
                    disabled={isNextButtonDisabled}
                    onClick={nextStep}
                >
                    下一步：填写订单信息
                </Button>
            </View>
        </View>
    )
}

export default Electronics
