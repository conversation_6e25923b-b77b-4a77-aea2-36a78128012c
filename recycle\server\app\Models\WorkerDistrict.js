'use strict'

const Model = use('Model')

//订单废品关联表
class WorkerDistrict extends Model {
  static get table(){return 'worker_district'}
  static get primaryKey () { return 'id' }
  static get createdAtColumn (){return 'createdAt'}
  static get updatedAtColumn (){return 'updatedAt'}

  // order () {
  //   return this.belongsTo('App/Models/Order','orderID','id')
  // }
  // waste () {
  //   return this.hasOne('App/Models/Waste','wasteID','id')
  // }
}

module.exports = WorkerDistrict
