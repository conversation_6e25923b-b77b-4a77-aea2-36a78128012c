/**
 * Created by <PERSON>(qq:404487132) on 20250410.
 */
const Route = use('Route')
const Env = use('Env')

// 无需登录
Route.group(() => {
  Route.post('worker/loginBypasswd', 'WorkerController.loginBypasswd')
  Route.post('worker/register', 'WorkerController.register')

  Route.any('hook/pay', 'PayController.hookWechatPay') //微信支付回调
  Route.any('hook/orderPay', 'PayController.hookOrderWechatPay') //微信支付回调
  Route.any('hook/refund', 'PayController.hookRefund')
  Route.post('checkCode', 'TrackingController.check')
  Route.post('postCode', 'TrackingController.store')
})
  .prefix('app/v1')
  .namespace('AppView')
  
// 需要登录
Route.group(() => {
  Route.get('getIndexData', 'OrderController.getIndexData')//获取首页数据
  Route.resource('order', 'OrderController').middleware(new Map([[['store'], ['postLock']]])) //订单
  Route.get('worker/info', 'WorkerController.show')//师傅信息
  Route.get('getOrderCount', 'OrderController.getOrderCount')//获取订单数量
  Route.post('orderRemark', 'OrderController.orderRemark')//订单备注
  Route.post('orderCall', 'OrderController.orderCall')//师傅拨打电话
  Route.post('order/orderBack', 'OrderController.orderBack') //订单撤回
  Route.get('worker/changePassword', 'WorkerController.changePassword') // 师傅修改密码
  Route.post('comfirmOrder', 'OrderController.comfirmOrder') //订单确认
  Route.get('walletlog', 'PayController.walletlog') //walletlog  
  Route.post('workerPay', 'PayController.workerPay') //师傅支付
  Route.get('orderLog/:id', 'OrderController.getOrderLog') // 订单记录
  Route.get('getCoWorker', 'WorkerController.getCoWorker') // 获取同事师傅
  Route.post('manager/orderChange', 'OrderController.orderChange') //订单改派  

  Route.get('chargePrice', 'ChargePriceController.index') // 师傅最低接单收费
  Route.post('createScheme', 'ChargePriceController.createScheme') // 创建scheme
  Route.get('worker/profile', 'WorkerController.getProfileData') // 师傅信息
  Route.get('worker/board', 'WorkerController.getWorkerBoardData') // 师傅看板数据
  Route.post('order/address', 'OrderController.getOrderAddressList') // 师傅订单地址
  Route.resource('banners', 'BannersController') //轮播图


  Route.resource('worker', 'WorkerController') //上门师傅信息
  Route.resource('company', 'CompanyController') //公司信息
  Route.post('order/chart', 'OrderController.chart') //获取饼状图信息
  Route.get('manager/order', 'OrderController.managerOrder') //订单管理  
  Route.get('orderPrice', 'OrderController.orderPrice')
  Route.post('worker/addCoWorker', 'WorkerController.addAssistant')
  Route.post('getAssistantOrders', 'WorkerController.getAssistantOrders')
})
  .prefix('app/v1')
  .namespace('AppView')
  .middleware('masterAuth')

// 文件上传接口
Route.group(() => {
  Route.post('file', 'FileController.file')
  Route.post('sms', 'SendSmsController.smsSend') //短信提醒
  Route.resource('address', 'AddressController') //获取街道
}).prefix('app/v1')
