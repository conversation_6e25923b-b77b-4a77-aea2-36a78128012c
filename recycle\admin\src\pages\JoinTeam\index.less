.joinTeam-container {
  .ant-pro-table {
    .ant-pro-table-search {
      padding: 24px;
      background: #fafafa;
      border-radius: 6px;
      margin-bottom: 24px;
    }
  }

  .ant-modal {
    .ant-modal-body {
      padding: 24px;
    }
  }

  .ant-drawer {
    .ant-drawer-body {
      padding: 24px;
    }
  }

  .status-tag {
    &.success {
      color: #52c41a;
      background-color: #f6ffed;
      border-color: #b7eb8f;
    }

    &.error {
      color: #ff4d4f;
      background-color: #fff2f0;
      border-color: #ffccc7;
    }
  }
}

.ant-form-item {
  margin-bottom: 16px;
}

.ant-btn {
  margin-right: 8px;
  
  &:last-child {
    margin-right: 0;
  }
}

.ant-table {
  .ant-table-tbody > tr > td {
    padding: 12px 16px;
  }
}

.ant-pro-table-toolbar {
  padding: 16px 0;
}

.ant-pro-table-list-toolbar-container {
  margin-bottom: 16px;
}

.search-form {
  .ant-form-item {
    margin-bottom: 0;
  }
}

.operation-buttons {
  display: flex;
  gap: 8px;
  
  .ant-btn {
    margin-right: 0;
  }
}

@media (max-width: 768px) {
  .joinTeam-container {
    .ant-pro-table {
      .ant-pro-table-search {
        padding: 16px;
      }
    }
  }
  
  .operation-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .joinTeam-container {
    .ant-descriptions {
      margin-bottom: 16px;
    }
  }
} 