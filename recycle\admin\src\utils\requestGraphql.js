import { notification } from 'antd';
import router from 'umi/router';
import { Config } from '../config'
import { getToken } from './authority'
import { GraphQLClient} from 'graphql-request'
export default function request(url, options) {
    let {method, query} = options
    if(!query){return }
    return new Promise((resolve, reject)=>{
        // 添加url前缀
        if (!url.indexOf('https://') > -1 || !url.indexOf('http://') > -1) {
            url = Config.SERVER_HOME + (url.indexOf('/')===0?url.substr(1):url)
        }
        // TODO 添加token
        let token = getToken();
        if (token) {

        }else{}
        const graphQLClient = new GraphQLClient(url, {
            method,
            mode: 'cors',
            headers: {
                Accept: 'application/json',
                Pragma:'no-cache',
                'Cache-Control':'no-cache',
                Expires:0,
                'Content-Type': 'application/json; charset=utf-8',
                authorization: `Bearer ${token}`,
            }
        })
        graphQLClient.request(query).then(response=>{
            console.log(data)
            resolve(response.data)
        }).catch((error)=>{
            if (error.response){
                let {status,data} = error.response;
                if (status === 400){
                    let {error, message} = data
                    notification.error({
                        message: `请求错误`,
                        description: message,
                    });
                }
                if (status === 401) {
                    window.g_app._store.dispatch({
                        type: 'login/logout',
                    });
                    return;
                }
                if (status === 403) {
                    router.push('/exception/403');
                    return;
                }
                if (status <= 504 && status >= 500) {
                    router.push('/exception/500');
                    return;
                }
                if (status >= 404 && status < 422) {
                    router.push('/exception/404');
                }
            }else if (error.request){
                throw error
            }else{
                throw error
            }
        })
    })
}
export function requestGet(url, body) {
    return request(url, { method: 'GET', query });
}
export function requestDelete(url) {
    return request(url, { method: 'DELETE' });
}
export function requestPost(url, body) {
    console.log('requestPost:', url, body);
    return request(url, { method: 'POST', query });
}
export function requestPatch(url, body) {
    return request(url, { method: 'PATCH', query });
}
export function requestPut(url, body) {
    return request(url, { method: 'PUT', query });
}