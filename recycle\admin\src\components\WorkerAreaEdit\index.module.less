.main{
  display: flex;
  flex-direction: column;
  background: #ffffff;
  padding: 20px 10px;
  .wrapper{
    display: flex;
    margin-bottom: 20px;
    .title{
      display: inline-block;
      height: 34px;
      line-height: 34px;
      text-align: right;
      flex-shrink: 0;
      min-width: 80px;
    }
    .right_wrapper{
      display: flex;
      flex-wrap: wrap;
      .area_item{
        width: 120px;
        height: 34px;
        border: 1px solid #1890ff;
        border-radius: 4px;
        line-height: 34px;
        text-align: center;
        color: #1890ff;
        flex-shrink: 0;
        margin-right: 20px;
        margin-bottom: 10px;
        cursor: pointer;
        overflow:hidden;
        text-overflow:ellipsis;
        white-space:nowrap
      }
      .selected{
        background: #1890ff;
        color: #ffffff;
      }
    }
  }
  
  .tableWrapper {
    margin-bottom: 20px;
  }

  // 新增区域编辑样式
  .areaEditContainer {
    width: 100%;
    padding: 10px;
  }

  .formRow {
    display: flex;
    margin-bottom: 20px;
  }

  .formLabel {
    width: 100px;
    flex-shrink: 0;
    line-height: 32px;
    position: relative;
    .allSelectBtn {
      color: #1890ff;
      cursor: pointer;
      font-size: 12px;
      margin-left: 5px;
      &:hover {
        text-decoration: underline;
      }
    }
  }

  .formContent {
    flex: 1;
  }

  .buttonGroup {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    
    :global(.ant-btn) {
      margin-bottom: 8px;
      min-width: 80px;
    }
  }
}
:global{
  .ant-checkbox-wrapper{
    display: inline-block;
    width: 40px;
    height: 34px;
    line-height: 34px;
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap
  }
  .ant-checkbox-wrapper + .ant-checkbox-wrapper{
    margin-left: 0;
  }
} 