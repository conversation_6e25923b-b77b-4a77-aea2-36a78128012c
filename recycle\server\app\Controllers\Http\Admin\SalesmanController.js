'use strict'

const _ = require('lodash')
const moment = require('moment')
const path = require('path')
const { <PERSON>man, Worker } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { WXService } = require('../../../Services')
const Config = require('../../../Util/Config')

const OSS = require('ali-oss')
const client = new OSS({
  region: 'oss-cn-shanghai',
  accessKeyId: "LTAI5tGy18wdAHdv6bqF4x83",
  accessKeySecret: "******************************",
  bucket: "hanhan-oss1"
})
//海尔促销员
class SalesmanController {
  async index({ request, response }) {
    let { current = 1, pageSize = 10, startDate, endDate, name } = request.all()
    let query = Salesman.query().with('workerInfo')
      .where({ superior_id: null })
    if (name) {
      query.where('name', 'like', `%${name}%`)
    }
    if (startDate && endDate) {
      query.with("userCount", builder => {
        builder
          .where('createdAt', '>=', moment(startDate).format('YYYY-MM-DD'))
          .where('createdAt', '<=', moment(endDate).add(1, 'd').format('YYYY-MM-DD'))
        return builder
      }).with("orderCount", builder => {
        builder
          .where('finishedAt', '>=', moment(startDate).format('YYYY-MM-DD'))
          .where('finishedAt', '<=', moment(endDate).add(1, 'd').format('YYYY-MM-DD'))
        return builder
      }).with('salesman', builder => {
        builder
          .where('createdAt', '>=', moment(startDate).format('YYYY-MM-DD'))
          .where('createdAt', '<=', moment(endDate).add(1, 'd').format('YYYY-MM-DD'))
        return builder
      })
    } else {
      query.with('salesman').with("userCount").with("orderCount")
    }
    let data = await query.paginate(current, pageSize)
    response.json(data)
  }
  async store({ request, response }) {
    let { name, status, superior_id, phone, remark } = request.all()
    let vo = await Salesman.create({ phone, name, remark, status, superior_id, title: (superior_id ? '2' : '1') })
    const { data: buf } = await WXService.getwxacode({
      AppID: Config.Client.AppID,
      AppSecret: Config.Client.AppSecret,
      data: {
        path: `pages/home/<USER>
        width: 430,
        env_version: 'release',
      },
    })
    let ossFilePath = path.join('hanhan/salesman', `DG${vo.id}.jpg`).replace(/\\/g, '/')
    let ossres = await client.put(ossFilePath, buf)
    vo.img_url = ossres.url
    const res = await vo.save()
    response.json(res)
  }
  async GenWorker({ request, response }) {
    let { } = request.all()
    let vo = await Worker.query()
      .select('id', 'workerName', 'mobile', 'isUse', 'forbidden')
      .where('isUse', E.WorkerAccountStatus.Agree)
      .where('forbidden', 1)
      .fetch()
    let ids = vo.rows.map(item => item)
    _.forEach(ids, async function (value) {
      let salesmanvo = await Salesman.create({workerID: value.id, phone: value.mobile, name: value.workerName, status: 1, title: (value.id ? '1' : '2') })
      const { data: buf } = await WXService.getwxacode({
        AppID: Config.Client.AppID,
        AppSecret: Config.Client.AppSecret,
        data: {
          path: `pages/home/<USER>
          width: 430,
          env_version: 'release',
        },
      })
      let ossFilePath = path.join('hanhan/salesman', `DG${salesmanvo.id}.jpg`).replace(/\\/g, '/')
      let ossres = await client.put(ossFilePath, buf)
      salesmanvo.img_url = ossres.url
      await salesmanvo.save()
    })
    response.json({code:200,message:'生成成功'})
  }
  async show({ request, params, response }) { }
  async update({ request, params, response }) {
    let { name, status, phone, remark } = request.all()
    let vo = await Salesman.query().where({ id: params.id }).update({ name: name, status, phone, remark })
    response.json(vo)
  }
  async destroy({ request, params, response }) { }
}
module.exports = SalesmanController
