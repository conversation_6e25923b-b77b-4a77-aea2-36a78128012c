'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Waste2ndCategory, CompanySelfWastePrice } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//废品二级分类
class Waste2ndCategoryController {
  async getWaste({ request, params, response }) {
    let { companyID = 1 } = request.all()
    let vo = await Waste2ndCategory.query().where('id', params.id).first()
    // console.log(vo.toJSON());
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    } else {
      let list = await vo.waste().fetch()
      // console.log(list.toJSON());
      for (let i = 0; i < list.rows.length; i++) {
        let self = await CompanySelfWastePrice.query().where('wasteID', list.rows[i].$attributes.id).where('priceType', 1).where({ companyID }).first()
        // console.log(self.toJSON());
        if (self) {
          list.rows[i].$attributes.price = self.price
        }
      }
      response.json(list)
    }
  }
}

module.exports = Waste2ndCategoryController
