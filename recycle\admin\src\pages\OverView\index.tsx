import { DownloadOutlined } from '@ant-design/icons'
import type { ProFormInstance } from '@ant-design/pro-components'
import { <PERSON><PERSON>, Modal } from 'antd'
import type { ProColumns, ActionType } from '@ant-design/pro-table'
import ProTable, { } from '@ant-design/pro-table'
import ProCard from '@ant-design/pro-card'
import { effect, reducer, useConnect } from 'dva17'
import { useEffect, useRef, useState } from 'react'
import { EGet, EGetList, NCollection, NOrder, NUser, NWorker, RSetState, NCompany } from '../../common/action'
import { AreasEnum, fromEnum, orderStatusEnum, recycleTypeEnum } from '../../common/enum'
import { SERVER_HOME, SERVER_HOME_File } from '../../common/config'
import qs from 'qs'
import OrderDetail from '../../components/OrderDetail'
import { computeAuthority } from '../../utils/Authorized/authority'
import { useParams, } from 'react-router-dom'
import { remindOrder } from '../../services/order'
import dayjs from 'dayjs'
type Item = {
  id: number
  number: number
  remindCount: number
  orderNo: string
  from: string
  createdAt: any
  mobile: string
  takeTime: any
  wasteType: string
  estimatedMoney: number
  company: {
    companyName: string
  }
  userAddress: {
    province: string
    city: string
    district: string
    subDistrct: string
  }
}

export default () => {
  const formRef = useRef<ProFormInstance>()
  const { currentUser } = useConnect(NUser)
  const { companyList } = useConnect(NCompany)
  const [ComEnum, setComEnum] = useState<any>({})
  const actionRef = useRef<ActionType>()
  const params = useParams()
  const { lastSearch } = useConnect(NCollection)
  const { visibleLog, logData } = useConnect(NOrder)

  useEffect(() => {
    effect(NCompany, EGet, {})
  }, [])

  useEffect(() => {
    const enumObject: Record<string, { text: string; status: string }> = {};
    companyList.forEach((item: any) => {
      enumObject[item.id] = {
        text: item.companyName,
        status: 'Processing',
      };
    });
    setComEnum(enumObject);
  }, [companyList])

  function getQueryVariable() {
    let href = decodeURI(window.location.href)
    let query = href.substring(href.indexOf('?') + 1)
    let vars = query.split('&')
    let obj: any = {}
    for (var i = 0; i < vars.length; i++) {
      let pair = vars[i].split('=')
      obj[pair[0]] = pair[1]
    }
    return obj
  }
  let show = (currentUser.level == '服务商' ? true : false)
  const refreshPage = () => {
    if (actionRef.current) {
      actionRef.current.reload()
    }
  }
  const columns: ProColumns<Item>[] = [
    {
      title: '订单号', width: '10%', dataIndex: 'orderNo',
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      copyable: false,
      ellipsis: true,
      valueEnum: orderStatusEnum,
    },

    {
      title: '下单时间',
      dataIndex: 'createdAt',
      copyable: false,
      ellipsis: true,
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            startDate: value[0],
            endDate: value[1],
          }
        },
      },
    },
    {
      title: '下单时间',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: (a, b) => a.createdAt - b.createdAt,
    },
    {
      title: '溯源状态', width: '10%', dataIndex: 'isTrack', valueEnum: {
        1: { text: '完成', status: 'Success' },
        0: { text: '未完成', status: 'Success' },
      },
    },
    // {
    //   title: '取消时间', dataIndex: ['cancel', 'createdAt'], copyable: false,
    //   ellipsis: true, valueType: 'dateTime', hideInSearch: true
    // },
    {
      title: '取消时间',
      dataIndex: ['cancel', 'createdAt'],
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            cancelStartDate: value[0],
            cancelEndDate: value[1],
          }
        },
      },
    },
    {
      title: '上门时间',
      dataIndex: 'workTime',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            workTimeStartDate: value[0],
            workTimeEndDate: value[1],
          }
        },
      },
    },
    // {
    //   title: '上门时间', dataIndex: 'workTime', copyable: false,
    //   ellipsis: true, valueType: 'dateTime', hideInSearch: true
    // },
    {
      title: '地区',
      dataIndex: 'city',
      hideInTable: true,
      valueEnum: AreasEnum,
    },
    {
      title: '完成时间',
      dataIndex: 'finishedAt',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            finishStartDate: value[0],
            finishEndDate: value[1],
          }
        },
      },
    },
    {
      title: '接单时间',
      dataIndex: 'takeTime',
      copyable: false,
      sorter: (a, b) => a.takeTime - b.takeTime,
      search: false,
      renderText: (_, row) => { return (row.takeTime ? dayjs(row.takeTime).format('YYYY-MM-DD HH:mm:ss') : '-') },
    },
    {
      title: '接单时间',
      dataIndex: 'takeTime',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            takeStartDate: value[0],
            takeEndDate: value[1],
          }
        },
      },
    },
    {
      title: '地址',
      width: '18%',
      dataIndex: 'address',
      copyable: false,
      render: (_, row: any) => {
        let province = row.province
        let city = row.city
        let address = row.address
        return province && city && address && [province, city, address].join('')
      },
    },
    {
      title: '回收类型',
      dataIndex: 'wasteType',
      copyable: false,
      ellipsis: true,
      hideInSearch: true,
      render: (_, row: any) => {
        return (
          <div>
            {row.type}
            <p>
              <a
                onClick={() => {
                  OrderDetail(row, row?.status, row?.waste_1st_ID)
                }}>
                查看详情
              </a>
            </p>
          </div>
        )
      },
    },
    {
      title: '回收类型',
      dataIndex: 'type',
      hideInTable: true,
      valueEnum: recycleTypeEnum,
    },
    {
      title: '回收人员',
      dataIndex: 'workerName',
      hideInTable: true,
    },
    { title: '客户姓名', dataIndex: 'userName', copyable: false },
    { title: '联系电话', dataIndex: 'userMobile', copyable: false, search: false },
    { title: '联系电话', dataIndex: 'userMobile', hideInTable: true },
    {
      title: '来源',
      dataIndex: 'from',
      copyable: false,
      ellipsis: true,
      valueEnum: fromEnum,
    },
    {
      title: '信息费',
      dataIndex: 'infoFee',
      copyable: false,
      ellipsis: true,
      search: false
    },
    { title: '回收人员', dataIndex: ['worker', 'workerName'], copyable: false, ellipsis: true, search: false },
    { title: '服务商', dataIndex: ['company', 'companyName'], copyable: false, ellipsis: true, search: false },
    {
      title: '催单次数',
      dataIndex: 'remindCount',
      copyable: false,
      ellipsis: true,
      hideInSearch: true,
      sorter: (a, b) => a.remindCount - b.remindCount,
      render: (_, row: any) => {
        return <div>
          <div style={{ fontWeight: 'bolder', fontSize: 20 }}>{row.remindCount}</div>
          <Button type='primary' onClick={async () => {
            await remindOrder(row).then((res) => {
              if (res) {
                refreshPage()
              }
            })
          }} >
            催单
          </Button>
        </div >
      },
    },
    {
      title: '服务商', dataIndex: 'companyNameID', copyable: false, hideInTable: true, valueEnum: ComEnum,
      hideInSearch: show,
    },

  ]

  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    const queryData = getQueryVariable()

    effect(NWorker, EGetList, { companyID: 0 })

    if (params.id && queryData.wasteType) {
      let waste_1st_ID = 3
      formRef.current?.setFieldsValue({
        salesman_id: params.id,
        waste_1st_ID: waste_1st_ID,
      })
      formRef.current?.submit()
    }
  }, [])

  /*--------------------- 响应 ---------------------*/
  const exportExcel = () => {
    let qsQuery = qs.stringify(lastSearch)
    window.open(
      `${SERVER_HOME_File}DGlogDataByExcelFile?${qsQuery}`
    )
  }
  /*--------------------- 渲染 ---------------------*/

  return (
    <ProCard>
      <ProTable<Item>
        formRef={formRef}
        actionRef={actionRef}
        columns={columns}
        request={async (params = {}, sorter) => {
          return (await effect(NCollection, EGet, { companyID: currentUser.companyID, ...params, ...sorter })) as any
        }}
        pagination={{
        }}
        rowKey="id"
        dateFormatter="string"
        headerTitle=" "
        search={{}}
        toolBarRender={() => [
          <Button
            key="3"
            type="primary"
            disabled={!computeAuthority('数据导出')}
            onClick={() => {
              exportExcel()
            }}>
            <DownloadOutlined />
            导出
          </Button>,
        ]}
      />
      <Modal
        zIndex={1100}
        open={visibleLog}
        title="订单记录"
        width={850}
        footer={[
          <Button type="primary" onClick={() => { reducer(NOrder, RSetState, { visibleLog: false }) }}>
            知道了
          </Button>,
        ]}
        onCancel={() => {
          reducer(NOrder, RSetState, { visibleLog: false })
        }}>
        <>
          {logData &&
            logData.map((vo: any, index: number) => {
              return (
                <div
                  key={index}
                  style={{
                    display: 'flex',
                    margin: 10,
                    flexWrap: 'wrap',
                    justifyContent: 'space-around',
                    borderBottom: '1px solid grey',
                  }}>
                  <div style={{ width: '22%' }}>{vo.createdAt}</div> -
                  <div style={{ width: '22%' }}>
                    {(vo.user?.realName && '用户:' + vo.user?.realName) ||
                      (vo.worker?.workerName && '师傅:' + vo.worker?.workerName + vo.worker?.mobile) ||
                      (vo.creater?.username && '后台:' + vo.creater?.username)}
                  </div>
                  -<div style={{ width: '22%' }}>{vo.status}</div>-<div style={{ width: '22%' }}>备注:{vo.content}</div>
                  <div style={{ textDecoration: 'underline', width: '100%' }} />
                </div>
              )
            })}
        </>
      </Modal>
    </ProCard>
  )
}



interface RecycleTypeEnum {
  '1': _1;
  '2': _1;
  '3': _1;
  '4': _1;
}

interface _1 {
  text: string;
  status: string;
}