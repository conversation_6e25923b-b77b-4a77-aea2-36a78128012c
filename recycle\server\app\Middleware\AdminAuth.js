'use strict'

const { CryptUtil } = require('..//Util')
const { ERR } = require('../../../../constants')
const { AdminUser } = require('../Models')

class AdminAuth {
  async handle({ request }, next) {
    const payload = CryptUtil.jwtDecode(request.header('Authorization') || request.input('token'))
    if (!payload) {
      throw ERR.AUTH_FAILED
    } else {
      if (!payload.adminID) {
        throw ERR.AUTH_FAILED
      }
      request.adminID = payload.adminID
      let adminUser = await AdminUser.query()
        .where('id', request.adminID)
        .with('authority')
        .first()
      request.adminUser = adminUser
    }
    await next()
  }
}

module.exports = AdminAuth
