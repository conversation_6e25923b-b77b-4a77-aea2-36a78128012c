'use strict'

const _ = require('lodash')
const moment = require('moment')
const Excel = require('exceljs')

const Env = use('Env')
const {
  Order, Worker, User, OrderCancel, OrderComplaint,
  CompanyArea, Company, OrderRating, OrderWaste, CompanySelfWastePrice, OrderLog,
  WorkerPay, WorkerWalletLog, UserAddress, ReqLog,
  PriceSet } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { WXService, AliYunService } = require('../../../Services')
const Config = require('../../../Util/Config')
const { callBack } = require('../../../Util/DebugUtil')
const { _remindMaster } = require('../../../Services/OrderService')
const { OrderStatus, actions } = require('../../../../../constants/E')
const { yunqixunSms } = require('../../../Services/AliYunService')

//订单
class OrderController {

  async index({ request, response }) {
    let { createdAt, workTime,
      current = 1, pageSize = 10, status, orderNo,
      waste_1st_type, userMobile, userName, workerName, from,
      cancelStartDate, cancelEndDate, startDate, endDate,
      workStartDate, workEndDate, orderType, type,
      finishStartDate, finishEndDate, companyNameID,
      isStore, remindCount,
      takeTime, takeStartDate, takeEndDate,
      isTrack, address, companyID
    } = request.all()
    // let { adminUser } = request
    // let { companyID } = adminUser
    isStore = parseInt(isStore)
    companyID = parseInt(companyID)
    let query = Order.query().with('company').with('sms')
    if (status) {
      if (status === E.OrderStatus.Pending) {
        query.where('status', E.OrderStatus.Reservation).whereNull('workerID')
      } else if (status === E.OrderStatus.Dispatched) {
        query.where('status', E.OrderStatus.Reservation).whereNotNull('workerID')
      } else {
        query.where('status', status).with('doneInfo',
          builder => {
            builder.select('orderID', 'sign', 'remark', 'imageUrl')
            return builder
          })
      }
    }
    if (from) {
      query.where('from', from)
    }
    if (orderType) {
      query.where('orderType', orderType)
    }
    if (isTrack) {
      query.where('isTrack', isTrack)
    }
    if (cancelStartDate && cancelEndDate) {
      let cancels = await OrderCancel.query().where('createdAt', '>=', moment(cancelStartDate).toDate())
        .where('createdAt', '<=', moment(cancelEndDate).add(1, 'd').toDate()).select('orderID', 'createdAt').fetch()
      let cancelIds = cancels.rows.map((vo) => vo.orderID)
      query.where('id', 'IN', _.uniq(cancelIds))
    }
    if (startDate && endDate) {
      query.where('createdAt', '>=', moment(startDate).toDate()).where('createdAt', '<=', moment(endDate).add(1, 'd').toDate())
    }
    if (workStartDate && workEndDate) {
      query.where('workTime', '>=', moment(workStartDate).toDate()).where('workTime', '<=', moment(workEndDate).add(1, 'd').toDate())
    }
    if (finishStartDate && finishEndDate) {
      query.where('finishedAt', '>=', moment(finishStartDate).toDate()).where('finishedAt', '<=', moment(finishEndDate).add(1, 'd').toDate())
    }
    if (takeStartDate && takeEndDate) {
      query.where('takeTime', '>=', moment(takeStartDate).toDate()).where('takeTime', '<=', moment(takeEndDate).add(1, 'd').toDate())
    }
    switch (takeTime) {
      case 'descend':
        query.orderBy('takeTime', 'desc')
        break
      case 'ascend':
        query.orderBy('takeTime', 'asc')
        break
      default:
        break
    }
    switch (createdAt) {
      case 'descend':
        query.orderBy('createdAt', 'desc')
        break;
      case 'ascend':
        query.orderBy('createdAt', 'asc')
        break;
      default:
        break;
    }
    switch (remindCount) {
      case 'descend':
        query.orderBy('remindCount', 'desc')
        break;
      case 'ascend':
        query.orderBy('remindCount', 'asc')
        break;
      default:
        break;
    }
    switch (workTime) {
      case 'descend':
        query.orderBy('workTime', 'desc')
        break;
      case 'ascend':
        query.orderBy('workTime', 'asc')
        break;
      default:
        break;
    }
    if (workerName) {
      let worker = await Worker.query().where({ workerName }).fetch()
      let workerList = []
      if (worker) {
        worker.rows.forEach((value, index) => {
          workerList.push(value.id)
        })
      }
      if (status === E.OrderStatus.Cancelled || status === E.OrderStatus.MasterReturn) {
        let orderList = []
        let orderc = await OrderCancel.query().whereIn('workerID', workerList).fetch()
        orderc.rows.forEach((value, index) => {
          orderList.push(value.orderID)
        })
        query.whereIn('id', _.uniq(orderList))
      } else {
        query.whereIn('workerID', _.uniq(workerList))
      }
    }
    if (companyID && !isStore) {
      query.where('companyID', companyID)
    } else if (companyID === 36 && isStore) {
      query.whereNot('companyID', 36)
    } else if (!companyID) {
    } else {
      query.where('companyID', companyID)
    }
    if (companyNameID) {
      query.where('companyID', companyNameID)
    }
    if (orderNo) {
      query.whereRaw('orderNo like ?', [`%${orderNo}%`])
    }
    if (type) {
      query.whereRaw('type like ?', [`%${type}%`])
    }
    if (waste_1st_type) {
      query.where('waste_1st_ID', waste_1st_type)
    }
    if (userMobile) {
      query.whereRaw('userMobile like ?', [`%${userMobile}%`])
    }
    if (userName) {
      query.whereRaw('userName like ?', [`%${userName}%`])
    }
    if (address) {
      query.whereRaw('address like ?', [`%${address}%`])
    }
    if (status === E.OrderStatus.Cancelled) {
      query.with('cancel', (b) =>
        b.whereIn('whoCancel', [E.CancleOrderStatus.MasterRevoke, E.CancleOrderStatus.Master]).with('worker')
      )
    } else if (status === E.OrderStatus.MasterReturn) {
      query.with('cancel', (b) =>
        b.whereIn('whoCancel', [E.CancleOrderStatus.MasterRevoke, E.CancleOrderStatus.Master]).with('worker')
      )
    } else if (status === E.OrderStatus.Pending) {
      query.with('cancel', (b) => b.where('whoCancel', E.CancleOrderStatus.MasterRevoke).with('worker'))
    } else if (status === E.OrderStatus.Completed) {
      query.with('pay')
    }
    let vo = await query.with('sales').with('worker').with('rating').paginate(current, pageSize)
    response.json(vo)
  }

  async downloadFile({ request, response, params }) {
    let { status, orderNo, sort = 'desc', waste_1st_type, userMobile, userName, workerName } = request.all()
    let query = Order.query().with('sms')
      .with('company')
    if (status) {
      if (status === E.OrderStatus.Pending) {
        query.where('status', E.OrderStatus.Reservation).whereNull('workerID')
      } else if (status === E.OrderStatus.Dispatched) {
        query.where('status', E.OrderStatus.Reservation).whereNotNull('workerID')
      } else {
        query.where('status', status)
      }
    }
    if (workerName) {
      let worker = await Worker.query().where({ workerName }).fetch()
      let workerList = []
      if (worker) {
        worker.rows.forEach((value, index) => {
          workerList.push(value.id)
        })
      }
      if (status === E.OrderStatus.Cancelled || status === E.OrderStatus.MasterReturn) {
        let orderList = []
        let orderc = await OrderCancel.query().whereIn('workerID', workerList).fetch()
        orderc.rows.forEach((value, index) => {
          orderList.push(value.orderID)
        })
        query.whereIn('id', _.uniq(orderList))
      } else {
        query.whereIn('workerID', _.uniq(workerList))
      }
    }
    if (orderNo) {
      query.whereRaw('orderNo like ?', [`%${orderNo}%`])
    }
    if (waste_1st_type) {
      query.where('waste_1st_ID', waste_1st_type)
    }
    if (userMobile) {
      query.whereRaw('userMobile like ?', [`%${userMobile}%`])
    }
    if (userName) {
      query.whereRaw('userName like ?', [`%${userName}%`])
    }
    if (status === E.OrderStatus.Cancelled) {
      query.with('cancel', (b) =>
        b.whereIn('whoCancel', [E.CancleOrderStatus.MasterRevoke, E.CancleOrderStatus.Master]).with('worker')
      )
    } else if (status === E.OrderStatus.MasterReturn) {
      query.with('cancel', (b) =>
        b.whereIn('whoCancel', [E.CancleOrderStatus.MasterRevoke, E.CancleOrderStatus.Master]).with('worker')
      )
    } else if (status === E.OrderStatus.Pending) {
      query.with('cancel', (b) => b.where('whoCancel', E.CancleOrderStatus.MasterRevoke).with('worker'))
    } else if (status === E.OrderStatus.Completed) {
      query.with('pay')
    }
    let vo = await query.with('worker').with('rating').orderBy('id', sort).fetch()
    let workbook = new Excel.Workbook()
    let worksheet = workbook.addWorksheet('Sheet 1')
    let font = { name: 'Times New Roman', size: 12 }
    let datas = vo.toJSON()
    worksheet.columns = [
      { header: '订单号', key: 'orderNo', width: 18, style: { font: font } },
      { header: '订单来源', key: 'from', width: 12, style: { font: font } },
      { header: '客户姓名', key: 'userName', width: 18, style: { font: font } },
      { header: '联系电话', key: 'userMobile', width: 18, style: { font: font } },
      { header: '师傅支付价格', key: 'commission', width: 18, style: { font: font } },
      { header: '地址', key: 'userAddress', width: 39, style: { font: font } },
      { header: '上门时间', key: 'workTime', width: 18, style: { font: font } },
      { header: '回收人员', key: 'worker', width: 18, style: { font: font } },
      { header: '取消时间', key: 'canceledAt', width: 18, style: { font: font } },
      { header: '完成时间', key: 'finishedAt', width: 18, style: { font: font } },
      { header: '下单时间', key: 'createdAt', width: 18, style: { font: font } },
      { header: '服务商', key: 'companyName', width: 18, style: { font: font } },
      { header: '用户备注', key: 'remark', width: 20, style: { font: font } },
    ]
    let rowDownload = datas.map(async (item) => {
      worksheet.addRow({
        orderNo: item.orderNo,
        from: item.from,
        userName: item.userName,
        userMobile: item.userMobile,
        commission: parseInt(item.commission) / 100,
        userAddress: `${item.address}`,
        workTime: moment(item.workTime).format('YYYY-MM-DD HH:mm'),
        worker: (item.worker && item.worker.workerName) || (item.cancel && item.cancel.slice(-1)[0] && item.cancel.slice(-1)[0].worker.workerName),
        canceledAt: (item.cancel && item.cancel[0]) ? moment(item.cancel[0].createdAt).format('YYYY-MM-DD HH:mm') : "-",
        finishedAt: item.finishedAt ? moment(item.finishedAt).format('YYYY-MM-DD HH:mm') : "-",
        createdAt: moment(item.createdAt).format('YYYY-MM-DD HH:mm'),
        companyName: item.company && item.company.companyName,
        remark: item.remark,
      })
    })
    rowDownload = await Promise.all(rowDownload)
    await workbook.xlsx.writeFile(`./订单${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`).then()
    return response.attachment(`./订单${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`)
  }
  async show({ request, params, response }) {
    let { status } = request.all()
    let query = Order.query().where('id', params.id)
    if (status === E.OrderStatus.Cancelled) {
      query.with('cancel', (b) =>
        b.whereIn('whoCancel', [E.CancleOrderStatus.Client, E.CancleOrderStatus.Master, E.CancleOrderStatus.Admin]).with('worker')
      )
    } else if (status === E.OrderStatus.Pending) {
      query.with('cancel', (b) => b.where('whoCancel', E.CancleOrderStatus.MasterRevoke).with('worker'))
    } else if (status === E.OrderStatus.Completed) {
      query.with('pay')
    }
    let vo = await query.with('worker').with('rating').first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }

  //更新订单内容
  async update({ request, params, response }) {
    let { adminUser, adminID } = request
    let vo = await Order.query().where({ id: params.id }).with('worker').first()
    let { status, workerID, countyCode, cancelReason, remark, address, companyID, infoFee, from } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台 更新订单' })
    let dataJson = request.all()
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    if (vo.toJSON().status === E.OrderStatus.Cancelled || vo.toJSON().status === E.OrderStatus.Completed) {
      return ({ error: 4003, message: '订单已取消或已完成，请刷新页面' })
    }
    // 取消订单
    if (status && status === E.OrderStatus.Cancelled) {
      let cancel = await OrderCancel.create({
        orderID: vo.id,
        userID: vo.userID,
        companyID: vo.companyID,
        cancelReason,
        whoCancel: E.CancleOrderStatus.Admin,
        workerID: vo.workerID
      })
      _.assign(vo, { status })
      let cvo = {
        "tracesList": [{
          "logisticProviderID": vo.toJSON().cpCode, "txLogisticID": vo.toJSON().orderNo, "mailNos": vo.toJSON().mailNo,
          "extendFields": "", "traces": [{
            "action": "ORDER_CLOSED", "country": "China", "city": vo.toJSON().city, "tz": "+8",
            "remark": cancelReason, "province": vo.toJSON().province,
            "extendFields": [{ "value": 1, "key": "cnRecycleType", "desc": "回收类型" }],
            "facilityName": Config.companySelfName, "facilityType": "1",
            "contacter": (vo.$relations.worker && vo.$relations.worker.workerName) || Config.companySelfHuman,
            "outBizCode": moment().format('x'),
            "time": moment().format('YYYY-MM-DD HH:mm:ss'),
            "contactPhone": (vo.$relations.worker && vo.$relations.worker.mobile) || Config.companySelfPhone,
            "desc": cancelReason
          }]
        }]
      }
      if (vo.toJSON().from === "菜鸟回收") {
        await callBack('TRACEPUSH', vo.cpCode, cvo)
      }
      await OrderLog.create({
        orderID: vo.id,
        content: cancelReason || '',
        status: E.OrderLogStatus.Cancelled,
        createrID: adminUser.id
      })
    } else {
      // 地址更新
      if (countyCode) {
        await Order.query().where('id', vo.id).update({ city: dataJson.city, province: dataJson.province, county: dataJson.county, receiverTown: dataJson.receiverTown })
        delete dataJson.countyCode
        delete dataJson.city
        delete dataJson.county
        delete dataJson.province
        delete dataJson.receiverTown
        delete dataJson.trackInfo
        _.assign(vo, { ...dataJson })
      }
      // 信息费修改
      if (infoFee) {
        vo.infoFee = infoFee
      }
    }
    // 后台派单
    if (workerID) {
      if ((status === E.OrderStatus.InProgress || status === E.OrderStatus.Reservation || E.OrderStatus.Dispatched) && remark) {
        console.log("不派单");
      } else if (workerID === "-1" || workerID === -1 || workerID === "-2" || workerID === -2) {
        vo.companyID = companyID
        if (vo.toJSON().province === '广东省' && vo.toJSON().from === '菜鸟回收') {
          let infoPrice = await PriceSet.query()
            .where("area", vo.county)
            .where("companyID", companyID)
            .where("source", '菜鸟回收')
            .where("type", vo.type)
            .first()
          if (!infoPrice) {
            infoPrice = await PriceSet.query()
              .where("area", vo.city)
              .where("companyID", companyID)
              .where("source", '菜鸟回收')
              .where("type", vo.type)
              .first()
          }
          if (infoPrice) {
            vo.commission = infoPrice.price
            vo.infoFee = infoPrice.price
          }
        }
        vo.status = E.OrderStatus.Reservation
        vo.workerID = null
        await OrderLog.create({
          orderID: vo.id,
          content: "后台派单公司",
          status: E.OrderLogStatus.makeOrder,
          createrID: adminUser.id
        })
      } else {
        let workerInfo = await Worker.find(workerID)
        await _remindMaster(workerInfo, vo, E.OrderStatus.Reservation)
        vo.companyID = workerInfo.companyID
        if (vo.toJSON().province === '广东省' && vo.toJSON().from === '菜鸟回收') {
          let infoPrice = await PriceSet.query()
            .where("area", vo.county)
            .where("companyID", workerInfo.companyID)
            .where("source", '菜鸟回收')
            .where("type", vo.type)
            .first()
          if (!infoPrice) {
            infoPrice = await PriceSet.query()
              .where("area", vo.city)
              .where("companyID", workerInfo.companyID)
              .where("source", '菜鸟回收')
              .where("type", vo.type)
              .first()
          }
          if (infoPrice) {
            vo.commission = infoPrice.price
            vo.infoFee = infoPrice.price
          }
        }
        if (countyCode === 1 || countyCode === "1") {
          await OrderLog.create({
            orderID: vo.id,
            content: "后台编辑",
            status: E.OrderLogStatus.Updates,
            createrID: adminUser.id
          })
        } else {
          vo.workerID = workerID
          vo.status = E.OrderStatus.Reservation
          await OrderLog.create({
            orderID: vo.id,
            content: "后台派单",
            status: E.OrderLogStatus.makeOrder,
            createrID: adminUser.id
          })
          if (vo && workerInfo.toJSON().workerName && workerInfo.toJSON().mobile) {
            await yunqixunSms({ clientName: vo.toJSON().userName, clientPhone: vo.toJSON().userMobile, masterName: workerInfo.toJSON().workerName, masterPhone: workerInfo.toJSON().mobile, orderID: vo.toJSON().id })
          }
        }
      }
    }
    await vo.save()
    response.json(vo)
    await ReqLog.create({ res: JSON.stringify(vo), source: '后台 更新订单' })
    // 后台 备注
    if (remark && !cancelReason) {
      await OrderLog.create({
        orderID: vo.id,
        content: remark,
        status: E.OrderLogStatus.System,
        createrID: adminUser.id
      })
    }
  }
  async orderEdit({ request, response, params }) {
    let { adminUser, adminID } = request
    let { type, userAddress, remark, userName, userMobile, estimatedMoney, workTime, infoFee, from } = request.all()
    let vo = await Order.find(params.id)
    if (vo.toJSON().status === E.OrderStatus.Cancelled || vo.toJSON().status === E.OrderStatus.Completed) {
      return ({ error: 4003, message: '订单已取消或已完成，请刷新页面' })
    }
    if (type) {
      vo.commission = type.commission
      await OrderWaste.query().where('orderID', params.id).update({
        wasteID: type.id, wasteTypeID: type.wasteName,
        wasteAttribute1ID: type.name,
      })
    }
    if (userAddress) {
      await UserAddress.query().where('id', userAddress.id).update({ ...userAddress })
    }
    vo.remark = remark
    vo.userName = userName
    vo.userMobile = userMobile
    vo.estimatedMoney = estimatedMoney
    vo.workTime = workTime
    vo.infoFee = infoFee
    vo.from = from
    await vo.save()
    response.json(vo)
    await ReqLog.create({ res: JSON.stringify(vo), source: '后台 更新订单' })
  }
  //订单撤回  批量
  async orderBack({ request, response }) {
    let { orderIDList } = request.all()
    let { adminUser } = request
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台 订单撤回' })
    if (adminUser.level === E.AdminLevel.服务商) {
      await _.forEach(orderIDList, async function (orderID) {
        let order = await Order.find(orderID)
        if (order.toJSON().status === E.OrderStatus.Cancelled || order.toJSON().status === E.OrderStatus.Completed) {
          return ({ error: 4003, message: '订单已取消或已完成，请刷新页面' })
        }
        let workerinfo = await Worker.find(order.workerID)
        await _remindMaster(workerinfo, order, E.OrderLogStatus.Recall)
        order.workerID = null
        order.status = E.OrderStatus.SystemReturn
        await order.save()
        await OrderLog.create({
          orderID, content: "后台撤回订单",
          status: E.OrderLogStatus.Recall,
          createrID: adminUser.id
        })
      })
    } else {
      await _.forEach(orderIDList, async function (orderID) {
        let order = await Order.find(orderID)
        if (order.toJSON().status === E.OrderStatus.Cancelled || order.toJSON().status === E.OrderStatus.Completed) {
          return ({ error: 4003, message: '订单已取消或已完成，请刷新页面' })
        }
        order.workerID = null
        order.companyID = 36
        order.status = E.OrderStatus.SystemReturn
        await order.save()
        await OrderLog.create({
          createrID: adminUser.id,
          orderID, content: "后台撤回订单",
          status: E.OrderLogStatus.Recall
        })
        let workerinfo = await Worker.find(order.workerID)
        await _remindMaster(workerinfo, order, E.OrderLogStatus.Recall)
      })
    }
    return ({ code: 200, msg: '撤回成功' })
  }

  //订单派单 批量
  async orderSend({ request, response }) {
    let { orderIDList, workerID, companyID } = request.all()
    let { adminUser } = request
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台 订单派单' })
    if (adminUser.level === E.AdminLevel.服务商) {
      await _.forEach(orderIDList, async function (orderID) {
        let order = await Order.find(orderID)
        if (order.toJSON().status === E.OrderStatus.Cancelled || order.toJSON().status === E.OrderStatus.Completed) {
          return ({ error: 4003, message: '订单已取消或已完成，请刷新页面' })
        }
        if (workerID) {
          order.workerID = workerID
          order.status = E.OrderStatus.Reservation
          await OrderLog.create({
            orderID, content: "后台派单",
            status: E.OrderLogStatus.makeOrder,
            createrID: adminUser.id
          })
          let workerInfo = await Worker.find(workerID)
          await _remindMaster(workerInfo, order, E.OrderLogStatus.makeOrder)
          if (order && workerInfo.toJSON().workerName && workerInfo.toJSON().mobile) {
            await yunqixunSms({ clientName: order.toJSON().userName, clientPhone: order.toJSON().userMobile, masterName: workerInfo.toJSON().workerName, masterPhone: workerInfo.toJSON().mobile, orderID: order.toJSON().id })
          }
        }
        await order.save()
      })
    } else {
      await _.forEach(orderIDList, async function (orderID) {
        let order = await Order.find(orderID)
        if (order.toJSON().status === E.OrderStatus.Cancelled || order.toJSON().status === E.OrderStatus.Completed) {
          return ({ error: 4003, message: '订单已取消或已完成，请刷新页面' })
        }
        if (companyID) {
          order.companyID = companyID
          if (order.toJSON().province === '广东省' && order.toJSON().from === '菜鸟回收') {
            let infoPrice = await PriceSet.query()
              .where("area", order.county)
              .where("companyID", companyID)
              .where("source", '菜鸟回收')
              .where("type", order.type)
              .first()
            if (!infoPrice) {
              infoPrice = await PriceSet.query()
                .where("area", order.city)
                .where("companyID", companyID)
                .where("source", '菜鸟回收')
                .where("type", order.type)
                .first()
            }
            if (infoPrice) {
              order.commission = infoPrice.price
              order.infoFee = infoPrice.price
            }
          }
          order.status = E.OrderStatus.Reservation
          order.workerID = null
          await OrderLog.create({
            orderID: orderID,
            content: "后台派单公司",
            status: E.OrderLogStatus.makeOrder,
            createrID: adminUser.id
          })
        } else if (workerID) {
          let workerInfo = await Worker.find(workerID)
          order.workerID = workerID
          order.companyID = workerInfo.toJSON().companyID
          order.status = E.OrderStatus.Reservation
          await OrderLog.create({
            orderID, content: "后台派单",
            status: E.OrderLogStatus.makeOrder,
            createrID: adminUser.id
          })
          await _remindMaster(workerInfo, order, E.OrderLogStatus.makeOrder)
          if (order && workerInfo.toJSON().workerName && workerInfo.toJSON().mobile) {
            await yunqixunSms({ clientName: order.toJSON().userName, clientPhone: order.toJSON().userMobile, masterName: workerInfo.toJSON().workerName, masterPhone: workerInfo.toJSON().mobile, orderID: order.toJSON().id })
          }
        }
        await order.save()
      })
    }
    return ({ code: 200, msg: '派单成功' })
  }
  async handleNew({ request, response }) {
    let { address, city, userMobile, county, province, receiverTown, userName, workTime, type = "冰箱", from = "其他", infoFee, source = "手动建单", remark, companyID = 36 } = request.all()
    let { adminUser } = request
    if (!address || !userName || !userMobile || !workTime || !type) {
      throw ERR.SQL_INCOMPLETE_PARAMS
    }
    let infoPrice
    if (city === '合肥市') {
      infoPrice = await PriceSet.query()
        .where("area", '合肥市')
        .where("source", from)
        .where("type", type)
        .first()
    } else if (province === '广东省') {
      infoPrice = await PriceSet.query()
        .where("area", city)
        .where("source", from)
        .where("type", type)
        .first()
    } else {
      infoPrice = await PriceSet.query()
        .where("area", province)
        .where("source", from)
        .where("type", type)
        .first()
    }
    const createData = {
      address,
      companyID,
      type,
      workTime, city,
      county, province, receiverTown,
      remark,
      commission: (infoPrice && infoPrice.price) || infoFee,
      infoFee: (infoPrice && infoPrice.price) || infoFee,
      waste_1st_ID: 3,
      userName,
      userMobile,
      from: from,
      status: E.OrderStatus.Reservation,
      keywords: userName + userMobile + address + type,
    }
    // 创建预约订单
    let vo = await Order.create(createData)
    vo = await Order.query().where('id', vo.id).first()
    vo.orderNo = moment().format('YYYYMMDD') + vo.id
    await vo.save()
    response.json(vo)
    await OrderLog.create({
      createrID: adminUser.id,
      orderID: vo.id,
      content: "后台建档",
      status: E.OrderLogStatus.Create,
    })
  }
  //获取投诉列表
  async complaints({ request, response }) {
    let { adminUser, adminID } = request
    let { companyID } = adminUser
    let { current = 1, pageSize = 10, sort = 'desc', workerID, userID, wasteType, orderNo } = request.all()
    let query = OrderComplaint.query()
    if (companyID && companyID !== 36) {
      query.where('companyID', companyID)
    }
    if (workerID) {
      query.where('workerID', workerID)
    }
    if (userID) {
      query.where('userID', userID)
    }
    if (wasteType) {
      query.where('wasteType', wasteType)
    }
    if (orderNo) {
      query.whereRaw('orderNo like ?', [`%${orderNo}%`])
    }
    let vo = await query
      .with('order', (b) => b.with('userAddress', (c) => c.select('id', 'realname', 'mobile', 'province', 'city', 'district', 'subDistrct', 'address')))
      .with('worker', (b) => b.select('id', 'workerName', 'mobile'))
      .with('company', (b) => b.select('id', 'companyName', 'mobile'))
      .orderBy('id', 'desc')
      .paginate(current, pageSize)
    response.json(vo)
  }
  //评价订单
  async rating({ request, response }) {
    let { adminUser, adminID } = request
    let { companyID } = adminUser
    let { current = 1, pageSize = 10, sort = 'desc', workerID, userID, wasteType, orderNo, workerRating } = request.all()
    let query = OrderRating.query()
    companyID = parseInt(companyID)
    if (companyID && companyID !== 36) {
      query.where('companyID', companyID)
    }
    if (workerID) {
      query.where('workerID', workerID)
    }
    if (workerRating) {
      query.where('workerRating', workerRating)
    }
    if (wasteType) {
      query.where('wasteType', wasteType)
    }
    let vo = await query
      .with('order', (b) => b.with('userAddress', (c) => c.select('id', 'realname', 'mobile', 'province', 'city', 'district', 'subDistrct', 'address')))
      .with('worker', (b) => b.select('id', 'workerName', 'mobile'))
      .with('company', (b) => b.select('id', 'companyName', 'mobile'))
      .orderBy('createdAt', sort)
      .paginate(current, pageSize)
    response.json(vo)
  }

  async comfirmOrder({ request, params, response }) {
    let { remark = "后台完单", orderID } = request.all()
    let id = orderID
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台 管理订单确认' })
    let data = await Order.find(id)
    if (data.toJSON().status === OrderStatus.Cancelled) {
      throw ERR.API_ERROR
    }
    let infoFee = parseInt(data.infoFee)
    let remuneration = infoFee
    let workerVO = await Worker.find(data.workerID)
    let moneyRes
    let uniqOrder = await WorkerWalletLog.query().where("orderID", id).where("isClient", 0).first()
    if (uniqOrder) {
      throw ERR.INVALID_PARAMS;
    }
    await WorkerWalletLog.create({
      workerID: data.workerID,
      money: -infoFee,
      remuneration,
      commission: infoFee,
      payMoney: infoFee,
      companyID: data.companyID,
      remark,
      orderID: id,
    })
    moneyRes = parseFloat(workerVO.toJSON().wallet) - parseInt(infoFee)
    workerVO.wallet = moneyRes
    await workerVO.save()
    data.status = '完成'
    data.infoFee = infoFee
    data.commission = infoFee
    data.finishedAt = moment().format('YYYY-MM-DD HH:mm:ss')
    await data.save()
    await OrderLog.create({
      status: E.OrderLogStatus.Completed,
      orderID: id,
      workerID: data.workerID,
      content: remark,
    })
    response.json(workerVO)
    data = data.toJSON()
    await ReqLog.create({ res: JSON.stringify(workerVO), source: '后台 管理订单确认' })
    let content = {
      "tracesList": [{
        "logisticProviderID": data.cpCode,
        "extendFields": "",
        "traces": [{
          "extendFields": [
            { "value": 1, "key": "cnRecycleType", "desc": "回收类型" },
            { "value": data.apprizeAmount, "key": "apprizeAmount", "desc": "回收金额" }
          ],
          "country": "China", "city": data.city,
          "facilityType": "1",
          "facilityName": Config.companySelfName,
          "tz": "+8",
          "action": actions.TMS_CHECK.name,
          "desc": actions.TMS_CHECK.remark, "contacter": workerVO.workerName,
          "contactPhone": workerVO.mobile, "outBizCode": moment().format('x'),
          "time": moment().format('YYYY-MM-DD HH:mm:ss')
        }], "txLogisticID": data.orderNo, "mailNos": data.mailNo
      }]
    }
    if (data.from === "菜鸟回收") {
      await callBack("TRACEPUSH", data.cpCode, content)
    }
    _remindMaster(workerVO, data, E.OrderStatus.Completed)
  }
  async comfirmOrderByAdmin({ request, params, response }) {
    let { remark = "补录完单", orderID } = request.all()
    let id = orderID
    let data = await Order.find(id)
    if (data.toJSON().status === OrderStatus.Cancelled) {
      throw ERR.API_ERROR
    }
    data.status = '完成'
    data.finishedAt = moment().format('YYYY-MM-DD HH:mm:ss')
    await data.save()
    await OrderLog.create({
      status: E.OrderLogStatus.Completed,
      orderID: id,
      createrID: 118,
      content: remark,
    })
    response.json(data)
    data = data.toJSON()
    let content = {
      "tracesList": [{
        "logisticProviderID": data.cpCode,
        "extendFields": "",
        "traces": [{
          "extendFields": [
            { "value": 1, "key": "cnRecycleType", "desc": "回收类型" },
            { "value": data.apprizeAmount, "key": "apprizeAmount", "desc": "回收金额" }
          ],
          "country": "China", "city": data.city,
          "facilityType": "1",
          "facilityName": Config.companySelfName,
          "tz": "+8",
          "action": actions.TMS_CHECK.name,
          "desc": actions.TMS_CHECK.remark, "contacter": Config.companySelfHuman,
          "contactPhone": Config.companySelfPhone, "outBizCode": moment().format('x'),
          "time": moment().format('YYYY-MM-DD HH:mm:ss')
        }], "txLogisticID": data.orderNo, "mailNos": data.mailNo
      }]
    }
    if (data.from === "菜鸟回收") {
      await callBack("TRACEPUSH", data.cpCode, content)
    }
    await ReqLog.create({ res: JSON.stringify(data), source: '系统补录完单' })
  }
  //订单分配
  async devideOrder({ request, response }) {
    let { adminUser } = request
    if (adminUser.level !== E.AdminLevel.总部) {
      throw ERR.RESTFUL_GET_AUTH
    }
    let { waste_1st_type } = request.all()
    let query = await Order.query().where('waste_1st_ID', waste_1st_type).whereNull('companyID').fetch()
    let result_code = 'FAIL'
    await _.forEach(query.rows, async function (vo) {
      let userAddress = vo.$relations.userAddress
      if (userAddress) {
        let companyArea = await CompanyArea.query()
          .where('area', userAddress.district)
          .where('name', userAddress.subDistrct)
          .where('type', vo.waste_1st_ID)
          .first()
        if (companyArea) {
          let company = await Company.find(companyArea.companyID)
          if (company.forbidden === 1) {
            let order = await Order.find(vo.id)
            order.companyID = company.id
            await order.save()
          }
        }
      }
      result_code = 'SUCCESS'
    })
    response.json({ result_code })
  }
  async getOrderLog({ request, response, params }) {
    if (!params.id) {
      throw ERR.API_ERROR
    }
    let logList = await OrderLog.query().with('user').with('worker').with('creater').with('order')
      .where('orderID', params.id)
      .orderBy('createdAt', 'asc')
      .fetch()
    response.json(logList)
  }
  async getPayLog({ request, response, params }) {
    if (!params.id) {
      throw ERR.API_ERROR
    }
    let logList = await WorkerPay.query()
      .whereNotNull('finishAt')
      .where('workerID', params.id)
      .orderBy('createdAt', 'desc')
      .fetch()
    let payList = await WorkerWalletLog.query()
      .select('id', 'workerID', 'createdAt', 'orderID', 'money', 'companyID')
      .where('workerID', params.id)
      .orderBy('createdAt', 'desc')
      .fetch()
    logList = logList.concat(payList)
    response.json(logList)
  }
  async getOrderCount({ request, response }) {
    let { companyID, waste_1st_type, isStore } = request.all()
    isStore = parseInt(isStore)
    companyID = parseInt(companyID)
    if (!waste_1st_type) {
      throw ERR.INVALID_PARAMS
    }
    let Pendingquery = Order.query().where('waste_1st_ID', waste_1st_type)
    let Cancelledquery = Order.query().where('waste_1st_ID', waste_1st_type)
    let Completedquery = Order.query().where('waste_1st_ID', waste_1st_type)
    let Dispatchedquery = Order.query().where('waste_1st_ID', waste_1st_type)
    let InProgressquery = Order.query().where('waste_1st_ID', waste_1st_type)
    let SystemReturnquery = Order.query().where('waste_1st_ID', waste_1st_type)
    let MasterRevokequery = Order.query().where('waste_1st_ID', waste_1st_type)
    if (companyID && !isStore) {
      Pendingquery.where('companyID', companyID)
      Cancelledquery.where('companyID', companyID)
      Completedquery.where('companyID', companyID)
      Dispatchedquery.where('companyID', companyID)
      InProgressquery.where('companyID', companyID)
      SystemReturnquery.where('companyID', companyID)
      MasterRevokequery.where('companyID', companyID)
    } else if (companyID === 36 && isStore) {
      Pendingquery.whereNot('companyID', 36)
      Cancelledquery.whereNot('companyID', 36)
      Completedquery.whereNot('companyID', 36)
      Dispatchedquery.whereNot('companyID', 36)
      InProgressquery.whereNot('companyID', 36)
      SystemReturnquery.whereNot('companyID', 36)
      MasterRevokequery.whereNot('companyID', 36)
    } else if (companyID) {
      Pendingquery.where('companyID', companyID)
      Cancelledquery.where('companyID', companyID)
      Completedquery.where('companyID', companyID)
      Dispatchedquery.where('companyID', companyID)
      InProgressquery.where('companyID', companyID)
      SystemReturnquery.where('companyID', companyID)
      MasterRevokequery.where('companyID', companyID)
    } else {

    }
    let PendingCount = await Pendingquery.where('status', E.OrderStatus.Reservation).whereNull('workerID').getCount('id')
    let DispatchedCount = await Dispatchedquery.where('status', E.OrderStatus.Reservation).whereNotNull('workerID').getCount('id')
    let InProgressCount = await InProgressquery.where('status', E.OrderStatus.InProgress).getCount('id')
    let CompletedCount = await Completedquery.where('status', E.OrderStatus.Completed).getCount('id')
    let SystemReturnCount = await SystemReturnquery.where('status', E.OrderStatus.SystemReturn).getCount('id')
    let MasterRevokeCount = await MasterRevokequery.where('status', E.OrderStatus.MasterReturn).getCount('id')
    let CancelledCount = await Cancelledquery.where('status', E.OrderStatus.Cancelled).getCount('id')
    let orderNumberArray = [PendingCount, MasterRevokeCount, SystemReturnCount, DispatchedCount, InProgressCount, CompletedCount, CancelledCount]
    response.json(orderNumberArray)
  }
  async getNoticeOrder({ request, response }) {
    let { current = 1, pageSize = 5 } = request.all()
    let data = await OrderCancel.query()
      .whereIn('whoCancel', ['师傅撤销', '客户取消'])
      .with('worker')
      .with('order')
      .whereBetween('createdAt', [moment().subtract(7, 'days').format('YYYY-MM-DD'),
      moment().add(1, 'days').format('YYYY-MM-DD')])
      .orderBy('createdAt', 'desc')
      .paginate(current, pageSize)
    return data
  }
  async changeWorker({ request, response, params }) {
    let { adminUser, adminID } = request
    let vo = await Order.query().where({ id: params.id }).with('worker').first()
    let { status, workerID } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台 改派订单' })
    let dataJson = request.all()
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    if (status === E.OrderStatus.InProgress || status === E.OrderStatus.Reservation || status === E.OrderStatus.Dispatched) {
      let workerInfo = await Worker.find(workerID)
      await _remindMaster(workerInfo, vo, E.OrderLogStatus.makeOrder)
      vo.companyID = workerInfo.companyID
      vo.workerID = workerID
      vo.status = E.OrderStatus.Reservation
      await OrderLog.create({
        orderID: vo.id,
        content: "后台改派订单",
        status: E.OrderLogStatus.TransferOrder,
        createrID: adminUser.id
      })
      if (vo && workerInfo.toJSON().workerName && workerInfo.toJSON().mobile) {
        await yunqixunSms({ clientName: vo.toJSON().userName, clientPhone: vo.toJSON().userMobile, masterName: workerInfo.toJSON().workerName, masterPhone: workerInfo.toJSON().mobile, orderID: vo.toJSON().id })
      }
      await vo.save()
      response.json(vo)
      await ReqLog.create({ res: JSON.stringify(vo), source: '后台 改派订单' })
    }
  }
}

module.exports = OrderController
