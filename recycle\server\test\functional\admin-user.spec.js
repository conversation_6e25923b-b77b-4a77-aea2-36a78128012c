'use strict'

const Suite = use('Test/Suite')('Admin User')
const { before, beforeEach, after, afterEach, test, trait } = Suite
const { E } = require('../../../../constants')

trait('Test/ApiClient')

const Database = use('Database')

const UserAuthorization = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySUQiOjE5LCJpYXQiOjE1Nzk0OTQ3MTl9.kOM-6zJ0Fvg-jScKIuDBlQoL2d7njx4BxW2vO7E2f1Y'
let AdminAuthorization = null
const MasterAuthorization = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySUQiOjMsImlhdCI6MTU4MTA0OTM4MH0.W9_etereYwSvnC425j91fJ2uaaXqOz3DXKPZeBDsk4Y'
let orderID = null

before(async () => {
  await Database.truncate('order')
  await Database.truncate('order_cancel')
  await Database.truncate('order_waste')
  console.log('before')
})

beforeEach(async () => {
  console.log('beforeEach')
  // executed before each test inside a given suite
})

test('用户创建订单', async ({ client }) => {
  let url = JSON.stringify(['https://c.stgame.cn/r3/1a28e393db1d2dff2a216a9b6a78a91a.png'])
  let res = await client
    .post('client/v1/order')
    .send({
      workTime: '2020-04-15 09:00',
      remark: 'unit test',
      userAddressID: 61,
      wasteType: '大家电',
      waste_1st_ID: 3,
      userName: 'moumou',
      userMobile: '13625606990',
      userUpload: url,
      userID: 1,
      from: '小程序',
      status: E.OrderStatus.Reservation,
      orderWastes: [{ wasteID: 15, wasteTypeID: '餐盒' }]
    })
    .header('Authorization', UserAuthorization)
    .end()
  orderID = res.body.id
  res.assertStatus(200)
})

test('后台管理员登录', async ({ client }) => {
  let res = await client.post('admin/v1/user/login').send({ username: '小张', password: '21' }).end()
  res.assertStatus(200)
  AdminAuthorization = `Bearer ${res.body.token}`
})

test('获取可派单人员并派单', async ({ client }) => {
  let order = await client.get(`admin/v1/order/${orderID}`).header('Authorization', AdminAuthorization).end()
  order = order.body
  let res = await client
    .post('admin/v1/worker/whichWorkers')
    .send({ companyID: order.companyID, subDistrct: order.userAddress.subDistrct, type: order.waste_1st_ID })
    .header('Authorization', AdminAuthorization)
    .end()
  let dispatch = await client.put(`admin/v1/order/${order.id}`).send({ isTransferID: res.body[1].workerID }).header('Authorization', AdminAuthorization).end()
  dispatch.assertStatus(200)
})

test('师傅接单', async ({ client }) => {
  let res = await client.put(`master/v1/order/${orderID}`).send({ status: E.OrderStatus.InProgress }).header('Authorization', MasterAuthorization).end()
  res.assertJSONSubset({ status: '进行中', id: orderID })
})

test('总部后台，订单撤回',async ({client})=>{
  let res = await client.post('admin/v1/order/orderBack').send({orderIDList: [orderID]}).header('Authorization',AdminAuthorization).end()
  let order = await client.get(`admin/v1/order/${orderID}`).header('Authorization', AdminAuthorization).end()
  order.assertJSONSubset({status: E.OrderStatus.Reservation,companyID: null})
})

test('重新分配服务商',async ({client})=>{
  let res = await client.post('admin/v1/order/devideOrder').send({waste_1st_type: 3}).header('Authorization',AdminAuthorization).end()
  // res.assertJSONSubset({result_code: 'SUCCESS'})
})
//
// test('获取可派单人员并派单', async ({ client }) => {
//   let order = await client.get(`admin/v1/order/${orderID}`).header('Authorization', AdminAuthorization).end()
//   order = order.body
//   let res = await client
//     .post('admin/v1/worker/whichWorkers')
//     .send({ companyID: order.companyID, subDistrct: order.userAddress.subDistrct, type: order.waste_1st_ID })
//     .header('Authorization', AdminAuthorization)
//     .end()
//   console.log('worker', res.body[1], order.id)
//   let dispatch = await client.put(`admin/v1/order/${order.id}`).send({ isTransferID: res.body[1].workerID }).header('Authorization', AdminAuthorization).end()
//   dispatch.assertStatus(200)
// })
//
// test('师傅接单', async ({ client }) => {
//   let res = await client.put(`master/v1/order/${orderID}`).send({ status: E.OrderStatus.InProgress }).header('Authorization', MasterAuthorization).end()
//   res.assertJSONSubset({ status: '进行中', id: orderID })
// })
//
// test('师傅，订单撤回',async ({client})=>{
//   let res = await  client.put(`master/v1/order/orderBack/${orderID}`).send({status: E.OrderStatus.Reservation,whoCancel: E.CancleOrderStatus.MasterRevoke}).header('Authorization',MasterAuthorization).end()
//   res.assertJSONSubset({ status: E.OrderStatus.Reservation, id: orderID })
// })
//
// test('用户取消订单', async ({ client }) => {
//   let res = await client
//     .post(`client/v1/order/${orderID}/updateStatus`)
//     .send({
//       cancelReason: '处理好了',
//       status: E.OrderStatus.Cancelled
//     })
//     .header('Authorization', UserAuthorization)
//     .end()
//   res.assertJSONSubset({ status: '取消', id: orderID })
// })

after(async () => {
  console.log('after', AdminAuthorization)
  // executed after all the tests for a given suite
})

afterEach(async () => {
  console.log('afterEach')
  // executed after each test inside a given suite
})
