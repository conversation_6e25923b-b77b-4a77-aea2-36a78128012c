// 冬瓜回收品牌色彩系统 - 与logo保持一致的绿色环保主题
@primary-color: #15b381;        // 冬瓜绿主色 - 与logo一致
@primary-light: #41c794;        // 浅绿色 - 增强活力感
@primary-lighter: #68d4a7;      // 更浅绿色 - 用于悬浮等状态
@primary-dark: #0d9b6f;         // 深绿色 - 增强稳重感
@primary-darker: #0a7d5a;       // 更深绿色 - 用于阴影等
@secondary-color: #f0fdf7;      // 浅绿背景 - 更贴近自然
@accent-color: #fbbf24;         // 金黄色强调色 - 更环保友好
@success-color: #22c55e;        // 成功绿色
@info-color: #0ea5e9;           // 信息蓝色
@warning-color: #f59e0b;        // 警告橙色
@error-color: #ef4444;          // 错误红色

// 文字颜色
@text-primary: #333333;         // 主文字色
@text-secondary: #666666;       // 次要文字色
@text-light: #999999;           // 浅色文字
@text-white: #ffffff;           // 白色文字

// 背景色
@background: #ffffff;           // 背景色
@background-light: #f8f9fa;     // 浅色背景
@background-gray: #f5f5f5;      // 灰色背景

// 边框和阴影 - 增强绿色主题
@border-color: #e8f5f0;         // 淡绿边框色
@border-light: #f0f9f4;         // 浅绿边框
@shadow-light: 0 2px 8px rgba(21, 179, 129, 0.08);
@shadow-medium: 0 4px 16px rgba(21, 179, 129, 0.12);
@shadow-strong: 0 8px 32px rgba(21, 179, 129, 0.18);
@shadow-accent: 0 4px 12px rgba(251, 191, 36, 0.15);  // 金色阴影

// 圆角
@radius-xs: 4px;
@radius-sm: 8px;
@radius-md: 12px;
@radius-lg: 16px;
@radius-xl: 24px;
@radius-round: 50%;

// 间距
@spacing-xs: 8px;
@spacing-sm: 12px;
@spacing-md: 16px;
@spacing-lg: 24px;
@spacing-xl: 32px;
@spacing-xxl: 48px;

// 字体大小 (rpx单位)
@font-xs: 20rpx;
@font-sm: 24rpx;
@font-md: 28rpx;
@font-lg: 32rpx;
@font-xl: 36rpx;
@font-xxl: 40rpx;
@font-title: 48rpx;

// 渐变色
@gradient-primary: linear-gradient(135deg, @primary-color 0%, @primary-light 100%);
@gradient-background: linear-gradient(180deg, @secondary-color 0%, @background 100%);

// 过渡动画
@transition-fast: 0.2s ease;
@transition-normal: 0.3s ease;
@transition-slow: 0.5s ease;