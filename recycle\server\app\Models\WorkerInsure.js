'use strict'

const Model = use('Model')

class WorkerInsure extends Model {
  static get table() {
    return 'worker_insure'
  }
  static get createdAtColumn() {
    return null
  }
  static get updatedAtColumn() {
    return 'updatedAt'
  }

  company() {
    return this.belongsTo('App/Models/Company', 'companyID', 'id')
  }
  worker() {
    return this.belongsTo('App/Models/Worker', 'workerID', 'id')
  }
  getFiles(value) {
    return value && JSON.parse(value)
  }
  setFiles(value) {
    return value && JSON.stringify(value)
  }
}

module.exports = WorkerInsure
