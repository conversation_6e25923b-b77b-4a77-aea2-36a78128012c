import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'

export async function getDiscoveryList(payload: any) {
  return requestGet('/discovery', payload)
}
export async function editDiscovery(payload: any) {
  return requestPut(`discovery/${payload.id}`, payload)
}
export async function createDiscovery(payload: any) {
  return requestPost('discovery', payload)
}
export async function getComments(payload: any) {
  return requestGet('discoveryComment', payload)
}
export async function editCommentJudge(payload: any) {
  return requestPut(`discoveryComment/${payload.id}`, payload)
}

export async function delDiscovery(payload: any) {
  return requestDelete(`discovery/${payload.id}`)
}