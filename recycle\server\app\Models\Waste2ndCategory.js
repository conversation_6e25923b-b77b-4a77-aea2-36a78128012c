'use strict'

const Model = use('Model')
const { ERR, E } = require('../../../../constants')

//废品二级分类
class Waste2ndCategory extends Model {
	static get table() { return 'waste_2nd_category' }
	static get primaryKey() { return 'id' }
	static get createdAtColumn() { return null }
	static get updatedAtColumn() { return null }
	waste() {
		return this.hasMany('App/Models/Waste', 'id', 'secondCateID')
			.where('type', E.Permission.Primary).where('source', E.OrderSource.CaiNiao )
	}
}

module.exports = Waste2ndCategory