import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'
import {
  EDeleteImport, EGet, EGetAddress, EGetImport, EGetList,
  EGetLineCharts, EGetCharts, EGetData,
  EPostImport, EPutImport, NCollection, RAdd, RSetState,
  EBatchDestroyImport, EOrderMaintainList, EOrderMaintainUpdate,
  EGetHiProvinceCharts, EGetHiChannelCharts, EGetHiCategoryCharts,
  EGetSmsLog, EYCOrderMaintainList, EYCOrderMaintainUpdate,
  EGetYCImport, EPostYCImport, EDeleteYCImport, EBatchDestroyYCImport, EPutYCImport,
  EGetRecycleImport, EConfirmOrder,
} from '../common/action'
import { adapterPaginationResult } from '../common/utils'

export default {
  namespace: NCollection,
  state: {
    lastSearch: {},
    orderMaintain: [],
    smsLog: [],
    total: 0,
    totalPay: 500,
    dashData: {},
    chartData: {},
    lineData: [],
    hiProvinceData: [], // 嗨回收省份分布数据
    hiChannelData: [], // 嗨回收渠道分布数据
    hiCategoryData: [], // 嗨回收品类分布数据
  },
  reducers: {
    [RSetState](state: any, payload: any) {
      return { ...state, ...payload }
    },
    [RAdd](state: { count: any }, payload: any) {
      return { ...state, count: state.count + payload }
    },
  },
  effects: {
    //  标准CURD示例
    async [EGet]({ payload }: any, { reducer }: any) {
      let result = await requestPost('collection', payload)
      reducer(RSetState, { lastSearch: payload })
      return adapterPaginationResult(result)
    },
    //  标准CURD示例
    async [EGetList]({ payload }: any, { reducer }: any) {
      let result = await requestGet('logMsg', payload)
      reducer(RSetState, { total: result && result.total, totalPay: result && result.totalPay })
      return adapterPaginationResult(result)
    },
    // 列表及翻页
    async [EGetAddress]({ payload }: any) {
      return await requestGet('address', payload)
    },
    //  标准CURD示例
    async [EGetImport]({ payload }: any, { reducer }: any) {
      let result = await requestGet('orderimport', { model: '送取不同步', ...payload })
      reducer(RSetState, { lastSearch: payload })
      return adapterPaginationResult(result)
    },
    async [EGetRecycleImport]({ payload }: any, { reducer }: any) {
      let result = await requestGet('orderimport', { model: '纯回收', ...payload })
      reducer(RSetState, { lastSearch: payload })
      return adapterPaginationResult(result)
    },
    async [EPostImport]({ payload }: any, { reducer }: any) {
      let result = await requestPost('orderimport', payload)
      reducer(RSetState, { lastSearch: payload })
      return adapterPaginationResult(result)
    },
    async [EDeleteImport]({ payload }: any, { reducer }: any) {
      let result = await requestDelete(`orderimport/${payload.id}`)
      reducer(RSetState, { lastSearch: payload })
      return adapterPaginationResult(result)
    },
    async [EBatchDestroyImport]({ payload }: any) {
      let result = await requestPost(`orderimport/batchDestroy`, payload)
      return result
    },
    async [EPutImport]({ payload }: any, { reducer }: any) {
      let result = await requestPut('orderimport', payload)
      reducer(RSetState, { lastSearch: payload })
      return adapterPaginationResult(result)
    },
    async [EGetData]({ payload }: any, { reducer }: any) {
      let result = await requestGet('getData', payload)
      reducer(RSetState, { dashData: result })
    },
    async [EGetCharts]({ payload }: any, { reducer }: any) {
      let result = await requestGet('getCharts', payload)
      reducer(RSetState, { chartData: result, lastSearch: payload })
    },
    async [EGetLineCharts]({ payload }: any, { reducer }: any) {
      let result = await requestGet('getLineCharts', payload)
      reducer(RSetState, { lineData: result, lastSearch: payload })
    },
    async [EOrderMaintainList]({ payload }: any, { reducer }: any) {
      let result = await requestGet('orderMaintainList', payload)
      reducer(RSetState, { orderMaintain: result, lastSearch: payload })
      return adapterPaginationResult(result)

    },
    async [EOrderMaintainUpdate]({ payload }: any, { reducer }: any) {
      let result = await requestPut(`orderMaintainUpdate/${payload.id}`, payload)
      if (result) {
        await this[EOrderMaintainList]({ status: '待维护' }, { reducer })
      }
    },
    // 获取嗨回收省份分布数据
    async [EGetHiProvinceCharts]({ payload }: any, { reducer }: any) {
      let result = await requestGet('getHiProvinceCharts', payload)
      reducer(RSetState, { hiProvinceData: result, lastSearch: payload })
    },
    // 获取嗨回收渠道分布数据
    async [EGetHiChannelCharts]({ payload }: any, { reducer }: any) {
      let result = await requestGet('getHiChannelCharts', payload)
      reducer(RSetState, { hiChannelData: result, lastSearch: payload })
    },
    // 获取嗨回收品类分布数据
    async [EGetHiCategoryCharts]({ payload }: any, { reducer }: any) {
      let result = await requestGet('getHiCategoryCharts', payload)
      reducer(RSetState, { hiCategoryData: result, lastSearch: payload })
    },
    // 获取短信日志
    async [EGetSmsLog]({ payload }: any, { reducer }: any) {
      let result = await requestGet('https://api.ouhe-tech.com/tongyi/admin/v1/smsLogList', { companyID: 1, perPage: 100, charge: true })
      reducer(RSetState, { smsLog: result && result.data, lastSearch: payload })
    },

    async [EYCOrderMaintainList]({ payload }: any, { reducer }: any) {
      let result = await requestGet('ycorderMaintainList', payload)
      reducer(RSetState, { orderMaintain: result, lastSearch: payload })
      return adapterPaginationResult(result)

    },
    async [EYCOrderMaintainUpdate]({ payload }: any, { reducer }: any) {
      let result = await requestPut(`ycorderMaintainUpdate/${payload.id}`, payload)
      if (result) {
        await this[EYCOrderMaintainList]({ status: '待维护' }, { reducer })
      }
    },
    //  标准CURD示例
    async [EGetYCImport]({ payload }: any, { reducer }: any) {
      let result = await requestGet('ycorderimport', payload)
      reducer(RSetState, { lastSearch: payload })
      return adapterPaginationResult(result)
    },
    async [EPostYCImport]({ payload }: any, { reducer }: any) {
      let result = await requestPost('ycorderimport', payload)
      reducer(RSetState, { lastSearch: payload })
      return adapterPaginationResult(result)
    },
    async [EDeleteYCImport]({ payload }: any, { reducer }: any) {
      let result = await requestDelete(`ycorderimport/${payload.id}`)
      reducer(RSetState, { lastSearch: payload })
      return adapterPaginationResult(result)
    },
    async [EBatchDestroyYCImport]({ payload }: any) {
      let result = await requestPost(`ycorderimport/batchDestroy`, payload)
      return result
    },
    async [EPutYCImport]({ payload }: any, { reducer }: any) {
      let result = await requestPut('ycorderimport', payload)
      reducer(RSetState, { lastSearch: payload })
      return adapterPaginationResult(result)
    },
    async [EConfirmOrder]({ payload }: any, { reducer }: any) {
      let result = await requestPost(`ycorderimport/confirmOrder`, payload)
      return result
    },
  },
}
