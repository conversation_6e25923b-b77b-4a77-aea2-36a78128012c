import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'
import {
  EDeleteImport, EGet, EGetAddress, EGetImport, EGetList,
  EGetLineCharts, EGetCharts, EGetData,
  EPostImport, EPutImport, NCollection, RAdd, RSetState,
  EBatchDestroyImport, EOrderMaintainList, EOrderMaintainUpdate,
  EGetHiProvinceCharts, EGetHiChannelCharts, EGetHiCategoryCharts
} from '../common/action'
import { adapterPaginationResult } from '../common/utils'

export default {
  namespace: NCollection,
  state: {
    lastSearch: {},
    orderMaintain: [],
    total: 0,
    totalPay: 500,
    dashData: {},
    chartData: {},
    lineData: [],
    hiProvinceData: [], // 嗨回收省份分布数据
    hiChannelData: [], // 嗨回收渠道分布数据
    hiCategoryData: [], // 嗨回收品类分布数据
  },
  reducers: {
    [RSetState](state: any, payload: any) {
      return { ...state, ...payload }
    },
    [RAdd](state: { count: any }, payload: any) {
      return { ...state, count: state.count + payload }
    },
  },
  effects: {
    //  标准CURD示例
    async [EGet]({ payload }: any, { reducer }: any) {
      let result = await requestPost('collection', payload)
      reducer(RSetState, { lastSearch: payload })
      return adapterPaginationResult(result)
    },
    //  标准CURD示例
    async [EGetList]({ payload }: any, { reducer }: any) {
      let result = await requestGet('logMsg', payload)
      reducer(RSetState, { total: result && result.total, totalPay: result && result.totalPay })
      return adapterPaginationResult(result)
    },
    // 列表及翻页
    async [EGetAddress]({ payload }: any) {
      return await requestGet('address', payload)
    },
    //  标准CURD示例
    async [EGetImport]({ payload }: any, { reducer }: any) {
      let result = await requestGet('orderimport', payload)
      reducer(RSetState, { lastSearch: payload })
      return adapterPaginationResult(result)
    },
    async [EPostImport]({ payload }: any, { reducer }: any) {
      let result = await requestPost('orderimport', payload)
      reducer(RSetState, { lastSearch: payload })
      return adapterPaginationResult(result)
    },
    async [EDeleteImport]({ payload }: any, { reducer }: any) {
      let result = await requestDelete(`orderimport/${payload.id}`)
      reducer(RSetState, { lastSearch: payload })
      return adapterPaginationResult(result)
    },
    async [EBatchDestroyImport]({ payload }: any) {
      let result = await requestPost(`orderimport/batchDestroy`, payload)
      return result
    },
    async [EPutImport]({ payload }: any, { reducer }: any) {
      let result = await requestPut('orderimport', payload)
      reducer(RSetState, { lastSearch: payload })
      return adapterPaginationResult(result)
    },
    async [EGetData]({ payload }: any, { reducer }: any) {
      let result = await requestGet('getData', payload)
      reducer(RSetState, { dashData: result })
    },
    async [EGetCharts]({ payload }: any, { reducer }: any) {
      let result = await requestGet('getCharts', payload)
      reducer(RSetState, { chartData: result, lastSearch: payload })
    },
    async [EGetLineCharts]({ payload }: any, { reducer }: any) {
      let result = await requestGet('getLineCharts', payload)
      reducer(RSetState, { lineData: result, lastSearch: payload })
    },
    async [EOrderMaintainList]({ payload }: any, { reducer }: any) {
      let result = await requestGet('orderMaintainList', payload)
      reducer(RSetState, { orderMaintain: result, lastSearch: payload })
      return adapterPaginationResult(result)

    },
    async [EOrderMaintainUpdate]({ payload }: any, { reducer }: any) {
      let result = await requestPut(`orderMaintainUpdate/${payload.id}`, payload)
      if (result) {
        await this[EOrderMaintainList]({ status: '待维护' }, { reducer })
      }
    },
    // 获取嗨回收省份分布数据
    async [EGetHiProvinceCharts]({ payload }: any, { reducer }: any) {
      let result = await requestGet('getHiProvinceCharts', payload)
      reducer(RSetState, { hiProvinceData: result, lastSearch: payload })
    },
    // 获取嗨回收渠道分布数据
    async [EGetHiChannelCharts]({ payload }: any, { reducer }: any) {
      let result = await requestGet('getHiChannelCharts', payload)
      reducer(RSetState, { hiChannelData: result, lastSearch: payload })
    },
    // 获取嗨回收品类分布数据
    async [EGetHiCategoryCharts]({ payload }: any, { reducer }: any) {
      let result = await requestGet('getHiCategoryCharts', payload)
      reducer(RSetState, { hiCategoryData: result, lastSearch: payload })
    },
  },
}
