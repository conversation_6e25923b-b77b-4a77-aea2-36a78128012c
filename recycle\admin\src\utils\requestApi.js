import { notification } from 'antd'
import router from 'umi/router'
import { Config } from '../config'
import { getToken } from './authority'
import axios from 'axios'

export default function request(url, options, blob) {
  return new Promise((resolve, reject) => {
    let { method, query } = options
    // 添加url前缀
    if (!url.indexOf('https://') > -1 || !url.indexOf('http://') > -1) {
      url = Config.SERVER_HOME + (url.indexOf('/') === 0 ? url.substr(1) : url)
    }
    let option = {
      method,
      url,
      header: {
        Accept: 'application/json',
        Pragma: 'no-cache',
        'Cache-Control': 'no-cache',
        Expires: 0,
        'Content-Type': 'application/json; charset=utf-8'
      }
    }
    // TODO 添加token
    let token = getToken()
    if (token) {
      option.headers = option.headers || {}
      option.headers.Authorization = `Bearer ${token}`
    }
    if (blob) {
      option.responseType = 'blob'
    }
    // 参数赋值
    switch (method) {
      case 'GET':
      case 'DELETE':
        option.params = query || {}
        break
      case 'POST':
      case 'PATCH':
      case 'PUT':
        option.data = query || {}
        break
    }
    axios(option)
      .then(response => {
        // console.log(22, response.data)
        resolve(response.data)
      })
      .catch(error => {
        // console.log(1111,JSON.stringify(error))
        if (error.response) {
          let { status, data } = error.response
          if (status === 400) {
            let { error, message } = data
            notification.error({
              message: `请求错误`,
              description: message
            })
          }

          if (status === 401) {
            window.g_app._store.dispatch({
              type: 'login/logout'
            })
            return
          }
          // environment should not be used
          if (status === 403) {
            router.push('/exception/403')
            return
          }
          if (status <= 504 && status >= 500) {
            router.push('/exception/500')
            return
          }
          if (status >= 404 && status < 422) {
            router.push('/exception/404')
          }
        } else if (error.request) {
          throw error
        } else {
          throw error
        }
      })
  })
}

export function requestGet(url, query) {
  return request(url, { method: 'GET', query })
}
export function requestGetExcel(url, query) {
  return request(url, { method: 'GET', query }, true)
}
export function requestDelete(url) {
  return request(url, { method: 'DELETE' })
}
export function requestPost(url, query) {
  console.log('requestPost:', url, query)
  return request(url, { method: 'POST', query })
}
export function requestPatch(url, query) {
  return request(url, { method: 'PATCH', query })
}
export function requestPut(url, query) {
  return request(url, { method: 'PUT', query })
}
