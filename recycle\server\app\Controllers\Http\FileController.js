"use strict";

const moment = require("moment");
const fs = require("fs-extra");
const path = require("path");
const Helpers = use("Helpers");
const hasha = require("hasha");
const axios = require("axios");
const shortid = require("shortid");

const OSS = require("ali-oss");
const client = new OSS({
  region: "oss-cn-shanghai",
  accessKeyId: "LTAI5tGy18wdAHdv6bqF4x83",
  accessKeySecret: "******************************",
  bucket: "hanhan-oss1"
});
const END_POINT = "https://oss.evergreenrecycle.cn/";
const PROJECT = "hanhan/upload";

const streamToBuffer = stream => {
  return new Promise((resolve, reject) => {
    let buffers = [];
    stream.on("error", reject);
    stream.on("data", data => buffers.push(data));
    stream.on("end", () => resolve(Buffer.concat(buffers)));
  });
};

const QRCode = require("qrcode");
const QRCodeToFile = Helpers.promisify(QRCode.toFile);

const { Seller } = require("../../Models");

class FileController {
  async jsonWithShortID({ request, response }) {
    let json = request.post();
    let shortID = shortid.generate();
    let ossFilePath = path.join("json", `${shortID}.json`).replace(/\\/g, "/");
    try {
      await client.put(ossFilePath, new Buffer(JSON.stringify(json)));
      response.json({
        shortID,
        url: END_POINT + ossFilePath
      });
    } catch (e) {
      console.error(e);
      throw { error: 12003, message: "OSS上传失败" };
    }
  }

  async file({ request, response }) {
    let file = request.file("file");
    let { project = PROJECT } = request.all();
    if (!file) {
      throw { error: 12001, message: "文件不存在" };
    }

    let { subtype, clientName } = file;
    // 上传文件到本地
    let tmpName = (moment().format("x") + "." + subtype).toLowerCase();
    fs.ensureDirSync(Helpers.tmpPath());
    await file.move(Helpers.tmpPath(), { name: tmpName });
    if (!file.moved()) {
      throw { error: 12002, message: "文件上传失败" };
    }

    // 上传文件到oss
    let md5 = await hasha.fromFile(Helpers.tmpPath(tmpName), {
      algorithm: "md5"
    });
    let ossFilePath = path
      .join(project, md5 + "." + subtype)
      .replace(/\\/g, "/");
    try {
      await client.put(ossFilePath, Helpers.tmpPath(tmpName));
      await fs.remove(Helpers.tmpPath(tmpName));
      response.json({
        fileName: clientName,
        url: END_POINT + ossFilePath
      });
    } catch (e) {
      console.error(e);
      throw { error: 12003, message: "OSS上传失败" };
    }
  }

  async base64({ request, response }) {
    let { file, name, project = PROJECT } = request.all();
    file = file.replace(/^data:image\/(png|jpg|jpeg|gif);base64,/, "");
    if (!file || !name) {
      throw { error: 12001, message: "文件不存在" };
    }
    // 上传文件到本地
    let subtype = name.split(".").pop() || "bin";
    let buf = new Buffer(file, "base64");
    let md5 = await hasha(buf, { algorithm: "md5" });

    // 上传文件到oss
    let ossFilePath = path
      .join(project, md5 + "." + subtype)
      .replace(/\\/g, "/");
    try {
      await client.put(ossFilePath, buf);
      response.json({
        fileName: name,
        url: END_POINT + ossFilePath
      });
    } catch (e) {
      console.error(e);
      throw { error: 12003, message: "OSS上传失败" };
    }
  }

  async url({ request, response }) {
    let { url, project = PROJECT } = request.all();

    let subtype = path.extname(url);
    let tmpName = (moment().format("x") + subtype).toLowerCase();

    try {
      // 下载文件
      let res = await axios.get(url, { responseType: "stream" });
      let buf = await streamToBuffer(res.data);

      // 生成md5
      let md5 = hasha(buf, { algorithm: "md5" });
      let ossFilePath = path.join(project, md5 + subtype).replace(/\\/g, "/");

      // 上传oss
      let obj = await client.put(ossFilePath, buf);
      await fs.remove(Helpers.tmpPath(tmpName));
      return {
        fileName: path.basename(url),
        url: END_POINT + ossFilePath
      };
    } catch (e) {
      console.error(e);
      return { url };
    }
  }

  async getQRCode({ request, response }) {
    let name = "seller_" + request.userID;
    let ossFilePath = path.join("invite", name + ".png").replace(/\\/g, "/");
    try {
      await client.head(ossFilePath);
      response.json({ url: END_POINT + ossFilePath });
    } catch (e) {
      // 文件不存在则重新创建
      let seller = await Seller.findBy("userID", request.userID);
      if (!seller) {
        throw ERR.INVALID_PARAMS;
      }
      let code = seller.id;
      const codePath = Helpers.resourcesPath(`qrcode_${name}.png`);
      await QRCodeToFile(
        codePath,
        `https://s.stgame.cn/st12/client/v1/share/${code}`,
        {}
      );
      const isExist = fs.existsSync(codePath);
      if (!isExist) {
        await QRCodeToFile(codePath, code);
      }
      await client.put(ossFilePath, codePath);
      await fs.remove(codePath);
      response.json({ url: END_POINT + ossFilePath });
    }
  }
}

module.exports = FileController;
