'use strict'

const Model = use('Model')

//订单记录
class YCOrderLog extends Model {
  static get primaryKey() {
    return 'id'
  }
  static get table() {
    return 'yc_order_log'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return null
  }
  worker() {
    return this.hasOne('App/Models/Worker', 'workerID', 'id').select('id', 'workerName', 'mobile', 'companyID')
  }
  user() {
    return this.hasOne('App/Models/UserAddress', 'userID', 'userID').select('id', 'realName', 'userID', 'mobile')
  }
  creater() {
    return this.hasOne('App/Models/AdminUser', 'createrID', 'id').select('id', 'username', 'name', 'level')
  }
  order() {
    return this.belongsTo('App/Models/YCOrder', 'orderID', 'id')
  }
}

module.exports = YCOrderLog
