'use strict'

const Model = use('Model')

//师傅支付
class WorkerPay extends Model {
  static get table() {
    return 'worker_pay'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return 'updatedAt'
  }
  worker() {
    return this.belongsTo('App/Models/Worker', 'workerID', 'id')
  }
  refundVo() {
    return this.hasMany('App/Models/WorkerPayRefund', 'id', 'payID')
  }
}

module.exports = WorkerPay
