import Taro from '@tarojs/taro'
import React, { Component } from 'react'
import T from "../../config/T"
import { connect } from 'react-redux'
import { View, Image, Button, Text } from '@tarojs/components'
import { AtModal } from 'taro-ui'
import './goodsItem.less'
import _findIndex from 'lodash/findIndex'
import { NOldGoods, NOrder, NUserAddress, NUser } from '../../config/constants'
class GoodsItem extends Component {
  constructor() {
    super(...arguments)
    this.state = {}
  }

  config = {}

  //-----------------------生命周期-------------------------//
  componentWillMount() {}

  componentDidMount() {
  }

  componentWillUnmount() {}

  componentDidShow() {}

  componentDidHide() {}

  //-----------------------事件-------------------------//
  // 添加
  creatData = async () => {
    const { TwoLevelMenuTitle } = await this.props
    let arr = await this.props.oldGoods.haveChosen
    let subscript = await _findIndex(arr, { title: TwoLevelMenuTitle })

    if (subscript == -1) {
      await arr.push({
        title: TwoLevelMenuTitle,
        item: [],
      })
      await this.props.dispatch.NOldGoods[NOldGoods.ESetState]({
        haveChosen: arr,
      })
    }
  }
  // 删除
  delData = async () => {
    const { TwoLevelMenuTitle } = await this.props
    let arr = await this.props.oldGoods.haveChosen
    let subscript = await _findIndex(arr, { title: TwoLevelMenuTitle })
    if (subscript > -1) {
      if (arr[subscript].item.length == 0) {
        await arr.splice(subscript, 1)
        await this.props.dispatch.NOldGoods[NOldGoods.ESetState]({
          haveChosen: arr,
        })
      }
    }
  }
  // 保存点击数据
  selectedItem = async () => {
    await this.creatData()
    const { TwoLevelMenuTitle } = await this.props
    let arr = await this.props.oldGoods.haveChosen
    let id = await _findIndex(arr, { title: TwoLevelMenuTitle })
    const { data } = await this.props
    let subscript = await _findIndex(arr[id].item, data)
    if (subscript == -1) {
      await arr[id].item.push(data)
    } else {
      await arr[id].item.splice(subscript, 1)
    }

    await this.props.dispatch.NOldGoods[NOldGoods.ESetState]({
      haveChosen: arr,
    })
    await this.delData()
  }

  onClickItem = id => {
    const { head, headId, page, pageArr } = this.props.oldGoods
    let newPageArr = pageArr
    if (newPageArr.includes(id)) {
      let index = newPageArr.indexOf(id)
      newPageArr.splice(index, 1)

      this.props.dispatch.NOldGoods[NOldGoods.ESetState]({
        pageArr: newPageArr,
      })
    } else {
      newPageArr.push(id)

      this.props.dispatch.NOldGoods[NOldGoods.ESetState]({
        pageArr: newPageArr,
      })
    }
  }

  //-----------------------渲染-------------------------//
  render() {
    const { url, type, price, i, pageArr, onClickItem, newId, data, TwoLevelMenu } = this.props
    return (
      <View
        className="goodsItem"
        style={{
          marginRight: `${(i + 1) % 3 == 0 ? '0' : '2.6vw'}`,
          boxShadow: `${
            pageArr && pageArr.includes(newId) ? '1px 2px 2px 2px rgba(223, 223, 223, 0.4)' : '0px 0px 3px 5px rgba(223, 223, 223, 0)'
          }`,
        }}
        onClick={() => {
          this.onClickItem(newId)
          this.selectedItem(TwoLevelMenu)
        }}
      >
        <Image src={url} className="goodsItem_img" />
        <Text className="goodsItem_text" style={{ marginTop: '1vw', color: '#7c8696', fontFamily: 'Helvetica_Italic', fontSize: '3.2vw' }}>
          {type}
        </Text>
        <Text
          className="goodsItem_text"
          style={{ marginTop: '1vw', color: '#999999', fontFamily: 'Helvetica_Italic', fontSize: '2.7vw', fontStyle: 'oblique' }}
        >
          {price}
        </Text>
        {pageArr && pageArr.includes(newId) ? (
          <Image src={require('./../../assets/oldGoods/check.png')} className="goodsItem_cancel" />
        ) : null}
      </View>
    )
  }
}
export default connect(({ NOldGoods }) => ({ oldGoods:NOldGoods }))(GoodsItem)
