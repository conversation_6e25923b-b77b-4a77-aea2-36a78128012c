import { Button, message, Input, Select, notification, Modal } from 'antd'
import ProCard from '@ant-design/pro-card'
import { effect, useConnect } from 'dva17'
import { useEffect, useRef, useState } from 'react'
import { ECityAddress, EDelCommission, EGet, EGetCommission, EPostCommission, EPutCommission, EPutbundlePrice, NCategoryPrice, NCompany } from '../../common/action'
import { computeAuthority } from '../../utils/Authorized/authority'
import type { ActionType, ProColumns } from '@ant-design/pro-table'
import ProTable, { } from '@ant-design/pro-table'
import ProForm, { ProFormInstance, ProFormSelect, ProFormText } from '@ant-design/pro-form'
import { PlusOutlined } from '@ant-design/icons'
import { Areas, fromEnum, recycleTypeEnum, SourceLevels, TypeLevels } from '../../common/enum'
import styles from './index.module.less'
import { getAddress } from '../../services/companyAPI'

const { Option } = Select
type Item = {
  id: number
  workerName: string
  mobile: string
  order: {
    status: string
    totalMoney: number
    count: number
  }[]
  company: {
    companyName: string
    id: number
  }[]
  district: string
  type: string
  forbidden: number
}
export default () => {
  const [visible, setVisible] = useState<boolean>(false)
  const [showData, setShowData] = useState<any>(null)
  const { cityList, companyList } = useConnect(NCompany)
  const [newCommission, setNewCommission] = useState<any>({})
  const [districtList, setDistrictList] = useState<any>([])
  const [visibleChange, setVisibleChange] = useState<boolean>(false)
  const [mySelectedRowKeys, setMySelectedRowKeys] = useState([]);
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>()
  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    effect(NCompany, EGet, { forbidden: 1 })
  }, [])
  /*--------------------- 响应 ---------------------*/
  const changePrice = async (orders: any[]) => {
    setVisibleChange(true)
  }
  const noticePut = () => {
    notification.success({
      message: '成功！',
      description: '修改成功',
      duration: 2
    })
    refreshPage()
  }
  const refreshPage = async () => {
    if (actionRef.current) {
      actionRef.current.reload()
    }
  }
  const handleEdit = async (row: any) => {
    setVisible(true)
    setShowData(row)
    setNewCommission({ show: false })
  }
  const handleDel = async (row: any) => {
    Modal.confirm({
      title: '确认删除该数据',
      content: <div>删除后数据无法恢复！</div>,
      okText: '确认删除',
      cancelText: '退出',
      onOk: async () => {
        effect(NCategoryPrice, EDelCommission, { ...row })
        notification.success({
          message: '成功！',
          description: '删除成功',
          duration: 2,
        })
        refreshPage()
      },
      width: 700,
    })
  }

  const rowSelectionChange = (selectedRowKeys: any, selectedRows: any) => {
    setMySelectedRowKeys(selectedRowKeys)
  }
  const rowSelection = {
    columnWidth: '2vw',
    selections: [
      {
        key: 'odd',
        text: '批量改价',
        onSelect: (changeableRowKeys: any) => {
          changePrice(mySelectedRowKeys)
        },
      },
    ],
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      rowSelectionChange(selectedRowKeys, selectedRows)
    },
  };
  const handleChange = async () => {
    if (mySelectedRowKeys.length > 0) {
      effect(NCategoryPrice, EPutbundlePrice, { ...newCommission, ids: mySelectedRowKeys })
      await refreshPage()
    }
    setVisibleChange(false)
  }
  const handleOK = async () => {
    const val2 = await formRef.current?.validateFieldsReturnFormatValue?.();
    if (!val2.type || !val2.price || !val2.source) {
      message.error({ content: '请重新输入!' })
    } else {
      if (newCommission.show) {
        val2.area = newCommission.district || newCommission.city
      }
      if (showData) {
        effect(NCategoryPrice, EPutCommission, { id: showData.id, ...val2 })
      } else {
        effect(NCategoryPrice, EPostCommission, { ...val2 })
      }
      setVisible(false)
    }
    noticePut()
  }
  const changCity = async (e: any) => {
    let res = await getAddress({ payload: { code: cityList[e].code } })
    setDistrictList(res)
    setNewCommission({ ...newCommission, city: cityList[e].name })
  }
  const changDistrit = async (e: any) => {
    setNewCommission({ ...newCommission, district: districtList[e].name })
  }
  const columns: ProColumns<Item>[] = [
    { title: '区域', dataIndex: 'area', copyable: true, ellipsis: true },
    { title: '品类', dataIndex: 'type', copyable: true, ellipsis: true ,valueEnum: recycleTypeEnum},
    { title: '平台', dataIndex: 'source', copyable: true, ellipsis: true ,valueEnum: fromEnum},
    {
      title: '服务商', dataIndex: 'companyID', copyable: true, ellipsis: true,
      render: (_, row: any) => {
        return (<>{row?.company?.companyName}</>)
      }
    },
    {
      title: '信息费', dataIndex: 'price', copyable: false, search: false, ellipsis: true
    },
    {
      title: '操作', width: '18%', copyable: false, ellipsis: true, search: false,
      render: (_, row: any) => {
        return (
          <>
            <Button
              onClick={() => {
                handleEdit(row)
              }}>
              修改
            </Button>
            <Button
              type='primary'
              onClick={() => {
                handleDel(row)
              }}>
              删除
            </Button>
          </>
        )
      },
    },
  ]
  const handleValuesChange = (changedValues: any, allValues: any) => {
    console.log(changedValues);

    if (changedValues.area === '广东省') {
      setNewCommission({ show: true })
      effect(NCompany, ECityAddress, { code: 44 })
    } else if (changedValues.area && changedValues.area !== '广东省') {
      setNewCommission({ show: false })
    } else {
    }
  };
  /*--------------------- 渲染 ---------------------*/
  return (
    <>
      <ProCard>
        <ProTable<Item>
          actionRef={actionRef}
          rowSelection={rowSelection}
          columns={columns}
          request={async (params = {}) => (await effect(NCategoryPrice, EGetCommission, {
            ...params
          })) as any}
          pagination={{
          }}
          rowKey="id"
          dateFormatter="string"
          headerTitle=""
          toolBarRender={() => [
            <Button key="3" type="primary"
              disabled={!computeAuthority('佣金编辑')}
              onClick={() => {
                setVisible(true)
                setShowData(null)
              }}>
              <PlusOutlined />
              新建
            </Button>,
          ]}
        />
      </ProCard>
      <Modal
        destroyOnClose={true}
        open={visible}
        title={`${showData ? '编辑佣金' : '新建佣金'}`}
        onCancel={() => {
          setVisible(false)
          setShowData(null)
        }}
        onOk={async () => {
          await handleOK()
        }}
      >
        <div style={{ width: '100%' }}>
          <ProForm
            submitter={false}
            formRef={formRef}
            onValuesChange={handleValuesChange}
          >
            <ProFormSelect
              initialValue={showData ? showData.area : null}
              options={
                Areas.map((vo: any) => {
                  return {
                    value: vo,
                    label: vo,
                  }
                })
              }
              width="md"
              name="area"
              label="区域"
              placeholder="请输入区域"
            />
            {newCommission.show && <><div className={styles.newCommission_item}>
              <span className={styles.newCommission_item_title}>市：</span>
              <Select
                style={{ width: 250 }}
                value={newCommission.city}
                onChange={e => {
                  changCity(e)
                }}>
                {cityList && cityList.length > 0 ? cityList.map((v: any, i: number) => <Option key={i}>{v.name}</Option>) : null}
              </Select>
            </div><div className={styles.newCommission_item}>
                <span className={styles.newCommission_item_title}>区：</span>
                <Select
                  style={{ width: 250 }}
                  value={newCommission.district}
                  onChange={e => {
                    changDistrit(e)
                  }}>
                  {districtList && districtList.length > 0 ? districtList.map((v: any, i: number) => <Option key={i}>{v.name}</Option>) : null}
                </Select>
              </div></>}
            <ProFormSelect
              initialValue={showData ? showData.type : null}
              options={
                TypeLevels.map((vo: any) => {
                  return {
                    value: vo,
                    label: vo,
                  }
                })
              }
              name="type"
              width="md"
              label="品类"
              placeholder="请选择品类"
            />
            <ProFormSelect
              initialValue={showData ? showData.companyID : null}
              options={
                companyList?.map((vo: any) => {
                  return {
                    value: vo.id,
                    label: vo.companyName,
                  }
                })
              }
              name="companyID"
              width="md"
              label="服务商"
              placeholder="请选择服务商"
            />
            <ProFormSelect
              initialValue={showData ? showData.source : null}
              options={
                SourceLevels.map((vo: any) => {
                  return {
                    value: vo,
                    label: vo,
                  }
                })
              }
              name="source"
              width="md"
              label="平台"
              placeholder="请选择平台"
            />
            <ProFormText
              initialValue={showData ? (showData.price) : null}
              width="md"
              name="price"
              label="价格"
              placeholder="请输入价格"
            />
          </ProForm>
        </div>
      </Modal>
      <Modal
        destroyOnClose={true}
        title="批量改价确认"
        open={visibleChange}
        onOk={handleChange}
        onCancel={() => { setVisibleChange(false) }}
      >
        <p>批量改价
        </p>
        <div className={styles.each}>
          {mySelectedRowKeys && mySelectedRowKeys.length}条
        </div>
        <div>
          信息费：<Input
            placeholder="请输入新价格！"
            onChange={e => {
              newCommission.price = e?.target?.value
              setNewCommission(newCommission)
            }}
            style={{ width: 250 }}
            value={newCommission.price}
            type="text"
          />元
        </div>

      </Modal>
    </>
  )
}
