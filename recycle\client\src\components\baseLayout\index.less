.BaseLayout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  .navigationBar {
    .statusBarHeight {
      height: 0;
    }
    .titleBar {
      display: flex;
      font-size: 36px;
      align-items: center;
      padding-left: 50px;
    }
  }
  .container {
    flex: 1;
    overflow-y: scroll;
  }
  .footer {
    width: 750px;
    height: 178px;
    background-size: cover;
    background-position: center;
    display: flex;
    &.img1 {
      background-image: url('https://oss.evergreenrecycle.cn/donggua/client/images/footer1.png');
    }
    &.img2 {
      background-image: url('https://oss.evergreenrecycle.cn/donggua/client/images/footer2.png');
    }
    &.img3 {
      background-image: url('https://oss.evergreenrecycle.cn/donggua/client/images/footer3.png');
    }
    .button {
      background-color: rgba(0, 0, 0, 0);
      flex: 1;
    }
  }
}
