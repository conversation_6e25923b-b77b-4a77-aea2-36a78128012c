import { DownOutlined, PlusOutlined, RightOutlined } from '@ant-design/icons'
import { Button, Badge, Space, Switch, Modal, notification, Input, message } from 'antd'
import type { ActionType, ProColumns } from '@ant-design/pro-table'
import ProTable from '@ant-design/pro-table'
import ProCard from '@ant-design/pro-card'
import { effect, reducer, useConnect } from 'dva17'
import { EGet, NCompany, NWorker, EGetFullTimeWorker, RSetState, ECompanyArea, EPutPrice, EPostPrice, EGetPrice } from '../../common/action'
import ProForm, { ProFormInstance, ProFormSelect, ProFormText } from '@ant-design/pro-form'
import { useEffect, useRef, useState } from 'react'
import styles from './index.module.less'
import { computeAuthority } from '../../utils/Authorized/authority'
import { delPrice } from '../../services/company'
import { AreaList } from '../../common/enum'

type Item = {
  id: number
  area: string
  lowCharger: string
  createdAt: any
}

export default () => {
  const { companyArea } = useConnect(NCompany)
  const [showData, setShowData] = useState<any>(null)
  const [visibleEdit, setVisibleEdit] = useState<any>(false)
  const [currentArea, setCurrentArea] = useState<any>(null)
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>()
  const columns: ProColumns<Item>[] = [
    {
      title: '区域',
      dataIndex: 'area',
      copyable: false,
      ellipsis: true,
    },
    {
      title: '最低充值',
      copyable: false,
      dataIndex: 'lowCharger',
      ellipsis: true,
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'updatedAt',
      copyable: false,
      ellipsis: true,
      search: false,
    },
    {
      title: '操作',
      width: '30%',
      copyable: false,
      ellipsis: true,
      search: false,
      render: (_, row) => (
        <>
          <Button
            disabled={!computeAuthority('服务商新建与编辑')}
            onClick={() => {
              handleEdit(row)
            }}>
            编辑
          </Button>
          <Button
            disabled={!computeAuthority('服务商新建与编辑')}
            onClick={() => {
              deleteItem(row)
            }}>
            删除
          </Button>
        </>
      ),
    },
  ]
  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    let currentArea: any = []
    companyArea.forEach((area: any, index: any) => {
      if (currentArea.indexOf(area.area) < 0) {
        currentArea.push(area.area)
      }
    })
    setCurrentArea(currentArea)
  }, [companyArea])
  /*--------------------- 响应 ---------------------*/
  const refreshPage = () => {
    if (actionRef.current) {
      actionRef.current.reload()
    }
  }
  const deleteItem = (e: any) => {
    Modal.confirm({
      title: '确认删除数据',
      content: <div>删除后数据无法恢复！</div>,
      okText: '确认删除',
      cancelText: '退出',
      onOk: async () => {
        await delPrice({ id: e.id })
        refreshPage()
        notification.success({
          message: '成功！',
          description: '删除成功',
          duration: 2,
        })
      },
      width: 700,
    })
  }
  const noticePut = () => {
    notification.success({
      message: '成功！',
      description: '修改成功',
      duration: 2
    })
    refreshPage()
  }
  const handleEdit = async (row: any) => {
    setVisibleEdit(true)
    setShowData(row)
  }
  const handleOK = async () => {
    const val2 = await formRef.current?.validateFieldsReturnFormatValue?.();
    if (!val2.area || !val2.lowCharger) {
      message.error({ content: '请重新输入!' })
    } else {
      if (showData) {
        effect(NCompany, EPutPrice, { id: showData.id, ...val2 })
      } else {
        effect(NCompany, EPostPrice, { ...val2 })
      }
      setVisibleEdit(false)
    }
    noticePut()
  }

  /*--------------------- 渲染 ---------------------*/
  return (
    <ProCard>
      <ProTable<Item>
        actionRef={actionRef}
        columns={columns}
        request={async (params = {}) => (await effect(NCompany, EGetPrice, params)) as any}
        pagination={{
          pageSize: 20,
        }}
        rowKey="id"
        dateFormatter="string"
        headerTitle=""
        toolBarRender={() => [
          <Button
            disabled={!computeAuthority('服务商新建与编辑')}
            key="3"
            type="primary"
            onClick={() => {
              setVisibleEdit(true)
            }}>
            <PlusOutlined />
            新建
          </Button>,
        ]}
      />
      <Modal
        destroyOnClose={true}
        open={visibleEdit}
        title={`${showData ? '编辑价格' : '新建价格'}`}
        onCancel={() => {
          setVisibleEdit(false)
          setShowData(null)
        }}
        onOk={async () => {
          await handleOK()
        }}
      >
        <div style={{ width: '100%' }}>
          <ProForm
            submitter={false}
            formRef={formRef}
          >
            <ProFormSelect
              initialValue={showData ? showData.area : null}
              options={
                AreaList.map((vo: any) => {
                  return {
                    value: vo,
                    label: vo,
                  }
                })
              }
              name="area"
              width="md"
              label="区域"
              placeholder="请选择区域"
            />
            <ProFormText
              initialValue={showData ? (showData.lowCharger) : null}
              width="md"
              name="lowCharger"
              label="价格"
              placeholder="请输入价格"
            />
          </ProForm>
        </div>
      </Modal>
    </ProCard >
  )
}
