'use strict'

const Model = use('Model')
const Database = use('Database')

//上门师傅信息
class JDWorkerPay extends Model {
  static get table() {
    return 'jd_worker_pay'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return 'updatedAt'
  }
  worker() {
    return this.belongsTo('App/Models/JDWorker', 'workerID', 'id')
  }
}

module.exports = JDWorkerPay
