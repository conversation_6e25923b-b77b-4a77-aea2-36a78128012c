import { Modal, Button, Table } from 'antd'
import { effect, useConnect } from 'dva17'
import { EGetWorkerWalletlog, NWorker } from '../../common/action'
import { SERVER_HOME } from '../../common/config'
import dayjs from 'dayjs'
import { useEffect } from 'react'

// 师傅钱包记录组件
interface WorkerWalletLogModalProps {
  visible: boolean;
  workerID: number;
  onClose: () => void;
}

const WorkerWalletLogModal = ({ visible, workerID, onClose }: WorkerWalletLogModalProps) => {
  const { workerPaylist } = useConnect(NWorker);
  
  const columns = [
    { title: '订单ID', render: (record: any) => <span>{record?.order?.orderNo}</span> },
    { title: '备注', render: (record: any) => <span>{record.remark}</span> },
    { title: '消费金额', render: (record: any) => <span>{(record?.money)}</span> },
    { title: '时间', dataIndex: 'createdAt', render: (createdAt: any) => 
      <span>{createdAt ? dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss') : '-'}</span> 
    },
  ];

  const exportWalletExcel = () => {
    window.open(`${SERVER_HOME}exportWalletXLS?workerID=${workerID}`);
  };

  useEffect(() => {
    if (visible) {
      effect(NWorker, EGetWorkerWalletlog, { current: 1, pageSize: 20, workerID: workerID })
    }
  }, [workerID])

  return (
    <Modal
      title="师傅钱包消费记录"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
    >
      <Button type='primary' onClick={exportWalletExcel} style={{ marginBottom: 16 }}>导出</Button>
      <Table
        columns={columns}
        loading={!workerPaylist}
        dataSource={workerPaylist?.data || []}
        rowKey={(record) => JSON.stringify(record)}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          pageSize: workerPaylist?.perPage || 20,
          current: workerPaylist?.page || 1,
          total: workerPaylist?.total || 0,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          onChange: (page, pageSize) => {
            effect(NWorker, EGetWorkerWalletlog, { current: page, pageSize: pageSize, workerID: workerID })
          }
        }}
      />
    </Modal>
  );
};

export default WorkerWalletLogModal; 