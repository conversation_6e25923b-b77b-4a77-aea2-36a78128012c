import { Modal, Button, Table } from 'antd'
import { useConnect } from 'dva17'
import { NWorker } from '../../common/action'
import { SERVER_HOME } from '../../common/config'
import dayjs from 'dayjs'

// 师傅钱包记录组件
interface WorkerWalletLogModalProps {
  visible: boolean;
  workerID: number;
  onClose: () => void;
}

const WorkerWalletLogModal = ({ visible, workerID, onClose }: WorkerWalletLogModalProps) => {
  const { workerPaylist } = useConnect(NWorker);
  
  const columns = [
    { title: '订单ID', render: (record: any) => <span>{record?.order?.orderNo}</span> },
    { title: '备注', render: (record: any) => <span>{record.remark}</span> },
    { title: '消费金额', render: (record: any) => <span>{(record?.money)}</span> },
    { title: '时间', dataIndex: 'createdAt', render: (createdAt: any) => 
      <span>{createdAt ? dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss') : '-'}</span> 
    },
  ];

  const exportWalletExcel = () => {
    window.open(`${SERVER_HOME}exportWalletXLS?workerID=${workerID}`);
  };

  return (
    <Modal
      title="师傅钱包消费记录"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
    >
      <Button type='primary' onClick={exportWalletExcel} style={{ marginBottom: 16 }}>导出</Button>
      <Table
        columns={columns}
        loading={!workerPaylist}
        dataSource={workerPaylist?.data || []}
        rowKey={(record) => JSON.stringify(record)}
        pagination={false}
      />
    </Modal>
  );
};

export default WorkerWalletLogModal; 