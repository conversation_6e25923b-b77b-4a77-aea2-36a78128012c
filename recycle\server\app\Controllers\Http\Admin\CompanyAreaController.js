'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Company, CompanyArea, SysArea, ReqLog } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { CryptUtil } = require('../../../Util')
const Env = use('Env')

//公司信息
class CompanyAreaController {
  //获取某服务商或某街道的回收情况
  async index({ request, response }) {
    let { companyID, code, areaCode } = request.all()
    if (!companyID && !code) {
      throw ERR.INVALID_PARAMS
    }
    let query = CompanyArea.query()
      .with('company', (b) => b.select('id', 'companyName'))
    if (companyID) {
      query.where('companyID', companyID)
    }
    if (code) {
      query.where('code', code)
    }
    if (areaCode) {
      query.where('areaCode', 'like', `${areaCode}%`)
    }
    let vo = await query.fetch()
    response.json(vo)
  }
  //编辑某服务商的回收信息
  async store({ request, response }) {
    let { companyID, addList, removeList } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台 编辑服务商回收信息' })
    if (!companyID) {
      throw ERR.INVALID_PARAMS
    }
    let company = await Company.find(companyID)
    if (!company) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    for (let i = 0; i < removeList.length; i++) {
      let vo = CompanyArea.query().where({ companyID }).where('code', removeList[i].code).where('typeName', removeList[i].typeName)
      if (vo) {
        await vo.delete()
      }
    }
    for (let i = 0; i < addList.length; i++) {
      let vo = await CompanyArea.create({ companyID, ...addList[i] })
    }
    // let vo = await CompanyArea.query()
    //   .where('companyID', companyID)
    //   .with('company', (b) => b.select('id', 'companyName'))
    //   .fetch()
    response.json({ msg: 'ok' })
  }
  async update({ request, params, response }) {
    let { postDatas } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台 编辑服务商回收信息' })
    if (!params.id) {
      throw ERR.API_ERROR
    }
    response.json({ msg: 'ok' })
    for (let i = 0; i < postDatas.length; i++) {
      let vo = await CompanyArea.create({ companyID: params.id, ...postDatas[i] })
    }
  }
  async getCompanyArea({ request, response, params }) {
    let { current = 1, pageSize = 10, province, city, keyWord } = request.all()
    if (!params.id) {
      throw ERR.API_ERROR
    }
    let query = CompanyArea.query()
      .select('id', 'code', 'name', 'companyID', 'areaCode', 'area')
      .where('companyID', params.id)
      .groupBy('code')
      .with('company', (b) => b.select('id', 'companyName'))
      .with('address')
      .with('typeInfo')
    if (province) {
      if (province !== 'all') {
        query.where('areaCode', 'like', `${province}%`)
      }
    }
    if (city) {
      let areaInfo = await SysArea.query()
        .where('name', 'LIKE', `%${city}%`)
        .where('level', 2)
        .select('id', 'name', 'cityCode')
        .fetch()
      let areacodes = areaInfo.rows.map((vo) => {
        return (vo.cityCode)
      }
      )
      query.where('areaCode', 'IN', _.uniq(areacodes))
    }
    if (keyWord) {
      query.where('name', 'like', `%${keyWord}%`)
        .orWhere('area', 'like', `%${keyWord}%`)
    }
    let total = await query.getCount()
    let data = await query
      .orderBy('code', 'desc')
      .offset(pageSize * (current - 1))
      .limit(pageSize)
      .fetch()
    return {
      code: 200, total, page: current, perPage: pageSize,
      lastPage: Math.floor(total / pageSize) + 1, data,
    }
  }
  async destroy({ request, params, response }) {
    let { adminUser: user } = request
    let { code, arrIds } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台 删除服务商回收信息' })
    if (user.level !== E.AdminLevel.总部) {
      throw ERR.USER_ROLE_NO_PRIVILEGE
    }
    if (code) {
      let vo = CompanyArea.query().where({ code })
      if (vo) {
        await vo.delete()
      }
    }
    if (arrIds) {
      let vo = CompanyArea.query().whereIn('code', arrIds)
      if (vo) {
        await vo.delete()
      }
    }

    return ({ msg: 'delete ok', code: 200 })
  }
}

module.exports = CompanyAreaController
