import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'

export async function getCompanyDetail(payload: any) {
  return requestGet(`/company/${payload.id}`)
}
export async function getAddress(payload: any) {
  return requestGet('address', payload)
}
export async function CompanyEdit(payload: any) {
  return requestPost(`companyArea`, payload)
}
export async function CompanyEditArea(payload: any) {
  return requestPut(`company/${payload.id}`, payload)
}
export async function getCompanyArealist(payload: any) {
  return requestGet(`getCompanyArea/${payload.id}`, payload)
}
export async function CompanyPostArea(payload: any) {
  return requestPut(`companyArea/${payload.id}`, payload)
}
export async function CompanyDeleteArea(payload: any) {
  return requestDelete(`companyArea/${payload.id}`, payload)
}
export async function getCompanyList(payload: any) {
  return requestGet('company',payload)
}
export async function getCompanyArea(payload: any) {
  return requestGet('companyArea', payload)
}
export async function getPrice(payload: any) {
  return requestGet('chargePrice', payload)
}
export async function postPrice(payload: any) {
  return requestPost('chargePrice', payload)
}
export async function delPrice(payload: any) {
  return requestDelete(`chargePrice/${payload.id}`)
}
export async function putPrice(payload: any) {
  return requestPut(`chargePrice/${payload.id}`, payload)
}