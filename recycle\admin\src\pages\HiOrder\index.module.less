.page {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.newOrder {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 20px 0;
  // border:1px solid red;
  .newOrder_item {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 10px 0;
    margin-bottom: 16px;
    &:last-child {
      margin-bottom: 0;
    }
    .newOrder_item_title {
      display: inline-block;
      text-align: right;
      font-size: 14px;
      color: #606266;
      line-height: 40px;
      text-align: right;
      width: 110px;
      font-weight: 500;
      min-width: 100px;
      margin-right: 12px;
    }
  }
}
.order_status_wrapper {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
  .order_status {
    width: 120px;
    height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f5f6fa;
    cursor: pointer;
    border-radius: 4px;
    box-shadow: 0 2px 8px #cccccc;
    font-weight: 600;
    padding: 8px 16px;
    margin-right: 16px;
    border: 1px solid #d9d9d9;
    transition: all 0.3s;
    > span {
      &:first-child {
        margin-bottom: 10px;
      }
    }
    &:hover {
      border-color: #1890ff;
      color: #1890ff;
    }
  }
  .order_status_active {
    background: #1890ff;
    color: #ffffff;
    border-color: #1890ff;
    &:hover {
      background: #40a9ff;
      border-color: #40a9ff;
    }
  }
}
.item_wrapper {
  display: flex;
  flex-direction: column;
  padding: 20px;
  background: #fafafa;
  border-radius: 6px;
  .item_wrapper_reasonList {
    display: flex;
    flex-wrap: wrap;
    .reasonList_item {
      border: 1px solid #1890ff;
      border-radius: 3px;
      padding: 5px 10px;
      cursor: pointer;
      margin-right: 15px;
    }
  }
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    &:last-child {
      margin-bottom: 0;
    }
    .item_title {
      font-weight: 600;
      color: #333;
      min-width: 120px;
      margin-right: 12px;
    }
    .item_log {
      display: inline-block;
      width: 90px;
      text-align: right;
      font-weight: 600;
      font-weight: bolder;
      cursor: pointer;
      text-decoration:underline;
      color: #1890ff;
    }
    .item_content {
      color: #666;
      flex: 1;
      word-break: break-all;
      .item_pic {
        height: 100px;
        width: 100px;
        border-radius: 4px;
        margin-right: 10px;
        margin-bottom: 10px;
        box-shadow: 0 2px 4px #cccccc;
      }
      .item_content_type {
        display: inline-block;
        width: 110px;
        height: 34px;
        line-height: 34px;
        text-align: center;
        border-radius: 4px;
        border: 1px solid #1890ff;
        color: #1890ff;
        margin: 0 8px 8px 0;
      }
      .waste_wrapper {
        width: 100%;
        .waste_wrapper_title {
          width: 100%;
          display: flex;
          font-weight: 600;
          .title {
            width: 50%;
            text-align: center;
          }
        }
      }
      .content_wrapper {
        width: 100%;
        display: flex;
        margin: 10px 0;
        .name {
          width: 50%;
          text-align: center;
        }
        .price {
          width: 50%;
          text-align: center;
        }
      }
    }
  }
}
.each {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  .each_one {
    width: 120px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border: 1px solid #cccccc;
    margin-right: 20px;
    margin-bottom: 15px;
    border-radius: 4px;
    cursor: pointer;
  }
  .each_selected {
    background: #1890ff;
    color: #ffffff;
    border: none;
  }
}
.modalTitle {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}
.spaceAroundLine {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 20px 0;
}