import React, { useState, useEffect } from 'react';
import { Modal, Card, Row, Col, DatePicker, Statistic, Table, Spin, message } from 'antd';
import { Pie } from '@ant-design/charts';
import { CalendarOutlined, CheckCircleOutlined, CloseCircleOutlined, ClockCircleOutlined, DollarOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import moment from 'moment';
import { effect } from 'dva17';
import { NWorker, EGetWorkerOrderData } from '../../common/action';
import styles from './index.module.less';

const { RangePicker } = DatePicker;

interface WorkerDataModalProps {
  visible: boolean;
  worker: any;
  onClose: () => void;
}

interface OrderData {
  ongoingOrders: number;
  cancelRate: number;
  completionRate: number;
  avgCompletionTime: number;
  totalAccepted: number;
  totalCanceled: number;
  totalCompleted: number;
  currentPeriodRecharge: number; // 本期充值金额
  totalRechargeAmount: number; // 总充值金额
  deviceTypeStats: Record<string, number>; // 设备类型统计
  orderDetails: any[];
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    pages: number;
  };
}

const WorkerDataModal: React.FC<WorkerDataModalProps> = ({
  visible,
  worker,
  onClose,
}) => {
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState<[moment.Moment, moment.Moment]>([
    moment().startOf('month'),
    moment().endOf('month')
  ]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [orderData, setOrderData] = useState<OrderData>({
    ongoingOrders: 0,
    cancelRate: 0,
    completionRate: 0,
    avgCompletionTime: 0,
    totalAccepted: 0,
    totalCanceled: 0,
    totalCompleted: 0,
    currentPeriodRecharge: 0,
    totalRechargeAmount: 0,
    deviceTypeStats: {},
    orderDetails: []
  });

  // 获取师傅订单数据
  const fetchWorkerData = async (paginationParams?: { current?: number; pageSize?: number }) => {
    if (!worker?.id) return;
    
    setLoading(true);
    try {
      const params = {
        workerID: worker.id,
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
        current: paginationParams?.current || pagination.current,
        pageSize: paginationParams?.pageSize || pagination.pageSize,
      };

      const response = await effect(NWorker, EGetWorkerOrderData, params) as any;
      
      
      if (response && response.success) {
        const data = response.data;
        
        // 计算各项指标
        const ongoingOrders = data.ongoingOrders || 0;
        const totalAccepted = data.totalAccepted || 0;
        const totalCanceled = data.totalCanceled || 0;
        const totalCompleted = data.totalCompleted || 0;
        const deviceTypeStats = data.deviceTypeStats || {};
        
        const cancelRate = totalAccepted > 0 ? (totalCanceled / totalAccepted * 100) : 0;
        const completionRate = totalAccepted > 0 ? (totalCompleted / totalAccepted * 100) : 0;
        const avgCompletionTime = data.avgCompletionTime || 0;

        setOrderData({
          ongoingOrders,
          cancelRate: Number(cancelRate.toFixed(2)),
          completionRate: Number(completionRate.toFixed(2)),
          avgCompletionTime: Number(avgCompletionTime.toFixed(2)),
          totalAccepted,
          totalCanceled,
          totalCompleted,
          currentPeriodRecharge: data.currentPeriodRecharge || 0,
          totalRechargeAmount: data.totalRechargeAmount || 0,
          deviceTypeStats,
          orderDetails: data.orderDetails || [],
          pagination: data.pagination
        });
        
        // 更新分页状态
        if (data.pagination) {
          setPagination({
            current: data.pagination.current,
            pageSize: data.pagination.pageSize,
            total: data.pagination.total,
          });
        }
      }
    } catch (error: any) {
      message.error('获取师傅数据失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible && worker?.id) {
      // 日期范围改变时，重置到第一页
      setPagination(prev => ({ ...prev, current: 1 }));
      fetchWorkerData({ current: 1 });
    }
  }, [visible, worker?.id, dateRange]);

  // 订单详情表格列配置
  const orderColumns = [
    {
      title: '订单编号',
      dataIndex: 'orderNo',
      key: 'orderNo',
      width: 120,
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusMap: Record<string, { text: string; color: string }> = {
          '进行中': { text: '进行中', color: '#1890ff' },
          '已完成': { text: '已完成', color: '#52c41a' },
          '已取消': { text: '已取消', color: '#f5222d' },
        };
        const config = statusMap[status] || { text: status, color: '#666' };
        return <span style={{ color: config.color }}>{config.text}</span>;
      },
    },
    {
      title: '下单时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (time: string) => time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: '接单时间',
      dataIndex: 'takeTime',
      key: 'takeTime',
      width: 150,
      render: (time: string) => time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: '完成时间',
      dataIndex: 'completeTime',
      key: 'completeTime',
      width: 150,
      render: (time: string) => time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: '完工用时(小时)',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
      render: (_: any, record: any) => {
        if (record.takeTime && record.completeTime) {
          const duration = dayjs(record.completeTime).diff(dayjs(record.takeTime), 'hour', true);
          return Number(duration.toFixed(2));
        }
        return '-';
      },
    },
  ];

  const handleDateChange = (dates: any) => {
    if (dates && dates.length === 2) {
      setDateRange([dates[0], dates[1]]);
    }
  };

  // 处理分页变化
  const handleTableChange = (pagination: any) => {
    setPagination({
      current: pagination.current,
      pageSize: pagination.pageSize,
      total: pagination.total,
    });
    fetchWorkerData({
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 渲染设备分布饼状图
  const renderDeviceChart = () => {
    const deviceTypeStats = orderData.deviceTypeStats || {};
    // 计算设备总数
    const total = Object.values(deviceTypeStats).reduce((sum, count) => sum + count, 0);
    
    if (total === 0) {
      return (
        <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
          暂无设备数据
          <br />
          <small style={{ color: '#ccc' }}>
            deviceTypeStats: {JSON.stringify(deviceTypeStats)}
          </small>
        </div>
      );
    }

    // 定义所有可能的设备类型
    const allDeviceTypes = ['电视', '冰箱', '洗衣机', '空调柜机', '空调挂机', '燃气热水器', '电热水器', '空气热泵热水器'];
    
    // 生成饼状图数据，包含所有设备类型
    const pieData = allDeviceTypes.map(type => {
      const count = deviceTypeStats[type] || 0;
      return {
        type,
        name: type,
        value: count > 0 ? count : 0.1, // 对于数量为0的设备，给一个很小的值以便在图例中显示
        realValue: count, // 保存真实数值
        percentage: total > 0 ? (count / total * 100).toFixed(1) : '0',
      };
    });
    

    // 设备类型颜色映射 - 根据实际设备类型调整
    const deviceColors: Record<string, string> = {
      '电视': '#ff7875',
      '冰箱': '#73d13d', 
      '洗衣机': '#1890ff',
      '空调柜机': '#722ed1',
      '空调挂机': '#fa8c16',
      '燃气热水器': '#52c41a',
      '电热水器': '#13c2c2',
      '空气热泵热水器': '#eb2f96',
    };

    // 为每个设备类型分配颜色，如果没有预定义颜色则使用默认颜色
    const getDeviceColor = (deviceType: string) => {
      return deviceColors[deviceType] || '#d9d9d9';
    };

    const pieConfig = {
      data: pieData,
      angleField: 'value',
      colorField: 'type',
      label: {
        text: 'value',
        style: {
          fontWeight: 'bold',
        },
      },
      legend: false,
      tooltip: ({ type, value }: { type: any, value: any }) => {
        // Extra fields
        return { type, value };
      },
      interaction: {
        tooltip: {
          render: (e: any, { items }: { items: any }) => {
            return (
              <React.Fragment>
                {items.map((item: any) => {
                  const { type, value, color } = item;
                  return (
                    <div key={type} style={{ margin: 0, display: 'flex', justifyContent: 'space-between' }}>
                      <div>
                        <span
                          style={{
                            display: 'inline-block',
                            width: 6,
                            height: 6,
                            borderRadius: '50%',
                            backgroundColor: color,
                            marginRight: 6,
                          }}
                        ></span>
                        <span>{type}</span>
                      </div>
                      <b>{value}</b>
                    </div>
                  );
                })}
              </React.Fragment>
            );
          },
        },
      },
      radius: 0.75,
      innerRadius: 0.4,
      color: pieData.map(item => getDeviceColor(item.type)),
    };

    return (
      <div style={{ padding: '10px 0' }}>
        <Pie {...pieConfig} height={200} />
        {/* 显示设备类型详细统计 */}
        <div style={{ marginTop: 10, fontSize: 12, color: '#666' }}>
          {allDeviceTypes.map((type, index) => {
            const count = deviceTypeStats[type] || 0;
            return (
              <span key={type} style={{ marginRight: 12 }}>
                <span 
                  style={{ 
                    display: 'inline-block', 
                    width: 8, 
                    height: 8, 
                    backgroundColor: getDeviceColor(type),
                    borderRadius: '50%',
                    marginRight: 4,
                  }}
                />
                {type}: {count}台
              </span>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <Modal
      title={`师傅数据统计 - ${worker?.workerName || ''}`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={"80vw"}
      destroyOnClose
    >
      <div className={styles.container}>
        {/* 时间筛选 */}
        <div className={styles.dateFilter}>
          <CalendarOutlined style={{ marginRight: 8 }} />
          <span style={{ marginRight: 8 }}>筛选时间：</span>
          <RangePicker
            value={dateRange}
            onChange={handleDateChange}
            format="YYYY-MM-DD"
            placeholder={['开始日期', '结束日期']}
          />
        </div>

        <Spin spinning={loading}>
          {/* 核心统计指标 */}
          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="进行中订单"
                  value={orderData.ongoingOrders}
                  prefix={<ClockCircleOutlined style={{ color: '#1890ff' }} />}
                  valueStyle={{ color: '#1890ff' }}
                  suffix="单"
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="本期取消率"
                  value={orderData.cancelRate}
                  prefix={<CloseCircleOutlined style={{ color: '#f5222d' }} />}
                  valueStyle={{ color: '#f5222d' }}
                  suffix="%"
                  precision={2}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="本期完单率"
                  value={orderData.completionRate}
                  prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                  valueStyle={{ color: '#52c41a' }}
                  suffix="%"
                  precision={2}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="平均完工时效"
                  value={orderData.avgCompletionTime}
                  prefix={<ClockCircleOutlined style={{ color: '#722ed1' }} />}
                  valueStyle={{ color: '#722ed1' }}
                  suffix="小时"
                  precision={2}
                />
              </Card>
            </Col>
          </Row>

          {/* 充值金额和设备分布 */}
          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col span={8}>
              <Card title="充值统计" size="small" style={{ height: 300 }}>
                <div style={{ padding: '20px 0' }}>
                  <Row gutter={[0, 24]}>
                    <Col span={24}>
                      <div className={styles.rechargeItem}>
                        <div className={styles.rechargeTitle}>
                          <DollarOutlined style={{ color: '#fa8c16', marginRight: 8 }} />
                          本期充值金额
                        </div>
                        <div className={styles.rechargeValue} style={{ color: '#fa8c16' }}>
                          ¥{orderData.currentPeriodRecharge.toLocaleString()}
                        </div>
                      </div>
                    </Col>
                    <Col span={24}>
                      <div className={styles.rechargeItem}>
                        <div className={styles.rechargeTitle}>
                          <DollarOutlined style={{ color: '#1890ff', marginRight: 8 }} />
                          累计充值金额
                        </div>
                        <div className={styles.rechargeValue} style={{ color: '#1890ff' }}>
                          ¥{orderData.totalRechargeAmount.toLocaleString()}
                        </div>
                      </div>
                    </Col>
                  </Row>
                </div>
              </Card>
            </Col>
            <Col span={8}>
              <Card title="设备类型分布" size="small" style={{ height: 300 }}>
                {renderDeviceChart()}
              </Card>
            </Col>
            <Col span={8}>
              <Card title="详细数据" size="small" style={{ height: 300 }}>
                <div style={{ padding: '10px 0' }}>
                  <Row gutter={[0, 16]}>
                    <Col span={24}>
                      <div className={styles.detailItem}>
                        <span className={styles.detailLabel}>接单总数：</span>
                        <span className={styles.detailValue}>{orderData.totalAccepted}单</span>
                      </div>
                    </Col>
                    <Col span={24}>
                      <div className={styles.detailItem}>
                        <span className={styles.detailLabel}>取消订单：</span>
                        <span className={styles.detailValue} style={{ color: '#f5222d' }}>
                          {orderData.totalCanceled}单
                        </span>
                      </div>
                    </Col>
                    <Col span={24}>
                      <div className={styles.detailItem}>
                        <span className={styles.detailLabel}>完成订单：</span>
                        <span className={styles.detailValue} style={{ color: '#52c41a' }}>
                          {orderData.totalCompleted}单
                        </span>
                      </div>
                    </Col>
                   
             
                  </Row>
                </div>
              </Card>
            </Col>
          </Row>

          {/* 订单详情表格 */}
          <Card title="订单明细" style={{ marginTop: 16 }}>
            <Table
              columns={orderColumns}
              dataSource={orderData.orderDetails}
              rowKey="id"
              pagination={{
                ...pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条数据`,
                pageSizeOptions: ['5', '10', '20', '50'],
              }}
              onChange={handleTableChange}
              scroll={{ x: 600 }}
              size="small"
            />
          </Card>
        </Spin>
      </div>
    </Modal>
  );
};

export default WorkerDataModal; 