import Taro from '@tarojs/taro'
import React, { Component } from 'react'
import T from '../../config/T'
import { connect } from 'react-redux'
import { View, Image, Button, Text, Checkbox, Icon } from '@tarojs/components'
import './orderDetail.less'
import E from '../../config/E'
import dayjs from 'dayjs'
import icon from '../../assets/icon/index'

class OrderDetail extends Component {
  constructor() {
    super(...arguments)
    this.state = {
      locale: 'zh-CN',
      whichManage: '预订',
      showList: true,
      imgs: [],
    }
  }

  config = {}

  //-----------------------生命周期-------------------------//
  componentWillMount() {
    let manage = Taro.getCurrentInstance().router.params.manage
    let orderNumber = Taro.getCurrentInstance().router.params.orderNumber
    this.setState({
      orderID: orderNumber,
      whichManage: manage,
    })
  }

  componentDidMount() {
    const locale = Taro.getStorageSync('locale')
    this.setState({ locale })
    this.props.dispatch({
      type: 'NOrder/EGetOrderDetail',
      payload: {
        id: this.state.orderID,
      },
    })

    Taro.setNavigationBarTitle({ title: locale == 'en' ? 'Order Details' : '订单详情' })
  }

  componentWillReceiveProps(nextProps) {
    if (!this.props.isGetDetail && nextProps.isGetDetail) {
      let detail = nextProps.orderDetail
      let imgs
      imgs = detail.images
      this.setState({ imgs: imgs ? imgs : [] })
    }
  }

  componentWillUnmount() { }

  componentDidShow() {
    this.props.dispatch({
      type: 'NOrder/EGetOrderDetail',
      payload: {
        id: this.state.orderID,
      },
    })
  }

  componentDidHide() { }

  //-----------------------事件-------------------------//
  changeManage(index) {
    this.setState({
      whichManage: index,
    })
  }

  orderDetail(index) {
    Taro.navigateTo({ url: `/pages/orderDetail/orderDetail?orderNumber=${index}&manage=${this.state.whichManage}` })
  }

  call(phone) {
    Taro.makePhoneCall({
      phoneNumber: phone,
    })
  }

  operatePhote(index) {
    wx.previewImage({
      current: index, // 当前显示图片的http链接
      urls: [index], // 需要预览的图片http链接列表
    })
  }

  previewImage(current) { }

  payOrder(order) {
    this.props.dispatch({
      type: 'NOrder/EUserPay',
      payload: {
        id: order.id,
      },
    })
  }

  //-----------------------渲染-------------------------//
  render() {
    let { whichManage, showList, imgs, locale } = this.state
    let { orderDetail } = this.props
    return orderDetail ? (
      <View className="orderDetail">
        <View className="orderHead">
          <Image
            mode="widthFix"
            src={'https://oss.evergreenrecycle.cn/donggua/client/images/state0.png'}
            style={{
              width: '120rpx',
            }}
          />
          <View className="top_wrapper">
            <View className={`manage_title ${(whichManage === whichManage) === E.OrderStatus.Cancelled ? 'format' : null}`}>
              <View className="title_left">
                {whichManage === E.OrderStatus.Cancelled
                  ? T.orderDetail.orderCancel
                  : whichManage === E.OrderStatus.Completed
                    ? T.orderDetail.orderCompleted
                    : whichManage === E.OrderStatus.InProgress
                      ? T.orderDetail.orderInProgress
                      : T.orderDetail.orderBooked}
              </View>
              {whichManage === E.OrderStatus.InProgress || whichManage === E.OrderStatus.Reservation ? (
                <View
                  className="title_right"
                  onClick={() => {
                    Taro.navigateTo({
                      url: `/pages/cancleOrder/cancleOrder?orderID=${orderDetail.id}&cancle=true&manage=${whichManage}`,
                    })
                  }}
                >
                  {T.orderDetail.cancelOrder}
                </View>
              ) : whichManage === E.OrderStatus.Completed ? (
                <View
                  className="title_right"
                  onClick={() => {
                    if (orderDetail.complaint) {
                      return
                    }
                    Taro.navigateTo({ url: `/pages/cancleOrder/cancleOrder?orderID=${orderDetail.id}&cancle=` })
                  }}
                >
                  {orderDetail.complaint ? T.orderDetail.complained : T.orderDetail.complaint}
                </View>
              ) : null}
            </View>
            {whichManage === E.OrderStatus.Cancelled ? (
              <View className="describe_content">
                {T.orderDetail.cancelReason}：<Text>{orderDetail.cancel ? orderDetail.cancel.cancelReason : ''}</Text>
              </View>
            ) : (
              <View className="work_time">
                {T.orderDetail.appointmentTime}：{orderDetail && orderDetail.workDate && dayjs(orderDetail.workDate).format('YYYY-MM-DD')}{' '}
                {orderDetail && orderDetail.workTime}
              </View>
            )}
          </View>
        </View>
        <View className="wrapperBox">
          {whichManage === E.OrderStatus.InProgress || whichManage === E.OrderStatus.Completed ? (
            <View className="format_wrapper">
              <View className="top_wrapper">
                <View className="recycler_wrapper">
                  <View className="recycler">
                    <View className="recycler_avatar">
                      <Text>
                        {orderDetail && orderDetail.worker
                          ? orderDetail.worker.workerName.substr(orderDetail.worker.workerName.length - 1, 1)
                          : ''}
                      </Text>
                    </View>
                    <View className="recycler_operate">
                      <Text>
                        {T.orderDetail.recycler}：{orderDetail.worker && orderDetail.worker.workerName}
                      </Text>
                      <View>{orderDetail.worker ? orderDetail.worker.mobile : ''}</View>
                    </View>
                  </View>
                  {whichManage === E.OrderStatus.InProgress ? (
                    <View
                      className="recycler_phone"
                      onClick={() => {
                        this.call(orderDetail?.worker?.mobile)
                      }}
                    >
                      <Image src={require(`./../../assets/icon/telephone.png`)} />
                      <Text>{T.orderDetail.contact}</Text>
                    </View>
                  ) : (
                    <View
                      className="recycler_phone"
                      onClick={() => {
                        Taro.navigateTo({ url: `/pages/remarkPage/remarkPage?orderID=${orderDetail.id}` })
                      }}
                    >
                      <Image src={require(`./../../assets/icon/reporter.png`)} />
                      <Text>{T.orderDetail.evaluation}</Text>
                    </View>
                  )}
                </View>
              </View>
              <View className="devide"></View>
            </View>
          ) : null}
          {orderDetail && (
            <View className="format_wrapper">
              <View className="top_wrapper">
                <View className="manage_title format"></View>
                <View className="format information_wrapper">
                  <Text>{orderDetail.userName}</Text>
                  <Text>{orderDetail?.userPhone}</Text>
                </View>
                <View className="describe_content">
                  {orderDetail?.province +
                    orderDetail?.city +
                    orderDetail?.town +
                    orderDetail?.address}
                </View>
              </View>
              <View className="devide"></View>
            </View>
          )}
          <View className="format_wrapper">
            <View className="top_wrapper" style={{ paddingBottom: '1px' }}>
              <View className="manage_title format">
                {T.orderDetail.appliances}
                {whichManage !== E.OrderStatus.Cancelled ? (
                  <View
                    className="close_button"
                    onClick={() => {
                      this.setState({ showList: !showList })
                    }}
                  >
                    {T.orderDetail.pickUp}
                    <Image src={require('./../../assets/icon/up.png')} style={{ transform: `rotate(${showList ? 0 : '180deg'})` }} />
                  </View>
                ) : null}
              </View>
              {whichManage === E.OrderStatus.Completed ? (
                <View>
                  <View className="total_price">
                    <Text>{T.orderDetail.total}</Text>
                    <Text>x {'套'}</Text>
                  </View>
                  <View className="total_price">
                    <Text>{T.orderDetail.totalRevenue}</Text>
                    <Text>
                      {`￥${orderDetail.estimatedPrice}`}
                    </Text>
                  </View>
                </View>
              ) : null}
              {showList
                ? orderDetail &&
                <View className="kind_price">
                  <View>
                    <Text>
                      {orderDetail.type}
                    </Text>
                    <Text>{`￥${orderDetail.estimatedPrice}`}</Text>
                  </View>
                  {orderDetail.logisticID || whichManage !== E.OrderStatus.Completed ? null : (
                    <Text>{value.quantity ? `x ${value.quantity}` : null}</Text>
                  )}
                </View>
                : null}
              {whichManage === E.OrderStatus.Completed ? (
                <View>
                  <View className="total_price">
                    <Text>{T.orderDetail.total}</Text>
                    <Text>x {orderDetail.unit ? orderDetail.unit : '-'}</Text>
                  </View>
                  <View className="total_price">
                    <Text>{orderDetail.waste_1st_ID === 4 ? T.orderDetail.totalExpenditure : T.orderDetail.totalRevenue}</Text>
                    <Text>
                      {orderDetail.actualMoney || orderDetail.actualMoney === 0
                        ? `￥${orderDetail.actualMoney / 100}`
                        : T.orderDetail.publicRecycling}
                    </Text>
                  </View>
                </View>
              ) : null}
            </View>
            <View className="devide"></View>
          </View>
          {orderDetail && (
            <View className="format_wrapper">
              <View className="top_wrapper">
                <View className="manage_title format">{T.orderDetail.upload}</View>
                <View className="image_wrapper">
                  {imgs.map((url, index) => (
                    <Image
                      className="image_item"
                      src={url}
                      onClick={() => {
                        this.operatePhote(url)
                      }}
                      mode="aspectFit"
                    />
                  ))}
                </View>
                {orderDetail.remark != 0 ? <View className="remark_wrapper">{orderDetail.remark}</View> : null}
              </View>
              <View className="devide"></View>
            </View>
          )}
          <View className="manage_title format">
            <View></View>
            <View>{T.orderDetail.orderInfo}</View>
          </View>
          {orderDetail && (
            <View className="format_wrapper">
              <View className="top_wrapper">
                {/* <View className="manage_title format">{T.orderDetail.orderInfo}</View> */}
                <View className="describe_content format">
                  {T.orderDetail.orderNo}：{orderDetail.orderNo}
                </View>
                <View className="describe_content format">
                  {T.orderDetail.orderTime}：{orderDetail.createdAt}
                </View>
                {whichManage === E.OrderStatus.Cancelled ? (
                  <View className="describe_content">
                    {T.orderDetail.cancelTime}：
                    {orderDetail.cancel ? dayjs(orderDetail.cancel.createdAt).format('YYYY-MM-DD HH:mm:ss') : '-'}
                  </View>
                ) : null}
                {whichManage === E.OrderStatus.Completed ? (
                  <View className="describe_content">
                    {T.orderDetail.completeTime}：
                    {orderDetail && orderDetail.finishedAt
                      ? dayjs(orderDetail.finishedAt).format('YYYY-MM-DD HH:mm:ss')
                      : dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')}
                  </View>
                ) : null}
              </View>
              <View className="devide"></View>
            </View>
          )}
          {whichManage === E.OrderStatus.InProgress && orderDetail.waste_1st_ID === 4 && orderDetail.actualMoney ? (
            <View className="format_wrapper">
              <View className="top_wrapper">
                <View className="manage_title format">{T.orderDetail.toBePaid}</View>
                <View className="describe_content format how_are_you">
                  <Text>
                    {T.orderDetail.dealPrice}：¥{orderDetail.actualMoney / 100}
                  </Text>
                  <Text
                    className="pay_button"
                    onClick={() => {
                      this.payOrder(orderDetail)
                    }}
                  >
                    {T.orderDetail.pay}
                  </Text>
                </View>
              </View>
            </View>
          ) : null}
          <View className="devide" style={{ height: '40px' }}></View>
        </View>
      </View>
    ) : null
  }
}
export default connect(({ NOrder: { orderDetail, isGetDetail } }) => ({
  orderDetail,
  isGetDetail,
}))(OrderDetail)
