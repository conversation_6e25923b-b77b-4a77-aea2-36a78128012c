import Taro, { getCurrentInstance } from '@tarojs/taro'
import { AtTextarea } from 'taro-ui'
import { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { View, Text, Button, Image, OfficialAccount, } from '@tarojs/components'
import './index.less'
import { NOrder, NUser } from '../../config/constants'
import T from '../../config/T'
import E from '../../config/E'

export default () => {
  const { workerInfo } = useSelector(state => state.NOrder)
  const { userInfo } = useSelector(state => state.NUser)
  const dispatch = useDispatch()
  const [masterID, setMasterID] = useState(0)
  const [expressionIndex, setExpressionIndex] = useState(3)
  const [reason, setReason] = useState('')
  const [selectReason, setSelectReason] = useState('很准时')
  const [selectIndex, setSelectIndex] = useState(0)


  function handlePost() {
    if ((reason || selectReason) && expressionIndex) {
      dispatch.NUser[NUser.EPostEvaluate]({
        userID: userInfo.id, expressionID: expressionIndex, reason, selectReason, workerID: parseInt(masterID)
      })
    }
  }
  function selectReasonFun(e, index) {
    setSelectIndex(index)
    setSelectReason(e)
  }

  function selectExpression(index) {
    setExpressionIndex(index)
  }
  // 解析
  function solve(code) {
    if (-1 == code.indexOf("?")) {
      return false;
    }
    let arr = code.split("?")[1].split("=");
    let obj = {};
    obj[arr[0]] = arr[1];
    return obj;
  }
  const getPhone = async (res) => {
    const phoneData = res?.detail
    if (res.detail.errMsg === 'getPhoneNumber:ok') {
      await Taro.showToast({
        title: '授权中...',
        icon: 'loading',
      })
      dispatch({
        type: 'NUser/EPutUserPhoneNew',
        payload: {
          encryptedData: phoneData.encryptedData,
          iv: phoneData.iv,
        },
      })
      await Taro.showToast({
        title: '评价成功',
        duration: 5000
      })
      Taro.navigateTo({ url: '/pages/home/<USER>' })
    }
  }
  const onLoadHandler = e => {
  }
  useEffect(() => {
    dispatch.NUser[NUser.ELogin]()
    const { params: { q } } = getCurrentInstance().router;
    let { fromMaster } = solve(decodeURIComponent(q));
    setMasterID(fromMaster)
  }, [])

  useEffect(() => {
    if (masterID) {
      dispatch.NOrder[NOrder.EGetWorker]({
        workerID: masterID
      })
    }
  }, [masterID])


  return (
    <View className='cancleOrder'>
      <View style={{width:'90%',borderRadius:'16rpx'}}><OfficialAccount onLoad={onLoadHandler} onError={onLoadHandler} /></View>
      <View className='recycler'>
        <View className='recycler_avatar'>
          <Text>{workerInfo?.workerName ? workerInfo.workerName.substr(workerInfo.workerName.length - 1, 1) : null}</Text>
        </View>
        <View className='recycler_operate'>
          <Text>{workerInfo?.workerName}</Text>
          <View>{workerInfo?.mobile}</View>
        </View>
      </View>
      <View className='expression_wrapper'>
        <View
          className='item_wrapper'
          onClick={() => {
            selectExpression(1)
          }}
        >
          {expressionIndex === 1 ? (
            <Image src={require(`../../assets/icon/not_satisfaction_active.png`)} className='expression' />
          ) : (
            <Image src={require(`../../assets/icon/not_satisfaction.png`)} className='expression' />
          )}
          <Text style={expressionIndex === 1 ? { color: '#15b381' } : null}>{T.reamrkPage.dissatisfied}</Text>
        </View>
        <View
          className='item_wrapper'
          onClick={() => {
            selectExpression(2)
          }}
        >
          {expressionIndex === 2 ? (
            <Image src={require(`../../assets/icon/general_active.png`)} className='expression' />
          ) : (
            <Image src={require(`../../assets/icon/general.png`)} className='expression' />
          )}
          <Text style={expressionIndex === 2 ? { color: '#15b381' } : null}>{T.reamrkPage.general}</Text>
        </View>
        <View
          className='item_wrapper'
          onClick={() => {
            selectExpression(3)
          }}
        >
          {expressionIndex === 3 ? (
            <Image src={require(`../../assets/icon/satisfaction_active.png`)} className='expression' />
          ) : (
            <Image src={require(`../../assets/icon/satisfaction.png`)} className='expression' />
          )}
          <Text style={expressionIndex === 3 ? { color: '#15b381' } : null}>{T.reamrkPage.satisfaction}</Text>
        </View>
      </View>

      <View className='content_wrapper'>
        <View className='top_select'>
          {E[['OrderComplaint', 'OrderMedium', 'OrderRating'][expressionIndex - 1]].map((vo, index) => (
            <Text
              onClick={() => {
                selectReasonFun(vo.zh_CN, index)
              }}
              key={index + vo}
              style={index === selectIndex ? { color: '#ffffff', background: '#15b381' } : null}
            >
              {vo.zh_CN}
            </Text>
          ))}
        </View>
        <Text className='input_title'>{T.reamrkPage.otherComments}：</Text>
        <AtTextarea
          className='detail_input'
          count={false}
          value={reason}
          maxLength={100}
          onChange={value => {
            setReason(value)
          }}
          placeholder={T.reamrkPage.remark}
          placeholderStyle='font-size: 12px;color: #7C8696;'
        />
        <Button
          openType='getPhoneNumber'
          className='submit_button'
          style={(reason || selectReason) && expressionIndex ? { background: '#15b381' } : null}
          onGetPhoneNumber={e => getPhone(e)}
          onClick={() => {
            handlePost()
          }}
        >
          {T.reamrkPage.submit}
        </Button>
      </View>
    </View>
  )
}


