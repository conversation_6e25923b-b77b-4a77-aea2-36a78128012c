const E = {
  SpecCODElIST: [
    { type: '电视', typeCode: 'LCDTV', En: "14_25_INCH", Ch: "14寸-25寸" },
    { type: '电视', typeCode: 'LCDTV', En: "32_41_INCH", Ch: "32寸-41寸" },
    { type: '电视', typeCode: 'LCDTV', En: "LCDTV_OTHER", Ch: "其他规格-液晶电视" },
    { type: '冰箱', typeCode: 'FRIDGE', En: "80_120_DD", Ch: "80L-119L双门" },
    { type: '冰箱', typeCode: 'FRIDGE', En: "120_220_DD", Ch: "120L-220L双门" },
    { type: '冰箱', typeCode: 'FRIDGE', En: "FRIDGE_OTHER", Ch: "其他规格-冰箱" },
    { type: '洗衣机', typeCode: 'WASHER', En: "TWIN_TUB", Ch: "双缸洗衣机" },
    { type: '洗衣机', typeCode: 'WASHER', En: "DRUM", Ch: "滚筒洗衣机" },
    { type: '洗衣机', typeCode: 'WASHER', En: "TINY_S_C", Ch: "小型单缸洗衣机" },
    { type: '洗衣机', typeCode: 'WASHER', En: "WASHER_OTHER", Ch: "其他规格-洗衣机" },
    { type: '空调', typeCode: 'AIRCDT', En: "IN_VT_OT_2P0", Ch: "空调内机（立式）+空调外机2.0P" },
    { type: '空调', typeCode: 'AIRCDT', En: "IN_MT_OT_S_1P5", Ch: "空调内机（挂式）+空调外机1.5P（单片）" },
    { type: '空调', typeCode: 'AIRCDT', En: "IN_MT_OT_2P0", Ch: "空调内机（挂式）+空调外机2.0P" },
    { type: '空调', typeCode: 'AIRCDT', En: "IN_MT_OT_D_1P5", Ch: "空调内机（挂式）+空调外机1.5P（双片）" },
    { type: '空调', typeCode: 'AIRCDT', En: "AIRCDT_OTHER", Ch: "其他规格-空调" }
  ],
  PermissionItem: [
    {
      title: '订单管理',
      key: '订单管理',
      children: [
        { title: '订单查看', key: '订单查看' },
        { title: '订单操作', key: '订单操作' }
      ]
    },
    {
      title: '回收价格管理',
      key: '回收价格管理',
      children: [
        { title: '回收价格查看', key: '回收价格查看' },
        { title: '回收价格编辑', key: '回收价格编辑' }
      ]
    },
    {
      title: '佣金管理',
      key: '佣金管理',
      children: [
        { title: '佣金查看', key: '佣金查看' },
        { title: '佣金编辑', key: '佣金编辑' }
      ]
    },
    {
      title: '回收人员管理',
      key: '回收人员管理',
      children: [
        { title: '回收人员查看', key: '回收人员查看' },
        { title: '回收人员编辑', key: '回收人员编辑' },
        { title: '回收人员审核', key: '回收人员审核' }
      ]
    },
    {
      title: '服务商管理',
      key: '服务商管理',
      children: [
        { title: '服务商查看', key: '服务商查看' },
        { title: '服务商新建与编辑', key: '服务商新建与编辑' }
      ]
    },
    {
      title: '账户与权限管理',
      key: '账户与权限管理',
      children: [
        { title: '账户的查看', key: '账户的查看' },
        { title: '账户新建与编辑', key: '账户新建与编辑' },
        { title: '权限编辑', key: '权限编辑' }
      ]
    },
    {
      title: '业务数据总览',
      key: '业务数据总览',
      children: [
        { title: '数据查询', key: '数据查询' },
        { title: '数据导出', key: '数据导出' }
      ]
    },
    {
      title: '公益模块',
      key: '公益模块',
      children: [
        { title: '帖子操作', key: '帖子操作' },
        { title: '评论审核', key: '评论审核' }
      ]
    }
  ],
  Permission: {
    Primary: '初级', //可管理 订单
    Intermediate: '中级', //可管理 订单 价格 佣金
    Senior: '高级' //可管理 回收人员 服务商 订单 价格 佣金
  },
  WasteType: {
    ClothingAndBook: '旧衣旧书',
    LivingWaste: '生活废品',
    Appliances: '大家电',
    Furniture: '大家具',
  },
  AreaList: ['浙江省', '江苏省', '上海市', '安徽省', '合肥市', '广东省'],
  OrderSource: {
    CaiNiao: '菜鸟回收',
    Donggua: '冬瓜回收',
    JingDong: '京东回收',
    Hai: '嗨回收',
    Call: '电话下单',
    YouPing: '以旧换新',
    Other: '其他',
  },
  SubAddressType: [
    { label: '学校', value: '1' },
    { label: '机关单位', value: '2' },
    { label: '企业', value: '3' },
    { label: '医院', value: '4' },
    { label: '商场', value: '5' },
    { label: '其他', value: '6' }
  ],
  period: [
    { label: '年度', value: '1' },
    { label: '季度', value: '2' },
    { label: '月度', value: '3' },
    { label: '天', value: '4' }
  ],
  RectificationStatus: {
    待整改: '待整改',
    '已整改(待审核)': '已整改(待审核)',
    审核完成: '审核完成'
  },
  ReportStatus: {
    审核中: '审核中',
    审核通过: '审核通过',
    审核未通过: '审核未通过'
  },
  ComplaintStatus: {
    受理中: '受理中',
    已受理: '已受理',
    驳回: '驳回'
  },
  HttpMethod: {
    Get: 'get',
    Post: 'post',
    Put: 'put',
    Patch: 'patch',
    Delete: 'delete'
  },
  OrderStatus: {
    Init: '初始化',
    Reservation: '预订',
    Pending: '待处理',
    MasterReturn: '师傅撤回',
    SystemReturn: '系统撤回',
    TransferOrder: '转派',
    Dispatched: '已派单',
    InProgress: '进行中',
    Cancelled: '取消',
    Hacker: '黑客攻击',
    Completed: '完成'
  },
  OrderLogStatus: {
    Create: '创建',
    Cancelled: '取消',
    AcceptOrder: '接单',
    TransferOrder: '转派',
    makeOrder: '派单',
    Completed: '完成',
    Recall: '撤回',
    Updates: '系统更新订单',
    RESCHEDULE: '师傅改约',
    Over: '菜鸟系统确认完成',
    Remark: '师傅备注',
    System: '后台备注',
    Call: '师傅拨号',
    RESCHEDULE: '改约'
  },
  WorkerLevel: {
    TeamLeader: '组长',
    TeamMember: '组员'
  },
  WorkerAccountStatus: {
    Init: '初始',
    Agree: '同意',
    Reject: '拒绝'
  },
  CancleOrderStatus: {
    Client: '客户取消',
    Master: '师傅取消',
    MasterRevoke: '师傅撤销',
    System: '系统取消',
    Admin: '后台取消'
  },

  PayType: {
    Debug: '测试支付',
    Wechat: '微信支付'
  },
  AdminLevel: {
    总部: '总部',
    服务商: '服务商',
    垃圾分类: '垃圾分类',
    中转站: '中转站',
    中转站管理: '中转站管理'
  },
  OrderFrom: {
    小程序: '小程序',
    美团: '美团',
    电话下单: '电话下单',
    二维码跳转下单: '二维码跳转下单',
    定时定点: '定时定点'
  },
  DiscoveryType: {
    WELFARE: 'welfare',
    ACTIVITIES: 'activities',
    MEDIA: 'media'
  },
  ///////for check///////
  CheckAddressType: ['单位', '小区', '农村', '沿街商铺', '道路与废物箱'],
  ScoreReason: {
    // 获取
    GainByOrderCancel: '订单取消返还',

    AddByAdmin: '管理员操作',
    SendScore: '积分赠送',

    // 消费
    ConsumeByOrder: '订单消费'
  }
}

// 将空值用键名填充
function fillValue(dict) {
  for (var key in dict) {
    if (typeof dict[key] === 'object' && dict[key]) {
      fillValue(dict[key])
    } else if (null === dict[key]) {
      dict[key] = key
    }
  }
}
fillValue(E)

module.exports = E
