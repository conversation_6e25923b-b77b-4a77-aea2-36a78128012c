
import Taro from '@tarojs/taro'
import React, { Component } from 'react'
import T from "../../config/T"
import { connect } from 'react-redux'
import { View, Image, Button, Text, Checkbox, Icon } from '@tarojs/components'
import { AtModal } from 'taro-ui'
import './appliances.less'
import E from '../../config/E'
import dayjs from 'dayjs'
import icon from "../../assets/icon/index.js"


class Appliances extends Component {
  constructor() {
    super(...arguments)
    this.state = {
      selectTwoLevel: 0,
      showRemind: false,
      locale: 'zh_CN',
      RecyclingInstructions: [
        {
          title: T.usedClothesModal.clothesTitle,
          content: [
            T.usedClothesModal.clothesContent_1,
            T.usedClothesModal.clothesContent_2,
            T.usedClothesModal.clothesContent_3,
            T.usedClothesModal.clothesContent_4,
            T.usedClothesModal.clothesContent_5,
          ]
        },
        {
          title: T.usedClothesModal.booksTitle,
          content: [
            T.usedClothesModal.booksContent_1,
            T.usedClothesModal.booksContent_2,
            T.usedClothesModal.booksContent_3,
            T.usedClothesModal.booksContent_4,
            T.usedClothesModal.booksContent_5,
            T.usedClothesModal.booksContent_6,
          ]
        },
        {
          title: T.usedClothesModal.otherTitle,
          content: [
            T.usedClothesModal.otherContent_1,
            T.usedClothesModal.otherContent_2,
            T.usedClothesModal.otherContent_3,
            T.usedClothesModal.otherContent_4,
            T.usedClothesModal.otherContent_5,
          ]
        }
      ],
    }
  }

  config = {}

  //-----------------------生命周期-------------------------//
  componentWillMount() {
    //Taro.setNavigationBarTitle({title:'订单管理'})
    let id = Taro.getCurrentInstance().router.params.id
    if (id === '3') {
      this.props.dispatch({
        type: 'NOldGoods/EGetEstimate',
        payload: {
          level: 1,
        }
      })
    } else {
      this.props.dispatch({
        type: 'NOldGoods/EGoodsGetOneLevel',
        payload: {
          id
        }
      })
    }
  }

  componentDidMount() {
    let id = Taro.getCurrentInstance().router.params.id
    this.props.dispatch({
      type: 'NOldGoods/EWhichType',
      payload: {
        id
      }
    })
    const locale = Taro.getStorageSync('locale')
    this.setState({
      locale: locale
    })
    Taro.setNavigationBarTitle({ title: locale == 'en' ? 'Recycling' : '环保回收' })
  }

  componentWillUnmount() {
    this.props.dispatch({
      type: 'NOldGoods/EFormatData'
    })
  }

  componentDidShow() { }

  componentDidHide() { }

  //-----------------------事件-------------------------//
  selectTwo(index, value) {
    let id = Taro.getCurrentInstance().router.params.id
    this.setState({
      selectTwoLevel: index
    })
    if (id !== '3') {
      this.props.dispatch({
        type: 'NOldGoods/EGoodsGetTwoLevel',
        payload: {
          id: value.id
        }
      })
    } else {
      this.props.dispatch({
        type: 'NOldGoods/EGetEstimate',
        payload: {
          level: 2,
          type: value.name
        }
      })
    }
  }

  getAttributeType(index, value) {
    let id = Taro.getCurrentInstance().router.params.id
    let { selectTwoLevel } = this.state
    let { twoLevel } = this.props
    let twoKind = twoLevel[selectTwoLevel]
    twoKind.item = []
    twoKind.item.push(value)
    if (id === '5') {
      this.props.dispatch({
        type: 'NOldGoods/ESetState',
        payload: {
          haveChosen: [twoKind]
        }
      })
      Taro.navigateTo({ url: '/pages/uploadImg/index' })
    } else if (id === '3') {
      this.props.dispatch({
        type: 'NOldGoods/EGetEstimate',
        payload: {
          level: 3,
          type: value.name
        }
      })
      this.props.dispatch({
        type: 'NOldGoods/ESelectWhichOne',
        payload: {
          value: twoKind
        }
      })
    } else {
      this.props.dispatch({
        type: 'NOldGoods/EGoodsGetAttributeType',
        payload: {
          id: value.id
        }
      })
      this.props.dispatch({
        type: 'NOldGoods/ESelectWhichOne',
        payload: {
          value: twoKind
        }
      })
    }
  }

  //-----------------------渲染-------------------------//
  render() {
    let { selectTwoLevel, showRemind, locale, RecyclingInstructions } = this.state
    let { twoLevel, threeLevel } = this.props
    return (
      <View className="appliances">
        <View className="left_wrapper">
          {twoLevel && twoLevel.length > 0
            ? twoLevel.map((value, index) => (
              <View
                className={`value_item ${index === selectTwoLevel ? 'value_selected' : ''}`}
                onClick={() => {
                  this.selectTwo(index, value)
                }}
                key={value + index}>
                {locale == 'en' ? value.enName : value.name}
                {index === selectTwoLevel ? <View className="mark"></View> : null}
              </View>
            ))
            : null}
        </View>
        <View className="right_wrapper">
          <View
            className="top_title"
            onClick={() => {
              this.setState({ showRemind: true })
            }}>
            <Image style={{ width: "25vw" }} src={'https://oss.evergreenrecycle.cn/donggua/client/images/shuoming.png'} mode="widthFix" />
          </View>
          <View className="item_wrapper">
            {threeLevel && threeLevel.length > 0
              ? threeLevel.map((value, index) => (
                <View
                  className="item"
                  key={value + index}
                  onClick={() => {
                    this.getAttributeType(index, value)
                  }}>
                  <View className="image_wrapper">
                    <Image src={value.img} mode={'aspectFit'} />
                  </View>
                  <Text style={{ fontSize: '4vw' }}>{locale == 'en' ? value.enName : value.name}</Text>
                </View>
              ))
              : null}
          </View>
        </View>
        {showRemind ? (
          <View className="remind_wrapper">
            <View className="message_wrapper">
              <Text className="remindTitle">
                {Taro.getCurrentInstance().router.params.id === '3' || Taro.getCurrentInstance().router.params.id === '5' ?
                  T.usedClothesModal.appliancesTitle :
                  T.usedClothesModal.furnitureTitle
                }
              </Text>
              <View className="content_wrapper">
                {RecyclingInstructions[2].content.map((remind, mark) => (
                  <Text className="remind_text" key={remind + mark}>
                    {remind}
                  </Text>
                ))}
              </View>
              <View className="know_button_wrapper">
                <View
                  className="know_button"
                  onClick={() => {
                    // this.closeRmind()
                    this.setState({ showRemind: false })
                  }}>
                  {T.usedClothesModal.ok}
                </View>
              </View>
            </View>
          </View>
        ) : null}
      </View>
    )
  }
}
export default connect(({ NOldGoods: { twoLevel, threeLevel } }) => ({
  twoLevel,
  threeLevel
}))(Appliances)
