'use strict'

const _ = require('lodash')
const moment = require('moment')
const { PageForm, PageOrder, PageNews } = require('../../../Models')

class PageController {
  async NewsDetail({ request, response, params }) {
    let { id } = params
    let news = await PageNews.find(id)
    if (!news) {
      return response.json({ message: '新闻不存在' }, 404)
    }
    return response.json(news)
  }
  async NewsList({ request, response }) {
    let { page = 1, category, limit = 10 } = request.all()
    let list = PageNews.query()
    if (category == 'all') {
      list = list.orderBy('createdAt', 'desc')
    } else {
      list = list.where('category', category)
    }
    list = await list.paginate(page, limit)
    return response.json(list)
  }
  async NewsCreate({ request, response }) {
    let { title, content, category } = request.all()
    let news = await PageNews.create({ title, content, category })
    return response.json(news)
  }
  async NewsUpdate({ request, response }) {
    let { id, title, content, category } = request.all()
    let news = await PageNews.find(id)
    news.title = title
    news.content = content
    news.category = category
    await news.save()
    return response.json(news)
  }
  async NewsDelete({ request, response }) {
    let { id } = request.all()
    let news = await PageNews.find(id)
    await news.delete()
    return response.json({ message: '删除成功' })
  }
  async FormList({ request, response }) {
    let { page, category } = request.all()
    let list = await PageForm.query().where('category', category).paginate(page, 10)
    return response.json(list)
  }
  async FormCreate({ request, response }) {
    let { email, message, name, phone, subject } = request.all()
    let form = await PageForm.create({ email, message, name, phone, subject })
    return response.json(form)
  }
  async FormUpdate({ request, response }) {
    let { id, email, message, name, phone, subject } = request.all()
    let form = await PageForm.find(id)
    form = _.merge(form, request.all())
    await form.save()
    return response.json(form)
  }
  async FormDelete({ request, response }) {
    let { id } = request.all()
    let form = await PageForm.find(id)
    await form.delete()
    return response.json({ message: '删除成功' })
  }
  async OrderList({ request, response }) {
    let { page, category } = request.all()
    let list = await PageOrder.query().where('category', category).paginate(page, 10)
    return response.json(list)
  }
  async OrderCreate({ request, response }) {
    let { category, address, phone, name, notes, purchaseTime, type, condition, status = '预订' } = request.all()
    let order = await PageOrder.create({ category, address, phone, name, notes, purchaseTime, type, condition, status })
    return response.json(order)
  }
  async OrderUpdate({ request, response }) {
    let { id, category, address, phone, name, notes, purchaseTime, type, condition, status = '预订' } = request.all()
    let order = await PageOrder.find(id)
    order = _.merge(order, request.all())
    await order.save()
    return response.json(order)
  }
  async OrderDelete({ request, response }) {
    let { id } = request.all()
    let order = await PageOrder.find(id)
    await order.delete()
    return response.json({ message: '删除成功' })
  }
}

module.exports = PageController
