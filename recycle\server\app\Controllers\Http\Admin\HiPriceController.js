'use strict'

const _ = require('lodash')
const moment = require('moment')
const { HiPrice, HiPriceLog } = require('../../../Models')
const { ERR } = require('../../../../../../constants')
const Excel = require('exceljs')

/**
 * 嗨回收价格配置控制器
 * 管理嗨回收平台的价格设置
 */
class HiPriceController {
  async index({ request, response }) {
    let { type, current = 1, pageSize = 10, lowPrice, hightPrice, price, city, sort = 'desc', source, province, level } = request.all()
    let query = HiPrice.query()
    if (type) {
      query.where('type', type)
    }
    if (level) {
      if (level === 'null') {
        query.whereNull('level')
      } else {
        query.where('level', level)
      }
    }
    if (province) {
      query.where('province', province)
    }
    if (lowPrice && hightPrice) {
      query.whereBetween('price', [lowPrice, hightPrice])
    }
    if (price === 'descend') {
      query.orderBy('price', 'desc')
    } else if (price === 'ascend') {
      query.orderBy('price', 'asc')
    } else {
      query.orderBy('id', sort)
    }
    if (city) {
      query.where('city', city)
    }
    if (source) {
      query.whereRaw('source like ?', [`%${source}%`])
    }
    let vo = await query.paginate(current, pageSize)
    response.json(vo)
  }
  async update({ request, params, response }) {
    let { adminUser } = request
    let vo = await HiPrice.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }

    // 保存修改前的数据
    const beforeData = {
      area: vo.area,
      type: vo.type,
      source: vo.source,
      province: vo.province,
      city: vo.city,
      level: vo.level,
      price: vo.price
    }

    // 更新数据
    _.assign(vo, request.all())
    await vo.save()

    // 保存修改后的数据
    const afterData = {
      type: vo.type,
      source: vo.source,
      province: vo.province,
      city: vo.city,
      area: vo.area,
      level: vo.level,
      price: vo.price
    }

    // 生成变更描述
    const changes = []
    if (beforeData.type !== afterData.type) {
      changes.push(`品类: ${beforeData.type} → ${afterData.type}`)
    }
    if (beforeData.source !== afterData.source) {
      changes.push(`渠道: ${beforeData.source} → ${afterData.source}`)
    }
    if (beforeData.province !== afterData.province) {
      changes.push(`省份: ${beforeData.province} → ${afterData.province}`)
    }
    if (beforeData.city !== afterData.city) {
      changes.push(`城市: ${beforeData.city} → ${afterData.city}`)
    }
    if (beforeData.area !== afterData.area) {
      changes.push(`区域: ${beforeData.area} → ${afterData.area}`)
    }
    if (beforeData.level !== afterData.level) {
      changes.push(`等级: ${beforeData.level} → ${afterData.level}`)
    }
    if (beforeData.price !== afterData.price) {
      changes.push(`价格: ${beforeData.price} → ${afterData.price}`)
    }

    const remark = changes.length > 0 ? changes.join(', ') : '无变更内容'

    // 记录修改日志
    await HiPriceLog.create({
      priceSetID: vo.id,
      adminID: adminUser.id,
      action: 'update',
      before_data: JSON.stringify(beforeData),
      after_data: JSON.stringify(afterData),
      remark: remark,
    })

    response.json(vo)
  }
  async store({ request, response }) {
    let { adminUser } = request
    let { type, province, city, area, level, price,source } = request.all()
    let vo = await HiPrice.create({ type, province, city, area, level, price,source })

    // 记录创建日志
    const createdData = {
      type: vo.type,
      source: vo.source,
      province: vo.province,
      city: vo.city,
      area: vo.area,
      level: vo.level,
      price: vo.price
    }

    await HiPriceLog.create({
      priceSetID: vo.id,
      adminID: adminUser.id,
      action: 'create',
      before_data: null,
      after_data: JSON.stringify(createdData),
      remark: `创建价格配置: ${type || ''} - ${province || ''} - ${city || ''} - ${price || 0}`,
    })

    response.json(vo)
  }

  async exportTable({ request, response }) {
    let { type, lowPrice, hightPrice, price, city, sort = 'desc', source, province } = request.all()
    let query = HiPrice.query()

    if (type) {
      query.where('type', type)
    }
    if (province) {
      query.where('province', province)
    }
    if (lowPrice && hightPrice) {
      query.whereBetween('price', [lowPrice, hightPrice])
    }
    if (price === 'descend') {
      query.orderBy('price', 'desc')
    } else if (price === 'ascend') {
      query.orderBy('price', 'asc')
    } else {
      query.orderBy('id', sort)
    }
    if (city) {
      query.where('city', city)
    }
    if (source) {
      query.whereRaw('source like ?', [`%${source}%`])
    }

    const vo = await query.fetch()
    let data = vo.toJSON()

    // 创建Excel工作簿和工作表
    let workbook = new Excel.Workbook()
    let worksheet = workbook.addWorksheet('Sheet 1')

    // 设置列头和列宽
    let font = { name: 'Times New Roman', size: 12 }
    worksheet.columns = [
      { header: '品类', key: 'type', width: 15, style: { font } },
      { header: '渠道', key: 'source', width: 20, style: { font } },
      { header: '省份', key: 'province', width: 15, style: { font } },
      { header: '城市', key: 'city', width: 15, style: { font } },
      { header: '区', key: 'area', width: 15, style: { font } },
      { header: '城市等级', key: 'level', width: 12, style: { font } },
      { header: '信息费', key: 'price', width: 12, style: { font } }  
    ]

    // 添加数据行
    let rowDownload = data.map(async (item) => {
      worksheet.addRow({
        type: item.type,
        source: item.source,
        province: item.province,
        city: item.city,
        area: item.area,
        level: item.level,
        price: item.price
      })
    })

    // 生成文件名
    const fileName = `嗨回收价格信息表${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`
    rowDownload = await Promise.all(rowDownload)
    // 写入文件并返回
    await workbook.xlsx.writeFile(`./${fileName}`)
    return response.attachment(`./${fileName}`)
  }

  // 删除价格配置
  async destroy({ request, params, response }) {
    let { adminUser } = request
    let vo = await HiPrice.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_DELETE_ID
    }

    // 保存删除前的数据
    const deletedData = {
      type: vo.type,
      source: vo.source,
      province: vo.province,
      city: vo.city,
      area: vo.area,
      level: vo.level,
      price: vo.price
    }

    // 记录删除日志
    await HiPriceLog.create({
      priceSetID: vo.id,
      adminID: adminUser.id,
      action: 'delete',
      before_data: JSON.stringify(deletedData),
      after_data: null,
      remark: `删除价格配置: ${vo.type || ''} - ${vo.province || ''} - ${vo.city || ''} - ${vo.price || 0}`,
    })

    await vo.delete()
    response.json({ success: true, message: '删除成功' })
  }

  // 查询修改记录
  async getLogs({ request, response, params }) {
    const { id } = params;
    const { current = 1, pageSize = 20 } = request.all();

    try {
      // 验证价格配置是否存在
      const priceSet = await HiPrice.find(id);
      if (!priceSet) {
        return response.status(404).json({ error: '未找到价格配置' });
      }

      // 查询修改记录
      const query = HiPriceLog.query()
        .where('priceSetID', id)
        .with('admin', (builder) => {
          builder.select('id', 'username', 'name', 'level')
        })
        .orderBy('createdAt', 'desc');

      const logs = await query.paginate(current, pageSize);

      return response.json(logs);
    } catch (error) {
      console.error('获取修改记录失败:', error);
      return response.status(500).json({ error: '获取修改记录失败' });
    }
  }
}

module.exports = HiPriceController 