import Taro from '@tarojs/taro'
import React, { useEffect, useState } from 'react'
import T from '../../config/T'
import { View, Text, Input } from '@tarojs/components'
import './proxySale.less'
import { useDispatch, useSelector } from 'react-redux'
import { NUser } from '../../config/constants'
const { EPostProxy, EGetProxy } = NUser

definePageConfig({
  navigationBarTitleText: '代理登记',
})
const Index = (props) => {
  const dispatch = useDispatch()
  const { proxyer } = useSelector(state => state.NUser)
  const [username, setUsername] = useState('')
  const [phone, setPhone] = useState(null)

  //-----------------------事件-------------------------//
  const onTap = () => { }

  const saveButton = () => {
    if (phone?.length === 11 && username) {
      dispatch.NUser[NUser.EPostProxy]({
        username, phone
      })
      Taro.showToast({
        title: '提交成功！',
        duration: 5000
      })
    } else {
      Taro.showToast({
        icon: 'error',
        title: '手机号不对！',
      })
    }
  }
  const getPhoneNumber = (e) => {
    setPhone(e.target.value)
  }
  const getUsername = (e) => {
    setUsername(e.target.value)
  }

  useEffect(() => {
    dispatch.NUser[NUser.EGetProxy]({})
  }, [])

  useEffect(() => {
    if (proxyer) {
      setPhone(proxyer?.phone)
      setUsername(proxyer?.username)
    }
  }, [proxyer])
  return (
    <View className="address">
      <View className="address_list" style={{ marginBottom: 20 }}>
        <Text>此页面为代理登记填报</Text>
        <Text style={{ color: 'green' }}>
          {proxyer ? '您已登记！' : ''}
        </Text>
      </View>

      <View className="address_list">
        <Input
          type="text"
          placeholder={T.addressPage.name}
          maxLength="10"
          placeholderStyle="font-size: 14px;color: #cccccc;"
          value={username}
          className="address_list_des"
          onInput={e => {
            getUsername(e)
          }}
        />
      </View>
      {/* 电话 */}
      <View className="address_list" style={{ position: 'relative' }}>
        <Input
          type="number"
          placeholder={T.addressPage.phone}
          maxLength="11"
          value={phone}
          className="address_list_des"
          placeholderStyle="font-size: 14px;color: #cccccc;"
          onInput={e => {
            getPhoneNumber(e)
          }}
        />
      </View>
      <View
        className={`address_save save_change`}
        onClick={() => {
          saveButton()
        }}
      >
        <View className="text_wrapper">
          <Text>{'提交'}</Text>
        </View>
        <View className="add_item"></View>
      </View>
    </View>
  )
}
export default Index
