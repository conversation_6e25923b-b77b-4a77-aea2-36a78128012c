=== 前端 react 框架 ===

---技术栈：react + dva17 + router---
src 目录结构:
|-- assets //静态资源
| |-- images //图片资源，使用 index 引用
|-- config //配置目录
| |-- Config //包含 SERVER_HOME 和 IS_DEBUG
| |-- Constants //包含 dva17 规则的 models 常量命名，N*,R*,E\*
| |-- Enum //常量配置
|-- models //models 目录
| |-- index //引用全部 models
| |-- User //基于 dva17 规范的 model，包含 namespace,state,reducer,effect
|-- pages //页面目录
| |-- Home //页面，包含 useConnect 和 reducer 使用
| |-- Login //页面，包含 effect 和 useLoading 使用
| |-- App //主页面，包含初始化 dva 的 initModels 和 initRequest，页面路由建立

---开发用脚本---
!!! 使用前，请在 package.json 中用项目命名+功能+版本（如 hx1/admin/v1)替换「YourProjectName」
npm run dev //开发调试
npm run build //构建项目，输出到 dist
npm run upload //上传 o2,测试发布
