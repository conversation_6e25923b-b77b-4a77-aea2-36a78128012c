.login-container {
  display: flex;
  min-height: 100vh;
  background: url('https://oss.evergreenrecycle.cn/yshs/login-bg.jpg') no-repeat center center;
  background-size: cover;
  position: relative;
  padding: 24px;

  .login-card {
    position: relative;
    display: flex;
    margin: auto;
    width: 1000px;
    min-height: 600px;
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .login-banner {
      flex: 1;
      background: url('https://oss.evergreenrecycle.cn/yshs/login-bg.jpg') no-repeat center center;
      background-size: cover;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;

      .banner-text {
        position: relative;
        z-index: 1;
        color: #fff;
        text-align: center;
        padding: 0 40px;

        h2 {
          font-size: 36px;
          margin-bottom: 20px;
          color: #fff;
        }

        p {
          font-size: 16px;
          opacity: 0.8;
        }
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(24, 144, 255, 0.9), rgba(24, 144, 255, 0.7));
      }
    }

    .login-form-container {
      width: 480px;
      padding: 60px 40px;
      background: #fff;
      display: flex;
      flex-direction: column;

      .ant-pro-form-login-logo {
        width: 80px;
        height: 80px;
        margin: 0 auto 24px;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .ant-pro-form-login-title {
        color: rgba(0, 0, 0, 0.85);
        font-size: 28px;
        text-align: center;
        margin-bottom: 40px;
        font-weight: 600;
      }

      .ant-pro-form-login-subtitle {
        display: none;
      }

      .ant-input-affix-wrapper {
        height: 50px;
        border-radius: 25px;
        border: 1px solid #e8e8e8;
        padding: 0 20px;
        margin-bottom: 16px;
        transition: all 0.3s;

        input {
          font-size: 16px;
        }

        .anticon {
          color: #bfbfbf;
        }

        &:hover, &:focus {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }
      }

      .ant-btn-primary {
        height: 50px;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 500;
        background: #1890ff;
        margin-top: 24px;
        transition: all 0.3s;

        &:hover {
          background: #40a9ff;
          box-shadow: 0 8px 16px rgba(24, 144, 255, 0.3);
        }
      }
    }
  }
} 