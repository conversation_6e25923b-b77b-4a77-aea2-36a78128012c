'use strict'

const _ = require('lodash')
const moment = require('moment')

const { AliYunService } = require('../../../Services')
const { Order, OrderCancel, WorkerPay, ReqLog, OrderLog, JDWorkerWalletLog, ClientOrder, HiWorkerWalletLog, HiOrder } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { Configs } = require('../../../Util')
const { callBack } = require('../../../Util/DebugUtil')
const Database = use('Database')

//订单废品关联表
class DebugController {
  async clearOrder({ require, response }) {
    console.log('111111')
    let vo = await Order.query().whereNull('from').fetch()
    return vo
  }

  async getCallBack({ request, response }) {
    return 1111111
  }

  async store({ request, response }) {
    console.log('11111111111---debug')
    let query = Order.query().where('id', 907)
    query = await query.fetch()
    _.forEach(query.rows, async function (value, index) {
      let startTime =
        moment(value.workTime).format('HH:mm') === '09:00'
          ? moment(value.workTime).format('YYYY-MM-DD HH:mm:ss')
          : moment(value.workTime).format('YYYY-MM-DD') + ' ' + '12:00:00'
      let endTime =
        moment(value.workTime).format('HH:mm') === '09:00'
          ? moment(value.workTime).format('YYYY-MM-DD') + ' ' + '12:00:00'
          : moment(value.workTime).format('YYYY-MM-DD') + ' ' + '18:00:00'
      console.log('value', startTime, endTime, value.id)
    })
  }

  //SMS_183150423 通用回收下单后通知模板
  //SMS_183145510 大家具服务回收
  //SMS_183150390 大家电服务短信
  async smsTest({ request, response }) {
    // response.json('sms1')
    let { mobile, smsCode, smsParam } = request.all()
    if (!mobile || !smsCode) {
      throw ERR.API_ERROR
    }
    if (!smsParam) {
      smsParam = ''
    }


    let smsRes = await AliYunService.sendSMS(mobile, smsCode, smsParam)
    response.json(smsRes)
  }
  async getData({ request, response }) {
    let totalCount = await Order.query().getCount()
    let todayCount = await Order.query()
      .whereBetween('createdAt', [moment().format('YYYY-MM-DD'), moment().add(1, 'days').format('YYYY-MM-DD')])
      .getCount()
    let PendingCount = await Order.query().where('status', E.OrderStatus.Reservation).whereNull('workerID').getCount('id')
    let InProgressCount = await Order.query().where('status', E.OrderStatus.InProgress).getCount('id')
    let CompletedCount = await Order.query()
      .whereBetween('finishedAt', [moment().format('YYYY-MM-DD'), moment().add(1, 'days').format('YYYY-MM-DD')])
      .where('status', E.OrderStatus.Completed).getCount('id')
    let yedCompletedCount = await Order.query()
      .whereBetween('finishedAt', [moment().subtract(1, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')])
      .where('status', E.OrderStatus.Completed).getCount('id')
    let CancelledCount = await OrderCancel.query()
      .whereBetween('createdAt', [moment().format('YYYY-MM-DD'), moment().add(1, 'days').format('YYYY-MM-DD')])
      .getCount()
    let yedCancelledCount = await OrderCancel.query()
      .whereBetween('createdAt', [moment().subtract(1, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')])
      .getCount()

    let todayCharge = await WorkerPay.query().where('status', "完成")
      .whereBetween('finishAt', [moment().format('YYYY-MM-DD'), moment().add(1, 'days').format('YYYY-MM-DD')])
      .getSum('totalPay')
    let weekCharge = await WorkerPay.query().where('status', "完成")
      .whereBetween('finishAt', [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().add(1, 'days').format('YYYY-MM-DD')])
      .getSum('totalPay')
    let monthCount = await Order.query()
      .whereBetween('createdAt', [moment().subtract(30, 'days').format('YYYY-MM-DD'), moment().add(1, 'days').format('YYYY-MM-DD')])
      .getCount()
    let monthcancelCount = await Order.query()
      .where('status', E.OrderStatus.Cancelled)
      .whereBetween('createdAt', [moment().subtract(30, 'days').format('YYYY-MM-DD'), moment().add(1, 'days').format('YYYY-MM-DD')])
      .getCount()
    let monthcompletedCount = await Order.query()
      .where('status', E.OrderStatus.Completed)
      .whereBetween('createdAt', [moment().subtract(30, 'days').format('YYYY-MM-DD'), moment().add(1, 'days').format('YYYY-MM-DD')])
      .getCount()
    let mcancel = Math.round(monthcancelCount * 10000 / monthCount) / 100
    let mcomplete = Math.round(monthcompletedCount * 10000 / monthCount) / 100
    let channelAmount = await Order.query().where('status', E.OrderStatus.Completed).whereBetween('finishedAt', [moment().format('YYYY-MM-DD 00:00:00'), moment().add(1, 'days').format('YYYY-MM-DD 23:59:59')]).getSum('infoFee')
    let monthChannelAmount = await Order.query().where('status', E.OrderStatus.Completed).whereBetween('finishedAt', [moment().startOf('month').format('YYYY-MM-DD 00:00:00'), moment().add(1, 'days').format('YYYY-MM-DD 23:59:59')]).getSum('infoFee')
    let JDchannelAmount = await JDWorkerWalletLog.query().whereBetween('createdAt', [moment().format('YYYY-MM-DD 00:00:00'), moment().add(1, 'days').format('YYYY-MM-DD 23:59:59')]).getSum('money')
    let JDmonthChannelAmount = await JDWorkerWalletLog.query().whereBetween('createdAt', [moment().startOf('month').format('YYYY-MM-DD 00:00:00'), moment().add(1, 'days').format('YYYY-MM-DD 23:59:59')]).getSum('money')
    let ZYchannelAmount = await ClientOrder.query().where('status', E.OrderStatus.Completed).whereBetween('finishedAt', [moment().format('YYYY-MM-DD 00:00:00'),
    moment().add(1, 'days').format('YYYY-MM-DD 23:59:59')]).getSum('infoFee')
    let ZYmonthChannelAmount = await ClientOrder.query().where('status', E.OrderStatus.Completed).whereBetween('finishedAt',
      [moment().startOf('month').format('YYYY-MM-DD 00:00:00'), moment().add(1, 'days').format('YYYY-MM-DD 23:59:59')]).getSum('infoFee')
    let JDmonthOrderCount = await JDWorkerWalletLog.query().whereBetween('createdAt', [moment().startOf('month').format('YYYY-MM-DD 00:00:00'), moment().add(1, 'days').format('YYYY-MM-DD 23:59:59')]).getCount()
    let ZYtodayOrderCount = await ClientOrder.query().where('status', E.OrderStatus.Completed).whereBetween('finishedAt', [moment().format('YYYY-MM-DD 00:00:00'), moment().add(1, 'days').format('YYYY-MM-DD 23:59:59')]).getCount()
    let JDtodayOrderCount = await JDWorkerWalletLog.query().whereBetween('createdAt', [moment().format('YYYY-MM-DD 00:00:00'), moment().add(1, 'days').format('YYYY-MM-DD 23:59:59')]).getCount()
    let ZYmonthOrderCount = await ClientOrder.query().where('status', E.OrderStatus.Completed).whereBetween('finishedAt', [moment().startOf('month').format('YYYY-MM-DD 00:00:00'), moment().add(1, 'days').format('YYYY-MM-DD 23:59:59')]).getCount()
    let HiTodayOrderCount = await HiWorkerWalletLog.query().whereBetween('createdAt', [moment().format('YYYY-MM-DD 00:00:00'), moment().add(1, 'days').format('YYYY-MM-DD 23:59:59')]).getCount()
    let HiMonthOrderCount = await HiWorkerWalletLog.query().whereBetween('createdAt', [moment().startOf('month').format('YYYY-MM-DD 00:00:00'), moment().add(1, 'days').format('YYYY-MM-DD 23:59:59')]).getCount()
    let HiChannelAmount = await HiWorkerWalletLog.query().whereBetween('createdAt', [moment().format('YYYY-MM-DD 00:00:00'), moment().add(1, 'days').format('YYYY-MM-DD 23:59:59')]).getSum('money')
    let HiMonthChannelAmount = await HiWorkerWalletLog.query().whereBetween('createdAt', [moment().startOf('month').format('YYYY-MM-DD 00:00:00'), moment().add(1, 'days').format('YYYY-MM-DD 23:59:59')]).getSum('money')
    return {
      totalCount, todayCount, PendingCount, InProgressCount,
      CompletedCount, yedCompletedCount, CancelledCount, yedCancelledCount,
      todayCharge, weekCharge, mcancel, mcomplete, channelAmount, monthChannelAmount, JDmonthOrderCount, ZYmonthChannelAmount,
      JDchannelAmount: - JDchannelAmount, JDmonthChannelAmount: - JDmonthChannelAmount,
      ZYchannelAmount, ZYmonthOrderCount, ZYtodayOrderCount, JDtodayOrderCount,
      HiTodayOrderCount, HiMonthOrderCount, HiChannelAmount: - HiChannelAmount, HiMonthChannelAmount: - HiMonthChannelAmount
    }
  }
  async getCharts({ request, response }) {
    let { workerID, province, startDate = moment().format('YYYY-MM-DD'), endDate = moment().add(1, 'days').format('YYYY-MM-DD'), type, companyID } = request.all()
    let query = Order.query().whereNot('status', E.OrderStatus.Cancelled)
    query.where('createdAt', '>=', moment(startDate).toDate()).where('createdAt', '<=', moment(endDate).add(1, 'd').toDate())
      .select(Database.raw("workerID,companyID,type,COUNT(*) AS count"))
      .groupBy('type')
    if (companyID) {
      query.where('companyID', companyID).with('company')
    }
    if (province) {
      query.where('province', province)
    }
    if (type) {
      query.where('type', type)
    }
    if (workerID) {
      query.where('workerID', workerID)
    }
    let res = await query.fetch()
    response.json(res)
  }
  async getLineCharts({ request, response }) {
    let { workerID, province, startDate = moment().subtract(7, 'days').format('YYYY-MM-DD'), endDate = moment().add(1, 'days').format('YYYY-MM-DD'), type, companyID } = request.all()
    let query = Order.query().whereNot('status', E.OrderStatus.Cancelled)
      .select(Database.raw("workerID,companyID,DATE_FORMAT(createdAt, '%Y-%m-%d') AS date,type,COUNT(*) AS count"))
      .groupBy('type')
      .groupBy('date')
    query.where('createdAt', '>=', moment(startDate).toDate()).where('createdAt', '<=', moment(endDate).add(1, 'd').toDate())
    if (companyID) {
      query.where('companyID', companyID).with('company')
    }
    if (type) {
      query.where('type', type)
    }
    if (province) {
      query.where('province', province)
    }
    if (workerID) {
      query.where('workerID', workerID)
    }
    let res = await query.fetch()
    response.json(res)
  }
  async cancelBundle({ request, response }) {
    let { orderNoList } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台 订单批量取消' })
    await _.forEach(orderNoList, async function (orderNo) {
      let order = await Order.findBy('orderNo', orderNo)
      let cvo = {
        "tracesList": [{
          "logisticProviderID": order.toJSON().cpCode, "txLogisticID": order.toJSON().orderNo, "mailNos": order.toJSON().mailNo,
          "extendFields": "", "traces": [{
            "action": "ORDER_CLOSED", "country": "China", "city": order.toJSON().city, "tz": "+8",
            "remark": "后台批量取消", "province": order.toJSON().province,
            "extendFields": [{ "value": 1, "key": "cnRecycleType", "desc": "回收类型" }],
            "facilityName": Configs.companySelfName, "facilityType": "1",
            "contacter": (order.$relations.worker && order.$relations.worker.workerName) || Configs.companySelfHuman,
            "outBizCode": moment().format('x'),
            "time": moment().format('YYYY-MM-DD HH:mm:ss'),
            "contactPhone": (order.$relations.worker && order.$relations.worker.mobile) || Configs.companySelfPhone,
            "desc": "后台批量取消"
          }]
        }]
      }
      if (order.toJSON().from === "菜鸟回收") {
        await callBack('TRACEPUSH', order.cpCode, cvo)
      }
      order.status = E.OrderStatus.Cancelled
      await order.save()
      await OrderLog.create({
        createrID: 118,
        orderID: order.id, content: "后台批量取消订单",
        status: E.OrderLogStatus.Cancelled
      })
      let cancel = await OrderCancel.create({
        orderID: order.id,
        companyID: order.companyID,
        cancelReason: "后台批量取消",
        whoCancel: E.CancleOrderStatus.Admin,
        workerID: order.workerID
      })
      console.log(order.toJSON().id);
    })
    return ({ code: 200, msg: '批量取消成功' })
  }
  async getWorkerOrderData({ request, response }) {
    let {
      workerID,
      startDate = moment().format('YYYY-MM-DD'),
      endDate = moment().add(1, 'days').format('YYYY-MM-DD'),
      current = 1,
      pageSize = 10
    } = request.all()

    try {
      // 查询指定师傅在时间范围内的订单
      let orderQuery = Order.query()
        .where('workerID', workerID)
        .where('createdAt', '>=', moment(startDate).toDate())
        .where('createdAt', '<=', moment(endDate).add(1, 'd').toDate())

      let orders = await orderQuery.fetch()
      let orderList = orders.toJSON()

      // 统计各项指标
      let ongoingOrders = 0 // 进行中订单
      let totalAccepted = orderList.length // 接单总数
      let totalCanceled = 0 // 取消订单数
      let totalCompleted = 0 // 完成订单数
      let totalCompletionTime = 0 // 总完成时间
      let completedWithTime = 0 // 有完成时间的订单数

      // 设备类型统计
      let deviceTypeStats = {
        '电视': 0,
        '冰箱': 0,
        '洗衣机': 0,
        '空调柜机': 0,
        '空调挂机': 0,
        '燃气热水器': 0,
        '电热水器': 0,
        '空气热泵热水器': 0,
        '其他': 0
      }

      // 遍历订单统计
      orderList.forEach(order => {
        switch (order.status) {
          case E.OrderStatus.InProgress:
          case E.OrderStatus.Reservation:
            ongoingOrders++
            break
          case E.OrderStatus.Completed:
            totalCompleted++
            // 计算完成时间
            if (order.takeTime && order.finishedAt) {
              let completionTime = moment(order.finishedAt).diff(moment(order.takeTime), 'hours', true)
              totalCompletionTime += completionTime
              completedWithTime++
            }
            break
          case E.OrderStatus.Cancelled:
            totalCanceled++
            break
        }

        // 统计设备类型
        if (order.type) {
          if (deviceTypeStats.hasOwnProperty(order.type)) {
            deviceTypeStats[order.type]++
          } else {
            deviceTypeStats['其他']++
          }
        }
      })

      // 查询师傅充值记录
      let currentPeriodRecharge = await WorkerPay.query()
        .where('workerID', workerID)
        .where('status', '完成')
        .where('finishAt', '>=', moment(startDate).toDate())
        .where('finishAt', '<=', moment(endDate).add(1, 'd').toDate())
        .getSum('totalPay') || 0

      let totalRechargeAmount = await WorkerPay.query()
        .where('workerID', workerID)
        .where('status', '完成')
        .getSum('totalPay') || 0

      // 计算平均完工时效
      let avgCompletionTime = completedWithTime > 0 ? totalCompletionTime / completedWithTime : 0

      // 订单详情分页查询
      let detailOrderQuery = Order.query()
        .where('workerID', workerID)
        .where('createdAt', '>=', moment(startDate).toDate())
        .where('createdAt', '<=', moment(endDate).add(1, 'd').toDate())
        .orderBy('createdAt', 'desc')

      // 获取总数
      let totalOrders = await detailOrderQuery.clone().getCount()

      // 分页查询
      let offset = (current - 1) * pageSize
      let paginatedOrders = await detailOrderQuery
        .offset(offset)
        .limit(pageSize)
        .fetch()

      let orderDetails = paginatedOrders.toJSON().map(order => ({
        id: order.id,
        orderNo: order.orderNo,
        status: order.status === E.OrderStatus.InProgress ? '进行中' :
          order.status === E.OrderStatus.Completed ? '已完成' :
            order.status === E.OrderStatus.Cancelled ? '已取消' : order.status,
        takeTime: order.takeTime,
        completeTime: order.finishedAt,
        createdAt: order.createdAt
      }))

      // 分页信息
      let pagination = {
        current: parseInt(current),
        pageSize: parseInt(pageSize),
        total: totalOrders,
        pages: Math.ceil(totalOrders / pageSize)
      }

      let responseData = {
        success: true,
        data: {
          ongoingOrders,
          totalAccepted,
          totalCanceled,
          totalCompleted,
          currentPeriodRecharge: currentPeriodRecharge / 100 || 0,
          totalRechargeAmount: totalRechargeAmount / 100 || 0,
          deviceTypeStats,
          avgCompletionTime: Number(avgCompletionTime.toFixed(2)),
          orderDetails,
          pagination
        }
      }

      response.json(responseData)
    } catch (error) {
      console.error('获取师傅数据失败:', error)
      response.status(500).json({
        success: false,
        message: '获取师傅数据失败',
        error: error.message
      })
    }
  }

  /**
   * 获取嗨回收省份分布数据
   */
  async getHiProvinceCharts({ request, response }) {
    try {
      let { startDate = moment().subtract(30, 'days').format('YYYY-MM-DD'), endDate = moment().add(1, 'days').format('YYYY-MM-DD') } = request.all()
      
      let query = HiOrder.query()
        .where('createdAt', '>=', moment(startDate).toDate())
        .where('createdAt', '<=', moment(endDate).add(1, 'd').toDate())
        .whereNotNull('province')
        .select(Database.raw("province, COUNT(*) AS count"))
        .groupBy('province')
        .orderBy('count', 'desc')

      let res = await query.fetch()
      response.json(res)
    } catch (error) {
      console.error('获取嗨回收省份分布数据失败:', error)
      response.status(500).json({
        success: false,
        message: '获取嗨回收省份分布数据失败',
        error: error.message
      })
    }
  }

  /**
   * 获取嗨回收渠道分布数据
   */
  async getHiChannelCharts({ request, response }) {
    try {
      let { startDate = moment().subtract(30, 'days').format('YYYY-MM-DD'), endDate = moment().add(1, 'days').format('YYYY-MM-DD') } = request.all()
      
      let query = HiOrder.query()
        .where('createdAt', '>=', moment(startDate).toDate())
        .where('createdAt', '<=', moment(endDate).add(1, 'd').toDate())
        .whereNotNull('from')
        .select(Database.raw(" `from` AS channel, COUNT(*) AS count"))
        .groupBy('from')
        .orderBy('count', 'desc')

      let res = await query.fetch()
      response.json(res)
    } catch (error) {
      console.error('获取嗨回收渠道分布数据失败:', error)
      response.status(500).json({
        success: false,
        message: '获取嗨回收渠道分布数据失败',
        error: error.message
      })
    }
  }

  /**
   * 获取嗨回收品类分布数据
   */
  async getHiCategoryCharts({ request, response }) {
    try {
      let { startDate = moment().subtract(30, 'days').format('YYYY-MM-DD'), endDate = moment().add(1, 'days').format('YYYY-MM-DD') } = request.all()
      
      let query = HiOrder.query()
        .where('createdAt', '>=', moment(startDate).toDate())
        .where('createdAt', '<=', moment(endDate).add(1, 'd').toDate())
        .whereNotNull('type')
        .select(Database.raw("type AS category, COUNT(*) AS count"))
        .groupBy('type')
        .orderBy('count', 'desc')

      let res = await query.fetch()
      response.json(res)
    } catch (error) {
      console.error('获取嗨回收品类分布数据失败:', error)
      response.status(500).json({
        success: false,
        message: '获取嗨回收品类分布数据失败',
        error: error.message
      })
    }
  }
}

module.exports = DebugController
