import { requestGet, requestPut, requestPost } from "../utils/request";

export async function postUser(payload) {
  return requestPost("user/login", payload);
}

export async function register(payload) {
  return requestPost('user/register',payload)
}

/*export async function postUser(payload) {
  return requestPost("user", payload);
}*/
export async function putUserPhone(payload) {
  return requestPost("user/getWxMobile", payload);
}

export async function putUserInfo(payload) {
  return requestPut(`user/${payload.id}`, payload);
}
export async function getAddress(payload) {
  return requestGet('address',payload)
}

export async function getUserPhone(payload) {
  return requestPost('user/phone', payload)
}

export async function postEvaluate(payload) {
  return requestPost('evaluate', payload)
}

export async function postProxy(payload) {
  return requestPost('proxyPost', payload)
}

export async function getProxy(payload) {
  return requestGet('proxyIndex', payload)
}
