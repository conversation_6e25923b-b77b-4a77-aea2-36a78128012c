'use strict'

const Model = use('Model')

//全国区域
class SysArea extends Model {
	static get table() { return 'sys_area' }
	static get primaryKey() { return 'id' }
	static get createdAtColumn() { return null }
	static get updatedAtColumn() { return null }
	province() {
		return this.hasOne('App/Models/SysArea', 'provinceCode', 'provinceCode')
			.where('level', '1').select('id', 'provinceCode', 'level', 'name')
	}
	city() {
		return this.hasOne('App/Models/SysArea', 'cityCode', 'cityCode')
			.where('level', '2').select('id', 'cityCode', 'level', 'name')
	}
}

module.exports = SysArea