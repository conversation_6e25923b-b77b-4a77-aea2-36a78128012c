import Taro from '@tarojs/taro'
import React, { Component } from 'react'
import T from '../../config/T'
import { connect } from 'react-redux'
import { View, Image, Button, Text, Input, Picker, <PERSON>er<PERSON>iew, PickerViewColumn, Map } from '@tarojs/components'
import { AtModal, AtTextarea } from 'taro-ui'
import './address.less'

import { NOldGoods, NOrder, NPublics, NSystem, NUser, NUserAddress } from '../../config/constants'

class Address extends Component {
  constructor() {
    super(...arguments)
    this.state = {
      locale: 'zh_CN',
      user: null,
      phone: '',
      username: '',
      array: null,
      cityArray: [],
      citiesIndex: '',
      detailAddress: '',
      gender: 1,
      canSave: false,
      isEdit: false,
      id: '',
      province: '',
      city: '',
      area: '',
      areaCode: '',
      subDistrctCode: null,
      pickerSelect: '',
      latitude: null,
      longitude: null,
      jingWeiDu: null,
    }
  }

  config = {}

  //-----------------------生命周期-------------------------//
  componentWillMount() {
    let value = Taro.getStorageSync('userInfo')
    if (value) {
      this.setState({ user: JSON.parse(value) })
    }
    let edit = Taro.getCurrentInstance().router.params.edit && JSON.parse(Taro.getCurrentInstance().router.params.edit)
    if (edit) {
      this.props.dispatch({
        type: 'NUserAddress.EGetJsonAddress',
        payload: {
          code: edit.districtCode,
        },
      })
      this.setState({
        phone: edit.mobile,
        username: edit.realname,
        gender: edit.gender,
        detailAddress: edit.address,
        isEdit: true,
        province: edit.province,
        city: edit.city,
        area: edit.district,
        areaCode: edit.districtCode,
        subDistrctCode: edit.subDistrctCode,
        street: edit.subDistrct,
        pickerSelect: edit.pickerSelect,
        id: edit.id,
      })
      Taro.setNavigationBarTitle({ title: '编辑地址' })
    } else {
      Taro.setNavigationBarTitle({ title: '新增地址' })
    }
  }

  async componentDidMount() {
    const locale = Taro.getStorageSync('locale')
    this.setState({ locale })
    let { isEdit, areaCode } = this.state
    this.props.dispatch({
      type: 'NUser/EFirstGetAddress',
    })

    this.saveButton()
    this.props.dispatch({
      type: 'NUserAddress.EGetJsonAddress',
      payload: {
        code: isEdit ? areaCode : '110101',
      },
    })
    Taro.setNavigationBarTitle({ title: locale == 'en' ? 'Add Address' : '新增地址' })
    if (locale == 'en') {
      const province = '上海市'
      const city = '上海市'
      const area = '闵行区'
      const areaCode = '310112'
      this.setState({
        province,
        city,
        area,
        areaCode,
      })
      await this.props.dispatch({
        type: 'NUserAddress/EGetJsonAddress',
        payload: {
          code: '310112',
        },
      })
      let list = this.props.streetJsonList
      let index = 7
      this.setState({
        street: list[index].name,
        pickerSelect: index,
      })
      setTimeout(() => {
        this.saveButton()
      })
      setTimeout(() => {
        this.saveButton()
      })
    }
  }

  componentWillReceiveProps(nextProps, nextContext) {
    if (!this.props.isPhoneGet && nextProps.isPhoneGet) {
      this.setState({
        phone: nextProps.userPhoneNumber,
      })
    }
    let { provinceList, cityList, areaList, streetList } = nextProps
    this.setState({
      cityArray: [provinceList, cityList, areaList, streetList],
    })
  }

  componentWillUnmount() { }

  componentDidShow() { }

  componentDidHide() { }

  //-----------------------事件-------------------------//
  onTap() { }

  saveButton() {
    let { username, phone, detailAddress, street, province, city, area, isEdit } = this.state
    if (username && phone && detailAddress && street && province && city && area) {
      this.setState({ canSave: true })
    } else {
      this.setState({ canSave: false })
    }
  }

  getUsername(e) {
    this.setState({
      username: e.target.value,
    })
    setTimeout(() => {
      this.saveButton()
    })
  }

  getPhoneNumber(e) {
    this.setState({ phone: e.target.value })
    setTimeout(() => {
      this.saveButton()
    })
  }

  saveAddress() {
    let {
      isEdit,
      canSave,
      username,
      gender,
      phone,
      detailAddress,
      user,
      id,
      province,
      city,
      area,
      street,
      areaCode,
      pickerSelect,
      subDistrctCode,
    } = this.state
    let content = {
      userID: user.id,
      realname: username,
      gender: gender,
      mobile: phone,
      province: province,
      city: city,
      district: area,
      districtCode: areaCode,
      subDistrctCode: subDistrctCode,
      subDistrct: street,
      address: detailAddress,
      pickerSelect,
    }

    if (canSave) {
      if (isEdit) {
        this.props.dispatch({
          type: 'NUserAddress/EEditAddress',
          payload: {
            ...content,
            id: id,
          },
        })
      } else {
        this.props.dispatch({
          type: 'NUserAddress/ESaveAddress',
          payload: {
            ...content,
            isDefault: 0,
          },
        })
      }
    }
  }

  getCode() {
    Taro.login().then(res => {
      Taro.setStorageSync('current_code', res.code)
    })
  }

  getPhoneNumbers({ detail: { errMsg, iv, encryptedData } }) {
    if (errMsg == 'getPhoneNumber:ok') {
      this.props.dispatch({
        type: 'NUser/EPutUserPhone',
        payload: { iv, encryptedData },
      })
    }
  }

  changeGender(gender) {
    this.setState({ gender })
  }

  getUserAddress() {
    Taro.chooseAddress().then(res => {
      this.setState({
        username: res.userName,
        province: res.provinceName,
        city: res.cityName,
        area: res.countyName,
        detailAddress: res.detailInfo,
        phone: res.telNumber,
      })
      this.props.dispatch({
        type: 'NUserAddress/EGetJsonAddress',
        payload: {
          code: res.nationalCode,
        },
      })
      setTimeout(() => {
        this.saveButton()
      })
    })
  }

  onChange = e => {
    let code = e.target.code
    let array = e.target.value
    this.setState({
      province: array[0],
      city: array[1],
      area: array[2],
      street: null,
      pickerSelect: '',
      areaCode: code[2],
    })
    this.props.dispatch({
      type: 'NUserAddress/EGetJsonAddress',
      payload: {
        code: code[2],
      },
    })
    setTimeout(() => {
      this.saveButton()
    })
  }

  onChangeStreet = e => {
    let list = this.props.streetJsonList
    let index = Number(e.detail.value)
    this.setState({
      street: list[index].name,
      subDistrctCode: list[index].code,
      pickerSelect: index,
    })
    setTimeout(() => {
      this.saveButton()
    })
  }

  //-----------------------渲染-------------------------//
  render() {
    const { chooseAddressShow, streetJsonList, isAddItem } = this.props
    let {
      phone,
      username,
      gender,
      detailAddress,
      canSave,
      isEdit,
      province,
      city,
      area,
      street,
      pickerSelect,
      jingWeiDu,
      locale,
    } = this.state
    return (
      <View className="address">
        <View
          className="wechat_get_address"
          onClick={() => {
            this.getUserAddress()
          }}
        >
          <View className="left_wrapper">
            <Image src={require('../../assets/icon/wechat.png')} />
            <Text>{T.addressPage.addWechatTxt}</Text>
          </View>
          <Image className="right_img" src={require('../../assets/icon/youjiantou.png')} />
        </View>
        {/* 姓名 */}
        <View className="address_list">
          <Input
            type="text"
            placeholder={T.addressPage.name}
            maxLength="10"
            placeholderStyle="font-size: 14px;color: #cccccc;"
            value={username}
            className="address_list_des"
            onInput={e => {
              this.getUsername(e)
            }}
          />
          <View className="gender_wrapper">
            {[
              { name: T.addressPage.mr, gender: 1, url: require('../../assets/icon/male.png') },
              {
                name: T.addressPage.ms,
                gender: 2,
                url: require('../../assets/icon/female.png'),
              },
            ].map((value, index) => (
              <View
                onClick={() => {
                  this.changeGender(value.gender)
                }}
                key={index + value}
                className={`gender_item ${gender === value.gender ? 'this_gender' : null}`}
              >
                {/* <Image src={value.url} className="icon_img"/> */}
                <Text>{value.name}</Text>
              </View>
            ))}
          </View>
        </View>
        {/* 电话 */}
        <View className="address_list" style={{ position: 'relative' }}>
          <Input
            type="number"
            placeholder={T.addressPage.phone}
            maxLength="11"
            value={phone}
            className="address_list_des"
            placeholderStyle="font-size: 14px;color: #cccccc;"
            onInput={e => {
              this.getPhoneNumber(e)
            }}
          />
        </View>
        <Picker mode="region" onChange={this.onChange} value={[province, city, area]} disabled={locale == 'en' ? true : false}>
          <View className="picker">
            <View className="address_list">
              <View className="address_list_icon">
                {
                  <Text style={!province ? { fontSize: '14px', color: '#cccccc' } : null}>
                    {province ? `${province + ' ' + city + ' ' + area}` : T.addressPage.ProvinceCityDistrict}
                  </Text>
                }
              </View>
              <Image src={require('./../../assets/icon/youjiantou.png')} className="address_Arrow_icon" />
            </View>
          </View>
        </Picker>
        <Picker
          mode="selector"
          range={streetJsonList ? streetJsonList : []}
          onChange={this.onChangeStreet}
          rangeKey="name"
          value={[Number(pickerSelect)]}
          disabled={locale == 'en' ? true : false}
        >
          <View className="picker">
            <View className="address_list">
              <View className="address_list_icon">
                <Text style={!street ? { fontSize: '14px', color: '#cccccc' } : null}>{street ? street : T.addressPage.street}</Text>
              </View>
              <Image src={require('./../../assets/icon/youjiantou.png')} className="address_Arrow_icon" />
            </View>
          </View>
        </Picker>
        <View className="address_list_bottom">
          <AtTextarea
            className="detail_input"
            count={false}
            value={detailAddress}
            maxLength={200}
            onChange={value => {
              this.setState({
                detailAddress: value,
              })
              setTimeout(() => {
                this.saveButton()
              })
            }}
            placeholder={T.addressPage.address}
            placeholderStyle="font-size: 14px;color: #cccccc;"
          />
        </View>
        <View
          className={`address_save ${canSave ? 'save_change' : null}`}
          onClick={() => {
            this.saveAddress()
          }}
        >
          <View className="text_wrapper">
            <Text>{T.addressPage.save}</Text>
          </View>
          {isAddItem ? <View className="add_item"></View> : null}
        </View>
      </View>
    )
  }
}

export default connect(
  ({
    NUser: {
      provinceList,
      cityList,
      areaList,
      streetList,
      isUpload,
      chooseAddressShow,
      province,
      city,
      area,
      street,
      userPhoneNumber,
      isPhoneGet,
    },
    NUserAddress: { streetJsonList },
    NSystem: { isAddItem },
  }) => ({
    provinceList,
    cityList,
    areaList,
    streetList,
    isUpload,
    chooseAddressShow,
    province,
    city,
    area,
    street,
    userPhoneNumber,
    isPhoneGet,
    streetJsonList,
    isAddItem,
  })
)(Address)
