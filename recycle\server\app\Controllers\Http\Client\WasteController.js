'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Waste } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//废品
class WasteController {
  async index({ request, response }) {
    let { page = 1, perPage = 10, source = E.OrderSource.CaiNiao, type = E.Permission.Primary } = request.all()
    let query = Waste.query().where('source', source).where('type', type)
    let vo = await query.paginate(page, perPage)
    response.json(vo)
  }
  async show({ request, params, response }) {
    let vo = await Waste.query()
      .where('id', params.id)
      .first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
}

module.exports = WasteController
