---
description: 调试和测试规范
alwaysApply: false
---

# 调试和测试规范

## 调试工具和方法
- 使用 firecrawl-mcp 进行调试
- 自动解决调试过程中遇到的各种问题
- 综合性问题排查和解决方案

## 前端调试

### 小程序调试
```javascript
// Taro 调试方法
import Taro from '@tarojs/taro';

// 开发环境日志
if (process.env.NODE_ENV === 'development') {
  console.log('调试信息:', data);
}

// 微信开发者工具调试
Taro.setEnableDebug({
  enableDebug: true
});

// 网络请求调试
Taro.request({
  url: 'api/test',
  success: (res) => {
    console.log('请求成功:', res);
  },
  fail: (err) => {
    console.error('请求失败:', err);
  }
});
```

### React 管理后台调试
```typescript
// Redux DevTools 配置
const store = configureStore({
  reducer: rootReducer,
  devTools: process.env.NODE_ENV !== 'production'
});

// React Developer Tools
// 组件调试和状态追踪

// 网络请求调试
import { message } from 'antd';

const request = async (url: string, options: any) => {
  try {
    const response = await fetch(url, options);
    const data = await response.json();
    
    if (data.code !== 200) {
      message.error(data.message);
      console.error('API错误:', data);
    }
    
    return data;
  } catch (error) {
    console.error('网络错误:', error);
    message.error('网络请求失败');
  }
};
```

## 后端调试

### Adonis.js 调试
```javascript
// 日志记录
const Logger = use('Logger');

class OrderController {
  async create({ request, response }) {
    try {
      Logger.info('创建订单请求', request.all());
      
      const order = await Order.create(request.all());
      
      Logger.info('订单创建成功', { orderId: order.id });
      
      return response.json({
        code: 200,
        data: order,
        message: '创建成功'
      });
    } catch (error) {
      Logger.error('订单创建失败', error);
      
      return response.status(500).json({
        code: 500,
        message: '服务器错误',
        error: process.env.NODE_ENV === 'development' ? error.message : '内部错误'
      });
    }
  }
}
```

### 数据库调试
```javascript
// SQL 查询调试
const Database = use('Database');

// 启用查询日志
Database.on('query', (query) => {
  console.log('SQL:', query.sql);
  console.log('参数:', query.bindings);
});

// 性能监控
Database.on('slow-query', (query) => {
  console.warn('慢查询:', query);
});
```

## 错误处理和日志

### 前端错误处理
```javascript
// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('全局错误:', event.error);
  // 发送错误日志到服务器
});

// Promise 错误处理
window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的Promise错误:', event.reason);
});

// React 错误边界
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('React错误:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <div>页面出现错误，请刷新重试</div>;
    }
    return this.props.children;
  }
}
```

## 测试策略

### 单元测试
```javascript
// Jest 测试示例
describe('订单服务', () => {
  test('创建订单应该返回订单ID', async () => {
    const orderData = {
      user_id: 1,
      category_id: 1,
      address: '测试地址'
    };
    
    const result = await OrderService.create(orderData);
    
    expect(result.id).toBeDefined();
    expect(result.status).toBe('pending');
  });
});
```

### 接口测试
```javascript
// API 测试
describe('订单API', () => {
  test('GET /api/orders 应该返回订单列表', async () => {
    const response = await request(app)
      .get('/api/orders')
      .expect(200);
      
    expect(response.body.code).toBe(200);
    expect(Array.isArray(response.body.data.list)).toBe(true);
  });
});
```

## 性能调试

### 前端性能
```javascript
// 性能监控
console.time('页面加载时间');
// 页面逻辑
console.timeEnd('页面加载时间');

// React 性能分析
import { Profiler } from 'react';

function onRenderCallback(id, phase, actualDuration) {
  console.log('组件渲染时间:', id, actualDuration);
}

<Profiler id="OrderList" onRender={onRenderCallback}>
  <OrderList />
</Profiler>
```

### 后端性能
```javascript
// 请求响应时间监控
class PerformanceMiddleware {
  async handle({ request, response }, next) {
    const start = Date.now();
    
    await next();
    
    const duration = Date.now() - start;
    console.log(`${request.method} ${request.url()} - ${duration}ms`);
  }
}
```

## 常见问题解决

### 网络问题
- 检查 API 地址配置
- 验证请求头和参数
- 确认服务器状态和网络连通性

### 数据问题
- 验证数据模型和字段类型
- 检查数据库连接和权限
- 确认数据迁移是否完成

### 权限问题
- 检查 JWT Token 是否有效
- 验证用户角色和权限
- 确认路由守卫配置

### 小程序问题
- 检查微信开发者工具配置
- 验证小程序配置文件
- 确认 API 域名白名单设置

## 调试最佳实践
- 使用有意义的日志信息
- 设置合适的日志级别
- 及时清理调试代码
- 建立错误追踪机制
- 定期检查和优化性能