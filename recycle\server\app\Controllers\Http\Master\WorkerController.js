'use strict'

const _ = require('lodash')
const moment = require('moment')
const Env = use('Env')

const { Worker, MessageSub, WorkerPoint, WorkerInsure, Salesman } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { WXService } = require('../../../Services')
const { CryptUtil, Configs } = require('../../../Util')

//上门师傅信息
class WorkerController {
  async index({ request, response }) {
    let { page = 1, perPage = 10, name, isUse = E.WorkerAccountStatus.Agree } = request.all()
    // let worker = request.worker
    let query = Worker.query().with('company', builder => {
      builder.select('id', 'companyName', 'city')
      return builder
    })
    // if (!worker.companyID) {
    //   throw ERR.USER_EXISTS
    // }
    // if (worker.type !== "超级管理员") {
    //   query.where('companyID', worker.companyID)
    // }
    if (name) {
      query.select('id', 'type', 'mobile', 'workerName', 'companyID', 'wallet', 'city').where('workerName', 'like', `%${name}%`)
    }
    let vo = await query.where('isUse', E.WorkerAccountStatus.Agree).fetch()
    response.json(vo)
  }
  async login({ request, response }) {
    let { openID, code, appID, token } = request.all()
    let vo = null
    if (code) {
      let { result, error } = await WXService.code2Session(code, Configs.WEAPP.AppID, Configs.WEAPP.AppSecret)
      if (error) {
        throw error
      } else {
        openID = result.openid
        vo = await Worker.findBy('openid', result.openid)
      }
    } else if (openID) {
      vo = await Worker.findBy('openid', openID)
    } else if (token) {
      const payload = CryptUtil.jwtDecode(request.header('Authorization') || request.input('token'))
      // console.log(payload);
      vo = await Worker.find(payload.userID)
    }
    if (!vo) {
      response.json({
        token: 0,
        openID
      })
    } else {
      response.json({
        token: CryptUtil.jwtEncode({ userID: vo.id }),
        openID,
        user: vo
      })
    }
  }
  async register({ request, response }) {
    try {
      let { openid } = request.all()
      if (!openid) {
        throw ERR.INVALID_PARAMS
      }
      let isReg = await Worker.findBy('openid', openid)
      if (isReg) {
        throw ERR.USER_EXISTS
      }

      let vo = await Worker.create({ openid })
      await Salesman.create({ workerID: vo.id, status: 1, title: '1' })
      response.json({
        token: CryptUtil.jwtEncode({ userID: vo.id }),
        user: vo
      })
      await WorkerPoint.create({ workerID: vo.id })
    } catch (e) {
      response.json(e)
    }
  }
  async show({ request, params, response }) {
    let vo = await Worker.query().where('id', params.id).first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
  //worker信息更新
  async update({ request, params, response }) {
    let workerID = request.worker.id
    let { mobile, insureFiles, workerName } = request.all()
    let vo = await Worker.find(workerID)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    if (mobile) {
      let passwd = mobile.slice(-6)
      let enPw = CryptUtil.md5(CryptUtil.encryptData256(passwd, Env.get('APP_KEY')))
      let data = request.all()
      delete data.insureFiles
      _.assign(vo, data, { isUse: E.WorkerAccountStatus.Init, password: enPw })
    } else {
      let data = request.all()
      delete data.insureFiles
      _.assign(vo, data, { isUse: E.WorkerAccountStatus.Init })
    }

    // 处理保险文件
    if (insureFiles && insureFiles.length > 0) {
      const companyID = vo.companyID
      // 检查是否已有保险记录
      let workerInsure = await WorkerInsure.findBy('workerID', workerID)

      if (!workerInsure) {
        // 创建新的保险记录
        await WorkerInsure.create({
          workerID,
          companyID,
          files: insureFiles,
        })
      } else {
        // 更新现有保险记录
        workerInsure.files = insureFiles
        workerInsure.updatedAt = moment().format('YYYY-MM-DD HH:mm:ss')
        await workerInsure.save()
      }
    }
    await vo.save()
    response.json(vo)
    let salesman = await Salesman.findBy('workerID', workerID)
    if (salesman) {
      salesman.workerName = workerName
      salesman.mobile = mobile
      await salesman.save()
    } else {
      await Salesman.create({ workerID, workerName, mobile, status: 1, title: '1' })
    }
    
  }
  async getMessage({ request, response }) {
    let { messageID } = request.all()
    let workerID = request.worker.id
    // let count = await MessageSub.query().where('workerID', workerID).where('messageID', messageID).getCount()
    let count = 0
    if (count) {
      return { count }
    } else {
      let data = await MessageSub.create({ workerID, messageID })
      return { count, data }
    }
  }
  async loginBypasswd({ request, response }) {
    let { phone, passwd, newPasswd } = request.all()
    let enPw = CryptUtil.md5(CryptUtil.encryptData256(passwd, Env.get('APP_KEY')))
    let vo = await Worker.query().where('mobile', phone).where('password', enPw).first()
    if (!vo) {
      throw ERR.SQL_DUP_NAME_OR_PASSWORD
    }
    if (newPasswd) {
      vo.password = CryptUtil.md5(CryptUtil.encryptData256(newPasswd, Env.get('APP_KEY')))
      await vo.save()
    }
    response.json({
      token: CryptUtil.jwtEncode({ userID: vo.id }),
      openID: vo.openid,
      user: vo
    })
  }
}

module.exports = WorkerController
