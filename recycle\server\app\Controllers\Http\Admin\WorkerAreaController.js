'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Company, WorkerArea, SysArea, ReqLog } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { CryptUtil } = require('../../../Util')
const Env = use('Env')

//公司信息
class WorkerAreaController {
  //回收人员work区域
  async index({ request, response }) {
    let { workerID, companyID } = request.all()
    if (!workerID && !companyID) {
      throw ERR.INVALID_PARAMS
    }
    let query = WorkerArea.query()
    if (workerID) {
      query.where('workerID', workerID)
    }
    if (companyID) {
      query.where('companyID', companyID)
    }
    let vo = await query.fetch()
    response.json(vo)
  }
  //编辑回收人员work区域
  async store({ request, response }) {
    let { companyID, areaList, workerID, addList, removeList } = request.all()
    await ReqLog.create({ req: workerID + "编辑区域太大", source: '后台 编辑回收人员区域' })
    if (!companyID) {
      throw ERR.INVALID_PARAMS
    }
    if (addList || removeList) {
      for (let i = 0; i < removeList.length; i++) {
        let vo = WorkerArea.query().where({ companyID, workerID }).where('code', removeList[i].code).where('typeName', removeList[i].typeName)
        if (removeList[i].detail) {
          vo.where('detail', removeList[i].detail)
        }
        if (vo) {
          await vo.delete()
        }
      }
      for (let i = 0; i < addList.length; i++) {
        let vo = await WorkerArea.create({ workerID, companyID, ...addList[i] })
      }
    } else {
      let list = WorkerArea.query()
        .where('companyID', companyID)
        .where('workerID', workerID)
      await list.delete()
      await _.forEach(areaList, async function (area) {
        let vo = await WorkerArea.query()
          .where('companyID', companyID)
          .where('workerID', workerID)
          .where('code', area.code)
          .where('type', area.type)
          .first()
        if (!vo) {
          let vo = await WorkerArea.create({ companyID, ...area, workerID })
        }
      })
    }
    let vo = await WorkerArea.query()
      .where('companyID', companyID)
      .where('workerID', workerID)
      .fetch()
    response.json(vo)
  }
  async getArea({ request, params, response, auth }) {
    let { current = 1, pageSize = 10, province, city, keyWord } = request.all()
    if (!params.id) {
      throw ERR.API_ERROR
    }
    let query = WorkerArea.query()
      .select('id', 'code', 'name', 'companyID', 'workerID', 'areaCode', 'area')
      .groupBy('code')
      .where('workerID', params.id)
      .with('company', (b) => b.select('id', 'companyName'))
      .with('address')
      .with('worker', (b) => b.select('id', 'workerName'))
      .with('typeInfo')
    if (province) {
      if (province !== 'all') {
        query.where('areaCode', 'like', `${province}%`)
      }
    }
    if (city) {
      let areaInfo = await SysArea.query()
        .where('name', 'LIKE', `%${city}%`)
        .where('level', 2)
        .select('id', 'name', 'cityCode')
        .fetch()
      let areacodes = areaInfo.rows.map((vo) => {
        return (vo.cityCode)
      }
      )
      query.where('areaCode', 'IN', _.uniq(areacodes))
    }
    if (keyWord) {
      query.where('area', 'like', `%${keyWord}%`)
    }
    let total = await query.getCount()
    let data = await query
      .orderBy('code', 'desc')
      .offset(pageSize * (current - 1))
      .limit(pageSize)
      .fetch()
    return {
      code: 200, total, page: current, perPage: pageSize,
      lastPage: Math.floor(total / pageSize) + 1, data,
    }
  }
  async destroy({ request, params, response }) {
    let { adminUser: user } = request
    let { code, workerID, arrIds } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台 删除回收人员区域' })
    // if (user.level !== E.AdminLevel.总部) {
    //   throw ERR.USER_ROLE_NO_PRIVILEGE
    // }
    if (code) {
      let vo = WorkerArea.query().where({ code, workerID })
      if (vo) {
        await vo.delete()
      }
    }
    if (arrIds) {
      _.forEach(arrIds, async function (value) {
        let vo = WorkerArea.query().where({ code: value, workerID })
        if (vo) {
          await vo.delete()
        }
      })
    }
    return ({ msg: 'delete ok', code: 200 })
  }
}

module.exports = WorkerAreaController
