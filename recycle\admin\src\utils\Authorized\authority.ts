// use localStorage to store the authority info, which might be sent from server in actual project.

import { notification } from "antd"
import { KEY_TOKEN, rolePermission } from "../../common/config";

export function getAuthority() {
  let authority: any = localStorage.getItem(rolePermission)
  if (authority) {
    authority = JSON.parse(authority)
  } else {
    authority = []
  }

  return authority
}

export function setAuthority(authority: any) {
  return localStorage.setItem(rolePermission, authority)
}

export function getToken() {
  return localStorage.getItem(KEY_TOKEN)
}
export function setToken(token: any) {
  return localStorage.setItem(KEY_TOKEN, token)
}
export function computeAuthority(value: string) {
  let authority: any = localStorage.getItem(rolePermission)
  if (authority) {
    authority = JSON.parse(authority)
  } else {
    authority = []
  }
  let result = true
  if (authority.indexOf(value) < 0) {
    result = false
  }
  if (result) {
    //   notification.error({ message: '您没有此项权限', duration: 2 })
  }
  return result
}
