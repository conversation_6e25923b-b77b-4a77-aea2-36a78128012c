import { bindJWTToken, requestGet, requestPost, requestPut } from 'dva17'
import { EGet, EGetcheck, EUserAutoLogin, EUserLogin, EUserLogout, NUser, RAdd, RSetState } from '../common/action'
import { KEY_TOKEN, rolePermission } from '../common/config'
import { UserState } from '../common/enum'
import { getAuthUser, getUserInfo } from '../services/user'
import { reloadAuthorized } from '../utils/Authorized/Authorized'

export default {
  namespace: NUser,
  state: {
    currentUser: null,
    status: UserState.loading,
  },
  reducers: {
    [RSetState](state: any, payload: any) {
      return { ...state, ...payload }
    },
    [RAdd](state: { count: any }, payload: any) {
      return { ...state, count: state.count + payload }
    },
  },
  effects: {
    async [EUserAutoLogin]({ payload: history }: any, { reducer }: any) {
      let oldToken = localStorage.getItem(KEY_TOKEN)
      if (oldToken) {
        bindJWTToken(oldToken)
        let { token, adminUser: admin, needLogin } = await requestPost(`user/autoLogin`, null)
        if (needLogin) {
          reducer(RSetState, { status: UserState.login })
        } else {
          bindJWTToken(token)
          localStorage.setItem(KEY_TOKEN, token)
          reducer(RSetState, { currentUser: admin, status: UserState.autherized })
        }
      } else {
        reducer(RSetState, { status: UserState.login })
      }
    },
    async [EUserLogin]({ payload }: any, { reducer }: any) {
      const { token, adminUser: admin } = await requestPost(`user/login`, payload)
      // 绑定并缓存token
      bindJWTToken(token)
      localStorage.setItem(KEY_TOKEN, token)
      let permission = ''
      if (admin) {
        permission = admin.authority.key
      }
      if (permission) {
        localStorage.setItem(rolePermission, permission)
      }
      reloadAuthorized()
      reducer(RSetState, { currentUser: admin, status: UserState.autherized })
    },
    async [EUserLogout]({ payload: history }: any) {
      /**
       * 退出登录，并且将当前的 url 保存
       */
      localStorage.removeItem(KEY_TOKEN)
      localStorage.removeItem(rolePermission)
      reloadAuthorized()
      location.reload()
    },
    async [EGetcheck]({ payload }: any, { reducer }: any) {
      const response: any = getUserInfo()
      let permission
      if (response) {
        permission = response.authority.key
      }
      if (permission) {
        localStorage.setItem(rolePermission, JSON.parse(permission))
      }
      reducer(RSetState, {
        currentUser: { name: payload.company ? `${payload.company.companyName}-${payload.username}` : `总部-${payload.username}` },
        user: payload,
        updata: false,
      })
    },
  },
}
