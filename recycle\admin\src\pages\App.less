.app-container {
  min-height: 100vh;
  background: url('https://oss.evergreenrecycle.cn/yshs/app-bg.jpg') no-repeat center center fixed;
  background-size: cover;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  :global {
    .ant-pro-basicLayout {
      position: relative;
      z-index: 1;
      height: 100vh;
    }

    .ant-layout {
      background: transparent;
    }

    .ant-pro-sider {
      background: rgba(255, 255, 255, 0.8) !important;
      backdrop-filter: blur(10px);
      border-right: 1px solid rgba(0, 0, 0, 0.06);
    }

    /* 强制设置侧边栏默认折叠状态 */
    .ant-pro-sider.ant-layout-sider-collapsed {
      min-width: 48px !important;
      max-width: 48px !important;
      width: 48px !important;
      flex: 0 0 48px !important;
    }
    
    /* 强制所有侧边栏都显示为折叠状态 */
    .ant-pro-sider:not(.ant-layout-sider-collapsed) {
      min-width: 48px !important;
      max-width: 48px !important;
      width: 48px !important;
      flex: 0 0 48px !important;
    }
    
    .ant-pro-sider:not(.ant-layout-sider-collapsed) .ant-menu-title-content {
      display: none !important;
    }
    
    .ant-pro-sider:not(.ant-layout-sider-collapsed) .ant-menu-submenu-title .ant-menu-title-content {
      display: none !important;
    }

    .ant-pro-sider-logo {
      background: transparent !important;
      h1 {
        color: rgba(0, 0, 0, 0.85);
      }
    }

    .ant-menu {
      background: transparent !important;
    }

    .ant-layout-header {
      background: rgba(255, 255, 255, 0.8) !important;
      backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    }

    .ant-pro-page-container {
      background: transparent;
    }

    .ant-tabs-card {
      > .ant-tabs-nav {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        margin-bottom: 16px;
        padding: 8px;
        border-radius: 8px;
      }
    }

    .ant-card {
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      border: none;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .ant-table {
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
    }

    .ant-pro-watermark {
      pointer-events: none !important;
    }
  }
}