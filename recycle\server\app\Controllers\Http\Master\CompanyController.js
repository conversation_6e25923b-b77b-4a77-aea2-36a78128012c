'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Company } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//服务商信息
class CompanyController {
  //获取具体服务商
  async index({ request, response }) {
    let { page = 1, perPage = 10 } = request.all()
    let worker = request.worker
    if (!worker.companyID) {
      return ERR.RESTFUL_GET_ID
    }
    let vo = await Company.query().where('deletedAt', '0')
      .where('id', worker.companyID)
      .first()
    response.json(vo)
  }
  async show({ request, params, response }) {
    let vo = await Company.query()
      .where('id', params.id)
      .first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
  //获取服务商列表
  async store({ request, response }) {
    let { forbidden } = request.all()
    let query = Company.query().where('deletedAt', '0')
    if (forbidden) {
      query.where('forbidden', forbidden)
    }
    query.where('id', 36)//仅显示一个主体
    let vo = await query.fetch()
    response.json(vo)
  }
}

module.exports = CompanyController
