.oldGoods {
  width: 100vw;
  min-height: 100vh;
  padding-bottom: 21.5vw;
  padding-top: 14.2vw;
  background-color: #f5f6f8;
  .oldGoods_head {
    width: 100vw;
    margin: 0 auto;
    height: 14.2vw;
    background-color: #ffffff;
    box-shadow: 0px 1px 5px 7px rgba(219, 219, 219, 0.4);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 2;
    .oldGoods_head_category {
      width: 89.2vw;
      margin: 0 auto;
      display: flex;
      .oldGoods_head_category_item {
        height: 13.8vw;
        display: flex;
        flex-direction: column;
        margin-right: 4vw;
        position: relative;
        left: 0;
        top: 0;
        .oldGoods_head_category_item_text {
          display: block;
          height: 13vw;
          line-height: 13vw;
          color: #a3a9b4;
          font-size: 4vw;
          text-align: center;
          font-family: 'NotoSansCJKsc-Medium';
          // border-bottom:2vw solid red;
        }
        .oldGoods_head_category_item_line {
          display: block;
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          height: 8px;
          // border: 1px solid #15b381;
          background: linear-gradient(to right, rgba(119, 220, 143, 1) 0% , rgba(21, 179, 129, 1) 100%);
          border-radius: 1vw;
          z-index: 9;
        }
      }
    }
  }
  .oldGoods_content {
    .old_clothes {
      .page-title {
        width: 90vw;
        margin: 0 auto;
        color: #7c8696;
        font-size: 4.2vw;
        text-align: left;
        font-family: NotoSansCJKsc-Medium;
        margin-top: 6.9vw;
        margin-bottom: 5.1vw;
        display: flex;
        align-items: center;
        > Text {
          font-size: 24px;
          color: #7c8696;
          margin-left: 30px;
          display: inline-block;
          &::after {
            content: '';
            display: block;
            height: 2px;
            width: 100%;
            background: #7c8696;
          }
        }
      }
      .page-body {
        width: 90vw;
        margin: 0 auto;
        display: flex;
        flex-wrap: wrap;
      }
    }
  }
  .oldGoods_bottom {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    background-color: #ffffff;
    .top_wrapper {
      width: 100%;
      height: 120px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .bottom_wrapper {
      height: 32px;
      width: 100%;
    }
    .oldGoods_bottom_shoppingCar {
      width: 50vw;
      height: 100%;
      // background-color:#F8F8F8;
      display: flex;
      // justify-content:center;
      align-items: center;
      .shoppingCar_box {
        width: 13.1vw;
        height: 13.1vw;
        // border-radius:50%;
        // border: 1px solid rgba(187, 187, 187, 1);
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        left: 0;
        top: 0;
        bottom: 0;
        margin: auto 0;
        .shoppingCar {
          display: block;
          width: 7.4vw;
          height: 7.4vw;
          object-fit: cover;
          // background-color: rgba(102, 102, 102, 1);
        }
        .type_num {
          position: absolute;
          right: -1.6vw;
          top: 1vw;
          width: 3.2vw;
          height: 3.2vw;
          border-radius: 50%;
          background-color: rgba(219, 219, 219, 1);
          color: rgba(166, 166, 166, 1);
          font-size: 2.7vw;
          text-align: center;
          font-family: Arial;
          border: 1px solid rgba(255, 255, 255, 0);
        }
      }
      .shoppingCar_text {
        display: block;
        color: rgba(102, 102, 102, 1);
        font-size: 3.5vw;
        height: 10vw;
        line-height: 10vw;
        // border:1px solid red;
        font-family: PingFangSC-regular;
      }
    }
    button {
      opacity: 0;
    }
    .oldGoods_bottom_text {
      z-index: 2;
      margin-right: 2.5vw;
      width: 46.4vw;
      height: 11.2vw;
      border-radius: 5.6vw;
      background: linear-gradient(to right, #e7505f 0%, #15b381 50%);
      color: #ffffff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      // padding:0 8vw 1vw 0;
      padding-left: 8vw;
      .btn_arrow {
        width: 7vw;
        height: 7vw;
        margin-right: 2vw;
      }
    }
  }
}
.button_getUser {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
  border: 1px #fff solid !important ; 
  border-radius: 0 !important ;
 background-color: transparent;
}
.gender_wrapper {
  width: 220px;
  display: flex;
  justify-content: space-between;
  .gender_item {
    // border: 1px solid #cccccc;
    width: 100px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28px;
    font-weight: 300;
  }
  .this_gender {
    border: 1px solid #15b381;
  }
}

.item_wrapper {
  width: 90vw;
  //height: 50vh;
  display: flex;
  flex-wrap: wrap;
  margin: 0 auto;
  .item {
    border: 1px solid transparent;
    width: 28.2vw;
    //height: 37.3vw;
    padding-top: 3.2vw;
    padding-bottom: 3.2vw;
    background-color: #ffffff;
    border-radius: 4vw;
    margin-bottom: 5vw;
    margin-right: 2.5vw;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    &:nth-child(3n + 0) {
      margin-right: 0;
    }
    .img {
      width: 16vw;
      height: 22vw;
      margin: 0 auto;
    }
    .name {
      width: 70%;
      margin-top: 1vw;
      color: #333;
      font-size: 24px;
      font-weight: 600;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .price {
      width: 70%;
      margin-top: 1vw;
      color: #999999;
      font-size: 2.7vw;
      font-style: oblique;
      text-align: center;
    }
    .check_item {
      position: absolute;
      right: -1vw;
      top: -2.5vw;
      width: 8.2vw;
      height: 8.2vw;
    }
  }
}
.remind_wrapper {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  letter-spacing: 1.2px;
  .message_wrapper {
    height: 896px;
    width: 600px;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    .remindTitle {
      font-size: 30px;
      color: #333333;
      font-weight: 700;
      margin-top: 34px;
      margin-bottom: 50px;
      display: inline-block;
    }
    .content_wrapper {
      width: 500px;
      height: 620px;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      .remind_text {
        font-size: 24px;
        color: #7c8696;
        line-height: 36px;
        margin-bottom: 30px;
      }
      .remind_title {
        font-size: 28px;
        color: #444444;
        margin-bottom: 20px;
      }
    }
    .know_button_wrapper {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 40px;
      display: flex;
      flex-direction: column;
      align-items: center;
      .know_button {
        height: 88px;
        width: 500px;
        background: linear-gradient(to right, rgba(119, 220, 143, 1) 0% , rgba(21, 179, 129, 1) 100%);
        line-height: 88px;
        text-align: center;
        border-radius: 44px;
        color: #ffffff;
        font-size: 30px;
        font-weight: 700;
      }
      .radio {
        font-size: 20px;
        color: #7c8696;
        margin-bottom: 20px;
        //    未选中的 背景样式
        .wx-radio-input {
          width: 20px;
          height: 20px;
          margin-right: 16px;
        }

        // 选中后的 背景样式 （红色背景 无边框 可根据UI需求自己修改）
        .wx-radio-input.wx-radio-input-checked {
          border-color: #15b381 !important;
          background: #15b381 !important;
        }

        .wx-radio-input.wx-radio-input-checked::before {
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          font-size: 15px;
          color: #fff;
          background: transparent;
          transform: translate(-50%, -50%) scale(1);
          -webkit-transform: translate(-50%, -50%) scale(1);
        }
      }
    }
  }
}
.cant_move {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 0;
}
