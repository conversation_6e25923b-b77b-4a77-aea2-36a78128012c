'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Waste, ReqLog } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { judgeAuthority } = require('../../../Services/UserService')

//废品
class WasteController {
  async index({ request, response }) {
    let { current = 1, pageSize = 10, source = E.OrderSource.CaiNiao, type = E.Permission.Primary } = request.all()
    let query = Waste.query().where('source', source).where('type', type)
    let vo = await query.paginate(current, pageSize)
    response.json(vo)
  }
  async show({ request, params, response }) {
    let vo = await Waste.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
  //总部回收物基准价格编辑
  async store({ request, response }) {
    let { adminUser } = request
    await judgeAuthority(adminUser, '回收价格编辑')
    let { list } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台 回收价格编辑' })
    if (list.length <= 0) {
      throw ERR.INVALID_PARAMS
    }
    _.forEach(list, async function (value) {
      let vo = await Waste.find(value.id)
      _.assign(vo, value)
      await vo.save()
    })
    response.json({ result: 'ok' })
  }
  async update({ request, params, response }) {
    let vo = await Waste.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
  }
}

module.exports = WasteController
