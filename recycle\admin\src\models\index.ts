import AdminUser from './AdminUser'
import CategoryPrice from './CategoryPrice'
import Collection from './Collection'
import Company from './Company'
import Order from './Order'
import User from './User'
import Worker from './Worker'
import NPermission from './NPermission'
import ClientOrder from './ClientOrder'
import Salesman from './Salesman'
import JDWorker from './JDWorker'
import JDOrder from './JDOrder'
import HiCollection from './HiCollection'
import HiWorker from './HiWorker'
import HiOrder from './HiOrder'
import HiPrice from './HiPrice'
import JoinTeam from './JoinTeam'


export default [User, AdminUser, CategoryPrice, Collection,
    Order, Worker, Company, NPermission, ClientOrder,
    Salesman, JDWorker, JDOrder, HiCollection, HiWorker, HiOrder, HiPrice, JoinTeam]
