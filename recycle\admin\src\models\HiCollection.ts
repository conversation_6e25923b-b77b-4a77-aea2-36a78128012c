import { requestPost } from 'dva17'
import {
    ECancelOrderStatus, EChangeOrderStatus, EConfirmOrder,
    EDevideOrderAgain, EGetNotice, EGetOrderDetail,
    EGetOrderList, EGetOrderNumberArray, EChangeOrderWorker,
    EGetWhichWorkers, EOrderBack, EPut, ESeeComplaint,
    ESeeRating, RSetState,
    EGetPrice,
    EPutPrice,
    EPostPrice,
    NHiCollection,
    EGetImport,
    EHiOrderMaintainList,
    EBatchDestroyImport,
    EDeleteImport
  } from '../common/action'
  import { adapterPaginationResult } from '../common/utils'
  import {
    getHiOrderList,
    putHiOrder,
    getWhichWorkers,
    getWorkerDetail,
    postHiOrder,
    postHiOrderBack,
    postHiOrderConfirm,
    getHiOrderMaintainList
  } from '../services/HiServices'
  import { message } from 'antd'
  
  export default {
    namespace: NHiCollection,
    state: {
      visibleLog: false,
      logData: null,
      orderNumberArray: null,
      orderList: null,
      isStatusChange: false,
      isCancelOrder: false,
      whichWorkers: null,
      complaintList: null,
      ratingList: null,
      searchQuery: {},
      notices: null
    },
    reducers: {
      [RSetState](state: any, payload: any) {
        return { ...state, ...payload }
      },
    },
    effects: {
      //获取各种状态的订单的列表
      async [EGetImport]({ payload }: any, { reducer }: any) {
        reducer(RSetState, { orderList: null })
        const response = await getHiOrderList(payload)
        // console.log('result: ', response)
        reducer(RSetState, { orderList: response, searchQuery: payload })
        return adapterPaginationResult(response)
      },
      async [EBatchDestroyImport]({ payload }: any) {
        let result = await requestPost(`hiOrder/batchDestroy`, payload)
        return result
      },
      async [EDeleteImport]({ payload }: any) {
        let result = await requestPost(`hiOrder/batchDestroy`, payload)
        return result
      },
      async [EHiOrderMaintainList]({ payload }: any, { reducer }: any) {
        const response = await getHiOrderMaintainList(payload)
        reducer(RSetState, { orderMaintainList: response })
        return adapterPaginationResult(response)
      },
      async [EPut]({ payload }: any, { reducer }: any) {
        const hide = message.loading('正在添加')
        reducer(RSetState, { isStatusChange: false })
        const response = await postHiOrder(payload)
        reducer(RSetState, { isStatusChange: true })
        hide()
      },
      //修改订单的状态
      async [EChangeOrderStatus]({ payload }: any, { reducer }: any) {
        const hide = message.loading('正在添加')
        reducer(RSetState, { isStatusChange: false })
        if (payload.isTransferID) {
          const response = await putHiOrder({
            id: payload?.id, isTransferID: payload?.isTransferID
          })
          const worker = await getWorkerDetail({
            id: payload.isTransferID
          })
          // const message = await sendSMS, {
          //   mobile: worker.mobile,
          //   smsCode: 'SMS_184825898',
          //   smsParam: {
          //     orderNo: response.orderNo,
          //     wasteType: payload.waste_1st_ID === 3 ? '大家电' : payload.waste_1st_ID === 2 ? '生活废品' :payload.waste_1st_ID === 5 ? '海尔电器' : '大家具'
          //   }
          // })
        } else {
          const response = await putHiOrder(payload)
        }
        reducer(RSetState, { isStatusChange: true })
        hide()
      },
      async [EConfirmOrder]({ payload }: any, { reducer }: any) {
        const hide = message.loading('正在确认订单')
        reducer(RSetState, { isStatusChange: false })
        const response = await postHiOrderConfirm(payload)
        reducer(RSetState, { isStatusChange: true })
        hide()
      },
      // 改派
      async [EChangeOrderWorker]({ payload }: any, { reducer }: any) {
        reducer(RSetState, { isStatusChange: false })
        const response = await putHiOrder(payload)
        reducer(RSetState, { isStatusChange: true })
      },
      
    }
  }
  