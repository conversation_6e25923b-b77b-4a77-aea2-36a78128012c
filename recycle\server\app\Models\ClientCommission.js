'use strict'

const Model = use('Model')


class ClientCommission extends Model {
  static get table() {
    return 'price_set_client'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return "createdAt"
  }
  static get updatedAtColumn() {
    return "updatedAt"
  }
  company(){
    return this.hasOne('App/Models/Company', 'companyID', 'id').select('id', 'companyName', 'province', 'city')
  }
}

module.exports = ClientCommission
