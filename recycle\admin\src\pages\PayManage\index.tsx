import { DownloadOutlined, PlusOutlined } from '@ant-design/icons'
import { Button, message, Modal, Input, Table } from 'antd'
import type { ActionType, ProColumns } from '@ant-design/pro-table'
import ProTable, { TableDropdown } from '@ant-design/pro-table'
import ProCard from '@ant-design/pro-card'
import { effect, useConnect, } from 'dva17'
import { useEffect, useRef, useState } from 'react'
import { EGetWorkerPaylist, NWorker } from '../../common/action'
import dayjs from 'dayjs'
import { statusEnum, typeEnum } from '../../common/enum'
import { postWorkerRefund } from '../../services/worker'
import { computeAuthority } from '../../utils/Authorized/authority'
import qs from 'qs'
import { SERVER_HOME } from '../../common/config'

type Item = {
  id: number
  createdAt: string
  finishAt: string
  type: string
  refundVo: {
    createdAt: string
    finishAt: string
    refundRMB: string
  }[]
  transactionID: string
  workerID: number
  worker: {
    mobile: string
    workerName: string
    id: number
  }[]
  status: string
  totalPay: number
  refundRMB: number
  refundAt: string
}



export default () => {
  const actionRef = useRef<ActionType>()
  const [visible, setVisible] = useState<any>(false)
  const [visibleLog, setVisibleLog] = useState<any>(false)
  const { lastSearch } = useConnect(NWorker)
  const [vo, setVo] = useState<any>({})
  const [voLog, setVoLog] = useState<any>([])
  const [maxRefund, setMaxRefund] = useState<number>(0)
  const [payValue, setPayValue] = useState<any>(0)
  const [reason, setReason] = useState<any>(null)
  /*--------------------- 生命周期 ---------------------*/
  const logColumns: any = [
    { title: '退款金额', render: (record: any) => <span>{(record?.refundRMB) / 100}</span> },
    { title: '退款时间', dataIndex: 'createdAt', render: (createdAt: any) => <span>{createdAt ? dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss') : '-'}</span> },
    { title: '状态', dataIndex: 'finishAt', render: (finishAt: any) => <span>{finishAt ? <div style={{ color: 'green' }}>退款完成</div> : <div style={{ color: 'red' }}>退款失败</div>}</span> },
  ]
  const columns: ProColumns<Item>[] = [
    {
      title: '姓名',
      dataIndex: ['worker', 'workerName'],
      copyable: false,
      ellipsis: true,
      search: false,
    },
    {
      title: '姓名',
      dataIndex: 'workerName',
      hideInTable: true
    },
    {
      title: '联系电话',
      dataIndex: ['worker', 'mobile'],
      copyable: false,
      ellipsis: true,
      search: false,
    },
    {
      title: '支付金额',
      dataIndex: 'totalPay',
      copyable: false,
      ellipsis: true,
      search: false,
      render: (_, row) => <div>{row.totalPay / 100} 元</div>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      copyable: false,
      ellipsis: true,
      valueEnum: statusEnum,
    },
    {
      title: '类型',
      dataIndex: 'type',
      copyable: false,
      ellipsis: true,
      valueEnum: typeEnum,
    },
    // {
    //   title: '支付订单号',
    //   dataIndex: 'transactionID',
    //   width: '20%',
    //   copyable: false,
    // },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      copyable: false,
      search: false,
      render: (_, row) => <div>
        <span>{row.createdAt}</span>
      </div>,
    },
    {
      title: '完成时间',
      dataIndex: 'finishAt',
      copyable: false,
      search: false,
      render: (_, row) => <div>{row.finishAt ? dayjs(row.finishAt).format('YYYY-MM-DD HH:mm:ss') : '-'} </div>,
    },
    {
      title: '完成时间',
      dataIndex: 'finishAt',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            finishStartDate: value[0],
            finishEndDate: value[1],
          }
        },
      },
    },
    {
      title: '操作',
      copyable: false,
      ellipsis: true,
      search: false,
      render: (_, row) => (
        <>
          <Button onClick={() => {
            setVisible(true)
            setVo(row)
            setMaxRefund(row.refundAt ? ((row.totalPay - row.refundRMB) / 100) : (row.totalPay / 100))
          }}>退款</Button>
          {row?.type === '师傅' && <Button
            type='primary'
            onClick={() => {
              setVisibleLog(true)
              setVoLog(row.refundVo)
            }}>退款记录</Button>}
        </>
      ),
    },
  ]
  useEffect(() => { }, [])
  /*--------------------- 响应 ---------------------*/
  const exportExcel = () => {
    let qsQuery = qs.stringify(lastSearch)
    console.log(qsQuery);
    window.open(
      // https://s.evergreenrecycle.cn/zhihong/admin/v1/
      `${SERVER_HOME}workerPaylistexport?${qsQuery}`
    )
  }
  const refreshPage = () => {
    if (actionRef.current) {
      actionRef.current.reload()
    }
  }
  /*--------------------- 渲染 ---------------------*/
  return (
    <ProCard>
      <ProTable<Item>
        actionRef={actionRef}
        columns={columns}
        request={async (params = {}) => (await effect(NWorker, EGetWorkerPaylist, params)) as any}
        pagination={{
        }}
        rowKey="id"
        dateFormatter="string"
        headerTitle="师傅押金管理"
        toolBarRender={() => [
          <Button
            key="3"
            type="primary"
            disabled={!computeAuthority('数据导出')}
            onClick={() => {
              exportExcel()
            }}>
            <DownloadOutlined />
            导出
          </Button>,
        ]}
      />
      <Modal
        open={visible}
        onCancel={() => {
          setVisible(false)
        }}
        onOk={async () => {
          const hide = message.loading('正在退款')
          await postWorkerRefund({ id: vo.id, reason, amount: (payValue) * 100 }).then(() => {
            setVisible(false)
            hide()
            message.success({ content: '本次退款成功!' })
            refreshPage()
          })

        }}
        title='押金退款'>
        <>
          <div style={{ marginBottom: 20, fontWeight: 'bolder' }}>最大可退金额:
            {maxRefund}
            元</div>
          <div style={{ marginBottom: 20 }}>
            本次退款金额:<Input
              type={'number'}
              style={{ width: '50%' }} onChange={(e) => {
                const value = parseInt(e.target.value)
                if (value > maxRefund) {
                  message.error({ content: '退款金额不可大于可退金额' })
                }
                setPayValue(value)
              }} placeholder='请输入退款金额'></Input>
          </div>
          <div style={{ marginBottom: 20 }}>
            退款原因:<Input
              style={{ width: '80%' }} onChange={(e) => {
                setReason(e.target.value)
              }} placeholder='请输入退款原因'></Input>
          </div>
        </>
      </Modal>
      <Modal
        open={visibleLog}
        onCancel={() => {
          setVisibleLog(false)
        }}
        onOk={async () => {
          setVisibleLog(false)
        }}
        title='退款记录'>
        <div style={{ fontSize: 18, color: 'Highlight', marginBottom: 20 }}>
          <Table
            columns={logColumns}
            loading={voLog ? false : true}
            dataSource={voLog ? voLog : []}
            rowKey={(record: any) => JSON.stringify(record)}
            pagination={false}
          />
        </div>
      </Modal>
    </ProCard>
  )
}
