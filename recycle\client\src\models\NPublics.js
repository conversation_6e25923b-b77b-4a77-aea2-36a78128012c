import { getUserDiscoveryList, replayTheContent, changeUpvote, getUpvoteList, getIndexPage, getPrice, postCreateOrder } from '../services/public'
import { NPublics } from '../config/constants'
const { EGetDiscoveryList, EReplayTheContent, EChangeUpvote, EGetUpvoteList, ESetState, EGetIndexPage, EGetPrice, EPostCreateOrder } = NPublics
export default {
  state: {
    discoveryList: [],
    upvoteList: [],
    isChangeUpvote: false,
    indexPage: null,
    priceInfo: null,
  },
  effects: dispatch => {
    return {
      async [EGetDiscoveryList](payload, rootState) {
        const response = await getUserDiscoveryList(payload)
        await this.RSetState({ discoveryList: response })
      },
      async [EGetIndexPage](payload, rootState) {
        const response = await getIndexPage()
        await this.RSetState({ indexPage: response })
      },
      async [EGetPrice](payload, rootState) {
        const response = await getPrice(payload)
        await this.RSetState({ priceInfo: response })
      },
      async [EReplayTheContent](payload, rootState) {
        await this.RSetState({ isChangeUpvote: false })
        const response = await replayTheContent(payload)
        await this.RSetState({ isChangeUpvote: true })
      },
      async [EChangeUpvote](payload, rootState) {
        await this.RSetState({ isChangeUpvote: false })
        const response = await changeUpvote(payload)
        const response1 = await getUpvoteList()
        await this.RSetState({ upvoteList: response1, isChangeUpvote: true })
      },
      async [EGetUpvoteList](payload, rootState) {
        const response = await getUpvoteList()
        await this.RSetState({ upvoteList: response })
      },
      async [EPostCreateOrder](payload, rootState) {
        const response = await postCreateOrder(payload)
        return response
      },
      async [ESetState](payload, rootState) {
        await this.RSetState({ ...payload })
      },
    }
  },
  reducers: {
    RSetState(state, payload) {
      return {
        ...state,
        ...payload,
      }
    },
  },
}
