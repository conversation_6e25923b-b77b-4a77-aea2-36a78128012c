import { getUserDiscoveryList, replayTheContent, changeUpvote, getUpvoteList } from '../services/public'
import { NPublics } from '../config/constants'
const { EGetDiscoveryList, EReplayTheContent, EChangeUpvote, EGetUpvoteList ,  ESetState} = NPublics
export default {
  state: {
    discoveryList: [],
    upvoteList: [],
    isChangeUpvote: false,
  },
  effects: dispatch => {
    return {
      async [EGetDiscoveryList](payload, rootState) {
        const response = await getUserDiscoveryList(payload)
        await this.RSetState({ discoveryList: response })
      },
      async [EReplayTheContent](payload, rootState) {
        await this.RSetState({ isChangeUpvote: false })
        const response = await replayTheContent(payload)
        await this.RSetState({ isChangeUpvote: true })
      },
      async [EChangeUpvote](payload, rootState) {
        await this.RSetState({ isChangeUpvote: false })
        const response = await changeUpvote(payload)
        const response1 = await getUpvoteList()
        await this.RSetState({ upvoteList: response1 , isChangeUpvote: true })
      },
      async [EGetUpvoteList](payload, rootState) {
        const response = await getUpvoteList()
        await this.RSetState({ upvoteList: response })
      },
      async [ESetState](payload, rootState) {
        await this.RSetState({...payload})
      },
    }
  },
  reducers: {
    RSetState(state, payload) {
      return {
        ...state,
        ...payload,
      }
    },
  },
}
