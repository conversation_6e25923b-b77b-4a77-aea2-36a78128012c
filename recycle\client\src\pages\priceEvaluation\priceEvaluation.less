.priceEvaluation{
  width: 100vw;
  height: 100vh;
  background-color: #f5f6f8;
  display: flex;
  flex-direction: column;
  .which{
    width: 100%;
    height: 200px;
    background: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    Text{
      color: #D0D0DC;
    }
  }
  .devide{
    width: 100%;
    height: 10px;
  }
  .remind_text{
    width: 100%;
    height: 72px;
    background: #ffffff;
    display: flex;
    align-items: center;
    padding: 0 30px;
    font-weight: 700;
    font-size: 28px;
  }
  .selected_attribute{
    width: 100%;
    min-height: 200px;
    display: flex;
    flex-wrap: wrap;
    padding: 40px 30px;
    padding-bottom: 20px;
    background: #ffffff;
    .attribute{
      width: 46%;
      height: 64px;
      border: 1px solid #cccccc;
      border-radius: 4px;
      line-height: 64px;
      text-align: center;
      font-size: 24px;
      margin-bottom: 20px;
      &:nth-child(even){
        margin-left: 8%;
      }
    }
  }
  .administration_add_address {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    //background-color: rgba(164, 164, 164, 1);
    background: #15b381;
    color: rgba(255, 255, 255, 1);
    font-size: 30px;
    .text_wrapper{
      display: flex;
      height: 120px;
      width: 100%;
      justify-content: center;
      align-items: center;
    }
    .add_item{
      height: 32px;
      width: 100%;
    }
  }
}