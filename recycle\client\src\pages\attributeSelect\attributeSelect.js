import Taro from '@tarojs/taro'
import React, { Component } from 'react'
import T from '../../config/T'
import { connect } from 'react-redux'
import { View, Image, Button, Text, Checkbox, Swiper, SwiperItem, ScrollView } from '@tarojs/components'
import './attributeSelect.less'


class Appliances extends Component {
  constructor() {
    super(...arguments)
    this.state = {
      locale: 'zh_CN',
      currentSelect: [],
      currentObject: [],
      currentSlide: 0,
      canClick: false,
      scrollLeft: 0,
    }
    // this.wrapper = Taro.createRef()
  }

  config = {}

  //-----------------------生命周期-------------------------//
  componentWillMount() {
  }

  componentDidMount() {
    const locale = Taro.getStorageSync('locale')
    this.setState({
      locale: locale,
    })
    Taro.setNavigationBarTitle({ title: locale == 'en' ? 'Type Selection' : '类型选择' })
  }

  componentWillUnmount() { }

  componentDidShow() { }

  componentDidHide() { }

  //-----------------------事件-------------------------//
  selectTwo(index, value) {
    this.setState({
      selectTwoLevel: index,
    })
    this.props.dispatch({
      type: 'NOldGoods/EGoodsGetTwoLevel',
      payload: {
        id: value.id,
      },
    })
  }

  getAttributeType(index, value) {
    this.props.dispatch({
      type: 'NOldGoods/EGoodsGetAttributeType',
      payload: {
        id: value.id,
      },
    })
  }

  getCurrentSlide() {
    let { attributeType, canClick } = this.props
    this.props.dispatch({
      type: 'NOldGoods/EGetEstimate',
      payload: {
        level: 4,
        name: this.props.whichAppliances?.item?.[0]?.name,
        type: attributeType[this.state.currentSlide]?.path,
      },
    })
  }

  selectValue(value) {
    let { currentSelect, currentObject } = this.state
    let { attribute, attributeType } = this.props
    let length = attributeType.length
    attribute.map((item, index) => {
      if (currentSelect.indexOf(item.name) >= 0) {
        currentSelect.forEach((select, mark) => {
          if (select === item.name) {
            currentSelect.splice(mark, 1)
            currentObject.splice(mark, 1)
          }
        })
      }
    })
    currentSelect.push(value.name)
    currentObject.push(value)
    this.setState({
      currentSelect,
      currentObject,
      currentSlide: this.state.currentSlide < length - 1 ? this.state.currentSlide + 1 : this.state.currentSlide,
      canClick: attributeType.length === currentSelect.length ? true : false,
    })
    if (attributeType.length === currentSelect.length) {
      this.props.dispatch({
        type: 'NOldGoods/EGetEstimate',
        payload: {
          level: 5,
          subType: this.props.whichAppliances?.item?.[0]?.name,
          spu: currentSelect[0],
          years: currentSelect[1],
          functions: currentSelect[2],
          outside: currentSelect[3],
          coldType: currentSelect[4],
        },
      })
    }
    setTimeout(() => {
      this.getCurrentSlide()
      this.move()
    })
    this.props.dispatch({
      type: 'NOldGoods/ESelectWhichAttribute',
      payload: {
        currentObject,
      },
    })
  }

  changeSide(index) {
    this.setState({
      currentSlide: index,
    })
    setTimeout(() => {
      this.getCurrentSlide()
      this.move()
    })
  }

  getPhoneNumber = async (e) => {
    let user = Taro.getStorageSync('userInfo') && JSON.parse(Taro.getStorageSync('userInfo'))
    let { canClick } = this.state
    if (canClick) {
      if (!user.nickName && !user.mobile) {
        if (e.detail.errMsg === 'getPhoneNumber:ok') {
          await Taro.showToast({
            title: '授权中...',
            icon: 'loading',
          })
          this.props.dispatch({
            type: 'NUser/EPutUserPhoneNew',
            payload: {
              encryptedData: e?.detail?.encryptedData,
              iv: e?.detail?.iv,
            },
          })
          this.debugaa()
        }
      } else {
        this.debugaa()
      }
    }
  }

  debugaa() {
    let { whichAppliances } = this.props
    let { currentObject } = this.state
    whichAppliances.item[0].wasteAttribute1ID = currentObject[0].name
    whichAppliances.item[0].wasteAttribute2ID = currentObject[1].name
    whichAppliances.item[0].wasteAttribute3ID = currentObject[2] ? currentObject[2].name : null
    whichAppliances.item[0].wasteAttribute4ID = currentObject[3] ? currentObject[3].name : null
    whichAppliances.item[0].wasteAttribute5ID = currentObject[4] ? currentObject[4].name : null
    whichAppliances.item[0].wasteAttribute6ID = currentObject[5] ? currentObject[5].name : null
    whichAppliances.item[0].wasteAttribute7ID = currentObject[6] ? currentObject[6].name : null

    this.props.dispatch({
      type: 'NOldGoods/ESetState',
      payload: {
        haveChosen: [whichAppliances],
      },
    })
    this.createOrder()
  }

  createOrder() {
    Taro.navigateTo({ url: '/pages/uploadImg/index' })
  }

  onScrollToUpper() { }

  // or 使用箭头函数
  // onScrollToUpper = () => {}

  onScroll(e) {
  }

  move() {
    let { attributeType } = this.props
    let { currentSlide } = this.state
    this.setState({
      scrollLeft: attributeType.length > 4 && currentSlide > 2 ? 185 * (currentSlide - 2) : 0,
    })
  }

  //-----------------------渲染-------------------------//
  render() {
    const Threshold = 20

    let { currentSelect, currentSlide, canClick, scrollLeft, locale } = this.state
    let { attribute, attributeType, whichAppliances, isAddItem, attributePrice, basicPrice, whichType } = this.props
    return (
      <View className="attributeSelect">
        <View className="which">
          <View className="left_wrapper">
            <Image src={whichAppliances.item[0].img} mode={'aspectFit'} />
          </View>
          <View className="right_wrapper">
            <View>
              {locale == 'en' ? whichAppliances.enName : whichAppliances.name}-
              {locale == 'en' ? whichAppliances.item[0].enName : whichAppliances.item[0].name}
            </View>
            <View>
              <Image mode="heightFix" src={'https://oss.evergreenrecycle.cn/donggua/client/images/tishi.png'} style={{ height: "30rpx" }} />
              {whichType && whichType.id === '3' ? <View>{T.attributeSelectPage.pay}</View> : null}
            </View>
          </View>
        </View>
        <View className="wrapper">
          <ScrollView
            className="devide"
            scrollX
            scrollWithAnimation
            scrollLeft={scrollLeft}
            lowerThreshold={Threshold}
            upperThreshold={Threshold}
            onScrollToUpper={this.onScrollToUpper.bind(this)}
            onScroll={this.onScroll}
          >
            {attributeType && attributeType.length > 0
              ? attributeType.map((value, index) => (
                <View
                  className={`demo-text-1 ${currentSlide === index ? 'showed' : ''}`}
                  key={index + 'a'}
                  onClick={() => {
                    this.changeSide(index)
                  }}
                >
                  <Text>{locale == 'en' ? value.enName : value.name}</Text>
                  {currentSlide === index ? <View className="show_which"></View> : null}
                </View>
              ))
              : null}
          </ScrollView>
        </View>

        <View className="select_wrapper">
          {attribute && attribute.length > 0
            ? attribute.map((item, mark) => (
              <View
                className={`select_item ${currentSelect.indexOf(item.name) >= 0 ? 'selected' : ''}`}
                key={mark + 'b'}
                onClick={() => {
                  this.selectValue(item)
                }}
              >
                {locale == 'en' ? item.enName : item.name}
                {/* {item.name} */}
              </View>
            ))
            : null}
        </View>

        <View className="oldGoods_bottom">
          <Button
            className="button_getUser top_wrapper"
            open-type="getPhoneNumber"
            style={{ opacity: 0 }}
            onGetPhoneNumber={this.getPhoneNumber}
          >
            <View className="oldGoods_bottom_shoppingCar">
              <Text style={canClick ? { color: '#444444' } : null}>
                {T.attributeSelectPage.appraisal}：{canClick ? <Text style={{ color: "#FF8D1A" }}>¥{basicPrice + attributePrice}</Text> : null}
              </Text>
            </View>
            <View className={`oldGoods_bottom_text ${canClick && 'canClick'}`}>

            </View>
          </Button>
          {/* <View className="top_wrapper"></View> */}
          {isAddItem ? <View className="bottom_wrapper"></View> : null}
        </View>
      </View>
    )
  }
}
export default connect(
  ({ NOldGoods: { attributeType, attribute, whichAppliances, attributePrice, basicPrice, whichType }, NSystem: { isAddItem } }) => ({
    attributeType,
    attribute,
    whichAppliances,
    isAddItem,
    attributePrice,
    basicPrice,
    whichType,
  })
)(Appliances)
