'use strict'

const _ = require('lodash')
const moment = require('moment')

const { AdminUser, User, Company, AdminUserPermission, JoinTeam, JDAdminUser } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { CryptUtil } = require('../../../Util')
const { GovTranserService } = require('../../../Services')
const Env = use('Env')
// 后台用户
class AdminUserController {
  async autoLogin({ request, response }) {
    const payload = CryptUtil.jwtDecode(request.header('Authorization') || request.input('token'))
    // console.log('payload: ', payload)
    if (payload && payload.adminID) {
      let adminUser = await AdminUser.query().where('id', payload.adminID).with('authority').first()
      return {
        needLogin: false,
        token: CryptUtil.jwtEncode({ adminID: adminUser.id }),
        adminUser,
      }
    } else {
      return { needLogin: true }
    }
  }
  async login({ request, response }) {
    let { username, password } = request.all()
    let vo = null
    let enPw = CryptUtil.md5(CryptUtil.encryptData256(password, Env.get('APP_KEY')))
    if (!username || !password) {
      throw ERR.SQL_DUP_NAME_OR_PASSWORD
    }
    // console.log('env', enPw)
    vo = await AdminUser.query().where('username', username).where('password', enPw).with('company').with('authority').first()
    if (!vo) {
      throw ERR.INVALID_PASSWORD
    } else if (vo.forbidden === 0) {
      throw ERR.USER_FORBIDDEN
    } else {
      const userObj = vo.toJSON()
      response.json({
        token: CryptUtil.jwtEncode({ adminID: vo.id }),
        username,
        adminUser: vo,
      })
    }
  }
  async JDlogin({ request, response }) {
    let { username, password } = request.all()
    let vo = null
    let enPw = CryptUtil.md5(CryptUtil.encryptData256(password, Env.get('APP_KEY')))
    if (!username || !password) {
      throw ERR.SQL_DUP_NAME_OR_PASSWORD
    }
    vo = await JDAdminUser.query().where('username', username).where('password', enPw).with('company').with('authority').first()
    if (!vo) {
      throw ERR.INVALID_PASSWORD
    } else if (vo.forbidden === 0) {
      throw ERR.USER_FORBIDDEN
    } else {
      const userObj = vo.toJSON()
      response.json({
        token: CryptUtil.jwtEncode({ adminID: vo.id }),
        username,
        adminUser: vo,
      })
    }
  }
  //登录账户列表
  async index({ request, response }) {
    let { adminUser } = request
    let { current = 1, pageSize = 10, username, sort = 'desc', level } = request.all()
    let { companyID } = adminUser
    let query = AdminUser.query().where('username', '!=', 'jeff').with('company')
    companyID = parseInt(companyID)
    if (companyID && companyID !== 36) {
      query.where('companyID', companyID)
    }
    if (level) {
      query.where('level', level)
    }
    if (username) {
      query.whereRaw('username like ?', [`%${username}%`])
    }
    let vo = await query.with('authority').orderBy('id', sort).paginate(current, pageSize)
    response.json(vo)
  }
  //创建登录账户
  async store({ request, response }) {
    let { adminUser } = request
    let { username, password, permission, name } = request.all()
    if (!username || !password) {
      throw ERR.INVALID_PARAMS
    }
    let companyID = adminUser.companyID
    let authority = await AdminUserPermission.query().where('companyID', companyID).where('name', permission).first()
    if (!authority) {
      authority = await AdminUserPermission.create({ name: permission, companyID })
    }
    let level = adminUser.level
    let enPw = CryptUtil.md5(CryptUtil.encryptData256(password, Env.get('APP_KEY')))
    let vo = await AdminUser.create({ permission, name, username, password: enPw, companyID, level, authorityID: authority.id, forbidden: 0 })
    response.json(vo)
  }
  // 获取登录人员信息
  async getUserInfo({ request, params, response }) {
    let adminUserID = request.adminID
    let vo = await AdminUser.query().where('id', adminUserID).with('company').with('authority').first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
  //登录人员信息修改
  async update({ request, params, response }) {
    let { adminUser } = request
    let { companyID } = adminUser
    let { password, username, permission, forbidden, name } = request.all()
    let vo = await AdminUser.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    // if (adminUser.level !== vo.level) {
    //   throw ERR.RESTFUL_GET_AUTH
    // }
    if (password) {
      let enPw = CryptUtil.md5(CryptUtil.encryptData256(password, Env.get('APP_KEY')))
      let company = await Company.query().where('userName', vo.username).where('id', vo.companyID).first()
      if (company) {
        company.userName = username
        await company.save()
      }
      vo.password = enPw
      vo.username = username
      vo.name = name
    } else {
      if (forbidden === 0 || forbidden === 1) {
        vo.forbidden = forbidden
      } else {
        let authority = await AdminUserPermission.query().where('companyID', companyID).where('name', permission).first()
        if (!authority) {
          authority = await AdminUserPermission.create({ name: permission, companyID })
        }
        vo.authorityID = authority.id
      }
    }
    await vo.save()
    response.json(vo)
  }
  async joinTeam({ request, params, response }) {
    let { address, carType, cert, company, name, phone, type, teamType, masterNum } = request.all()
    if (company && name && phone) {
      let vo = await JoinTeam.create({ address, carType, cert, company, name, phone, type, teamType, masterNum })
      response.json(vo)
    }
  }
}

module.exports = AdminUserController
