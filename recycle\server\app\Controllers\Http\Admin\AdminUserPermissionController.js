'use strict'

const _ = require('lodash')
const moment = require('moment')

const { AdminUserPermission } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { CryptUtil } = require('../../../Util')
const Env = use('Env')
//后台权限
class AdminUserPermissionController {
  //获取后台权限内容 根据服务商及权限名
  async index({ request, response }) {
    let { name } = request.all()
    if (!name) {
      throw ERR.INVALID_PARAMS
    }
    let { adminUser } = request
    let { companyID } = adminUser
    let query = AdminUserPermission.query().where('companyID', companyID)
    let vo = await query.where('name', name).first()
    response.json(vo)
  }
  //后台权限内容创建
  async store({ request, response }) {
    let { name, key } = request.all()
    if (!name || !key) {
      throw ERR.INVALID_PARAMS
    }
    let { adminUser } = request
    let { companyID } = adminUser
    let vo = await AdminUserPermission.query()
      .where('name', name)
      .where('companyID', companyID)
      .first()
    if (!vo) {
      vo = await AdminUserPermission.create({ name, companyID, key })
    } else {
      vo.key = key
      await vo.save()
    }
    response.json(vo)
  }
}

module.exports = AdminUserPermissionController
