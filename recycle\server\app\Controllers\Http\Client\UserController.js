'use strict'

const _ = require('lodash')
const moment = require('moment')
const Env = use('Env')

const { User, UserAddress, WorkerInvite,  } = require('../../../Models')
const { CryptUtil, Configs } = require('../../../Util')
const { ERR } = require('../../../../../../constants')
const { WXService } = require('../../../Services')
class UserController {
  async test({ request, params, response }) {
    let code = request.input('code')
    let { result, error } = await WXService.code2Session(code)
    response.json(result)
  }
  async testSync({ request, params, response }) {
    response.json('test sync')
  }
  async login({ request, response }) {
    let { openID, code, salesman } = request.all()
    let vo = null
    let { result, error } = await WXService.code2Session(code, Configs.Client.AppID, Configs.Client.AppSecret)
    // console.log('result: ', result)
    if (code) {
      if (error) {
        throw error
      } else {
        openID = result.openid
        vo = await User.findBy('openid', result.openid)
        if (vo) {
          vo.sessionKey = result.session_key
          await vo.save()
        } else {
          const createData = {
            openid: result.openid,
            sessionKey: result.session_key,
          }
          if (salesman) {
            createData.salesman_id= salesman
          }
          vo = await User.create(createData)
        }
      }
    } else if (openID) {
      vo = await User.findBy('openid', openID)
      if (vo) {
        vo.sessionKey = result.session_key
        await vo.save()
      }
    }
    if (!vo) {
      response.json({
        token: 0,
        openID,
      })
    } else {
      response.json({
        token: CryptUtil.jwtEncode({ userID: vo.id }),
        openID,
        user: vo,
      })
    }
  }
  async register({ request, response }) {
    try {
      let { openid } = request.all()
      if (!openid) {
        throw ERR.INVALID_PARAMS
      }
      let isReg = await User.findBy('openid', openid)
      if (isReg) {
        throw ERR.USER_EXISTS
      }

      let vo = await User.create({ openid })
      response.json({
        token: CryptUtil.jwtEncode({ userID: vo.id }),
        user: vo,
      })
    } catch (e) {
      response.json(e)
    }
  }
  async userAll({ request, params, response }) {
    let users = await User.all()
    response.json(users)
  }

  //获取手机号
  async getWxMobile({ request, params, response }) {
    let { code, encryptedData, iv } = request.all()
    // console.log(code, encryptedData, iv)
    let res = await WXService.getMobileNumber(code, encryptedData, iv)
    response.json(res)
  }
  //个人信息更新
  async update({ request, params, response }) {
    // let { userID } = request.user

    let vo = await User.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
  }
  async show({ request, params, response }) {
    try {
      let vo = await User.query().where('id', params.id).first()
      if (!vo) {
        throw ERR.RESTFUL_GET_ID
      }
      response.json(vo)
    } catch (e) {
      response.json(e)
    }
  }
  //用户地址列表
  async userAddress({ request, params, response }) {
    let { user } = request
    // console.log('user', user)
    let { sort = 'desc' } = request.all()
    try {
      let vo = await UserAddress.query().where('userID', user.id).orderBy('id', sort).fetch()
      if (!vo) {
        throw ERR.RESTFUL_GET_ID
      }
      response.json(vo)
    } catch (e) {
      response.json(e)
    }
  }
  //用户地址查询
  async searchUserAddress({ request, params, response }) {
    let { user } = request
    try {
      let { realname, mobile } = request.all()
      let vo = await UserAddress.query()
        .where('userID', user.id)
        .where(function () {
          this.where('realname', 'like', `%${realname}%`).orWhere('mobile', 'like', `%${mobile}%`)
        })
        .fetch()
      if (!vo) {
        throw ERR.RESTFUL_GET_ID
      }
      response.json(vo)
    } catch (e) {
      response.json(e)
    }
  }
  //设置默认地址
  async setDefaultUserAddress({ request, params, response }) {
    let { user } = request
    try {
      let { userAddressID } = request.all()

      await UserAddress.query().where('userID', user.id).update({ isDefault: 0 })

      await UserAddress.query().where('id', userAddressID).update({ isDefault: 1 })
      response.json({ error: 0, message: 'ok' })
    } catch (e) {
      throw ERR.API_ERROR
    }
  }

  async getPhone({ request, params, response }) {
    let user = request.user
    // console.log('user: ', user.sessionKey)
    let { iv, encryptedData } = request.all()
    let { phoneNumber } = WXService.clientFetchPhoneNumber(encryptedData, iv, user.sessionKey)
    if (phoneNumber) {
      user.mobile = phoneNumber
      await user.save()
    }
    response.json({ mobile: user.mobile })
  }
  async postEvaluate({ request, response }) {
    let { user } = request
    let { expressionID, reason, selectReason, workerID, userID } = request.all()
    try {
      let res = await WorkerInvite.create({ expressionID, reason, selectReason, workerID, userID })
      response.json({ code: 200, message: 'ok', data: res })
    } catch (error) {
      response.json(error)
    }
  }
}

module.exports = UserController
