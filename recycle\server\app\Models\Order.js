'use strict'

const moment = require("moment")

const Model = use('Model')

//订单
class Order extends Model {
  static get table() {
    return 'new_order'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return 'updatedAt'
  }
  getTakeTime(value) {
    return value && moment(value).format('YYYY-MM-DD HH:mm:ss')
  }
  setTakeTime(value) {
    return value && moment(value).format('YYYY-MM-DD HH:mm:ss')
  }
  getImgJson(value) {
    return value && JSON.parse(value)
  }
  setImgJson(value) {
    return value && JSON.stringify(value)
  }
  worker() {
    return this.belongsTo('App/Models/Worker', 'workerID', 'id')
  }
  sms() {
    return this.belongsTo('App/Models/LogMsg', 'id', 'orderID')
  }
  sales() {
    return this.belongsTo('App/Models/Salesman', 'salesman_id', 'id').with('manager')
  }

  orderWastes() {
    return this.hasMany('App/Models/OrderWaste', 'id', 'orderID')
  }
  company() {
    return this.belongsTo('App/Models/Company', 'companyID', 'id')
  }
  rating() {
    return this.belongsTo('App/Models/OrderRating', 'id', 'orderID')
  }
  cancel() {
    return this.hasMany('App/Models/OrderCancel', 'id', 'orderID').with('worker')
  }
  complaint() {
    return this.belongsTo('App/Models/OrderComplaint', 'id', 'orderID')
  }
  pay() {
    return this.hasMany('App/Models/Pay', 'id', 'orderID').whereNotNull('finishAt')
  }
  doneInfo() {
    return this.hasOne('App/Models/WorkerWalletLog', 'id', 'orderID')
  }
  trackInfo() {
    return this.hasOne('App/Models/TrackLog', 'id', 'orderID')
  }
}

module.exports = Order
