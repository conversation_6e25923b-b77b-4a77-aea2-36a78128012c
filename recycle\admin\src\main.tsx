import React from 'react'
import ReactDOM from 'react-dom'
import App from './pages/App'
import './index.css'
import { initModels, initRequest, reducer } from 'dva17'
import { Provider } from 'react-redux'
import models from './models'
import { ConfigProvider, notification } from 'antd'
import { KEY_TOKEN, SERVER_HOME } from './common/config'
import { HashRouter } from 'react-router-dom'
import moment from 'moment';
import 'moment/dist/locale/zh-cn';
moment.locale('zh-cn');
import zhCN from "antd/lib/locale/zh_CN";

initRequest(
  SERVER_HOME,
  (status, data: any) => {
    console.log('status, message: ', status, data)
    if (401 == status) {
      confirm('登录失效，请重新登录')
      localStorage.removeItem(KEY_TOKEN)
      location.reload() //刷新页面重新登录
    } else {
      let { error, message } = data
      notification.error({ message, duration: 2 })
    }
  },
  false //是否打印request记录
)
// 设置CSS变量

document.documentElement.style.setProperty('--theme-color', '#ffffff');

ReactDOM.render(
  <ConfigProvider locale={zhCN}>
    <Provider store={initModels(models, false /*是否打印dva17记录 */) as any}>
      <HashRouter>
        <App />
      </HashRouter>
    </Provider>
  </ConfigProvider>,
  document.getElementById('root')!
)
