import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'

export async function getWorkerReview(payload: any) {
  return requestPost('/JDworker/workerReview', payload)
}
export async function getWorkerMaintain(payload: any) {
  return requestPost('/JDworker/workerMaintain', payload)
}
export async function putWorkerMaintain(payload: any) {
  return requestPut(`/JDworker/workerMaintain/${payload.id}`, payload)
}
export async function getFullTimeWorker(payload: any) {
  return requestGet('/JDworker', payload)
}
export async function postWorkerWallet(payload: any) {
  return requestPost('/JDworkerCharge', payload)
}
export async function getWorkerWalletLog(payload: any) {
  return requestGet('/JDwalletlog', payload)
}
export async function getDetailWorker(payload: any) {
  return requestGet(`/JDworker/${payload.id}`)
}
export async function getAddress(payload: any) {
  return requestGet('/address', payload)
}
export async function changeWorkerUse(payload: any) {
  return requestPut(`/JDworker/${payload.id}`, payload)
}
export async function addWorkAddress(payload: any) {
  return requestPost('JDworker/addWorkAddress', payload)
}
export async function removeWorkAddress(payload: any) {
  return requestPost('JDworker/removeWorkAddress', payload)
}
export async function getTownList(payload: any) {
  return requestPost('JDworker/getTown', payload)
}
export async function getWorkerArealist(payload: any) {
  return requestGet(`getWorkerArea/${payload.id}`, payload)
}
export async function getTheWorkerList(payload: any) {
  return requestPost('JDworker', payload)
}
export async function postWorkerInsure(payload: any) {
  return requestPost('workerInsure', payload)
}
export async function getWorkerInsure(payload: any) {
  return requestGet('workerInsure', payload)
}
export async function putWorkerInsure(payload: any) {
  return requestPut(`workerInsure/${payload.id}`, payload)
}
export async function delWorkerInsure(payload: any) {
  return requestDelete(`workerInsure/${payload.id}`, payload)
}
export async function getWorkerPaylist(payload: any) {
  return requestGet('/JDworkerPaylist', payload)
}

export async function postWorkerRefund(payload: any) {
  return requestPost('masterPayRefund', payload)
}
export async function workerAreaEdit(payload: any) {
  return requestPost('workerArea', payload)
}
export async function getWorkerSelected(payload: any) {
  return requestGet('workerArea', payload)
}
export async function workerDeleteArea(payload: any) {
  return requestDelete(`workerArea/${payload.workerID}`, payload)
}
export async function deleteWorker(payload: any) {
  return requestDelete(`JDworker/${payload.id}`, payload)
}
export async function deleteCoWorker(payload: any) {
  return requestDelete(`JDCoWorker/${payload.id}`, payload)
}

export async function putcoworker(payload: any) {
  return requestPut(`JDworker/coworker/${payload.id}`, payload)
}
export async function postcoworker(payload: any) {
  return requestPost('JDworker/coworker', payload)
}

export async function changeYCWorkerUse(payload: any) {
  return requestPut(`ycworker/${payload.id}`, payload)
}
export async function postYCWorkerWallet(payload: any) {
  return requestPost('ycworkerCharge', payload)
}
export async function getYCWorkerMaintain(payload: any) {
  return requestPost('ycworkerMaintain', payload)
}
export async function putYCWorkerMaintain(payload: any) {
  return requestPut(`ycworkerMaintain/${payload.id}`, payload)
}
export async function getYCWorkerPaylist(payload: any) {
  return requestGet('/workerPaylist', payload)
}
export async function getYCWorkerWalletLog(payload: any) {
  return requestGet('/ycwalletlog', payload)
}