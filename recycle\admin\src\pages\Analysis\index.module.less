.main{
  // border:1px solid red;
      background-color: #fff;
      padding:30px 20px;
      display: flex;
      flex-direction: column;
      .main_changeData{
          padding-bottom:20px;
      }
      .main_head{
          display: flex;
          justify-content:space-between;
          align-items:center;
          .main_head_item{
              display: flex;
              border-radius: 5px;
              padding:0 5px;
              border: 1px solid #dcdfe6;
              .main_head_item_icon{
                  width: 66px;
                  height: 66px;
                  border-radius:10px;
                  Padding:16px;
                  object-fit: cover;
                  object-position: center;
                  background-color: rgba(23,210,159,1);
                  margin:auto 10px;
              }
              .main_head_item_{
                  height: 110px;
                  display: flex;
                  flex-wrap: wrap;
                  align-content: center;
              }
              .main_head_item_text{
                  width: 100%;
                  color: #333333;
                  font-family:"Microsoft YaHei";
              }
          }
      }
      .main_dataAnalysis{
          height: 300px;
          // border: 1px solid #e8ecf3;
          margin-top: 30px;
          // padding: 25px 20px;
          display: flex;
          justify-content: space-between;
          .main_dataAnalysis_left{
              width: 63%;
              height: 300px;
              border: 1px solid #e8ecf3;
              padding: 25px 20px;
              .left_nav{
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  .left_nav_title{
                      font-size:16px;
                      line-height:28px;
                      height: 28px;
                      color: #333333;
                      font-weight: 700;
                      text-indent: 5px;
                      border-left:7px solid rgba(23,210,159,1);
                  }
                  .left_nav_button{
                      display: flex;
                      align-items: center;
                      width: 60%;
                      .button_{
                          background: #fff;
                          width: 121px;
                          padding: 0;
                          border: 1px solid #e8ecf3;
                          border-left: 0;
                          overflow: hidden;
                          display: flex;
                          justify-content: space-between;
                          align-items: center;
                          .button_type{
                              display:inline-block;
                              width: 60px;
                              height: 34px;
                              line-height: 34px;
                              font-size: 12px;
                              color: #57595d;
                              text-align: center;
                              border-left: 1px solid #e8ecf3;
                              cursor: pointer;
                          }
                      }
                      .button_box{
                          width: 50px;
                          height: 34px;
                          line-height: 34px;
                          font-size: 12px;
                          color: #57595d;
                          text-align: center;
                          cursor: pointer;
                          // border:1px solid red;
                      }
                  }
              }
              .box{
                  width: 100%;
                  height:90%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  // border: 1px solid red;
                  .box_AddData{
                      width: 500px;
                      height: 100px;
                      line-height: 100px;
                      text-align: center;
                      font-size: 16px;
                      font-weight: 550;
                      border: 1px solid #e8ecf3;
                      box-shadow: 0 0 5px 5px #e8ecf3;
                  }
              }
          }
          .main_dataAnalysis_right{
              width: 34%;
              height: 300px;
              border: 1px solid #e8ecf3;
              padding: 25px 20px;
              .right_item{
                  height: 28px;
                  border-left:7px solid rgba(23,210,159,1);
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin:10px 0;
                  .item_left{
                      display: inline-block;
                      font-size:16px;
                      line-height:28px;
                      font-weight: 700;
                      text-indent: 5px;
                      color: #333333;
                      height: 28px;
                  }
                  .item_right{
                      display: inline-block;
                      line-height:28px;
                      height: 28px;
                      font-size: 12px;
                      color: rgba(23,210,159,1);
                      cursor: pointer;
                  }
              }
              .right_item_{
                  font-size: 12px;
                  color: #57595d;
                  margin: 10px 0 0 15px;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
              }
          }
      }
      .main_map{
          .main_map_title{
              font-size: 16px;
              color: #333;
              margin: 30px 0 20px;
          }  
          .main_map_img{
              width: 100%;
              object-fit: cover;
              object-position: center;
          }
      }
  }