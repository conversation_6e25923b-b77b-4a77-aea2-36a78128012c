.uploadImg {
  width: 100vw;
  min-height: 100vh;
  padding-bottom: 23.2vw;
  //    padding-top: ;
  background-color: #ffffff;

  .upload_img {
    width: 100vw;
    min-height: 63vw;
    padding-bottom: 3vw;
    border-bottom: 2.7vw solid #f3f3f3;
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;
    .theImage {
      position: absolute;
      left: 31.8%;
      top: 50%;
      transform: translateY(-50%);
    }
    .upload_img_box_button {
      width: 27.7vw;
      height: 27.7vw;
      border-radius: 1vw;
      background-color: rgba(0, 0, 0, 0.02);
      border: 1px solid rgba(0, 0, 0, 0.15);
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      .upload_img_Icon {
        width: 13.9vw;
        height: 13.9vw;
      }
      .upload_img_Icon_text {
        position: absolute;
        bottom: -7vw;
        left: 0;
        right: 0;
        margin: 0 auto;
        color: rgba(0, 0, 0, 0.45);
        font-size: 3.7vw;
        text-align: left;
        font-family: PingFangSC-regular;
      }
    }
    .img_wrapper {
      width: 27.7vw;
      height: 27.7vw;
      margin-left: 4.225vw;
      margin-top: 3vw;
      position: relative;
      border-radius: 1vw;
      > image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 1vw;
      }
    }
  }

  .alreadyImg {
    border: 1px solid red;
    width: 92.5vw;
    margin: 0 auto;
    min-height: 63vw;
    border-bottom: 2.7vw solid #f3f3f3;
    display: flex;
    padding: 4.5vw 0;
    // justify-content: space-around;
    flex-wrap: wrap;

    .upload_img_box {
      width: 27.7vw;
      height: 27.7vw;
      border-radius: 1vw;
      background-color: rgba(0, 0, 0, 0.02);
      border: 1px solid rgba(0, 0, 0, 0.15);
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 4.2vw;
      position: relative;
      .upload_img_Icon {
        width: 13.9vw;
        height: 13.9vw;
        //  background-color: rgba(204, 204, 204, 1);
      }
    }

    .upload_img_size {
      display: block;
      width: 27.7vw;
      height: 27.7vw;
      object-fit: cover;
      object-position: center;
      // overflow: hidden;
    }
  }

  .have_chosen {
    width: 89.1vw;
    margin: 0 auto;

    .have_chosen_title {
      height: 16vw;
      display: flex;
      align-items: center;
      width: 100%;
      .have_chosen_title_text {
        display: flex;
        align-items: center;
        view:first-of-type {
          height: 28px;
          width: 8px;
          background: linear-gradient(to right, rgba(119, 220, 143, 1) 0% , rgba(21, 179, 129, 1) 100%);
          margin-right: 28px;
        }
      }

      .waring_wrapper {
        margin-left: 2vw;
        flex: 1;
        display: flex;
        justify-content: space-between;
        font-size: 3.2vw;
        color: #666666;
        > Text {
          &:last-child {
            border-bottom: 1px solid #666666;
          }
        }
      }
    }

    .have_chosen_page_ul {
      height: 16vw;
      display: flex;
      align-items: center;
      .have_chosen_ul_type {
        height: 5.4vw;
        display: inline-block;
        border-left: 1vw solid #808080;
        text-indent: 3.5vw;
        color: rgba(102, 102, 102, 1);
        font-size: 4vw;
        text-align: left;
        font-family: PingFangSC-bold;
      }

      .have_chosen_ul_num {
        color: rgba(102, 102, 102, 1);
        font-size: 3.2vw;
        text-align: left;
        font-family: PingFangSC-regular;
      }
    }

    .have_chosen_page_li {
      height: 68px;
      display: flex;
      align-items: center;
      margin-left: 4.3vw;
      background-color: #f5f5f5;
      padding-left: 16px;
      color: #15b381;
      font-size: 30px;
      position: relative;
      top: 0%;
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        right: 0;
        width: 36px;
        height: 36px;
        background: url('https://oss.evergreenrecycle.cn/donggua/client/images/gouxuan.png') center no-repeat;
        background-size: 36px 36px;
      }
      .have_chosen_page_li_text {
        display: inline-block;
        text-align: left;
        font-family: PingFangSC-regular;
      }
    }
  }

  .uploadImg_button {
    position: fixed;
    left: 50px;
    bottom: 20px;
    width: 100%;
    display: flex;
    justify-content: center;
    width: 650px;
    border-radius: 45px;
    background: #e5e5e5;
    color: #999999;
    &.isClick {
      background: #15b381;
      color: #fff;
    }
    // background: linear-gradient(to right, rgba(119, 220, 143, 1) 0% , rgba(21, 179, 129, 1) 100%);
    //border: 1px solid rgba(255, 255, 255, 0);
    .text_wrapper {
      display: flex;
      height: 90px;
      justify-content: center;
      align-items: center;
    }
    .add_item {
      height: 32px;
      width: 100%;
    }
  }
}

.delete_button {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: -1vw;
  top: -2.5vw;
  z-index: 20;
  image {
    width: 4.2vw;
    height: 4.3vw;
  }
}
.remind_wrapper {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  letter-spacing: 1.2px;
  .message_wrapper {
    height: 896px;
    width: 600px;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    .remindTitle {
      font-size: 30px;
      color: #333333;
      font-weight: 700;
      margin-top: 34px;
      margin-bottom: 50px;
      display: inline-block;
    }
    .content_wrapper {
      width: 500px;
      height: 620px;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      .remind_text {
        font-size: 24px;
        color: #7c8696;
        line-height: 36px;
        margin-bottom: 30px;
      }
      .remind_title {
        font-size: 28px;
        color: #444444;
        margin-bottom: 20px;
      }
    }
    .know_button_wrapper {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 40px;
      display: flex;
      flex-direction: column;
      align-items: center;
      .know_button {
        height: 88px;
        width: 500px;
        background: #15b381;
        line-height: 88px;
        text-align: center;
        border-radius: 44px;
        color: #ffffff;
        font-size: 30px;
        font-weight: 700;
      }
      .radio {
        font-size: 20px;
        color: #7c8696;
        margin-bottom: 20px;
        //    未选中的 背景样式
        .wx-radio-input {
          width: 20px;
          height: 20px;
          margin-right: 16px;
        }

        // 选中后的 背景样式 （红色背景 无边框 可根据UI需求自己修改）
        .wx-radio-input.wx-radio-input-checked {
          border-color: #15b381 !important;
          background: #15b381 !important;
        }

        .wx-radio-input.wx-radio-input-checked::before {
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          font-size: 15px;
          color: #fff;
          background: transparent;
          transform: translate(-50%, -50%) scale(1);
          -webkit-transform: translate(-50%, -50%) scale(1);
        }
      }
    }
  }
}
