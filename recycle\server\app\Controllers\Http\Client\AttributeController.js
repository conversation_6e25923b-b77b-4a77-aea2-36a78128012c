'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Attribute, CompanySelfAttribute } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//废品属性
class AttributeController {
  //获取废品属性列表
  async show({ request, params, response }) {
    let { companyID = 1 } = request.all()
    let vo = await Attribute.query().where('typeID', params.id).fetch()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    for (let i = 0; i < vo.rows.length; i++) {
      let current = await CompanySelfAttribute.query().where('attributeID', vo.rows[i].id).where({ companyID }).first()
      if (current) {
        vo.rows[i].minusPrice = Math.abs(current.minusPrice)
        vo.rows[i].plusPrice = Math.abs(current.plusPrice)
      }
    }
    response.json(vo)
  }
}

module.exports = AttributeController
