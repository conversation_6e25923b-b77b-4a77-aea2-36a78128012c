'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Company, AdminUser, AdminUserPermission, CompanyArea, ReqLog } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { CryptUtil } = require('../../../Util')
const Env = use('Env')
const { judgeAuthority } = require('../../../Services/UserService')

//服务商信息
class CompanyController {
  //服务商的列表
  async index({ request, response }) {
    let { current = 1, pageSize = 10, companyName, mobile, sort = 'desc' ,forbidden} = request.all()
    let { adminUser } = request
    let { companyID } = adminUser
    let query = Company.query().where('deletedAt', 0).with('order')
    companyID = parseInt(companyID)
    if (companyID && companyID !== 36) {
      query.where('id', companyID)
    }
    if (companyName) {
      query.whereRaw('companyName like ?', [`%${companyName}%`])
    }
    if (forbidden) {
      query.where('forbidden', forbidden)
    }
    if (mobile) {
      query.whereRaw('mobile like ?', [`%${mobile}%`])
    }
    let vo = await query.orderBy('id', sort).paginate(current, pageSize)
    response.json(vo)
  }
  //服务商详情
  async show({ request, params, response }) {
    let vo = await Company.query()
      .where('id', params.id)
      .first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
  //服务商的创建
  async store({ request, response }) {
    let { adminUser: user } = request
    if (user.level !== E.AdminLevel.总部) {
      throw ERR.USER_ROLE_NO_PRIVILEGE
    }
    let { companyName, mobile, userName, password, contactPerson, email, province, city, district, address, addressCode } = request.all()
    if (!companyName || !mobile || !userName || !password || !contactPerson || !email || !province || !city || !district || !address || !addressCode) {
      throw ERR.INVALID_PARAMS
    }
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台 删除服务商创建' })
    let vo = await Company.create({
      companyName,
      mobile,
      contactPerson,
      email,
      province,
      city,
      district,
      address,
      userName,
      addressCode
    })
    let companyID = vo.id
    let enPw = CryptUtil.md5(CryptUtil.encryptData256(password, Env.get('APP_KEY')))
    let permission = await AdminUserPermission.create({ companyID, name: "高级", key: `["订单管理","订单查看","订单操作","回收价格查看","账户与权限管理","账户的查看","账户新建与编辑","权限编辑","数据查询","业务数据总览","数据导出","回收人员查看","回收人员管理","回收人员编辑","回收人员审核","回收价格编辑","回收价格管理"]` })
    let adminUser = await AdminUser.create({
      username: userName,
      password: enPw,
      companyID,
      level: E.AdminLevel.服务商,
      forbidden: 1,
      authorityID: permission.id
    })
    response.json(vo)
    await ReqLog.create({ res: JSON.stringify(vo), source: '后台 服务商创建' })
  }
  //服务商信息更新
  async update({ request, params, response }) {
    let { adminUser } = request
    // let result = await judgeAuthority(adminUser, '服务商新建与编辑')
    // if (!result) {
    //   throw ERR.USER_ROLE_NO_PRIVILEGE
    // }
    let { password, userName } = request.all()
    let vo = await Company.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    if (userName && password) {
      let adminUser = await AdminUser.query()
        .where('companyID', params.id)
        .first()
      if (!adminUser) {
        throw ERR.RESTFUL_UPDATE_ID
      }
      let enPw = CryptUtil.md5(CryptUtil.encryptData256(password, Env.get('APP_KEY')))
      adminUser.password = enPw
      adminUser.username = userName
      await adminUser.save()
      vo.userName = userName
      await vo.save()
    } else {
      _.assign(vo, request.all())
      await vo.save()
    }
    response.json(vo)
  }
  //服务商删除
  async destroy({ request, params, response }) {
    let { adminUser: user } = request
    if (user.level !== E.AdminLevel.总部) {
      throw ERR.USER_ROLE_NO_PRIVILEGE
    }
    // await judgeAuthority(user, '服务商新建与编辑')
    let vo = await Company.find(params.id)
    await ReqLog.create({ req: params.id, source: '后台 删除服务商' })
    let adminUsers = AdminUser.query().where('companyID', params.id)
    if (adminUsers) {
      await adminUsers.delete()
    }
    if (!vo) {
      throw ERR.RESTFUL_DELETE_ID
    }
    vo.deletedAt = moment().unix()
    vo.forbidden = 0
    await vo.save()
    await this._getCompanyArea(params.id)
    response.json(vo)
  }
  async _getCompanyArea(companyID) {
    let theArea = CompanyArea.query().where('companyID', companyID)
    await theArea.delete()
    // await theArea.delete()
  }
}

module.exports = CompanyController
