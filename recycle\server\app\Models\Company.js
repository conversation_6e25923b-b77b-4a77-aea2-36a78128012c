'use strict'

const Model = use('Model')
const Database = use('Database')

//公司信息
class Company extends Model {
	static get table() { return 'company' }
	static get primaryKey() { return 'id' }
	static get createdAtColumn() { return 'createdAt' }
	static get updatedAtColumn() { return 'updatedAt' }
	order() {
		return this.hasMany('App/Models/Order', 'id', 'companyID')
			.select(Database.raw('companyID,sum(infoFee)/10000 as totalMoney,count(id) as count'))
			.groupBy('companyID')
			.whereNotNull('finishedAt')
			.where('status',"完成")
	}
}

module.exports = Company