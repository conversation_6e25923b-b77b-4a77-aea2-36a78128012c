import { effect, useLoading } from 'dva17'
import { Alert, Image } from 'antd'
import { LoginForm, ProFormText } from '@ant-design/pro-form'
import { LockOutlined, UserOutlined } from '@ant-design/icons'
import { EGet, EUserLogin, NUser } from '../../common/action'
import './index.less'
import logo2 from '../../assets/images/tinywow_logo.png'

export default () => {
  const { loading } = useLoading(NUser)

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-banner">
          <div className="banner-text">
            <img src={logo2} alt="logo" className="logo" style={{ width: '100px', height: '100px' }} />
            <h2>冬瓜回收管理系统</h2>
            <p>绿链通-AI赋能 专业的家电回收管理平台，让回收更简单</p>
          </div>
        </div>
        <div className="login-form-container">
          <LoginForm
            title="欢迎登录"
            submitter={{
              searchConfig: {
                submitText: '登 录'
              }
            }}
            onFinish={async values => {
              await effect(NUser, EUserLogin, values)
            }}>
            <ProFormText
              name="username"
              fieldProps={{
                size: 'large',
                prefix: <UserOutlined className={'prefixIcon'} />,
              }}
              placeholder={'请输入账号'}
              rules={[
                {
                  required: true,
                  message: '请输入账号!',
                },
              ]}
            />
            <ProFormText.Password
              name="password"
              fieldProps={{
                size: 'large',
                prefix: <LockOutlined className={'prefixIcon'} />,
              }}
              placeholder={'请输入密码'}
              rules={[
                {
                  required: true,
                  message: '请输入密码！',
                },
              ]}
            />
          </LoginForm>
        </div>
      </div>
    </div>
  )
}
