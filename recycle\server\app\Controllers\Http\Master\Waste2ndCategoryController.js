'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Waste2ndCategory } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//废品二级分类
class Waste2ndCategoryController {
  async index({ request, response }) {
    let { id } = request.all()
    let query = Waste2ndCategory.query().where('firstCateID', id)
    let vo = await query.fetch()
    response.json(vo)
  }
}

module.exports = Waste2ndCategoryController
