const ESetState = "ESetState"

/*--------------- effect ---------------*/
//NOldGoods
const EGetTowLevelData = 'EGetTowLevelData'
const EGetThireLevelData = 'EGetThireLevelData'
const EGetSecondKind = 'EGetSecondKind'
const EFormatData = 'EFormatData'
const EGoodsGetOneLevel = 'EGoodsGetOneLevel'
const EGoodsGetTwoLevel = 'EGoodsGetTwoLevel'
const EGoodsGetAttributeType = 'EGoodsGetAttributeType'
const EGoodsGetAttribute = 'EGoodsGetAttribute'
const ESelectWhichOne = 'ESelectWhichOne'
const ESelectWhichAttribute = 'ESelectWhichAttribute'
const EWhichType = 'EWhichType'
const EGetEstimate = 'EGetEstimate'

export const NOldGoods = {
  EGetTowLevelData,
  EGetThireLevelData,
  EGetSecondKind,
  EFormatData,
  EGoodsGetOneLevel,
  EGoodsGetTwoLevel,
  EGoodsGetAttributeType,
  EGoodsGetAttribute,
  ESelectWhichOne,
  ESelectWhichAttribute,
  EWhichType,
  ESetState,
  EGetEstimate
}
//NOrder

const ETheOrderInfo = 'ETheOrderInfo'
const ECreateOrder = 'ECreateOrder'
const EGetAimTime = 'EGetAimTime'
const EGetOrderList = 'EGetOrderList'
const EUpdateOrder = 'EUpdateOrder'
const EOrderComplaint = 'EOrderComplaint'
const EUpdateOrderStatus = 'EUpdateOrderStatus'
const EGetOrderDetail = 'EGetOrderDetail'
const ERemarkOrder = 'ERemarkOrder'
const ESelectAddress = 'ESelectAddress'
const EGetAllOrder = 'EGetAllOrder'
const EPayOrder = 'EPayOrder'
const EUserPay = 'EUserPay'
const EGetRating = 'EGetRating'
const ECreateOrderRating = 'ECreateOrderRating'
const EGetWorker = 'EGetWorker'

export const NOrder = {
  ETheOrderInfo,
  ECreateOrder,
  EGetAimTime,
  EGetOrderList,
  EUpdateOrder,
  EOrderComplaint,
  EUpdateOrderStatus,
  EGetOrderDetail,
  ERemarkOrder,
  ESelectAddress,
  EGetAllOrder,
  EPayOrder,
  EUserPay,
  EGetRating,
  ECreateOrderRating,
  ESetState,
  EGetWorker
}
//NPublic
const EGetDiscoveryList = 'EGetDiscoveryList'
const EReplayTheContent = 'EReplayTheContent'
const EChangeUpvote = 'EChangeUpvote'
const EGetUpvoteList = 'EGetUpvoteList'

export const NPublics = {
  EGetDiscoveryList,
  EReplayTheContent,
  EChangeUpvote,
  EGetUpvoteList,
  ESetState
}


//NSystem
const EWxGetSystemInfo = 'EWxGetSystemInfo'
const EWxGetMenuButtonBoundingClientRect = 'EWxGetMenuButtonBoundingClientRect'
const EJudgeIsAddItem = 'EJudgeIsAddItem'

export const NSystem = {
  EWxGetSystemInfo,
  EWxGetMenuButtonBoundingClientRect,
  EJudgeIsAddItem,
  ESetState
}

//NUser

const ELogin = 'ELogin' // 登录
const EPutUserPhone = 'EPutUserPhone'
const EPutUserPhoneNew = 'EPutUserPhoneNew'
const EPutUserInfo = 'EPutUserInfo'
const EFirstGetAddress = 'EFirstGetAddress'
const EPostEvaluate = 'EPostEvaluate'
const EPostProxy = 'EPostProxy'
const EGetProxy = 'EGetProxy'
const EGetAddress = 'EGetAddress'
const EPutUserAvatar = 'EPutUserAvatar'


export const NUser = {
  EPutUserAvatar,
  ELogin,
  EPutUserPhone,
  EPutUserPhoneNew,
  EPutUserInfo,
  EFirstGetAddress,
  EPostEvaluate,
  EPostProxy,
  EGetProxy,
  EGetAddress,
  ESetState
}

//NUserAddress
const EGetUserAddressList = 'EGetUserAddressList'
const ESaveAddress = 'ESaveAddress'
const EEditAddress = 'EEditAddress'
const EDeleteAddress = 'EDeleteAddress'
const ESearchAddress = 'ESearchAddress'
const ESetDefault = 'ESetDefault'
const EGetJsonAddress = 'EGetJsonAddress'
export const NUserAddress = {
  EGetUserAddressList,
  ESaveAddress,
  EEditAddress,
  EDeleteAddress,
  ESearchAddress,
  ESetDefault,
  EGetJsonAddress,
  ESetState
}
