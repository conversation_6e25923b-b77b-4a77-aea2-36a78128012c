'use strict'

const Model = use('Model')
const Database = use('Database')

//上门师傅信息
class Salesman extends Model {
  static get table() {
    return 'salesman'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return 'updatedAt'
  }
  salesman() {
    return this.hasMany('App/Models/Salesman', 'id', 'superior_id').with('userCount').with('orderCount')
  }
  manager() {
    return this.belongsTo('App/Models/Salesman', 'superior_id', 'id')
  }
  userCount() {
    return this.hasOne('App/Models/User', 'id', 'salesman_id').select(Database.raw('salesman_id,count(salesman_id) as count'))
  }
  orderCount() {
    return this.hasMany('App/Models/ClientOrder', 'id', 'salesman_id')
      .select(Database.raw('salesman_id,wasteType,count(salesman_id) as count'))
      .groupBy('wasteType')
  }
  workerInfo() {
    return this.hasOne('App/Models/Worker', 'workerID', 'id')
  }
}

module.exports = Salesman
