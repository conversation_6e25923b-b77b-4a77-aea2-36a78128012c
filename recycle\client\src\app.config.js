export default defineAppConfig({
  pages: [
    'pages/home/<USER>',
    'pages/discover/index',
    'pages/my/index',
    'pages/oldGoods/index',
    'pages/uploadImg/index',
    'pages/order/index', //预约确定
    'pages/address/address', //新增地址
    'pages/administration/administration', //地址管理
    'pages/successfulOrder/successfulOrder', //地址管理
    'pages/orderManage/orderManage', //订单管理
    'pages/orderDetail/orderDetail', //订单详情页面
    'pages/cancleOrder/cancleOrder', //订单的取消或投诉
    'pages/payPage/payPage', //支付页面
    'pages/appliances/appliances', //大家电
    'pages/appliances/electronics/index', //电子产品
    'pages/appliances/order-form/index', //订单表单
    'pages/appliances/order-success/index', //订单成功
    'pages/attributeSelect/attributeSelect', //属性值选择
    'pages/priceEvaluation/priceEvaluation', //价格评估
    'pages/remarkPage/remarkPage', //师傅评价
    'pages/question/question', //常规问题解答
    'pages/introduce/introduce', //介绍
    'pages/evaluate/index',
    'pages/webview/index' //WebView网页
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: 'WeChat',
    navigationBarTextStyle: 'black',
    // navigationStyle: 'custom',
    navigationStyle: 'default',
  },
  enableShareAppMessage: true,
  enableShareTimeline: true,
  requiredPrivateInfos: [
    "chooseAddress",
    "getLocation",
    "chooseLocation"
  ],
  permission: {
    "scope.userLocation": {
      desc: "你的位置信息将用于小程序位置接口的效果展示"
    }
  },
  tabBar: {
    list: [
      {
        pagePath: 'pages/home/<USER>',
        text: '首页'
      },
      {
        pagePath: 'pages/discover/index',
        text: '发现'
      },
      {
        pagePath: 'pages/my/index',
        text: '我的'
      }
    ]
  }
})
