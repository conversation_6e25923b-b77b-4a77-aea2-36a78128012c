.successfulOrder {
  width: 100vw;
  height: 100vh;
  background: #f5f6f8;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0px 2px 11px #e9ebed inset;
  .step_wrapper {
    width: 542px;
    margin-top: 70px;
    .step_icon_wrapper {
      padding: 0 8px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      .step_line {
        border-top: 1px dotted #dcdcde;
        position: absolute;
        left: 48px;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 1px;
        width: 200px;
        color: #dcdcde;
      }
      .step_point_wrapper {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        position: relative;
        border: 2px solid rgba(124, 134, 150, 0.5);
        .point {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background-color: rgba(124, 134, 150, 0.5);
        }
      }
    }
    .step_text_wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
      Text {
        color: #7c8696;
        font-size: 24px;
      }
    }
  }

  //min-height: 100vh;
  //border: 1px solid red;
  //    background-color: #F3F3F3;
  //    display: flex;
  //    flex-direction: column;
  .step_items {
    //width:50vw;
    border: 1px solid red;
    margin: 0 auto;
    display: flex;
    //justify-content: space-between;
    align-items: center;

    .step_items_text {
      width: 17.5vw;
      text-align: center;
      color: rgba(102, 102, 102, 1);
      font-size: 3.2vw;
      font-family: PingFangSC-regular;
    }

    .step_items_icon {
      width: 3.2vw;
      height: 3.2vw;
      border-radius: 50%;
      border: 1px solid rgba(217, 217, 217, 1);
      display: flex;
      justify-content: center;
      align-items: center;

      .step_items_pointer {
        display: block;
        width: 2.1vw;
        height: 2.1vw;
        border-radius: 50%;
        background-color: rgba(140, 140, 140, 1);
      }
    }

    .step_items_line {
      /*width:10vw;
      height:0;
      border: 1px solid rgba(238, 238, 238, 1);*/
    }
  }

  .successful_Icon {
    margin-top: 100px;
    display: block;
    width: 240px;
    height: 240px;
  }

  .successful_title {
    font-size: 36px;
    color: #333333;
    letter-spacing: 1px;
    margin: 40px 0;
    font-weight: 600;
  }

  .successful_des {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 26px;
    color: #7c8696;

    Text {
      margin-top: 8px;
      letter-spacing: 1px;
    }
  }

  .successful_button {
    width: 574px;
    height: 88px;
    line-height: 88px;
    margin: 0 auto;
    margin-bottom: 20px;
    color: #ffffff;
    font-size: 30px;
    text-align: center;
    border-radius: 44px;
    background: #15b381;
    letter-spacing: 2px;
  }

  .successful_cancel {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 44px;
    margin: 0 auto;
    width: 112px;
    height: 4vw;
    line-height: 4vw;
    color: #7c8696;
    font-size: 26px;
    letter-spacing: 2px;
    font-family: PingFangSC-regular;
    // border:1px solid red;
    border-bottom: 1px solid rgba(51, 51, 51, 1);
  }
}
