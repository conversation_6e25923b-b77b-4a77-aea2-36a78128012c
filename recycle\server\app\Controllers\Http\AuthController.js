'use strict'

const { Salesman, Worker } = use('App/Models')
const { sendCodeSms } = require('../../Services/AliYunService')
const axios = require('axios')
const path = require('path')
const { CryptUtil } = require('../../Util')
const OSS = require('ali-oss')
const client = new OSS({
  region: 'oss-cn-shanghai',
  accessKeyId: "LTAI5tGy18wdAHdv6bqF4x83",
  accessKeySecret: "******************************",
  bucket: "hanhan-oss1"
})
class AuthController {
  // 获取验证码
  async getCode({ request, response }) {
    const { phone } = request.all()
    try {

      // 生成6位随机验证码
      // 将验证码存入Redis,5分钟过期
      let res = await axios.post(`http://************:3008/api/sms/send`, { phone })
      let code = res.data.code
      console.log(code)
      // 发送验证码短信
      await sendCodeSms({
        clientPhone: phone,
        code: code
      })

      return response.json({
        code: 0,
        success: true,
        message: '验证码已发送'
      })
    } catch (error) {
      console.error('发送验证码失败:', error)
      return response.status(500).json({
        message: '发送验证码失败,请稍后重试'
      })
    }
  }
  async checkPhone({ request, response }) {
    const { phone, code } = request.all()
    try {
      let res = await axios.post(`http://************:3008/api/sms/verify`, { phone, code })
      if (!res.data.success) {
        return response.status(400).json({
          message: '验证码错误或已过期'
        })
      }
      let isWorker = await Worker.findBy('mobile', phone)
      if (!isWorker) {
        return response.status(400).json({
          message: '该手机号未注册'
        })
      }
      if (isWorker.toJSON().isUse !== '同意' || isWorker.toJSON().forbidden !== 1) {
        return response.status(400).json({
          message: '该手机号已禁用'
        })
      }
      const token = CryptUtil.jwtEncode({ userID: isWorker.id })
      return response.json({
        success: true,
        message: '登录成功',
        token,
        user: isWorker
      })
    } catch (error) {
      console.error('验证手机号失败:', error)
      return response.status(500).json({
        message: '验证手机号失败,请稍后重试'
      })
    }
  }
  // 登录
  async login({ request, response }) {
    try {
      const { phone, code } = request.all()
      // 验证验证码
      let res = await axios.post(`http://************:3008/api/sms/verify`, { phone, code })
      if (!res.data.success) {
        return response.status(400).json({
          message: '验证码错误或已过期'
        })
      }

      // 查找渠道商
      let channel = await Salesman.findBy('phone', phone)
      if (!channel) {
        return response.status(400).json({
          message: '请先注册'
        })
      }

      if (channel.status === '0') {
        return response.status(403).json({
          message: '账号已被禁用'
        })
      }

      // 生成JWT token
      const token = CryptUtil.jwtEncode({ userID: channel.id })

      return response.json({
        code: 0,
        success: true,
        message: '登录成功',
        token,
        user: channel
      })
    } catch (error) {
      console.error('登录失败:', error)
      return response.status(500).json({
        message: '登录失败,请稍后重试'
      })
    }
  }

  // 注册
  async register({ request, response }) {
    try {
      const { phone, code, name, address } = request.all()
      // 验证验证码
      let res = await axios.post(`http://************:3008/api/sms/verify`, { phone, code })
      if (!res.data.success) {
        return response.status(400).json({
          message: '验证码错误或已过期'
        })
      }

      // 检查手机号是否已注册
      const existingChannel = await Salesman.findBy('phone', phone)
      if (existingChannel) {
        return response.status(400).json({
          message: '该手机号已注册'
        })
      }

      // 创建渠道商
      const channel = await Salesman.create({
        phone,
        name,
        address,
        status: 1,
        balance: 0,
        password: phone.slice(-6),
        remark: '注册成功',
        title: '1'
      })
      const { data: buf } = await WXService.getwxacode({
        AppID: Config.Client.AppID,
        AppSecret: Config.Client.AppSecret,
        data: {
          path: `pages/home/<USER>
          width: 430,
          env_version: 'release',
        },
      })
      let ossFilePath = path.join('hanhan/salesman', `${channel.id}.jpg`).replace(/\\/g, '/')
      let ossres = await client.put(ossFilePath, buf)
      channel.img_url = ossres.url
      await channel.save()

      // 删除验证码
      await axios.post(`http://************:3008/api/sms/deleteCode/${phone}`)

      return response.json({
        code: 0,
        success: true,
        message: '注册成功',
        user: channel
      })
    } catch (error) {
      console.error('注册失败:', error)
      return response.status(500).json({
        message: '注册失败,请稍后重试'
      })
    }
  }

  // 密码登录
  async loginBypasswd({ request, response }) {
    const { phone, password } = request.all()
    // 查找渠道商
    let channel = await Salesman.query().where('phone', phone).first()
    if (!channel) {
      return response.status(400).json({
        message: '请先注册'
      })
    }
    // 验证密码
    console.log(password, channel.toJSON().password)
    const isPasswordValid = password === channel.toJSON().password
    if (!isPasswordValid) {
      return response.status(400).json({
        message: '密码错误'
      })
    }
    // 生成JWT token
    const token = CryptUtil.jwtEncode({ userID: channel.id })
    return response.json({
      code: 0,
      success: true,
      message: '登录成功',
      token,
      user: channel
    })
  }
}

module.exports = AuthController 