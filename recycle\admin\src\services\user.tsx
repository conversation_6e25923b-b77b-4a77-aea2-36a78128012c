import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'

export async function queryCurrent() {
  return requestGet('/api/currentUser')
}
export async function login(payload:any) {
  //let {username,password} = payload
  return requestPost('/user/login', payload)
}
export async function getAuthUser(payload:any) {
  return requestGet('/admin/check')
}
export async function getUserInfo() {
  return requestGet('/adminUser/getUserInfo')
}

export async function getUserLocation(payload:any) {
  return requestGet('/userLocation')
}
