'use strict'

const Model = use('Model')
const Database = use('Database')

//上门师傅信息
class JDWorkerWalletLog extends Model {
  static get table() {
    return 'jd_worker_wallet_log'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return null
  }
  worker() {
    return this.belongsTo('App/Models/JDWorker', 'workerID', 'id')
  }
  order() {
    return this.belongsTo('App/Models/JDOrder', 'orderID', 'id')
  }
}

module.exports = JDWorkerWalletLog
