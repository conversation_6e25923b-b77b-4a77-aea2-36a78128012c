/**
 * Created by f<PERSON><PERSON> on 25/04/2017.
 */
'use strict'
const _ = require("lodash");
var axios = require('axios');
var qs = require('qs');
const Env = use('Env')
const crypto = require('crypto');
const { ReqLog } = require("../Models");
const { ASKey, CaiNiaoLink } = require("./Config");
const { log } = require("console");
const { ERR } = require("../../../../constants");

const DebugUtil = {
  lastTime: 0,
  time(label) {
    let now = new Date().getTime()
    if (label) {
      console.log(label, `${now - this.lastTime}ms`, `${(now - this.lastTime) / 1000}s`)
    }
    this.lastTime = now
  },
  regReturn(itemName, extendFields) {
    const typeList = ['冰箱', '电视', '洗衣机', '空调', '空调柜机', '空调挂机', '空气热泵热水器', '燃气热水器', '电热水器', '壁挂炉', '洗碗机', '烟灶']
    for (let index = 0; index < typeList.length; index++) {
      const element = typeList[index];
      const result = _.includes(itemName, element);
      // console.log(element, itemName, result);
      if (result) {
        if (element === "空调") {
          let guiji = _.includes(extendFields, "柜机")
          // console.log(guiji, element);
          if (guiji) {
            return "空调柜机"
          } else {
            return "空调挂机"
          }
        } else {
          return element
        }
      } else {
        continue
      }
    }
  },

  getValueByKey(key, orderExtendFields) {
    for (let item of orderExtendFields) {
      if (item.key === key) {
        return item.value;
      }
    }
    return null;
  },
  async callBack(msg_type, logistic_provider_id, content, tocode) {
    let reqVo = await ReqLog.create({ req: JSON.stringify(content), source: '订单回执菜鸟' })
    let revo = doSign(reqVo.req)
    var data = qs.stringify({
      'msg_type': msg_type,
      'logistic_provider_id': logistic_provider_id,
      'logistics_interface': reqVo.req,
      'data_digest': revo
    });
    if (tocode) {
      data = qs.stringify({
        'msg_type': msg_type,
        'logistic_provider_id': logistic_provider_id,
        'logistics_interface': reqVo.req,
        'data_digest': revo,
        'to_code': tocode
      });
    }
    var config = {
      method: 'post',
      url: CaiNiaoLink,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; rv:2.0.1) Gecko/20100101 Firefox/4.0.1',
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: data
    };
    if (Env.get('NODE_ENV') == 'development') {
      throw ERR.UNDEFINED
    } else {
      try {
        axios(config)
          .then(async function (response) {
            await ReqLog.create({ res: JSON.stringify(response.data), source: '订单回执菜鸟' })
            return (JSON.stringify(response.data))
          })
          .catch(function (error) {
            console.log(error);
          });
      } catch (error) {
        console.log(error);
      }
      return null;
    }
  },

}
function doSign(content) {
  // console.log(content);
  let sign = "";
  let keys = ASKey
  content = content + keys;
  try {
    const md5Hash = crypto.createHash('md5');
    md5Hash.update(content, 'UTF-8');
    const md5Digest = md5Hash.digest();
    sign = Buffer.from(md5Digest).toString('base64');
  } catch (e) {
    throw new Error(e);
  }
  // console.log(sign);
  return sign;
}
module.exports = DebugUtil
