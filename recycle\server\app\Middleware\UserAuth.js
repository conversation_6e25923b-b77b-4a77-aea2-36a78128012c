"use strict";

const { CryptUtil } = require("..//Util");
const { ERR } = require("../../../../constants");
const { User } = require("../Models");
const Env = use("Env");

class UserAuth {
  async handle({ request }, next) {
    const payload = CryptUtil.jwtDecode(
      request.header("Authorization") || request.input("token")
    );
    if (!payload) {
      throw ERR.AUTH_FAILED;
    } else {
      if (!payload.userID) {
        throw ERR.AUTH_FAILED;
      }
      request.userID = payload.userID;
    }
    // }
    let user = await User.find(request.userID);
    if (user && user.isForbidden) {
      throw ERR.USER_FORBIDDEN;
    } else if (!user) {
      throw ERR.AUTH_FAILED;
    } else {
      request.user = user;
    }
    await next();
  }
}

module.exports = UserAuth;
