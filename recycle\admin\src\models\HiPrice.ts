import {
  EGetHiPriceList, EGetHiPriceDetail, ECreateHiPrice, EUpdateHiPrice,
  EDeleteHiPrice, EBatchUpdateHiPrice, EBatchUpdateHiPriceStatus,
  EExportHiPriceTable, EQueryHiPriceByCondition, ECopyHiPriceConfig,
  RSetState, NHiPrice
} from '../common/action'
import { adapterPaginationResult } from '../common/utils'
import { message } from 'antd'
import {
  getHiPriceList, getHiPriceDetail, createHiPrice, updateHiPrice,
  deleteHiPrice, batchUpdateHiPrice, batchUpdateHiPriceStatus,
  exportHiPriceTable, queryHiPriceByCondition, copyHiPriceConfig
} from '../services/hiPrice'

export default {
  namespace: NHiPrice,
  state: {
    priceList: null,
    priceDetail: null,
    exportData: null,
    queryPriceResult: null,
    loading: false
  },
  reducers: {
    [RSetState](state: any, payload: any) {
      return { ...state, ...payload }
    },
  },
  effects: {
    // 获取价格配置列表
    async [EGetHiPriceList]({ payload }: any, { reducer }: any) {
      const response = await getHiPriceList(payload)
      return adapterPaginationResult(response)
    },

    // 获取价格配置详情
    async [EGetHiPriceDetail]({ payload }: any, { reducer }: any) {
      const response = await getHiPriceDetail(payload)
      if (response.success) {
        reducer(RSetState, { priceDetail: response.data })
      }
      return response
    },

    // 创建价格配置
    async [ECreateHiPrice]({ payload }: any, { reducer }: any) {
      const response = await createHiPrice(payload)
      if (response.success) {
        message.success('创建价格配置成功')
      } else {
        message.error(response.message || '创建价格配置失败')
      }
      return response
    },

    // 更新价格配置
    async [EUpdateHiPrice]({ payload }: any, { reducer }: any) {
      const response = await updateHiPrice(payload)
      if (response.success) {
        message.success('更新价格配置成功')
      } else {
        message.error(response.message || '更新价格配置失败')
      }
      return response
    },

    // 删除价格配置
    async [EDeleteHiPrice]({ payload }: any, { reducer }: any) {
      const response = await deleteHiPrice(payload)
      if (response.success) {
        message.success('删除价格配置成功')
      } else {
        message.error(response.message || '删除价格配置失败')
      }
      return response
    },

    // 批量更新价格
    async [EBatchUpdateHiPrice]({ payload }: any, { reducer }: any) {
      const response = await batchUpdateHiPrice(payload)
      if (response.success) {
        message.success('批量更新价格成功')
      } else {
        message.error(response.message || '批量更新价格失败')
      }
      return response
    },

    // 批量更新状态
    async [EBatchUpdateHiPriceStatus]({ payload }: any, { reducer }: any) {
      const response = await batchUpdateHiPriceStatus(payload)
      if (response.success) {
        message.success('批量更新状态成功')
      } else {
        message.error(response.message || '批量更新状态失败')
      }
      return response
    },

    // 导出价格配置表格
    async [EExportHiPriceTable]({ payload }: any, { reducer }: any) {
      const response = await exportHiPriceTable(payload)
      if (response.success) {
        message.success('导出成功')
        reducer(RSetState, { exportData: response.data })
        
        // 如果有下载链接，自动下载文件
        if (response.downloadUrl) {
          const link = document.createElement('a')
          link.href = response.downloadUrl
          link.download = response.fileName || '嗨回收价格配置表.xlsx'
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        }
      } else {
        message.error(response.message || '导出失败')
      }
      return response
    },

    // 根据条件查询价格
    async [EQueryHiPriceByCondition]({ payload }: any, { reducer }: any) {
      const response = await queryHiPriceByCondition(payload)
      if (response.success) {
        reducer(RSetState, { queryPriceResult: response.data })
      } else {
        message.error(response.message || '查询价格失败')
      }
      return response
    },

    // 复制价格配置
    async [ECopyHiPriceConfig]({ payload }: any, { reducer }: any) {
      const response = await copyHiPriceConfig(payload)
      if (response.success) {
        message.success(`成功复制到${response.data?.length || 0}个区域`)
      } else {
        message.error(response.message || '复制价格配置失败')
      }
      return response
    },
  }
} 