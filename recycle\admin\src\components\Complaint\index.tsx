import React, { useRef } from 'react'
import { Modal } from 'antd'
import { <PERSON>rder, <PERSON><PERSON>eeR<PERSON>, ESee<PERSON>omplaint } from '../../common/action'
import styles from './index.module.less'
import { effect } from 'dva17'
import { useEffect } from 'react';
import ProTable, { ActionType, ProColumns } from '@ant-design/pro-table'

type Item = {
  id: number
  orderNo: string
  content: string
  wasteType: string
  createdAt: any
  order: {
    orderNo: string
    from: string
    realname: string
    workTime: any
    mobile: string
    province: string
    city: string
    address: string
  }
  company: {
    companyName: string
  }
  worker: {
    workerName: string
  }
  workerRatingText: string
  workerRating: number
}
export default (props: any) => {
  let { visible, onChange, wasteType = "大家电", isRate = false } = props
  const actionRef = useRef<ActionType>()
  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    if (visible) {
      if (actionRef.current) {
        actionRef.current.reload()
      }
    }
    return () => {
    }
  }, [visible])

  const cancelVisible = () => {
    onChange({ visible: false })
  }

  /*--------------------- 响应 ---------------------*/
  const columnsComp: ProColumns<Item>[] = [
    { title: '订单号', dataIndex: ['order', 'orderNo'], copyable: true, search: false, ellipsis: false, },
    { title: '订单来源', dataIndex: ['order', 'from'], search: false, ellipsis: true },
    { title: '客户名称', dataIndex: ['order', 'userName'], copyable: false, search: false },
    { title: '联系电话', dataIndex: ['order', 'userMobile'], search: false },
    {
      title: '投诉内容', dataIndex: 'content', copyable: false, search: false,
    },
    { title: '提交时间', dataIndex: 'createdAt', copyable: false, ellipsis: false, search: false },
    { title: '工作人员', dataIndex: ['worker', 'workerName'], copyable: false, ellipsis: false, search: false },
    { title: '服务商', dataIndex: ['company', 'companyName'], copyable: false, ellipsis: false, search: false },
    // {
    //   title: '操作', copyable: false, ellipsis: false, search: false, render: (_, row: any) => (
    //     <><a onClick={() => { }}>已处理</a></>
    //   )
    // },
  ]
  const columnsRate: ProColumns<Item>[] = [
    { title: '订单号', dataIndex: ['order', 'orderNo'], copyable: true, search: false, ellipsis: false, },
    { title: '订单来源', dataIndex: ['order', 'from'], search: false, ellipsis: true },
    { title: '客户名称', dataIndex: ['order', 'userName'], copyable: false, search: false },
    { title: '联系电话', dataIndex: ['order', 'userMobile'], search: false },
    {
      title: '评价等级', dataIndex: 'workerRating', search: false, render: (_, row: any) => (
        <>{row.workerRating === 1 ? '不满意' : row.workerRating === 2 ? '一般' : '满意'}</>
      )
    },
    {
      title: '评价内容', dataIndex: 'workerRatingText', copyable: false, search: false,
    },
    { title: '提交时间', dataIndex: 'createdAt', copyable: false, ellipsis: false, search: false },
    { title: '工作人员', dataIndex: ['worker', 'workerName'], copyable: false, ellipsis: false, search: false },
    { title: '服务商', dataIndex: ['company', 'companyName'], copyable: false, ellipsis: false, search: false },
    // {
    //   title: '操作', copyable: false, ellipsis: false, search: false, render: (_, row: any) => (
    //     <><a onClick={() => { }}>已处理</a></>
    //   )
    // },
  ]
  /*--------------------- 渲染 ---------------------*/
  return (
    <Modal
      open={visible}
      onOk={() => {
        cancelVisible()
      }}
      onCancel={() => {
        cancelVisible()
      }}
      width={'90%'}
    >
      <ProTable<Item>
        search={false}
        actionRef={actionRef}
        columns={isRate ? columnsRate : columnsComp}
        request={async (params = {}, sorter) => {
          if (isRate) {
            return await effect(NOrder, ESeeRating, {
              ...params,
              wasteType
            }) as any
          } else {
            return await effect(NOrder, ESeeComplaint, {
              ...params,
              wasteType
            }) as any
          }
        }}
        pagination={{
        }}
        rowKey="orderID"
        dateFormatter="string"
        headerTitle=""
      />
    </Modal>
  )
}
