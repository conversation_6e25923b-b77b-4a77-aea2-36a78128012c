.city-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.city-selector-modal {
  width: 100%;
  background-color: #fff;
  border-radius: 20px 20px 0 0;
  max-height: 100vh;
  height: 85vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.city-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px 32px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.city-selector-back {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.back-icon {
  font-size: 32px;
  margin-right: 8px;
  color: #15b381;
}

.back-text {
  font-size: 28px;
  color: #15b381;
  font-weight: 500;
}

.city-selector-title {
  font-size: 36px;
  font-weight: bold;
  color: #333;
}

.city-selector-close {
  font-size: 48px;
  color: #999;
  cursor: pointer;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.city-selector-section {
  padding: 32px;
  border-bottom: 1px solid #f8f8f8;
}

.city-selector-section:last-child {
  border-bottom: none;
}

.city-selector-section-title {
  font-size: 28px;
  color: #666;
  margin-bottom: 24px;
  font-weight: 500;
}

.city-selector-current {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 2px solid #15b381;
}

.location-icon {
  font-size: 32px;
  margin-right: 16px;
}

.location-text {
  font-size: 28px;
  color: #15b381;
  font-weight: 500;
}

.city-selector-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.city-selector-item {
  flex: 0 0 calc(33.333% - 14px);
  padding: 20px 16px;
  background-color: #f8f9fa;
  border-radius: 12px;
  text-align: center;
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.city-selector-item.active {
  background-color: #e8f5f1;
  border-color: #15b381;
}

.city-selector-item-text {
  font-size: 26px;
  color: #333;
  font-weight: 400;
}

.city-selector-item.active .city-selector-item-text {
  color: #15b381;
  font-weight: 500;
}

.city-selector-list {
  max-height: 50vh;
}

/* 省份标题样式 */
.province-title {
  font-size: 30px;
  font-weight: 500;
  color: #333;
  padding: 16px 0;
  margin-top: 10px;
  border-bottom: 1px solid #f0f0f0;
}

/* 省份列表项样式 */
.province-item {
  display: inline-block;
  padding: 12px 24px;
  margin-right: 16px;
  background-color: #f8f9fa;
  border-radius: 30px;
  border: 2px solid transparent;
  transition: all 0.2s ease;
  min-width: 100px;
  text-align: center;
}

.province-item.active {
  background-color: #e8f5f1;
  border-color: #15b381;
}

.province-item-text {
  font-size: 26px;
  color: #333;
}

.province-item.active .province-item-text {
  color: #15b381;
  font-weight: 500;
}

/* 省份城市列表容器 */
.province-cities {
  margin-bottom: 24px;
}

/* 选中省份的城市列表 */
.selected-province-cities {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding-top: 16px;
}

/* 省份标签滚动容器 */
.province-scroll-container {
  padding: 10px 0;
  margin-bottom: 10px;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.province-scroll-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* 增加动画效果 */
.province-item {
  transform: translateY(0);
  transition: all 0.3s ease;
}

.province-item:active {
  transform: translateY(2px);
}

/* 当前选中省份标识 */
.current-province-indicator {
  font-size: 32px;
  font-weight: 600;
  color: #15b381;
  padding: 16px 0;
  border-bottom: 2px solid #15b381;
  margin-bottom: 16px;
}