import { getAccountList, createAccount, editAccount, getPermissionContent, savePermission } from '../services/permission'
import { notification } from 'antd'
import { EGetAccountList, ECreateAccount, EEditAccount, EGetPermissionContent, ESavePermissionContent, NPermission, RAdd, RSetState } from '../common/action'

export default {
  namespace: NPermission,
  state: {
    accountList: null,
    isCreateAccount: false,
    isEditAccount: false,
    permissionContent: null,
    getPermission: false,
  },
  reducers: {
    [RSetState](state: any, payload: any) {
      return { ...state, ...payload }
    },
    [RAdd](state: { count: any }, payload: any) {
      return { ...state, count: state.count + payload }
    },
  },
  effects: {
    //  标准CURD示例

    //获取账户列表
    async [EGetAccountList]({ payload }: any, { reducer }: any) {
      const response = await getAccountList(payload)
      reducer(RSetState, { accountList: response })
    },
    //新建账户
    async [ECreateAccount]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isCreateAccount: false })
      const response = await createAccount(payload)
      reducer(RSetState, { isCreateAccount: true })
    },
    //修改账户
    async [EEditAccount]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isEditAccount: false })
      const response = await editAccount(payload)
      reducer(RSetState, { isEditAccount: true })
    },
    //获取总部或服务商内部权限
    async [EGetPermissionContent]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { getPermission: false })
      const response = await getPermissionContent(payload)
      reducer(RSetState, { permissionContent: response })
      reducer(RSetState, { getPermission: true })
    },
    //保存权限内容
    async [ESavePermissionContent]({ payload }: any, { reducer }: any) {
      const response = await savePermission(payload)

      notification.success({
        message: '成功',
        description: '权限设置成功！',
      })
    },
  },
}
