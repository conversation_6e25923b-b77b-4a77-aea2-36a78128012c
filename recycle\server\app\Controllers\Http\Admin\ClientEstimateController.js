'use strict'

const _ = require('lodash')
const moment = require('moment')

const { ClientEstimate } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { CryptUtil } = require('../../../Util')
const Env = use('Env')
// 后台客户估价 type,subType,coldType,price,years,functions,outside,spu
class ClientEstimateController {
  //估价列表
  async index({ request, response }) {
    let { adminUser } = request
    let { current = 1, pageSize = 10, sort = 'desc', type, subType, coldType, price, years, functions, outside, spu } = request.all()
    let { companyID } = adminUser
    let query = ClientEstimate.query().where('status', 1)
    companyID = parseInt(companyID)
    if (companyID && companyID !== 36) {
      return response.json({
        code: 400,
        message: '无权限'
      })
    }
    if (type) {
      query.where('type', 'like', `%${type}%`)
    }
    if (subType) {
      query.where('subType', 'like', `%${subType}%`)
    }
    if (coldType) {
      query.where('coldType', 'like', `%${coldType}%`)
    }
    if (price) {
      switch (price) {
        case 'asc':
          query.orderBy('price', 'asc')
          break
        case 'desc':
          query.orderBy('price', 'desc')
          break
        default:
          query.orderBy('price', 'desc')
          break
      }
    }
    if (years) {
      query.where('years', 'like', `%${years}%`)
    }
    if (functions) {
      query.where('functions', 'like', `%${functions}%`)
    }
    if (outside) {
      query.where('outside', 'like', `%${outside}%`)
    }
    if (spu) {
      query.where('spu', 'like', `%${spu}%`)
    }
    let vo = await query.orderBy('id', sort).paginate(current, pageSize)
    response.json(vo)
  }
  //创建估价
  async store({ request, response }) {
    let { type, subType, coldType, price, years, functions, outside, spu } = request.all()
    let vo = await ClientEstimate.create({ type, subType, coldType, price, years, functions, outside, spu })
    response.json(vo)
  }
  //估价信息修改
  async update({ request, params, response }) {
    let {   } = request.all()
    let vo = await ClientEstimate.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
  }
  //估价信息删除
  async destroy({ request, params, response }) {
    let vo = await ClientEstimate.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    vo.status = 0
    await vo.save()
    response.json(vo)
  }

}

module.exports = ClientEstimateController
