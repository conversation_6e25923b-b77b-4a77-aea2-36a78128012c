import { requestGet, requestPatch, requestPost } from "../utils/request"

export async function postCalendar(payload) {
  return requestPost("calendar", payload)
}
export async function postCalendarLike(payload) {
  return requestPost("calendarLike", payload)
}

export async function getCalendar() {
  return requestGet(`calendar`)
}
export async function getShareCalendar(id) {
  return requestGet(`calendar/` + id)
}
export async function getCalendarRank(payload) {
  return requestGet("calendarRank", payload)
}
