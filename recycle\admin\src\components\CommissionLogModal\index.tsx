import { Modal, But<PERSON>, Table, Tag } from 'antd'
import { useEffect, useState } from 'react'
import { SERVER_HOME } from '../../common/config'
import dayjs from 'dayjs'
import { getPriceSetLogList } from '../../services/price'

interface CommissionLogModalProps {
  visible: boolean;
  orderID: number;
  onClose: () => void;
}

const CommissionLogModal = ({ visible, orderID, onClose }: CommissionLogModalProps) => {
  const [commissionLogs, setCommissionLogs] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible && orderID) {
      fetchCommissionLogs();
    }
  }, [visible, orderID]);

  const fetchCommissionLogs = async () => {
    setLoading(true);
    try {
      const data = await getPriceSetLogList({ orderID });
      setCommissionLogs(data || []);
    } catch (error) {
      console.error('获取佣金修改记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: '修改时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (createdAt: any) => (
        <span>{createdAt ? dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss') : '-'}</span>
      ),
    },
    {
      title: '修改前金额',
      dataIndex: 'oldAmount',
      key: 'oldAmount',
      render: (amount: number) => <span>¥{amount}</span>,
    },
    {
      title: '修改后金额',
      dataIndex: 'newAmount',
      key: 'newAmount',
      render: (amount: number) => <span>¥{amount}</span>,
    },
    {
      title: '变化',
      key: 'change',
      render: (record: any) => {
        const change = record.newAmount - record.oldAmount;
        return (
          <Tag color={change > 0 ? 'green' : change < 0 ? 'red' : 'default'}>
            {change > 0 ? '+' : ''}{change}
          </Tag>
        );
      },
    },
    {
      title: '修改人',
      dataIndex: 'operatorName',
      key: 'operatorName',
      render: (name: string, record: any) => (
        <span>{name || record.operator?.name || '-'}</span>
      ),
    },
    {
      title: '修改原因',
      dataIndex: 'remark',
      key: 'remark',
      render: (remark: string) => <span>{remark || '-'}</span>,
    },
  ];

  const exportCommissionExcel = () => {
    window.open(`${SERVER_HOME}exportCommissionLogXLS?orderID=${orderID}`);
  };

  return (
    <Modal
      title="佣金修改记录"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={900}
    >
      <div style={{ marginBottom: 16 }}>
        <Button type='primary' onClick={exportCommissionExcel}>导出记录</Button>
      </div>
      <Table
        columns={columns}
        loading={loading}
        dataSource={commissionLogs}
        rowKey={(record) => record.id || JSON.stringify(record)}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
        }}
      />
    </Modal>
  );
};

export default CommissionLogModal; 