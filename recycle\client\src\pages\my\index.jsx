import Taro from '@tarojs/taro'
import { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { View, Text, Button, Image, OfficialAccount, Input, Form } from '@tarojs/components'
import { AtModal, AtBadge } from 'taro-ui'
import './index.less'
import BaseLayout from '../../components/baseLayout'
import myImg from '../../assets/my/index.js'
import { NUser } from '../../config/constants'
import { AtFloatLayout } from 'taro-ui'
import 'taro-ui/dist/style/components/float-layout.scss'
import NavCustomBar from '../../components/navbar'
import { SERVER_HOME } from '../../config/config'
// import 'taro-ui/dist/style/components/badge.scss'
import 'taro-ui/dist/style/components/float-layout.scss'
const manageList = [
  {
    url: 'record',
    status: '预订',
    text: '已预约',
    img: 'https://oss.evergreenrecycle.cn/donggua/client/images/state0.png',
  },
  {
    url: 'doing',
    status: '进行中',
    text: '进行中',
    img: 'https://oss.evergreenrecycle.cn/donggua/client/images/state1.png',
  },
  {
    url: 'success',
    status: '完成',
    text: '已完成',
    img: 'https://oss.evergreenrecycle.cn/donggua/client/images/state2.png',
  },
  {
    url: 'cancel',
    status: '取消',
    text: '已取消',
    img: 'https://oss.evergreenrecycle.cn/donggua/client/images/state3.png',
  },
]

const list = [
  { name: '地址管理', url: myImg.address, pageUrl: '/pages/administration/administration' },
  { name: '咨询客服', url: myImg.service, type: 'contact', pageUrl: '' },
  { name: '常规问题解答', url: myImg.issue, pageUrl: '/pages/question/question' },
  // { name: '代理登记', url: myImg.proxy, pageUrl: '/pages/proxySale/proxySale' },
]
definePageConfig({
  navigationBarTitleText: '首页',
  // navigationStyle: 'default',
  navigationStyle: 'custom',
})
export default props => {
  const { userInfo } = useSelector(state => state.NUser)
  const { messageRemind } = useSelector(state => state.NOrder)
  const [open, setOpen] = useState(false)

  const [avatar, setAvatar] = useState('https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132')

  const dispatch = useDispatch()
  const switchToWaste = (id, routerUrl) => {
    Taro.navigateTo({ url: `${routerUrl}?id=${id}` })
  }

  async function getUserInfo(e) {
    let user = Taro.getStorageSync('userInfo') && JSON.parse(Taro.getStorageSync('userInfo'))
    if(e){
    }
    if (user.nickName === '微信用户') {
    await Taro.getUserProfile({
      desc: '用于完善用户资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: res => {
        dispatch.NUser[NUser.EPutUserInfo]({
          ...res.userInfo,
          id: user.id,
          page: 'img',
        })
      },
    })
    }
  }
  // const onLoadHandler = e => {
  //   console.log(e)
  // }
  const handleClose = e => {
    setOpen(false)
  }

  const onChooseAvatar = e => {
    Taro.uploadFile({
      url: SERVER_HOME + 'file', //仅为示例，非真实的接口地址
      filePath: e?.detail?.avatarUrl,
      name: 'file',
      formData: {
        user: 'hanhan',
      },
      success: function (res) {
        let data = JSON.parse(res.data)
        setAvatar(data.url)
      },
    })
  }
  const onPutUserData = (e) => {
    let user = Taro.getStorageSync('userInfo') && JSON.parse(Taro.getStorageSync('userInfo'))
    if (avatar && e.detail.value.nickname) {
      dispatch.NUser[NUser.EPutUserAvatar]({
        id: user.id,
        nickName: e.detail.value.nickname,
        avatarUrl: avatar,
      }).then(() => {
        setOpen(false)
        Taro.showToast({
          icon: 'none',
          title: '修改成功',
          duration: 2000,
        })
      })
    } else {
      Taro.showToast({
        icon: 'none',
        title: '请填写完整',
        duration: 2000,
      })
    }
  }
  return (
    <BaseLayout menuIndex={2} navigationBar={false} footer={true} title="我的">
      <NavCustomBar mainTitle='我的' needBackIcon={false} />
      <AtFloatLayout isOpened={open} onClose={handleClose} title={'更新头像和昵称'} className="float_box">
        <Form onSubmit={onPutUserData}>
          <View className="chooseAvatar_container">
            <View className="tl-card-1">
              <View className="tl-height-100 border-bottom tl-font-32-333">
                头像
                <Button className="tl-p-r tl-img-60" open-type="chooseAvatar" onChooseAvatar={onChooseAvatar}>
                  <Image className="tl-img-60" src={avatar} />
                </Button>
              </View>
              <View className="tl-line-2"></View>

              <View className="tl-height-100 tl-font-32-333">
                昵称
                <Input type="nickname" name="nickname" className="tl-p-r2" placeholder="请输入昵称" />
              </View>
            </View>
          </View>
          <Button className="submit" formType="submit">
            确认
          </Button>
        </Form>
      </AtFloatLayout>
      <View className="user_center">
        <View className="top_container">
          <View className="top_wrapper">
            <View className="avatar_wrapper">
              <View
                className="wrapper"
                onClick={async () => {
                  setOpen(true)
                }}
              >
                <View className="avatar">
                  <Image src={userInfo && userInfo.avatarUrl} className="avatar_image" />
                </View>
                <Text>{userInfo && userInfo.nickName !== '微信用户' ? userInfo.nickName : '未登录'}</Text>
                -
                <Text>{!userInfo?.nickName ? '点击登录' : ''}</Text>
              </View>
            </View>
            <View className="select_item">
              {manageList.map((value, index) => (
                <View
                  className="item"
                  onClick={() => {
                    Taro.navigateTo({ url: `/pages/orderManage/orderManage?manage=${value.status}` })
                  }}
                  key={index + value}
                >
                  <View className="item_image_wrapper">
                    {/* <AtBadge value={messageRemind[index]} maxValue={9} className={messageRemind[index] === 0 ? 'hidden' : null}></AtBadge> */}
                    <Image src={value.img} className={`item_image1`} />
                  </View>
                  <Text>{value.text}</Text>
                </View>
              ))}
            </View>
          </View>
        </View>

        <View style={{ width: '90%', borderRadius: '16rpx' }}></View>
        <View className="management_wrapper">
          {list.map((value, index) => {
            return (
              <Button openType={value.type}>
                <View
                  className="management_item"
                  key={index + value}
                  onClick={() => {
                    Taro.navigateTo({ url: value.pageUrl })
                  }}
                >
                  <View className="left_wrapper">
                    <Image src={value.url} className="management_item_image" />
                    <Text>{value.name}</Text>
                  </View>
                  <Image src={myImg.right} className="operate_item_image" />
                </View>
              </Button>
            )
          })}
        </View>
      </View>
      <View className="add_item"></View>
    </BaseLayout>
  )
}
