import Taro from '@tarojs/taro'
import React, { Component } from 'react'
import T from "../../config/T"
import {connect} from 'react-redux'
import { View, Image, Button, Text, Checkbox, WebView } from '@tarojs/components'
import { AtModal } from 'taro-ui'
import './payPage.less'
import E from '../../config/E'

class PayPage extends Component {
  constructor() {
    super(...arguments)
    this.state = {}
  }

  config = {}

  //-----------------------生命周期-------------------------//
  componentWillMount() {
    Taro.setNavigationBarTitle({ title: '支付' })
  }

  componentDidMount() {}

  componentWillUnmount() {}

  componentDidShow() {}

  componentDidHide() {}

  //-----------------------事件-------------------------//
  pay() {
    this.props.dispatch({
      type: 'NOrder/EPayOrder'
      // payload: {
      //   type: '微信支付',
      //     orderID: 206
      // }
    })
  }

  //-----------------------渲染-------------------------//
  render() {
    let { step } = this.state
    // const {haveChosenClothing, haveChosenPage} = this.props.oldGoods
    return <WebView src="https://mp.weixin.qq.com/s/JHr3P9A1Yz-84EY2H8Mp_Q"></WebView>
  }
}

export default connect(({  }) => ({}))(PayPage)
// <View className='payPage'>
//     {/* <Button onClick={()=>{this.pay()}} className="pay_button">支付订单</Button> */}
// </View>
