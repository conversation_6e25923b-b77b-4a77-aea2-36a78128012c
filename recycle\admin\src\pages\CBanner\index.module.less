.page {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.item {
  width: 100%;
  display: flex;
  .item_title {
    display: inline-block;
    width: 80px;
    text-align: right;
  }
  .item_content {
    flex: 1;
    .imag_wrapper {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      .addImage {
        overflow: hidden;
        height: 200px;
        width: 200px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        margin-right: 20px;
        margin-bottom: 20px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        .actualClick {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          opacity: 0;
        }
        .mask {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.4);
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          transition: all 0.5s;
          opacity: 0;
          &:hover {
            opacity: 1;
          }
        }
      }
      .img_item {
        width: 100%;
        object-fit: contain;
      }
    }
  }
}