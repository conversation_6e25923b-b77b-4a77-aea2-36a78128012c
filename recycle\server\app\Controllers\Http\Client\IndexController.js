'use strict'

const _ = require('lodash')
const moment = require('moment')

const { CLaba, CPath, CBanner, ClientEstimate } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const Env = use('Env')

class IndexController {
  //获取首页数据
  async index({ request, response }) {
    let Banners = await CBanner.query().where('status', 1).fetch()
    let Labas = await CLaba.query().where('status', 1).fetch()
    let Paths = await CPath.query().where('status', 1).fetch()
    let vo = {
      Banners,
      Labas,
      Paths
    }
    response.json(vo)
  }
  async getPrice({ request, response }) {
    let { level, name = '空调', functions, subType, coldType, spu, years, outside } = request.all()
    let price = {}

    if (level == 1 || level == '1') {
      let subquery = ClientEstimate.query().where('status', 1).where('type', name)
      price.subType = await subquery.distinct('subType').fetch()
      let skuquery = ClientEstimate.query().where('status', 1).where('type', name)
      price.spu = await skuquery.distinct('spu').fetch()
      let yearquery = ClientEstimate.query().where('status', 1).where('type', name)
      price.years = await yearquery.distinct('years').fetch()
      let outsidequery = ClientEstimate.query().where('status', 1).where('type', name)
      price.outside = await outsidequery.distinct('outside').fetch()
      let functionsquery = ClientEstimate.query().where('status', 1).where('type', name)
      price.functions = await functionsquery.distinct('functions').fetch()
      let coolTypequery = ClientEstimate.query().where('status', 1).where('type', name)
      price.coldType = await coolTypequery.distinct('coldType').fetch()
    } else {
      let pricequery = ClientEstimate.query().where('status', 1).where('type', name)
      if (functions) {
        pricequery.where('functions', functions)
      }
      if (subType) {
        pricequery.where('subType', subType)
      }
      if (spu) {
        pricequery.where('spu', spu)
      }
      if (years) {
        pricequery.where('years', years)
      }
      if (outside) {
        pricequery.where('outside', outside)
      }
      if (name == '空调' && coldType && coldType != 'null') {
        pricequery.where('coldType', coldType)
      }
      let pricejson = await pricequery.first()
      price = pricejson ? pricejson.toJSON() : {}
    }
    response.json(price)
  }
}

module.exports = IndexController
