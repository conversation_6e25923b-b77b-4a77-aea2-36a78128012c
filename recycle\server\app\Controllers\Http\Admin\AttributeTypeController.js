'use strict'

const _ = require('lodash')
const moment = require('moment')

const { AttributeType } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//废品属性类型
class AttributeTypeController {
	async index({ request, response }) {
		let { current = 1, pageSize = 10, id } = request.all()
		let query = AttributeType.query().where('wasteID', id)
		let vo = await query.paginate(current, pageSize)
		response.json(vo)
	}
	async show({ request, params, response }) {
		let vo = await AttributeType.query().where('wasteID', params.id).fetch()
		if (!vo) {
			throw ERR.RESTFUL_GET_ID
		}
		response.json(vo)
	}
	async store({ request, response }) {
		let { wasteID, name } = request.all()
		if (!wasteID || !name) {
			throw ERR.INVALID_PARAMS
		}
		let vo = await AttributeType.create({ wasteID, name })
		response.json(vo)
	}
	async update({ request, params, response }) {
		let vo = await AttributeType.find(params.id)
		if (!vo) {
			throw ERR.RESTFUL_UPDATE_ID
		}
		_.assign(vo, request.all())
		await vo.save()
		response.json(vo)
	}
	async destroy({ request, params, response }) {
		let vo = await AttributeType.find(params.id)
		if (!vo) {
			throw ERR.RESTFUL_DELETE_ID
		}
		await vo.delete()
		response.json(vo)
	}
}

module.exports = AttributeTypeController
