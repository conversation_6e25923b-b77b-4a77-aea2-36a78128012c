.BaseLayout {
  background: url(https://oss.evergreenrecycle.cn/donggua/client/images/s-bg.png) no-repeat top center;
  background-size: 100vw auto;
  background-color: #f5f5f5;
}
.user_center {
  min-height: calc(100vh - 13.1vw);
  width: 100vw;
  // background: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-y: auto;
  .select_item {
    padding: 30px 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #ffffff;
    box-shadow: 0px 4px 8px rgba(232, 232, 232, 0.5);
    border-radius: 16px;

    .item {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 24px;
      color: #999999;
      .item_image_wrapper {
        height: 70px;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        position: relative;
        .item_image1 {
          width: 56px;
          height: 50px;
        }
        .item_image2 {
          width: 76px;
          height: 44px;
        }
        .item_image3 {
          width: 48px;
          height: 42px;
        }
        .at-badge {
          position: absolute;
          right: 0;
          top: 0;
        }
      }
    }
  }
  .top_wrapper {
    width: 750px;
    padding: 0 40px;

    .avatar_wrapper {
      display: flex;
      align-items: center;
      padding: 50px 0;
      .wrapper {
        height: 100%;
        padding-bottom: 34px;
        display: flex;
        align-items: center;
        font-size: 30px;
        color: #fff;
        font-weight: 700;
        .avatar {
          height: 116px;
          width: 116px;
          border-radius: 50%;
          overflow: hidden;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 22px;
          .avatar_image {
            height: 100%;
            width: 100%;
          }
        }
      }
    }
  }
  .divide {
    width: 100%;
    height: 12px;
    background: #f3f3f3;
    margin-top: 24px;
  }
  .management_wrapper {
    width: 670px;
    padding: 0 50px;
    margin-top: 40px;
    background: #ffffff;
    height: fit-content;
    box-shadow: 0px 4px 8px rgba(232, 232, 232, 0.5);
    border-radius: 16px;
    button {
      padding: 0;
      border: none;
      // opacity: 0;
      background-color: transparent;
    }
    button::after {
      border: none;
    }
    .management_item {
      z-index: 9;
      height: 110px;
      padding: 32px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 28px;
      color: #556073;
      border-bottom: 1px #ebeced solid;
      font-weight: 700;
      position: relative;
      .left_wrapper {
        display: flex;
        align-items: center;
        .management_item_image {
          height: 30px;
          max-width: 30px;
          margin-right: 18px;
        }
      }
      .operate_item_image {
        height: 20px;
        width: 20px;
      }
    }
    .button_management_item {
      padding: 18px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 28px;
      color: #556073;
      font-weight: 700;
      position: relative;
      .left_wrapper {
        margin-left: 20px;
        display: flex;
        align-items: center;
        .management_item_image {
          height: 30px;
          max-width: 30px;
          margin-right: 36px;
        }
      }
      .operate_item_image {
        height: 20px;
        width: 20px;
      }
    }
  }
}
.top_container {
  position: relative;
  width: 100%;
  .bg {
    width: 100%;
    height: 354px;
  }
}
.hidden {
  opacity: 0;
}
.add_item {
  height: 120px;
}
.the_customer {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
}
.chooseAvatar_container {
  padding-top: 70px;
  .tl-card-1 {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    background: #fff;
    margin-top: 24rpx;
    margin-bottom: 24rpx;
    .tl-height-100 {
      position: relative;
      /*width: 686rpx;*/
      padding: 28rpx 24rpx;
    }
    .tl-line-2 {
      width: 638rpx;
      height: 2rpx;
      background: #eeeeee;
      margin: 0 auto;
    }
  }
  .tl-img-24 {
    width: 24rpx;
    height: 24rpx;
  }
  .tl-img-60 {
    width: 68rpx;
    height: 68rpx;
    border-radius: 50%;
  }
  .tl-p-r {
    position: absolute;
    top: 16rpx;
    right: 26rpx;
  }
  .tl-p-r2 {
    position: absolute;
    top: 28rpx;
    right: 26rpx;
    text-align: right;
  }
  // 重置按钮的样式
  button::after {
    border: none;
  }
  button {
    position: relative;
    display: block;
    margin-left: auto;
    margin-right: auto;
    padding-left: 0px;
    padding-right: 0px;
    box-sizing: border-box;
    // font-size: 18px;
    text-align: center;
    text-decoration: none;
    // line-height: 1;
    line-height: 0;
    background: none;
    // border-radius: 5px;
    -webkit-tap-highlight-color: transparent;
    overflow: hidden;
    color: #000000;
    width: 100%;
    height: 100%;
  }
}
.submit{
  margin-top: 90px;
  height: 88px;
  width: 600px;
  background: linear-gradient(to right, rgba(119, 220, 143, 1) 0% , rgba(21, 179, 129, 1) 100%);
  line-height: 88px;
  text-align: center;
  border-radius: 44px;
  color: #ffffff;
  font-size: 30px;
  font-weight: 700;
}
