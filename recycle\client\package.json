{"name": "d<PERSON>er_minapp", "version": "1.0.0", "private": true, "description": "回收师傅端", "templateInfo": {"name": "default", "typescript": false, "css": "less"}, "scripts": {"build:weapp": "taro build --type weapp", "build:h5": "taro build --type h5", "dev:weapp": "taro build --type weapp --watch --env production", "dev:h5": "npm run build:h5 -- --watch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.7.7", "@rematch/core": "^2.2.0", "@rematch/loading": "^2.1.2", "@tarojs/components": "3.4.3", "@tarojs/plugin-framework-react": "3.4.3", "@tarojs/react": "3.4.3", "@tarojs/runtime": "3.4.3", "@tarojs/taro": "3.4.3", "axios": "^0.26.1", "dayjs": "^1.11.0", "moment": "^2.29.1", "react": "^17.0.0", "react-dom": "^17.0.0", "react-redux": "^7.2.6", "redux": "^5.0.1", "taro-ui": "^3.0.0-alpha.3"}, "devDependencies": {"@babel/core": "^7.8.0", "@tarojs/mini-runner": "3.4.3", "@tarojs/webpack-runner": "3.4.3", "@types/react": "^17.0.2", "@types/webpack-env": "^1.13.6", "babel-preset-taro": "3.4.3", "eslint": "^6.8.0", "eslint-config-taro": "3.4.3", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "stylelint": "^14.4.0"}, "trustedDependencies": ["core-js", "core-js-pure", "es5-ext", "swiper"]}