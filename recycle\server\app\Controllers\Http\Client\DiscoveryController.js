'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Discovery, DiscoveryComment } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { CryptUtil } = require('../../../Util')
const Env = use('Env')

//帖子
class DiscoveryController {
  //获取帖子列表
  async index({ request, response }) {
    let { type, page = 1, perPage = 10, sort = 'desc' } = request.all()
    let query = Discovery.query()
      .where('deletedAt', 0)
      .where('isReport', 1)
      .with('comments')

    if (type) {
      query.where('type', type)
    }
    let vo = await query.orderBy('id', sort).fetch()
    response.json(vo)
  }
  //发表帖子评论
  async store({ request, response }) {
    let { discoveryID, content, toUserID, toUserName } = request.all()
    let user = request.user
    if (!discoveryID || !content) {
      throw ERR.INVALID_PARAMS
    }
    let discovery = await Discovery.find(discoveryID)
    if (!discovery) {
      throw ERR.RESTFUL_GET_ID
    }
    let vo = await DiscoveryComment.create({ discoveryID, content, toUserID, userID: user.id, userName: user.nickName, toUserName })
    discovery.commentCount = discovery.commentCount + 1
    await discovery.save()
    response.json(vo)
  }
}

module.exports = DiscoveryController
