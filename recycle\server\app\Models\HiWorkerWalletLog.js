'use strict'

const Model = use('Model')

/**
 * 嗨回收师傅钱包记录模型
 * 记录师傅钱包的收入和支出明细
 */
class HiWorkerWalletLog extends Model {
  static get table() {
    return 'hi_worker_wallet_log'
  }
  
  static get primaryKey() {
    return 'id'
  }
  
  static get createdAtColumn() {
    return 'createdAt'
  }
  
  static get updatedAtColumn() {
    return null
  }

  /**
   * 关联师傅信息
   */
  worker() {
    return this.belongsTo('App/Models/HiWorker', 'workerID', 'id')
  }

  /**
   * 关联订单信息（如果是订单相关的钱包变动）
   */
  order() {
    return this.belongsTo('App/Models/HiOrder', 'orderID', 'id')
  }

  /**
   * 获取交易类型文本
   */
  static getTypeText(type) {
    const typeMap = {
      '1': '订单收入',
      '2': '系统充值',
      '3': '提现扣除',
      '4': '系统扣费',
      '5': '订单退款',
      '6': '奖励发放',
      '7': '违约扣费'
    }
    return typeMap[type] || '其他'
  }

  /**
   * 获取交易状态文本
   */
  static getStatusText(status) {
    const statusMap = {
      '0': '处理中',
      '1': '成功',
      '2': '失败'
    }
    return statusMap[status] || '未知'
  }
}

module.exports = HiWorkerWalletLog 