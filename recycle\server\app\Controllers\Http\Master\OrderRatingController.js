'use strict'

const _ = require('lodash')
const moment = require('moment')

const { OrderRating, Order, Worker } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//订单评价
class OrderRatingController {
  async index({ request, response }) {
    let workerID = request.worker.id
    let vo = await OrderRating.query()
      .where('workerID', workerID)
      .with('order')
      .orderBy('createdAt', 'desc')
      .fetch()
    response.json(vo)
  }
}

module.exports = OrderRatingController
