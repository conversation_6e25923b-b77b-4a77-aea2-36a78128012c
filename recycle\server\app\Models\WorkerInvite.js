'use strict'

const Model = use('Model')

class WorkerInvite extends Model {
	static get table() { return 'worker_invite' }
	static get primaryKey() { return 'id' }
	static get createdAtColumn() { return 'createdAt' }
	static get updatedAtColumn() { return null }
	userInfo() {
		return this.hasOne('App/Models/User', 'userID', 'id')
	}
	workerInfo() {
		return this.hasOne('App/Models/Worker', 'workerID', 'id')
	}
}

module.exports = WorkerInvite