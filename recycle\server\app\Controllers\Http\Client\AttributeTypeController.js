'use strict'

const _ = require('lodash')
const moment = require('moment')

const { AttributeType } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//废品属性类型
class AttributeTypeController {
  //废品属性类型列表
  async show({ request, params, response }) {
    let vo = await AttributeType.query()
      .where('wasteID', params.id)
      .fetch()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
}

module.exports = AttributeTypeController
