---
description: 安全标准和最佳实践
---

# 安全标准和最佳实践

## 认证和授权

### JWT 认证实现
```javascript
// 1. JWT 配置
// config/auth.js
module.exports = {
  jwt: {
    secret: Env.get('APP_SECRET', 'secretKey'),
    expiresIn: '7d', // 令牌有效期
    refreshExpiresIn: '30d' // 刷新令牌有效期
  }
};

// 2. 用户登录和令牌生成
class AuthController {
  async login({ request, auth, response }) {
    const { phone, password } = request.all();
    
    try {
      // 验证用户
      const user = await User.query()
        .where('phone', phone)
        .where('status', 'active')
        .first();
        
      if (!user) {
        return response.status(401).json({
          code: 401,
          message: '用户不存在或已被禁用'
        });
      }
      
      // 验证密码
      const isValid = await Hash.verify(password, user.password);
      
      if (!isValid) {
        return response.status(401).json({
          code: 401,
          message: '用户名或密码错误'
        });
      }
      
      // 生成令牌
      const token = await auth.generate(user, {
        expiresIn: '7d',
        // 添加自定义 claims
        data: {
          role: user.role,
          permissions: await user.permissions()
        }
      });
      
      // 返回令牌和用户信息
      return response.json({
        code: 200,
        data: {
          token: token.token,
          refreshToken: token.refreshToken,
          user: {
            id: user.id,
            name: user.name,
            phone: user.phone,
            role: user.role
          }
        },
        message: '登录成功'
      });
    } catch (error) {
      return response.status(500).json({
        code: 500,
        message: '服务器错误',
        error: error.message
      });
    }
  }
}

// 3. 权限中间件
class PermissionMiddleware {
  async handle({ auth, response }, next, props) {
    try {
      // 验证令牌
      const user = await auth.getUser();
      
      // 检查权限
      const hasPermission = await this.checkPermission(user, props);
      
      if (!hasPermission) {
        return response.status(403).json({
          code: 403,
          message: '没有足够权限'
        });
      }
      
      await next();
    } catch (error) {
      return response.status(401).json({
        code: 401,
        message: '未授权或令牌已过期'
      });
    }
  }
  
  async checkPermission(user, requiredPermissions) {
    if (user.role === 'admin') {
      return true; // 管理员拥有所有权限
    }
    
    const userPermissions = await user.permissions();
    
    // 检查是否拥有所需的所有权限
    return requiredPermissions.every(permission => 
      userPermissions.includes(permission)
    );
  }
}

// 4. 路由配置
Route.group(() => {
  Route.post('/orders', 'OrderController.create')
    .middleware(['auth', 'permission:create-order']);
    
  Route.get('/orders/:id', 'OrderController.show')
    .middleware(['auth', 'permission:view-order']);
    
  Route.put('/orders/:id', 'OrderController.update')
    .middleware(['auth', 'permission:update-order']);
    
  Route.delete('/orders/:id', 'OrderController.destroy')
    .middleware(['auth', 'permission:delete-order']);
}).prefix('api/v1');
```

### 前端安全实践
```javascript
// 1. API 请求拦截器
// utils/request.js
import Taro from '@tarojs/taro';

// 请求拦截
const request = (url, options = {}) => {
  const token = Taro.getStorageSync('token');
  
  const defaultOptions = {
    url: `${API_BASE}${url}`,
    header: {
      'Content-Type': 'application/json'
    },
    method: 'GET'
  };
  
  // 添加认证头
  if (token) {
    defaultOptions.header['Authorization'] = `Bearer ${token}`;
  }
  
  // 合并选项
  const finalOptions = { ...defaultOptions, ...options };
  
  return new Promise((resolve, reject) => {
    Taro.request({
      ...finalOptions,
      success: (res) => {
        const { statusCode, data } = res;
        
        // 处理成功响应
        if (statusCode >= 200 && statusCode < 300) {
          resolve(data);
          return;
        }
        
        // 处理认证错误
        if (statusCode === 401) {
          // 清除令牌
          Taro.removeStorageSync('token');
          
          // 跳转到登录页
          Taro.navigateTo({ url: '/pages/login/index' });
          
          reject(new Error('登录已过期，请重新登录'));
          return;
        }
        
        // 处理其他错误
        reject(data);
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
};

// 2. 令牌刷新机制
let isRefreshing = false;
let subscribers = [];

const addSubscriber = (callback) => {
  subscribers.push(callback);
};

const onTokenRefreshed = (newToken) => {
  // 执行所有等待的请求
  subscribers.forEach(callback => callback(newToken));
  subscribers = [];
};

const refreshToken = async () => {
  try {
    const refreshToken = Taro.getStorageSync('refreshToken');
    
    const response = await Taro.request({
      url: `${API_BASE}/auth/refresh`,
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        refreshToken
      }
    });
    
    const { token, refreshToken: newRefreshToken } = response.data.data;
    
    // 保存新令牌
    Taro.setStorageSync('token', token);
    Taro.setStorageSync('refreshToken', newRefreshToken);
    
    return token;
  } catch (error) {
    // 刷新令牌失败，清除用户会话
    Taro.removeStorageSync('token');
    Taro.removeStorageSync('refreshToken');
    
    // 重定向到登录页
    Taro.navigateTo({ url: '/pages/login/index' });
    
    return Promise.reject(error);
  }
};
```

## 数据安全

### 敏感数据处理
```javascript
// 1. 数据脱敏
class UserController {
  async show({ params, response }) {
    const user = await User.find(params.id);
    
    // 脱敏个人信息
    const safeUser = {
      id: user.id,
      name: user.name,
      phone: this.maskPhone(user.phone),
      email: this.maskEmail(user.email),
      avatar: user.avatar,
      created_at: user.created_at
    };
    
    return response.json({
      code: 200,
      data: safeUser
    });
  }
  
  // 手机号脱敏
  maskPhone(phone) {
    if (!phone) return '';
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }
  
  // 邮箱脱敏
  maskEmail(email) {
    if (!email) return '';
    const [name, domain] = email.split('@');
    if (name.length <= 2) {
      return `${name}****@${domain}`;
    }
    return `${name.substring(0, 2)}****@${domain}`;
  }
}

// 2. 敏感数据加密存储
const Encryption = use('Encryption');

class UserModel extends Model {
  static boot() {
    super.boot();
    
    // 保存前加密
    this.addHook('beforeSave', async (userInstance) => {
      // 加密敏感信息
      if (userInstance.dirty.id_card) {
        userInstance.id_card = await Encryption.encrypt(userInstance.id_card);
      }
      
      if (userInstance.dirty.bank_account) {
        userInstance.bank_account = await Encryption.encrypt(userInstance.bank_account);
      }
    });
    
    // 获取后解密
    this.addHook('afterFind', async (userInstance) => {
      if (userInstance.id_card) {
        userInstance.id_card = await Encryption.decrypt(userInstance.id_card);
      }
      
      if (userInstance.bank_account) {
        userInstance.bank_account = await Encryption.decrypt(userInstance.bank_account);
      }
    });
  }
}

// 3. 数据导出加密
const exportUserData = async (userId) => {
  const user = await User.find(userId);
  const userData = user.toJSON();
  
  // 创建加密密码
  const password = crypto.randomBytes(16).toString('hex');
  
  // 加密数据
  const encrypted = await encryptData(userData, password);
  
  // 生成加密文件
  const filePath = `exports/user_${userId}_${Date.now()}.enc`;
  await fs.writeFile(filePath, encrypted);
  
  // 通过安全渠道发送解密密码
  await sendDecryptionPassword(user.phone, password);
  
  return filePath;
};
```

## 输入验证和清理

### 防止 XSS 攻击
```javascript
// 1. 输入验证中间件
class SanitizeMiddleware {
  async handle({ request }, next) {
    // 获取所有输入数据
    const inputs = request.all();
    
    // 清理输入
    const sanitized = this.sanitizeInputs(inputs);
    
    // 替换原始输入
    request.body = sanitized;
    
    await next();
  }
  
  sanitizeInputs(inputs) {
    if (typeof inputs !== 'object' || inputs === null) {
      return inputs;
    }
    
    const result = Array.isArray(inputs) ? [] : {};
    
    // 递归处理嵌套对象
    for (const key in inputs) {
      const value = inputs[key];
      
      if (typeof value === 'object' && value !== null) {
        result[key] = this.sanitizeInputs(value);
      } else if (typeof value === 'string') {
        // 清理字符串
        result[key] = this.sanitizeString(value);
      } else {
        result[key] = value;
      }
    }
    
    return result;
  }
  
  sanitizeString(value) {
    // 清理 HTML 标签和特殊字符
    return value
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }
}

// 2. 前端输入验证
const validateForm = (values) => {
  const errors = {};
  
  // 验证姓名
  if (!values.name) {
    errors.name = '姓名不能为空';
  } else if (values.name.length > 50) {
    errors.name = '姓名不能超过50个字符';
  }
  
  // 验证手机号
  if (!values.phone) {
    errors.phone = '手机号不能为空';
  } else if (!/^1[3-9]\d{9}$/.test(values.phone)) {
    errors.phone = '请输入有效的手机号';
  }
  
  // 验证地址
  if (!values.address) {
    errors.address = '地址不能为空';
  } else if (values.address.length > 200) {
    errors.address = '地址不能超过200个字符';
  }
  
  return errors;
};

// 3. 内容安全策略
// 在 Nginx 配置中添加
// add_header Content-Security-Policy "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:; connect-src 'self' api.example.com; frame-ancestors 'none'; object-src 'none';";
```

## 防止常见攻击

### 防止 SQL 注入
```javascript
// 1. 使用查询构建器
class OrderController {
  async index({ request, response }) {
    const { status, user_id, keyword } = request.all();
    
    // 使用查询构建器
    const query = Order.query();
    
    // 安全地添加过滤条件
    if (status) {
      query.where('status', status);
    }
    
    if (user_id) {
      query.where('user_id', user_id);
    }
    
    if (keyword) {
      query.where(builder => {
        builder.where('order_no', 'like', `%${keyword}%`)
          .orWhere('remark', 'like', `%${keyword}%`);
      });
    }
    
    const orders = await query.paginate(page, limit);
    
    return response.json({
      code: 200,
      data: orders
    });
  }
}

// 2. 使用参数化查询
const Database = use('Database');

const getRawOrders = async (status) => {
  // 使用参数化查询，防止 SQL 注入
  const orders = await Database
    .raw('SELECT * FROM orders WHERE status = ?', [status]);
    
  return orders;
};

// 3. 数据库权限最小化
// database/migrations/xxx_create_users_table.js
up() {
  this.create('users', (table) => {
    table.increments();
    table.string('name', 100).notNullable();
    table.string('phone', 20).notNullable().unique();
    table.string('password', 100).notNullable();
    table.string('role').defaultTo('user');
    table.boolean('is_active').defaultTo(true);
    table.timestamps();
  });
}
```

### 防止 CSRF 攻击
```javascript
// 1. 服务端 CSRF 防护
// start/kernel.js
const globalMiddleware = [
  'Adonis/Middleware/BodyParser',
  'Adonis/Middleware/Session',
  'Adonis/Middleware/Shield' // 包含 CSRF 保护
];

// config/shield.js
module.exports = {
  csrf: {
    enable: true,
    methods: ['POST', 'PUT', 'DELETE'],
    filterUris: ['/api/v1/webhook'], // 排除 webhook 接口
    cookieOptions: {
      httpOnly: true,
      sameSite: 'strict',
      path: '/'
    }
  }
};

// 2. 前端 CSRF 令牌处理
// React 组件
const Form = () => {
  const [csrfToken, setCsrfToken] = useState('');
  
  useEffect(() => {
    // 获取 CSRF 令牌
    const getCsrfToken = async () => {
      const response = await fetch('/api/csrf-token');
      const data = await response.json();
      setCsrfToken(data.csrfToken);
    };
    
    getCsrfToken();
  }, []);
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    
    await fetch('/api/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken
      },
      body: JSON.stringify(Object.fromEntries(formData))
    });
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <input type="hidden" name="_csrf" value={csrfToken} />
      {/* 表单字段 */}
      <button type="submit">提交</button>
    </form>
  );
};
```

## 文件上传安全

### 安全的文件上传处理
```javascript
// 1. 文件上传验证
class UploadController {
  async upload({ request, response }) {
    // 文件验证规则
    const validationOptions = {
      types: ['image'],
      size: '2mb',
      extnames: ['jpg', 'png', 'jpeg', 'webp']
    };
    
    // 验证文件
    const image = request.file('image', validationOptions);
    
    if (!image) {
      return response.status(400).json({
        code: 400,
        message: '没有上传文件'
      });
    }
    
    // 验证失败处理
    if (!image.isValid) {
      return response.status(400).json({
        code: 400,
        message: image.error().message
      });
    }
    
    // 生成安全文件名
    const fileName = `${Date.now()}_${Math.random().toString(36).substr(2, 8)}.${image.extname}`;
    
    // 存储文件
    await image.move('public/uploads', {
      name: fileName,
      overwrite: false
    });
    
    // 返回文件路径
    return response.json({
      code: 200,
      data: {
        url: `/uploads/${fileName}`
      },
      message: '上传成功'
    });
  }
}

// 2. 前端文件上传安全处理
// 上传前检查
const validateFile = (file) => {
  // 检查文件大小
  const MAX_SIZE = 2 * 1024 * 1024; // 2MB
  if (file.size > MAX_SIZE) {
    return '文件大小不能超过2MB';
  }
  
  // 检查文件类型
  const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp'];
  if (!ALLOWED_TYPES.includes(file.type)) {
    return '只允许上传JPG、PNG、WEBP格式的图片';
  }
  
  // 检查文件名
  const FILENAME_REGEX = /^[a-zA-Z0-9_\- .]+$/;
  if (!FILENAME_REGEX.test(file.name)) {
    return '文件名包含非法字符';
  }
  
  return null; // 验证通过
};

// 上传组件
const FileUpload = () => {
  const [file, setFile] = useState(null);
  const [error, setError] = useState('');
  
  const handleChange = (e) => {
    const selectedFile = e.target.files[0];
    if (!selectedFile) return;
    
    // 验证文件
    const validationError = validateFile(selectedFile);
    if (validationError) {
      setError(validationError);
      setFile(null);
      return;
    }
    
    setError('');
    setFile(selectedFile);
  };
  
  const handleUpload = async () => {
    if (!file) return;
    
    const formData = new FormData();
    formData.append('image', file);
    
    try {
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      });
      
      const result = await response.json();
      
      if (response.ok) {
        // 上传成功
        console.log('文件上传成功:', result.data.url);
      } else {
        // 上传失败
        setError(result.message || '上传失败');
      }
    } catch (error) {
      setError('上传过程中发生错误');
    }
  };
  
  return (
    <div>
      <input type="file" onChange={handleChange} />
      {error && <div className="error">{error}</div>}
      <button onClick={handleUpload} disabled={!file || error}>
        上传
      </button>
    </div>
  );
};
```

## 安全配置检查清单

### 部署前安全检查
```markdown
## 服务器安全配置
- [ ] 禁用未使用的服务和端口
- [ ] 配置防火墙规则
- [ ] 启用 HTTPS
- [ ] 配置合适的 SSL/TLS 设置
- [ ] 设置 HTTP 安全头部

## 应用程序安全
- [ ] 移除开发模式和调试信息
- [ ] 配置适当的错误处理
- [ ] 启用 CSRF 保护
- [ ] 配置 CSP (内容安全策略)
- [ ] 设置适当的 CORS 规则
- [ ] 启用 XSS 保护机制

## 数据库安全
- [ ] 使用最小权限原则配置数据库用户
- [ ] 禁用远程数据库连接
- [ ] 加密敏感数据
- [ ] 确保数据库备份加密

## 认证与授权
- [ ] 配置适当的密码策略
- [ ] 实现账户锁定机制
- [ ] 配置 JWT 有效期和刷新机制
- [ ] 实现双因素认证

## 文件上传
- [ ] 设置上传文件大小限制
- [ ] 配置安全的文件存储位置
- [ ] 实施文件类型验证

## 日志和监控
- [ ] 配置安全相关日志
- [ ] 实施入侵检测机制
- [ ] 配置异常行为监控
- [ ] 设置安全告警机制
```