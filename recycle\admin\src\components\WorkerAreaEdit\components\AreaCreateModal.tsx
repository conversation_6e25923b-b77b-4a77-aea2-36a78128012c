import { useEffect, useState } from 'react'
import { notification, Select, Button, message, Modal } from 'antd'
import styles from '../index.module.less'
import { effect, reducer, useConnect } from 'dva17'
import {
    NCompany,
    EGetTownAddress,
    NWorker,
    EGetAddress,
    EWorkerAreaEdit,
    ECityAddress,
    RSetState,
} from '../../../common/action'
import { getFirstKind, getSecondKind } from '../../../services/price'
import { filter } from 'lodash'
import { provinceList } from '../../../common/enum'

const { Option } = Select

interface AreaCreateModalProps {
    visible: boolean;
    workerDetail: any;
    workerId: number;
    onCancel: () => void;
    onSuccess: () => void;
}

const AreaCreateModal = ({ visible, workerDetail, workerId, onCancel, onSuccess }: AreaCreateModalProps) => {
    /*--------------------- 常变量 ---------------------*/
    const [firstKind, setFirstKind] = useState<any>([])
    const [selectProvince, setSelectProvince] = useState<any>(null)
    const [addList, setAddList] = useState<any>([])
    const [details, setDetails] = useState<any>([])
    const [selectCity, setSelectCity] = useState<any>(null)
    const { addressList, townAddressList } = useConnect(NWorker)
    const { cityList } = useConnect(NCompany)

    /*--------------------- 生命周期 ---------------------*/
    useEffect(() => {
        if (visible) {
            getAddressData()
        }
    }, [visible])

    useEffect(() => {
        let objAddress = filter(addressList, { value: true })
        let objTown = filter(townAddressList, { value: true })
        let objType = filter(firstKind, { value: true })
        let objDetail = filter(details, { value: true })
        if (objType.length > 0 && objTown.length > 0) {
            let arrobj: any = []
            for (let index = 0; index < objType.length; index++) {
                const element = objType[index];
                if (element.id === 3 && objDetail.length > 0) {
                    for (let c = 0; c < objTown.length; c++) {
                        for (let b = 0; b < objDetail.length; b++) {
                            const elementDetail = objDetail[b];
                            let townItem: any = {}
                            townItem.type = element.id
                            townItem.typeName = element.name
                            townItem.detail = elementDetail.id
                            townItem.detailName = elementDetail.name
                            arrobj.push({ ...townItem, ...objTown[c] })
                        }
                    }
                } else {
                    for (let a = 0; a < objTown.length; a++) {
                        let townItem: any = {}
                        townItem.type = element.id
                        townItem.typeName = element.name
                        arrobj.push({ ...townItem, ...objTown[a] })
                    }
                }
            }
            arrobj.map((vo: any) => {
                let areaCode = vo.code?.substring(0, 6)
                vo.areaCode = areaCode
                vo.area = filter(objAddress, { code: areaCode })
                    && filter(objAddress, { code: areaCode })[0]
                    && filter(objAddress, { code: areaCode })[0].name
                delete vo.value
            })
            setAddList(arrobj)
        }
    }, [addressList, townAddressList, firstKind, details])

    /*--------------------- 函數 ---------------------*/
    // 省市选择
    function funSelectProvince(e: any) {
        let provinceCode
        setSelectProvince(e)
        provinceList.forEach((province: any, index: number) => {
            if (province.name === e) {
                provinceCode = province.code
                effect(NCompany, ECityAddress, {
                    code: provinceCode,
                })
                return
            }
        })
    }

    // 城市选择
    const funSelectCity = (e: any) => {
        setSelectCity(e)
        let cityCode
        cityList.forEach((city: any, index: number) => {
            if (city.name === e) {
                cityCode = city.code
                effect(NWorker, EGetAddress, { code: cityCode })
                return
            }
        })
    }

    async function getAddressData() {
        const firstKind: any = await getFirstKind(0)
        setFirstKind(firstKind.data)
    }

    // 区域选择
    const areaSelect = (address: any, index: number) => {
        addressList.map((vo: any) => {
            if (vo === address) {
                vo.value = !vo.value; // 直接取反，简化逻辑
            }
        })
        reducer(NWorker, RSetState, { addressList: [...addressList] })
    }

    // 街道选择
    const streetSelect = (address: any, index: number) => {
        townAddressList.map((vo: any) => {
            if (vo === address) {
                vo.value = !vo.value; // 直接取反，简化逻辑
            }
        })
        reducer(NWorker, RSetState, { townAddressList: [...townAddressList] })
    }

    // 街道全选
    const selectAllStreet = () => {
        // 检查是否所有街道都已选中
        const allSelected = townAddressList?.every((item: any) => item.value);
        
        townAddressList?.map((vo: any) => {
            vo.value = !allSelected; // 如果全部选中则取消全选，否则全选
        })
        reducer(NWorker, RSetState, { townAddressList: [...townAddressList] })
    }

    // 类型选择扩展
    const setRecycelType = async (kind: any, theIndex: any) => {
        if (kind.id === 3 && !kind.value) {
            let resdata = await getSecondKind({ id: 3 })
            setDetails(resdata)
        }
        firstKind.map((vo: any) => {
            if (kind.id === vo.id) {
                vo.value = !vo.value; // 直接取反，简化逻辑
            }
        })
        setFirstKind([...firstKind])
    }

    // 类型全选
    const selectAllKind = () => {
        // 检查是否所有类型都已选中
        const allSelected = details.every((item: any) => item.value);
        
        details.map((vo: any) => {
            vo.value = !allSelected; // 如果全部选中则取消全选，否则全选
        })
        setDetails([...details])
    }

    // 小类选择
    const detailSelect = (detail: any, index: number) => {
        details.map((vo: any) => {
            if (vo === detail) {
                vo.value = !vo.value; // 直接取反，简化逻辑
            }
        })
        setDetails([...details])
    }

    // 全选区域
    const selectAllArea = () => {
        // 检查是否所有区域都已选中
        const allSelected = addressList.every((item: any) => item.value);
        
        addressList.map((vo: any) => {
            vo.value = !allSelected; // 如果全部选中则取消全选，否则全选
        })
        reducer(NWorker, RSetState, { addressList: [...addressList] })
    }

    // 保存新增区域
    async function saveRecycelType() {
        if (addList.length > 0) {
            const hide = message.loading('正在添加')
            await effect(NCompany, EWorkerAreaEdit, {
                workerID: workerId,
                companyID: workerDetail.companyID,
                addList: addList,
                removeList: [],
            }).then(() => {
                notification.success({
                    message: "成功",
                    description: '保存成功！',
                    duration: 2
                })
                hide()
                onSuccess()
            })
        } else {
            message.warning('请选择区域和类型')
        }
    }

    // 清空状态
    const handleCancel = () => {
        setSelectProvince(null);
        setSelectCity(null);
        setAddList([]);
        onCancel();
    };

    /*--------------------- 渲染 ---------------------*/
    return (
        <Modal
            title={`${workerDetail?.workerName || ''}-新建服务区域`}
            open={visible}
            onCancel={handleCancel}
            width={1200}
            footer={[
                <Button key="cancel" onClick={handleCancel}>取 消</Button>,
                <Button key="submit" type="primary" onClick={saveRecycelType}>保 存</Button>
            ]}
        >
            <div className={styles.areaEditContainer}>
                <div className={styles.formRow}>
                    <div className={styles.formLabel}>省份选择：</div>
                    <div className={styles.formContent}>
                        <Select
                            value={selectProvince}
                            style={{ width: 200 }}
                            onChange={e => {
                                funSelectProvince(e)
                            }}>
                            {provinceList.map((province: any, index: any) => (
                                <Option key={index} value={province.name}>{province.name}</Option>
                            ))}
                        </Select>
                    </div>
                </div>

                <div className={styles.formRow}>
                    <div className={styles.formLabel}>城市选择：</div>
                    <div className={styles.formContent}>
                        <Select
                            value={selectCity}
                            style={{ width: 200 }}
                            onChange={e => {
                                funSelectCity(e)
                            }}>
                            {cityList && workerDetail && cityList.map((city: any, index: any) => (
                                <Option
                                    key={index}
                                    value={city.name}>
                                    {city.name}
                                </Option>
                            ))}
                        </Select>
                    </div>
                </div>

                {addressList ? (
                    <div className={styles.formRow}>
                        <div className={styles.formLabel}>
                            区域选择：
                            <span style={{ color: 'blue', cursor: 'pointer', textDecoration: 'underline' }} onClick={() => { selectAllArea() }}>全选</span>
                        </div>
                        <div className={styles.formContent}>
                            <div className={styles.buttonGroup}>
                                {addressList.map((address: any, index: any) => (
                                    <Button
                                        type={address?.value ? "primary" : "default"}
                                        key={address + index}
                                        onClick={() => {
                                            areaSelect(address, index)
                                        }}>
                                        {address.name}
                                    </Button>
                                ))}
                            </div>
                        </div>
                    </div>
                ) : null}

                {addressList && (
                    <div className={styles.formRow}>
                        <div className={styles.formLabel}>
                            街道选择：
                            <span style={{ color: 'blue', cursor: 'pointer', textDecoration: 'underline' }} onClick={() => { selectAllStreet() }}>全选</span>
                        </div>
                        <div className={styles.formContent}>
                            <div className={styles.buttonGroup}>
                                {townAddressList && townAddressList.map((address: any, index: any) => (
                                    <Button
                                        type={address?.value ? "primary" : "default"}
                                        key={address + index}
                                        onClick={() => {
                                            streetSelect(address, index)
                                        }}>
                                        {address.name}
                                    </Button>
                                ))}
                            </div>
                        </div>
                    </div>
                )}

                <div className={styles.formRow}>
                    <div className={styles.formLabel}>回收类型：</div>
                    <div className={styles.formContent}>
                        <div className={styles.buttonGroup}>
                            {firstKind &&
                                firstKind.map((kind: any, index: any) => (
                                    <Button
                                        type={kind?.value ? "primary" : "default"}
                                        key={kind + index}
                                        onClick={() => {
                                            setRecycelType(kind, index)
                                        }}>
                                        {kind.name}
                                    </Button>
                                ))}
                        </div>
                    </div>
                </div>

                {firstKind && filter(firstKind, { id: 3, value: true }).length > 0 && (
                    <div className={styles.formRow}>
                        <div className={styles.formLabel}>
                            家电类型：
                            <span style={{ color: 'blue', cursor: 'pointer', textDecoration: 'underline' }} onClick={() => { selectAllKind() }}>全选</span>
                        </div>
                        <div className={styles.formContent}>
                            <div className={styles.buttonGroup}>
                                {details &&
                                    details.map((detail: any, index: any) => (
                                        <Button
                                            type={detail?.value ? "primary" : "default"}
                                            key={detail + index}
                                            onClick={() => {
                                                detailSelect(detail, index)
                                            }}>
                                            {detail.name}
                                        </Button>
                                    ))}
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </Modal>
        
    )
}

export default AreaCreateModal 