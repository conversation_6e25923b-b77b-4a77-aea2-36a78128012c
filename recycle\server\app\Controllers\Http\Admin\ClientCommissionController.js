'use strict'

const _ = require('lodash')
const moment = require('moment')

const { ClientCommission } = require('../../../Models')
const { ERR, E } = require('../../../../../constants')
const { CryptUtil } = require('../../../Util')
const Env = use('Env')

//定价表
class ClientCommissionController {
  async index({ request, response }) {
    let { source, type, area, priceType, current = 1, pageSize = 100, sort = 'asc' } = request.all()
    let query = ClientCommission.query().with('company')
    if (source) {
      query.whereRaw('source like ?', [`%${source}%`])
    }
    if (type) {
      query.whereRaw('type like ?', [`%${type}%`])
    }
    if (priceType) {
      query.where('priceType', priceType)
    }
    if (area) {
      query.whereRaw('area like ?', [`%${area}%`])
    }
    let vo = await query.orderBy('id', sort).paginate(current, pageSize)
    response.json(vo)
  }
  //创建
  async store({ request, response }) {
    let { type, price, area, source, companyID, priceType, priceRate } = request.all()
    if (!type || !price) {
      throw ERR.INVALID_PARAMS
    }
    let vo = await ClientCommission.create({
      type,
      price,
      area,
      source,
      companyID,
      priceType,
      priceRate
    })
    response.json(vo)
  }
  //更新
  async update({ request, params, response }) {
    let { price, type, area, source } = request.all()
    let vo = await ClientCommission.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
  }
  //删除
  async destroy({ request, params, response }) {
    let vo = await ClientCommission.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_DELETE_ID
    } else {
      await vo.delete()
    }
    response.json(vo)
  }
  async bundleClientPrice({ request, response }) {
    let { ids, price } = request.all()
    if (ids.length === 0) {
      response.json({ msg: '无数据', code: 200 })
    } else {
      await _.forEach(ids, async function (vo) {
        let priceVo = await ClientCommission.find(vo)
        priceVo.price = price
        await priceVo.save()
      })
    }
    response.json({ msg: 'ok', code: 200 })
  }
}

module.exports = ClientCommissionController
