'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Worker, WorkerPay, YCWorkerWalletLog } = require('../../../Models')
const { ERR, E } = require('../../../../../constants')
const { OrderService, ExcelService } = require('../../../Services')

class YCWorkerPointController {
  async exportMasterXLS({ request, params, response }) {
    let query = Worker.query().with('company').where('isUse', '同意')
    let vo = await query.orderBy('createdAt', 'desc').fetch()
    let data = []
    vo.rows.forEach((value, index) => {
      let arrInner = []
      arrInner.push(value.id)
      arrInner.push(value.workerName)
      arrInner.push(value.mobile)
      arrInner.push(value.wallet)
      arrInner.push(value.$relations.company ? value.$relations.company.companyName : '未知')
      arrInner.push(value.$relations.company ? value.$relations.company.mobile : '未知')
      arrInner.push(parseInt(value.forbidden) ? '正常' : '停用')
      arrInner.push(moment(value && value.createdAt).format('YYYY-MM-DD HH:mm'))
      data.push(arrInner)
    })
    let theTitle = [
      'ID',
      '师傅名称',
      '师傅电话',
      '余额',
      '公司',
      '公司电话',
      '状态',
      '注册时间',
    ]
    let theWidth = [
      { wch: 14 },
      { wch: 16 },
      { wch: 22 },
      { wch: 14 },
      { wch: 22 },
      { wch: 22 },
      { wch: 14 },
      { wch: 22 },
    ]
    let fileTitle = '师傅统计-' + `${moment().format('YYYYMMDDHH')}`
    await ExcelService.generateExcel(fileTitle, theTitle, data, response, { '!cols': theWidth })
  }
  //订单数据导出接口
  async exportXLS({ request, params, response }) {
    let { status, workerID, type, workerName, source, current = 1, transactionID, pageSize = 10,
      finishStartDate, finishEndDate,
    } = request.all()
    let query = WorkerPay.query().with('worker')
    if (status) {
      if (status === 'all') { } else {
        query.where('status', status)
      }
    }
    if (finishStartDate && finishEndDate) {
      query.where('finishAt', '>=', moment(finishStartDate).toDate()).where('finishAt', '<=', moment(finishEndDate).add(1, 'd').toDate())
    }
    if (source) {
      if (source === 'all') { } else {
        query.where('source', source)
      }
    }
    if (type) {
      if (type === 'all') { } else {
        query.where('type', type)
      }
    }
    if (workerID) {
      query.where('workerID', workerID)
    }
    if (transactionID) {
      query.where('transactionID', 'like', `%${transactionID}%`)
    }
    if (workerName) {
      let workers = await Worker.query().where('workerName', 'like', `%${workerName}%`).select('id', 'workerName').fetch()
      let workerIDs = workers.rows.map((vo) => vo.id)
      query.where('workerID', 'IN', _.uniq(workerIDs))
    }
    let vo = await query.orderBy('finishAt', 'desc').fetch()
    let data = []
    vo.rows.forEach((value, index) => {
      let arrInner = []
      arrInner.push(value.id)
      arrInner.push(value.type)
      arrInner.push(value.source)
      arrInner.push(value.$relations.worker ? value.$relations.worker.workerName : '未知')
      arrInner.push(value.$relations.worker ? value.$relations.worker.mobile : '未知')
      arrInner.push(value ? parseInt(value.totalPay) / 100 : null)
      arrInner.push(value.status)
      arrInner.push(moment(value && value.finishAt).format('YYYY-MM-DD HH:mm'))
      data.push(arrInner)
    })
    let theTitle = [
      'ID',
      '类型',
      '来源',
      '师傅',
      '师傅手机号',
      '支付金额',
      '状态',
      '完成时间',
    ]
    let theWidth = [
      { wch: 14 },
      { wch: 16 },
      { wch: 14 },
      { wch: 16 },
      { wch: 12 },
      { wch: 12 },
      { wch: 22 },
      { wch: 22 },
    ]
    let fileTitle = '押金统计-' + `${moment().format('YYYYMMDDHH')}`
    await ExcelService.generateExcel(fileTitle, theTitle, data, response, { '!cols': theWidth })
  }
  async exportWalletXLS({ request, response }) {
    let { workerID } = request.all()
    let data = await WorkerPay.query().where('workerID', workerID)
      .where('status', E.OrderStatus.Completed)
      .whereNotNull('finishAt')
      .orderBy('finishAt', 'desc')
      .fetch()
    // return data
    // 合并消费记录
    let orderLog = await YCWorkerWalletLog.query()
      .where('workerID', workerID)
      .select('workerID', 'id', 'createdAt', 'money', 'orderID').with('order',
        builder => {
          builder.select('id', 'orderNo', 'status', 'address')
          return builder
        })
      .fetch()

    let resJson = orderLog.toJSON().map((vo, index) => {
      vo.updatedAt = vo.createdAt
      vo.totalPay = parseInt(vo.money) * 100
      return vo
    })
    let res = resJson.concat(data.toJSON())
    let returnVO = _.orderBy(res, ['updatedAt'], ['desc'])
    let query = await Worker.find(workerID)
    let redata = []
    returnVO.forEach((value, index) => {
      let arrInner = []
      arrInner.push(value.id)
      arrInner.push(query.toJSON() && query.toJSON().workerName)
      arrInner.push(value.workerID)
      arrInner.push(value.totalPay ? value.totalPay / 100 : value.money)
      arrInner.push(value.order ? '扣款' : '充值')
      arrInner.push(value.order ? value.order.orderNo : value.transactionID)
      arrInner.push(value.updatedAt)
      redata.push(arrInner)
    })
    let theTitle = [
      'ID',
      '师傅名称',
      '师傅ID',
      '金额',
      '类型',
      '单号',
      '更新时间',
    ]
    let theWidth = [
      { wch: 8 },
      { wch: 10 },
      { wch: 8 },
      { wch: 10 },
      { wch: 10 },
      { wch: 22 },
      { wch: 22 },
    ]
    let fileTitle = '师傅钱包统计-' + `${query.toJSON().workerName}` + `${moment().format('YYYYMMDDHH')}`
    await ExcelService.generateExcel(fileTitle, theTitle, redata, response, { '!cols': theWidth })
  }
  async workerPaylist({ request, response, params }) {
    let { status, workerID, type, workerName, source, current = 1, transactionID, pageSize = 10,
      finishStartDate, finishEndDate,
    } = request.all()
    let query = WorkerPay.query().with('worker')
    if (status) {
      if (status === 'all') { } else {
        query.where('status', status)
      }
    }
    if (finishStartDate && finishEndDate) {
      query.where('finishAt', '>=', moment(finishStartDate).toDate()).where('finishAt', '<=', moment(finishEndDate).add(1, 'd').toDate())
    }
    if (source) {
      if (source === 'all') { } else {
        query.where('source', source)
      }
    }
    if (type) {
      if (type === 'all') { } else {
        query.where('type', type)
      }
    }
    if (workerID) {
      query.where('workerID', workerID)
    }
    if (transactionID) {
      query.where('transactionID', 'like', `%${transactionID}%`)
    }
    if (workerName) {
      let workers = await Worker.query().where('workerName', 'like', `%${workerName}%`).select('id', 'workerName').fetch()
      let workerIDs = workers.rows.map((vo) => vo.id)
      query.where('workerID', 'IN', _.uniq(workerIDs))
    }
    let data = await query.orderBy('finishAt', 'desc').paginate(current, pageSize)
    return data
  }
  async masterPayRefund({ request, response }) {
    let { id, reason = "后台退押金", amount } = request.all()
    let res = await OrderService.refundMasterPay(id, reason, amount)
    return res
  }
}

module.exports = YCWorkerPointController
