import { requestGet } from 'dva17'
import {
  EChangeCoWorker,
  EDelete, EGet, EGetAddress, EGetDetailWorker,
  EGetDiscoveryList, EGetFullTimeWorker, EGetList,
  EGetTownAddress, EGetTownList, EGetWorkerMaintain, EGetWorkerPaylist,
  EGetWorkerReview, EGetWorkerWalletlog, EPost,
  EPostCoWorker,
  EPostDiscoveryList, EPostWorkerWallet, EPut,
  EPutDiscoveryList, EPutWorkerMaintain, EWorkerSelected, NHiWorker, RAdd,
  RSetState,
} from '../common/action'
import { adapterPaginationResult } from '../common/utils'
import {
  getWorkerReview, getFullTimeWorker,
  getDetailWorker, changeWorkerUse,
  addWorkAddress, removeWorkAddress,
  getTownList, getTheWorkerList,
  getWorkerPaylist, getWorkerArealist,
  postWorkerWallet, getWorkerWalletLog,
  postWorkerInsure,
  getWorkerMaintain,
  putWorkerMaintain,
  deleteWorker,
  deleteCoWorker,
  putcoworker,
  postcoworker,
} from '../services/HiWorker'
import { getDiscoveryList, editDiscovery, createDiscovery } from '../services/discovery'

import { EChangeWorkerUse, EAddWorkAddress, EPostWorkerInsure, EGetTheWorkerList, EDeleteWorker, EDeleteCoWorker } from '../common/action'

export default {
  namespace: NHiWorker,
  state: {
    workerReviewList: [],
    workerMaintainList: [],
    lastSearch: {},
    workerList: null,
    workerDetail: null,
    addressList: null,
    townAddressList: null,
    isGetDetail: false,
    isGetTown: false,
    isUseChange: false,
    townList: null,
    theWorkerList: null,
    workerPaylist: [],
  },
  reducers: {
    [RSetState](state: any, payload: any) {
      return { ...state, ...payload }
    },
    [RAdd](state: { count: any }, payload: any) {
      return { ...state, count: state.count + payload }
    },
  },
  effects: {
    //  标准CURD示例
    async [EGet]({ payload }: any, { reducer }: any) {
      let result = await requestGet('HiWorker', { ...payload, isUse: '同意' })
      await reducer(RSetState, { workerList: result.data })
      return adapterPaginationResult(result)
    },
    // 充值
    async [EPostWorkerWallet]({ payload }: any, { reducer }: any) {
      const response = await postWorkerWallet(payload)
      await reducer(RSetState, { workerWallet: response })
    },
    //钱包消费记录
    async [EGetWorkerWalletlog]({ payload }: any, { reducer }: any) {
      const response = await getWorkerWalletLog(payload)
      reducer(RSetState, { workerPaylist: response })
      return adapterPaginationResult(response)
    },

    // 获取待审核人员
    async [EGetWorkerReview]({ payload }: any, { reducer }: any) {
      const response = await getWorkerReview(payload)
      reducer(RSetState, { workerReviewList: response })
    },
    // 获取待维护人员
    async [EGetWorkerMaintain]({ payload }: any, { reducer }: any) {
      const response = await getWorkerMaintain(payload)
      reducer(RSetState, { workerMaintainList: response })
    },
    // 添加待维护人员
    async [EPutWorkerMaintain]({ payload }: any, { reducer }: any) {
      const response = await putWorkerMaintain(payload)
    },
    // 获取已审核人员列表
    async [EGetFullTimeWorker]({ payload }: any, { reducer }: any) {
      const response = await getFullTimeWorker(payload)
      reducer(RSetState, { workerList: response })
    },
    //获取人员详情
    async [EGetDetailWorker]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isGetDetail: false })
      const response = await getDetailWorker(payload)
      reducer(RSetState, { isGetDetail: true, workerDetail: response })
      return response
    },
    //获取区地址
    async [EGetAddress]({ payload }: any, { reducer }: any) {
      let res = await requestGet('address', payload)
      reducer(RSetState, { addressList: res })
    },
    //获取街道地址
    async [EGetTownAddress]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isGetTown: false })
      let res = await requestGet('address', payload)
      reducer(RSetState, { townAddressList: res, isGetTown: true })
    },
    //worker信息更新
    async [EPut]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isUseChange: false })
      const response = await changeWorkerUse(payload)
      reducer(RSetState, { isUseChange: true })
    },
    async [EWorkerSelected]({ payload }: any, { reducer }: any) {
      const response = await getWorkerArealist(payload)
      return adapterPaginationResult(response)
    },
    //添加工作区域
    async [EPost]({ payload }: any, { reducer }: any) {
      const response = await addWorkAddress(payload)
    },
    //删除工作区域
    async [EDelete]({ payload }: any, { reducer }: any) {
      const response = await removeWorkAddress(payload)
    },
    //街道列表
    async [EGetTownList]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { townList: null })
      const response = await getTownList(payload)
      reducer(RSetState, { townList: response })
    },
    //数据总览中，获取回收人员列表
    async [EGetList]({ payload }: any, { reducer }: any) {
      const response = await getTheWorkerList(payload)
      reducer(RSetState, { theWorkerList: response })
    },
    async [EChangeWorkerUse]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isUseChange: false })
      const response = await changeWorkerUse(payload)
      reducer(RSetState, { isUseChange: true })
    },
    async [EAddWorkAddress]({ payload }: any, { reducer }: any) {
      const response = await addWorkAddress(payload)
    },

    async [EGetTheWorkerList]({ payload }: any, { reducer }: any) {
      const response = await getTheWorkerList(payload)
      reducer(RSetState, { theWorkerList: response })
    },
    //支付记录
    async [EGetWorkerPaylist]({ payload }: any, { reducer }: any) {
      const response = await getWorkerPaylist(payload)
      reducer(RSetState, { lastSearch: payload })
      reducer(RSetState, { workerPaylist: response })
      return adapterPaginationResult(response)
    },
    async [EGetDiscoveryList]({ payload }: any, { reducer }: any) {
      const response = await getDiscoveryList(payload)
      return adapterPaginationResult(response)
    },
    async [EPostDiscoveryList]({ payload }: any, { reducer }: any) {
      const response = await createDiscovery(payload)
    },
    async [EPostWorkerInsure]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isUseChange: false })
      const response = await postWorkerInsure(payload)
      reducer(RSetState, { isUseChange: true })
    },
    async [EPutDiscoveryList]({ payload }: any, { reducer }: any) {
      const response = await editDiscovery(payload)
    },
    async [EChangeCoWorker]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isUseChange: false })
      const response = await putcoworker(payload)
      reducer(RSetState, { isUseChange: true })
    },
    async [EDeleteWorker]({ payload }: any, { reducer }: any) {
      const response = await deleteWorker(payload)
    },
    async [EDeleteCoWorker]({ payload }: any, { reducer }: any) {
      const response = await deleteCoWorker(payload)
    },
    async [EPostCoWorker]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isUseChange: false })
      const response = await postcoworker(payload)
      reducer(RSetState, { isUseChange: true })
    },
  },
}
