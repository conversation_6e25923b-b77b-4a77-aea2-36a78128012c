/**
 * Created by <PERSON>(qq:24242811) on 2018/11/16.
 */
'use strict'

const xml2js = require('xml2js')
const _ = require('lodash')

const XMLUtil = {
	async parseXML(xmlString){
		return new Promise((resolve, reject)=>{
			var parser = new xml2js.Parser({trim:true, explicitArray:false, explicitRoot:false});
			parser.parseString(xmlString, (err, result)=>{
				if (err){
					reject(err)
				}else{
					resolve(result)
				}
			});
		})
	},
	buildXML(json){
		var builder = new xml2js.Builder({renderOpts:{pretty:false, cdata:true}});
		return builder.buildObject(json);
	}
}

module.exports = XMLUtil