'use strict'

const moment = require("moment")

const Model = use('Model')

//订单
class ClientOrder extends Model {
  static get table() {
    return 'client_Order'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return 'updatedAt'
  }
  worker() {
    return this.belongsTo('App/Models/Worker', 'workerID', 'id')
  }
  company() {
    return this.belongsTo('App/Models/Company', 'companyID', 'id')
  }
  rating() {
    return this.belongsTo('App/Models/ClientOrderRating', 'id', 'orderID')
  }
  cancel() {
    return this.hasMany('App/Models/ClientOrderCancel', 'id', 'orderID').with('worker')
  }
  orderWastes() {
    return this.hasMany('App/Models/OrderWaste', 'id', 'orderID')
  }
  wasteInfo() {
    return this.hasOne('App/Models/ClientEstimate', 'wasteID', 'id')
  }
  userAddress() {
    return this.belongsTo('App/Models/UserAddress', 'userAddressID', 'id')
  }
  complaint() {
    return this.belongsTo('App/Models/ClientOrderComplaint', 'id', 'orderID')
  }
  operate() {
    return this.hasMany('App/Models/ClientOrderOperate', 'id', 'orderID')
  }
  sales() {
    return this.belongsTo('App/Models/Salesman', 'salesman_id', 'id').with('manager')
  }
  doneInfo() {
    return this.hasOne('App/Models/WorkerWalletLog', 'id', 'orderID')
  }
}

module.exports = ClientOrder
