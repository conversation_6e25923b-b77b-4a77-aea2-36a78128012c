'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Waste1stCategory, Waste, CompanySelfWastePrice } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//废品一级分类
class Waste1stCategoryController {
  async index({ request, response }) {
    let { page = 1, perPage = 10 } = request.all()
    let query = Waste1stCategory.query()
    let vo = await query.paginate(page, perPage)
    response.json(vo)
  }
  async show({ request, params, response }) {
    let vo = await Waste1stCategory.query().where('id', params.id).first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
  async get2ndCategory({ request, params, response }) {
    let vo = await Waste1stCategory.query().where('id', params.id).first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    let secondCategory = await vo.secondCategory().fetch()
    response.json(secondCategory)
  }

  // 根据二级类目（如旧衣旧书）获取三级类目列表，同时根据服务商返回相应的基准价格
  async getThirdList({ request, response }) {
    let { companyID = 1, id, source = E.OrderSource.CaiNiao, type = E.Permission.Primary } = request.all()
    let vo = await Waste1stCategory.query().where('id', id).first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    let secondCategory = await vo.secondCategory().fetch()
    for (let i = 0; i < secondCategory.rows.length; i++) {
      let vo = await Waste.query()
        .where('source', source)
        .where('type', type)
        .where('secondCateID', secondCategory.rows[i].id)
        .fetch()
      for (let j = 0; j < vo.rows.length; j++) {
        let self = await CompanySelfWastePrice.query().where('wasteID', vo.rows[j].$attributes.id).where('priceType', 1).where({ companyID }).first()
        if (self) {
          vo.rows[j].$attributes.price = self.price
        }
      }
      secondCategory.rows[i].list = vo
    }
    response.json(secondCategory)
  }

  async store({ request, response }) {
    let { name } = request.all()
    if (!name) {
      throw ERR.INVALID_PARAMS
    }
    let vo = await Waste1stCategory.create({ name })
    response.json(vo)
  }
  async update({ request, params, response }) {
    let vo = await Waste1stCategory.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
  }
  async destroy({ request, params, response }) {
    let vo = await Waste1stCategory.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_DELETE_ID
    }
    await vo.delete()
    response.json(vo)
  }
}

module.exports = Waste1stCategoryController
