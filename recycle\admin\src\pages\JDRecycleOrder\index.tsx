import { CopyOutlined, DownloadOutlined, PlusOutlined } from '@ant-design/icons'
import type { ProFormInstance } from '@ant-design/pro-components'
import { Button, Input, Modal, Badge, Select, Upload, message, notification, Space, Spin } from 'antd'
import ProTable, { } from '@ant-design/pro-table'
import type { ActionType, ProColumns } from '@ant-design/pro-table'
import ProCard from '@ant-design/pro-card'
import { effect, useConnect } from 'dva17'
import { Fragment, useEffect, useRef, useState } from 'react'
import { EDeleteImport, EGetRecycleImport, EBatchDestroyImport, NCollection, NUser, EOrderMaintainList, EOrderMaintainUpdate } from '../../common/action'
import { recycleTypeEnum } from '../../common/enum'
import { SERVER_HOME, SERVER_HOME_File } from '../../common/config'
import dayjs from 'dayjs'
import qs from 'qs'
import copy from 'copy-to-clipboard'
import styles from './index.module.less'
import { changeOrderImportStatus } from '../../services/order'
type Item = {
  offTime: any
  id: number
  number: number
  orderNo: string
  from: string
  createdAt: any
  finishedAt: any
  takeTime: any
  mobile: string
  wasteType: string
  estimatedMoney: number
  company: {
    companyName: string
  }
}

type OrderMaintainItem = {
  id: number
  orderNo: string
  status: string
  createdAt: string
  updatedAt: string
  remark: string
}
const { Option } = Select

export default () => {
  const { currentUser } = useConnect(NUser)
  const { lastSearch, orderMaintain } = useConnect(NCollection)
  const formRef = useRef<ProFormInstance>()
  const [visibleImport, setVisibleImport] = useState<boolean>(false)
  const [visibleMaintain, setVisibleMaintain] = useState<boolean>(false)
  const actionRef = useRef<ActionType>()
  const [visible, setVisible] = useState<boolean>(false)
  const [loading, setLoading] = useState<boolean>(false)
  const [showData, setShowData] = useState<any>(null)
  const [selectedRows, setSelectedRows] = useState<any>([])
  const [inputValue, setInputValue] = useState<any>(null)
  const [totalCount, setTotalCount] = useState<number>(0)
  const [totalInfoFee, setTotalInfoFee] = useState<number>(0)
  const handleEdit = async (row: any) => {
    setVisible(true)
    setShowData(row)
  }
  const orderCancel = () => {
    setVisible(false)
  }
  const orderOk = async () => {
    if (!dayjs(inputValue).isValid()) {
      message.error({ content: '日期格式错误，请按照"年-月-日"格式', duration: 3000 })
    } else {
      showData.workTime = dayjs(inputValue).format('YYYY-MM-DD') + dayjs(inputValue).format(' HH:mm:ss')
      console.log(showData)
      if (showData) {
        delete showData.updatedAt
        delete showData.finishedAt
        delete showData.createdAt
        delete showData.company
        delete showData.worker
        await changeOrderImportStatus(showData).then(async () => {
          notification.success({
            message: '成功！',
            description: '修改成功',
            duration: 2,
          })
          setVisible(false)
          refreshPage()
        })
      } else {
        notification.success({
          message: '成功！',
          description: '创建成功',
          duration: 2,
        })
        setVisible(false)
        refreshPage()
      }
    }
  }
  const changeValue = (vo: any) => {
    if (vo.workTime) {
      vo.workTime = showData?.workTime && dayjs(showData?.workTime).format('YYYY-MM-DD ') + vo.workTime
    }
    if (vo.dateTime) {
      setInputValue(vo.dateTime)
      delete vo.dateTime
    }
    if (vo.address) {
      showData.countyCode = 1
    }
    setShowData({ ...showData, ...vo })
  }
  const handleDel = async (row: any) => {
    Modal.confirm({
      title: '确认删除该数据',
      content: <div>删除后数据无法恢复！</div>,
      okText: '确认删除',
      cancelText: '退出',
      onOk: async () => {
        effect(NCollection, EDeleteImport, { id: row })
        notification.success({
          message: '成功！',
          description: '删除成功',
          duration: 2,
        })
        refreshPage()
      },
      width: 700,
    })

  }
  const expandedRowRender = (row: any) => {
    return (
      <div className={styles.item_wrapper} >
        <div className={styles.item} style={{ marginTop: 30 }}>
          <span className={styles.item_title}> 下单时间：</span>
          <div className={styles.item_content} > {dayjs(row.createdAt).format('YYYY-MM-DD HH:mm:ss')} </div>
        </div>
        <div className={styles.item} style={{ marginTop: 20 }}>
          <span className={styles.item_title}> 信息费：</span>
          <div className={styles.item_content} > ¥{row?.infoFee}元 </div>
        </div>
        <div className={styles.item}>
          <span className={styles.item_title}> 预约时间：</span>
          < div className={styles.item_content} style={{ flexWrap: 'wrap' }}>
            {dayjs(row.workTime).format('YYYY-MM-DD A')}
          </div>
        </div>
        <div className={styles.item}>
          <span className={styles.item_title}> 回收物：</span>
          < div className={styles.item_content} style={{ flexWrap: 'wrap' }}>
            {row.type}
          </div>
        </div>
        <div className={styles.item}>
          <span className={styles.item_title}>备注：</span>
          < div className={styles.item_content} style={{ flexWrap: 'wrap' }}>
            {row.remark}
          </div>
        </div>
        <Fragment>
          <div className={styles.item} >
            <span className={styles.item_title}> 客户姓名：</span>
            < div className={styles.item_content} > {row.userName} </div>
          </div>
          < div className={styles.item} >
            <span className={styles.item_title}> 客户电话：</span>
            < div className={styles.item_content} > {row.userMobile ? row.userMobile : '-'} </div>
          </div>
          <>

            <div className={styles.item} >
              <span className={styles.item_title}> 回收预估价：</span>
              < div className={styles.item_content} >¥{row.apprizeAmount} </div>
            </div>
          </>
          <div className={styles.item}>
            <span className={styles.item_title}> 回收地址：</span>
            < div className={styles.item_content} >
              {
                row.address
              }
            </div>
          </div>
          {
            row.worker ? (
              <Fragment>
                <div className={styles.item} >
                  <span className={styles.item_title}> 回收员姓名：</span>
                  < div className={styles.item_content} > {row.worker?.workerName} </div>
                </div>
                < div className={styles.item} >
                  <span className={styles.item_title}> 回收员电话：</span>
                  < div className={styles.item_content} > {row.worker?.mobile} </div>
                </div>
                < div className={styles.item} >
                  <span className={styles.item_title}> 接单时间：</span>
                  < div className={styles.item_content} > {row.takeTime} </div>
                </div>
              </Fragment>
            ) : null
          }
          <Fragment>
            < div className={styles.item} >
              <span className={styles.item_title}> 完成时间：</span>
              < div className={styles.item_content} > {row.finishedAt && dayjs(row.finishedAt).format('YYYY-MM-DD HH:mm:ss')} </div>
            </div>
          </Fragment>
        </Fragment>
        {
          row.company ? (
            <Fragment>
              < div className={styles.item} >
                <span className={styles.item_title}> 服务商：</span>
                < div className={styles.item_content} > {row.company.companyName} </div>
              </div>
              < div className={styles.item} >
                <span className={styles.item_title}> 服务商电话：</span>
                < div className={styles.item_content} > {row.company.mobile} </div>
              </div>
            </Fragment>
          ) : null
        }
      </div>
    );
  };
  const copyText = async (row: any) => {
    let copyContent: string =
      row.orderNo +
      row.userName +
      ' (' +
      row.userMobile +
      ') ' +
      row.address +
      '\n旧机信息:' +
      row.type +
      '-' +
      '\n预约时间:' +
      dayjs(row.workTime).format('YYYY-MM-DD A') +
      '\n信息费 ￥:' +
      row.infoFee
    copy(copyContent)
    notification.success({
      message: '成功！',
      description: '复制成功',
      duration: 2,
    })
  }
  const refreshPage = async () => {
    if (actionRef.current) {
      actionRef.current.reload()
    }
  }
  const batchDeleteOrders = async (selectedRows: any) => {
    Modal.confirm({
      title: '确认删除该数据',
      content: <div>删除后数据无法恢复！</div>,
      okText: '确认删除',
      cancelText: '退出',
      onOk: async () => {
        await effect(NCollection, EBatchDestroyImport, { ids: selectedRows.map((vo: any) => vo.id) })
        notification.success({
          message: '成功！',
          description: '删除成功',
          duration: 2,
        })
        refreshPage()
      }
    })
  }
  //上传详情
  const handleChangeFile = async ({ fileList }: any) => {
    if (fileList.length > 0) {
      setLoading(true)
      fileList.map((vo: any, index: number) => {
        if (vo.status === 'done') {
          effect(NCollection, EOrderMaintainList, { status: '待维护' })
          setVisibleImport(false)
          refreshPage()
          setLoading(false)
        }
      })
    }
  }
  const handleMaintain = async (row: any) => {
    Modal.confirm({
      title: '确认维护该数据',
      content: <div>维护后数据无法恢复！</div>,
      okText: '确认维护',
      cancelText: '退出',
      onOk: async () => {
        await effect(NCollection, EOrderMaintainUpdate, { id: row.id, status: '已维护' }).then(() => {
          notification.success({
            message: '成功！',
            description: '维护成功',
            duration: 2,
          })
        })
      }
    })
  }
  const handleExportJDOrder = async () => {
    try {
      notification.info({
        message: '导出中',
        description: '正在准备导出文件，请稍候...',
      })

      let qsQuery = qs.stringify(lastSearch)
      window.open(
        `${SERVER_HOME}exportJDRecycleOrder?${qsQuery}`
      )

      notification.success({
        message: '导出成功',
        description: '数据已成功导出为Excel文件',
      })
    } catch (error) {
      notification.error({
        message: '导出失败',
        description: '导出数据时发生错误',
      })
    }
  }
  const handleExportOrderMaintain = async () => {
    try {
      notification.info({
        message: '导出中',
        description: '正在准备导出待维护订单列表，请稍候...',
      })

      window.open(
        `${SERVER_HOME}exportOrderMaintain?status=待维护`
      )

      notification.success({
        message: '导出成功',
        description: '数据已成功导出为Excel文件',
      })
    } catch (error) {
      notification.error({
        message: '导出失败',
        description: '导出数据时发生错误',
      })
    }
  }
  
  const beforeUpload = (file: any) => {
    let isFile;
    if (['xlsx/*', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'].includes(file.type)) {
      isFile = true
    } else {
      isFile = false;
      alert('请上传正确的格式')
    }
    return isFile
  };
  const orderMaintainColumns: ProColumns<OrderMaintainItem>[] = [
    {
      title: '订单号',
      dataIndex: 'orderNo',
    },
    {
      title: '状态',
      dataIndex: 'status',
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
    {
      title: '操作',
      dataIndex: 'action',
      hideInSearch: true,
      render: (_, row: any) => {
        return (
          <Button onClick={() => {
            handleMaintain(row)
          }}>维护</Button>
        )
      },
    },
  ]
  const columns: ProColumns<Item>[] = [
    {
      title: '订单号',
      dataIndex: 'orderNo',
      width: '10%',
      copyable: false,
      render: (_, row: any) => (
        <>
          <div style={{ textAlign: 'center' }}>{row?.orderNo}</div>
          <a
            style={{ marginLeft: 15 }}
            onClick={() => {
              copyText(row)
            }}
            key="3">
            <CopyOutlined />
            复制
          </a>
        </>
      ),
    },
    {
      title: '导入时间',
      dataIndex: 'offTime',
      ellipsis: true,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '导入时间',
      dataIndex: 'offTime',
      ellipsis: true,
      hideInTable: true,
      valueType: 'dateRange',
      search: {
        transform: value => {
          return {
            startDate: value[0],
            endDate: value[1],
          }
        },
      },
    },
    // {
    //   title: '来源',
    //   dataIndex: 'from',
    //   ellipsis: false,
    // },
    {
      title: '下单',
      dataIndex: 'createdAt',
      ellipsis: true,
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            startDate: value[0],
            endDate: value[1],
          }
        },
      },
    },
    {
      title: '接单',
      dataIndex: 'takeTime',
      copyable: false,
      sorter: (a, b) => a.takeTime - b.takeTime,
      search: false,
      renderText: (_, row) => { return (row.takeTime ? dayjs(row.takeTime).format('YYYY-MM-DD HH:mm:ss') : '-') },
    },
    {
      title: '接单',
      dataIndex: 'takeTime',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            takeStartDate: value[0],
            takeEndDate: value[1],
          }
        },
      },
    },
    {
      title: '类型',
      dataIndex: 'wasteType',
      ellipsis: false,
      hideInSearch: true,
      render: (_, row: any) => {
        return (
          <div >
            {row.type}
          </div>
        )
      },
    },
    {
      title: '类型',
      dataIndex: 'type',
      hideInTable: true,
    },
    {
      title: '省',
      dataIndex: 'province',
      ellipsis: false,
    },
    {
      title: '市',
      dataIndex: 'city',
      ellipsis: false,
    },
    {
      title: '地址',
      width: '18%',
      dataIndex: 'address',
      copyable: false,
      render: (_, row: any) => {
        let province = row.province
        let city = row.city
        let address = row.address
        let receiverTown = row.receiverTown
        return (<>{province}{city}{receiverTown}{address}</>)
      },
    },
    {
      title: '姓名',
      dataIndex: 'userName',
      copyable: false,
      ellipsis: true,
    },
    {
      title: '模式',
      dataIndex: 'model',
      copyable: false,
      ellipsis: true,
      valueEnum: {
        送取不同步: '送取不同步',
        送取同步: '送取同步',
      },
    },
    {
      title: '完成',
      dataIndex: 'finishedAt',
      copyable: false,
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: (a, b) => a.finishedAt - b.finishedAt,
    },
    {
      title: '完成',
      dataIndex: 'finishedAt',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            finishStartDate: value[0],
            finishEndDate: value[1],
          }
        },
      },
    },
    {
      title: '回收人员',
      dataIndex: 'workerName',
      hideInTable: true,
    },
    {
      title: '信息费',
      dataIndex: 'infoFee',
      copyable: false,
      ellipsis: true,
      search: false
    },
    { title: '回收人', dataIndex: 'wokerName', copyable: false, ellipsis: true, search: false },
    { title: '结算手机', dataIndex: 'countPhone', copyable: false, ellipsis: true },
    {
      title: '结算人', dataIndex: 'countName', copyable: false, ellipsis: true, search: false,
      render: (_, row: any) => {
        return (
          <div>
            {row.worker?.workerName}
          </div>
        )
      },
    },
    { title: '结算人', dataIndex: 'countName', ellipsis: true, hideInTable: true },
    {
      title: '操作', width: '8%', copyable: false, ellipsis: true, search: false,
      render: (_, row: any) => {
        return (
          <>
            {/* <Button
              onClick={() => {
                handleEdit(row)
              }}>
              编辑
            </Button> */}
            <Button
              type='primary'
              disabled={currentUser.name !== "兰姐" && currentUser.name !== "系统"}
              onClick={() => {
                handleDel(row.id)
              }}>
              删除
            </Button>
          </>
        )
      },
    },
  ]

  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    effect(NCollection, EOrderMaintainList, { status: '待维护' })
  }, [])

  useEffect(() => {
    if (showData) {
      setInputValue(dayjs(showData.workTime).format('YYYY-MM-DD'))
    } else {
    }
    return () => { }
  }, [showData])
  /*--------------------- 响应 ---------------------*/

  /*--------------------- 渲染 ---------------------*/

  return (
    <ProCard>
      <Spin spinning={loading}>
        <ProTable<Item>
          formRef={formRef}
          actionRef={actionRef}
          columns={columns}
          expandable={{
            expandedRowRender: (record) => expandedRowRender(record),
          }}
          request={async (params = {}, sorter) => {
            const result = (await effect(NCollection, EGetRecycleImport, { ...params, ...sorter })) as any
            
            // 计算统计数据
            if (result && result.data) {
              setTotalCount(result.total || result.data.length || 0)
              // 计算信息费合计
              const infoFeeSum = result.totalInfoFee
              setTotalInfoFee(infoFeeSum)
            }
            
            return result
          }}
          pagination={{}}
          rowKey="id"
          dateFormatter="string"
          headerTitle={
            <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
              <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
                订单量: {totalCount && totalCount.toLocaleString()}
              </span>
              <span style={{ fontWeight: 'bold', color: '#52c41a' }}>
                信息费合计: ¥{totalInfoFee &&totalInfoFee.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </span>
            </div>
          }
          search={{}}
          toolBarRender={() => [
            <Button
              disabled={currentUser.level == '服务商'}
              style={{ marginRight: 20 }}
              type="primary"
              onClick={() => {
                setVisibleImport(true)
              }}>
              导入
            </Button>,
            <Button
              key="exportOrder"
              type="primary"
              onClick={handleExportJDOrder}
            >
              导出订单数据
            </Button>,
            <Badge count={orderMaintain?.total || 0}>
              <Button
                disabled={currentUser.level == '服务商'}
                key="5"
                onClick={() => {
                  setVisibleMaintain(true)
                }}>
                待维护订单列表
              </Button>
            </Badge>,
          ]}
          tableAlertRender={({ selectedRowKeys, selectedRows, onCleanSelected }) => (
            <Space size={24}>
              <span>
                已选 {selectedRowKeys.length} 项
              </span>
            </Space>
          )}
          rowSelection={{
            onChange: (_, selectedRows) => {
              setSelectedRows(selectedRows)
            },
          }}
          tableAlertOptionRender={() => {
            return (
              <Space size={16}>
                <a onClick={() => {
                  batchDeleteOrders(selectedRows)
                }}>批量删除</a>
              </Space>
            )
          }}
        />
        <Modal
          title="订单维护"
          open={visibleMaintain}
          onOk={() => {
            setVisibleMaintain(false)
          }}
          onCancel={() => {
            setVisibleMaintain(false)
          }}
          width={800}
          okText={'确认取消'}
          cancelText={'退出'}>
          <ProTable<OrderMaintainItem>
            columns={orderMaintainColumns}
            pagination={{}}
            rowKey="id"
            dataSource={orderMaintain?.data}
            dateFormatter="string"
            toolBarRender={() => [
              <Button
                key="export"
                type="primary"
                onClick={handleExportOrderMaintain}
              >
                <DownloadOutlined />
                导出待维护订单
              </Button>,
            ]}
            request={async (params = {}, sorter) => {
              return (await effect(NCollection, EOrderMaintainList, { status: '待维护', ...params, ...sorter })) as any
            }}
          />
        </Modal>
        <Modal
          title="订单导入"
          open={visibleImport}
          onOk={() => {
            setVisibleImport(false)
          }}
          onCancel={() => {
            setVisibleImport(false)
          }}
          width={800}
          okText={'确认取消'}
          cancelText={'退出'}>
          <div className="spaceAroundLine">
            <Upload
              accept={".xlsx"}
              showUploadList={false}
              beforeUpload={beforeUpload}
              action={`${SERVER_HOME_File}uploadDGRecycleJDOrder`}
              onChange={handleChangeFile}
            >
              <Button type={"primary"} loading={false}>已有模板，直接导入</Button>
            </Upload>
            <Button style={{ marginLeft: 20 }} onClick={() => {
              window.open('https://oss.evergreenrecycle.cn/yshs/%E4%BF%9D%E5%AF%86_%E5%B7%A5%E5%8D%95%E4%BF%A1%E6%81%AF_%E9%9D%92%E5%B7%9D%E7%A2%B3%E6%B1%87_20241028154450634.xlsx')
            }}>下载导入模板</Button>
          </div>
        </Modal>
        <Modal
          open={visible}
          onOk={() => {
            orderOk()
          }}
          onCancel={() => {
            orderCancel()
          }}
          width={600}
          okText={'确认修改'}
          cancelText={'退出'}>
          <h4 className={styles.modalTitle}>{'编辑订单'}</h4>
          <div className={styles.newOrder}>
            <div className={styles.newOrder_item}>
              <span className={styles.newOrder_item_title}>订单号：</span>
              <Input disabled={true} style={{ width: 250 }} value={showData?.orderNo} type="text" />
            </div>
            <div className={styles.newOrder_item}>
              <span className={styles.newOrder_item_title}>客户姓名：</span>
              <Input
                placeholder="请输入客户姓名"
                onChange={e => {
                  changeValue({ userName: e?.target?.value })
                }}
                style={{ width: 250 }}
                value={showData?.userName}
                type="text"
              />
            </div>
            <div className={styles.newOrder_item}>
              <span className={styles.newOrder_item_title}>联系电话：</span>
              <Input
                placeholder="请输入联系电话"
                onChange={e => {
                  changeValue({ userMobile: e?.target?.value })
                }}
                style={{ width: 250 }}
                value={showData?.userMobile}
                maxLength={11}
                type="text"
              />
            </div>
            <div className={styles.newOrder_item}>
              <span className={styles.newOrder_item_title}>预约时间：</span>
              <div style={{ width: '80%' }}>
                <Input
                  type={'date'}
                  style={{ width: '30%' }}
                  value={inputValue}
                  onChange={(e: any) => {
                    changeValue({ dateTime: e.target.value })
                  }}
                />
                <Select
                  value={showData?.workTime && dayjs(showData?.workTime).format('HH:mm:ss')}
                  style={{ width: 100 }}
                  onChange={(e: any) => {
                    changeValue({ workTime: e })
                  }}>
                  <Option value="09:00:00">上午</Option>
                  <Option value="13:00:00">下午</Option>
                </Select>
              </div>
            </div>
            <div className={styles.newOrder_item}>
              <span className={styles.newOrder_item_title}>区域：</span>
              <Input
                style={{ width: 250 }}
                value={showData?.area}
              >
              </Input>
            </div>
            <div className={styles.newOrder_item}>
              <span className={styles.newOrder_item_title}>详细地址：</span>
              <Input
                placeholder="请输入详细地址，具体到门牌号"
                onChange={e => {
                  changeValue({ address: e?.target?.value })
                }}
                style={{ width: 400 }}
                value={showData?.address}
                type="text"
              />
            </div>
            <div className={styles.newOrder_item}>
              <span className={styles.newOrder_item_title}>备注：</span>
              <Input
                placeholder="请输入详细备注"
                onChange={e => {
                  changeValue({ remark: e?.target?.value })
                }}
                style={{ width: 400 }}
                value={showData?.remark}
                type="text"
              />
            </div>
            <div className={styles.newOrder_item}>
              <span className={styles.newOrder_item_title}>信息费：</span>
              <Input
                placeholder="请输入信息费"
                onChange={e => {
                  changeValue({ infoFee: e?.target?.value })
                }}
                style={{ width: 400 }}
                value={showData?.infoFee}
                type="text"
              />
            </div>
          </div>
        </Modal>
      </Spin>
    </ProCard>
  )
}
