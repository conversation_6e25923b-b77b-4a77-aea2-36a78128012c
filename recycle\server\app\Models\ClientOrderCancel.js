'use strict'

const Model = use('Model')

//订单评价
class ClientOrderCancel extends Model {
  static get primaryKey() {
    return 'id'
  }
  static get table() {
    return 'client_order_cancel'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return null
  }
  worker() {
    return this.belongsTo('App/Models/Worker', 'workerID', 'id').select('id', 'workerName', 'mobile', 'companyID')
  }
  order() {
    return this.belongsTo('App/Models/ClientOrder', 'orderID', 'id').select('id', 'orderNO', 'from', 'userName')
  }
}

module.exports = ClientOrderCancel
