import { Modal, notification, InputNumber } from 'antd'
import { useState } from 'react'
import { effect } from 'dva17'
import { NWorker, EPostWorkerWallet } from '../../common/action'
import styles from './index.module.less'

// 师傅充值组件
interface WorkerChargeModalProps {
  visible: boolean;
  worker: any;
  onClose: () => void;
  onSuccess: () => void;
}

const WorkerChargeModal = ({ visible, worker, onClose, onSuccess }: WorkerChargeModalProps) => {
  const [chargeNum, setChargeNum] = useState(0);

  const handleCharge = async () => {
    await effect(NWorker, EPostWorkerWallet, { workerID: worker?.id, chargeNum });
    notification.success({
      message: '成功！',
      description: '充值成功',
      duration: 2,
    });
    onSuccess();
    onClose();
  };

  return (
    <Modal
      title="师傅充值"
      open={visible}
      onOk={handleCharge}
      onCancel={onClose}
      width={800}
    >
      <div className={styles.item_wrapper}>
        <div className={styles.item} style={{ marginTop: 30, paddingBottom: 10 }}>
          <span className={styles.item_title}>姓名：</span>
          <div className={styles.item_content}>{worker?.workerName}</div>
        </div>
        <div className={styles.item}>
          <span className={styles.item_title}>手机号：</span>
          <div className={styles.item_content}>{worker?.mobile}</div>
        </div>
        <div className={styles.item}>
          <span className={styles.item_title}>余额：</span>
          <div className={styles.item_content}>{worker?.wallet}</div>
        </div>
        <div className={styles.item}>
          <span className={styles.item_title}>充值：</span>
          <div className={styles.item_content}>
            <InputNumber onChange={(value: number | null) => {
              if (value !== null) {
                setChargeNum(value);
              }
            }} />
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default WorkerChargeModal; 