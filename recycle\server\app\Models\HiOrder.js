'use strict'

const Model = use('Model')

/**
 * 嗨回收订单模型
 * 专门处理嗨回收平台的订单数据
 */
class HiOrder extends Model {
  static get table() {
    return 'hi_order'
  }
  
  static get primaryKey() {
    return 'id'
  }
  
  static get createdAtColumn() {
    return 'createdAt'
  }
  
  static get updatedAtColumn() {
    return null
  }

  /**
   * 关联师傅信息
   */
  worker() {
    return this.belongsTo('App/Models/Worker', 'workerID', 'id')
  }

  /**
   * 关联公司信息
   */
  company() {
    return this.belongsTo('App/Models/Company', 'companyID', 'id')
  }

  /**
   * 解析图片JSON
   */
  getImgJson(value) {
    return value && JSON.parse(value)
  }

  /**
   * 设置图片JSON
   */
  setImgJson(value) {
    return value && JSON.stringify(value)
  }
}

module.exports = HiOrder 