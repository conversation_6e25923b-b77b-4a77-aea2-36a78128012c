import { View, Image, Swiper, SwiperItem, Radio, Text } from '@tarojs/components'
import Taro from '@tarojs/taro'
import './index.less'
import BaseLayout from '../../components/baseLayout'
import homeImg from '../../assets/home/<USER>'
import { Fragment, useEffect, useState } from 'react'
import { E } from '../../../../master/src/config'
import NavCustomBar from '../../components/navbar'

const Classifications = [
  { imgUrl: 'https://oss.evergreenrecycle.cn/donggua/client/images/navigation1.png', url: '/pages/appliances/appliances?id=3' },
  { imgUrl: 'https://oss.evergreenrecycle.cn/donggua/client/images/navigation2.png', url: '/pages/oldGoods/index?id=1' },
  { imgUrl: 'https://oss.evergreenrecycle.cn/donggua/client/images/navigation3.png', url: '/pages/oldGoods/index?id=2' },
  // { imgUrl: 'https://c.evergreenrecycle.cn/hanhan/images/navigation4.png', url: '/pages/appliances/appliances?id=4' },
]

definePageConfig({
  enableShareAppMessage: true,
  navigationStyle: 'custom',
})
export default () => {
  const [showRemind, setShowRemind] = useState(true);
  const [showRemindNext, setShowRemindNext] = useState(false);
  // 生命周期
  useEffect(() => {
    // Taro.hideTabBar({
    //   success: res => {
    //     Taro.setTabBarStyle({ backgroundColor: '#FFFFFF', borderStyle: 'white' })
    //   },
    //   fail: res => {
    //   },
    //   complete: res => {
    //   }
    // })
    let donggua_remind_next = Taro.getStorageSync('donggua_remind_next')
    if (donggua_remind_next === false) {
      setShowRemind(false)
    }
    return () => {
    }
  }, [])
  const closeRemind = () => {
    if (showRemindNext) {
      Taro.setStorageSync('donggua_remind_next', false)
    }
    setShowRemind(false)
  }
  return (
    <BaseLayout menuIndex={0} title='环保回收' navigationBar={false} footer>
      <NavCustomBar mainTitle='环保回收' needBackIcon={false} />
      <View className='home_page'>
        <Swiper className='coverSwiper' indicatorColor='#999' indicatorActiveColor='#333' circular indicatorDots autoplay>
          <SwiperItem className='cover_warpper'>
            <Image mode='widthFix' src={'https://oss.evergreenrecycle.cn/donggua/client/images/hover.png'} />
          </SwiperItem>
        </Swiper>
        <View className='navigation'>
          <View className='title' key={0}>
            <Image src={'https://oss.evergreenrecycle.cn/donggua/client/images/typeselect.png'} mode='widthFix' />
          </View>
          <View className='tabBox'>
            {Classifications.map(v => {
              return (
                // eslint-disable-next-line react/jsx-key
                <Image
                  key={v?.id}
                  src={v.imgUrl}
                  mode='widthFix'
                  onClick={() => {
                    Taro.navigateTo({ url: v.url })
                  }}
                />
              )
            })}
          </View>
        </View>
        <View className='guidance'>
          <Image style={{ width: '40%', marginBottom: 20 }} src={'https://oss.evergreenrecycle.cn/donggua/client/images/liucheng.png'} mode='widthFix' />
        </View>
        <View className='guidance'>
          <Image src={'https://c.evergreenrecycle.cn/hanhan/images/guidance.png'} mode='widthFix' />
        </View>
        {showRemind ? (
          <View className="remind_wrapper">
            <View className="message_wrapper">
              <View className="remindTitle">
                {/* <View><Image src={'https://c.evergreenrecycle.cn/hanhan/banma.png'} style={{ width: '15%', height: '8%' ,padding:5}} /></View> */}
                {'冬瓜回收用户使用协议与隐私政策'}
              </View>
              <View className="content_wrapper">
                {E.SecreteRule?.map((kind, index) => {
                  return (
                    <Fragment key={index + 1}>
                      <View className="remind_title">{kind.title}</View>
                      {index !== 2 && index !== 3 && index !== 5
                        ? kind.content?.map((remind, mark) => (
                          <View className="remind_text" key={remind + mark}>
                            {remind}
                          </View>
                        ))
                        : kind.content?.map((remind, mark) => (
                          <View className="remind_text" key={mark}>
                            {remind.title}
                            {remind.content?.map((content2, i) => (
                              <View className="remind_text2">{content2}</View>
                            ))}
                          </View>
                        ))}
                    </Fragment>
                  )
                }
                )}
              </View>
              <View className="know_button_wrapper">
                <Radio
                  checked={showRemindNext}
                  onClick={() => {
                    setShowRemindNext(!showRemindNext)
                  }}
                  className="radio"
                  style={!showRemind ? { display: 'none' } : null}>
                  下次不再显示
                </Radio>
                <View
                  className="know_button"
                  onClick={() => {
                    closeRemind()
                  }}>
                  我知道了
                </View>
              </View>
            </View>
          </View>
        ) : null}
      </View>
    </BaseLayout>
  )
}

