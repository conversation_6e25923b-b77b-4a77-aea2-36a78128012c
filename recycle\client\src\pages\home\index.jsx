/* eslint-disable no-undef */
/* eslint-disable import/first */
import { View, Image, Radio, Text, ScrollView, Swiper, SwiperItem, OfficialAccount } from '@tarojs/components'
import Taro from '@tarojs/taro'
import './index.less'
import BaseLayout from '../../components/baseLayout'
import { Fragment, useEffect, useState } from 'react'
import E from '../../config/E'
import NavCustomBar from '../../components/navbar'
import { NPublics } from '../../config/constants'
import { useSelector, useStore } from 'react-redux'


// 招募信息
const RecruitmentInfo = {
  title: '努力工作 共创未来',
  desc: '环保回收师傍加盟招募中',
  benefits: [
    '灵活工作时间',
    '高提成政策',
    '专业技能培训',
    '晨晚高峰补贴'
  ],
  buttonText: '立即加入',
  url: 'https://oss.evergreenrecycle.cn/donggua/recuit/index.html'
}
// 公众号信息
const OfficialAccountInfo = {
  title: '关注微信公众号',
  desc: '实时获取回收行情与优惠活动',
  qrcode: 'https://www.dongguahuishou.com/%E5%86%AC%E7%93%9C%E5%85%AC%E4%BC%97%E5%8F%B7.png'
}



// 新闻资讯数据将从接口获取

// 用户评价数据
const UserReviews = [
  {
    id: 1,
    username: '用户137***3268',
    rating: 5,
    content: '回收师傅很专业，价格透明，服务很快！旧空调卖了320元，很满意。'
  },
  {
    id: 2,
    username: '用户185***1123',
    rating: 5,
    content: '旧机免费上门，环保又方便，点赞！师傅还帮忙搬下楼。'
  },
  {
    id: 3,
    username: '用户139***9988',
    rating: 5,
    content: '下单后很快就联系我，体验很好。洗衣机回收价格比其他平台高。'
  },
  {
    id: 4,
    username: '用户158***7766',
    rating: 5,
    content: '师傅准时上门，态度很好，现场就给钱了，非常靠谱！'
  }
]

definePageConfig({
  enableShareAppMessage: true,
  navigationStyle: 'custom',
})
export default () => {
  const { dispatch } = useStore()
  const { indexPage } = useSelector(state => state.NPublics)
  const [showRemind, setShowRemind] = useState(true);
  const [showRemindNext, setShowRemindNext] = useState(false);
  const [BannerImages, setBannerImages] = useState([])
  const [Paths, setPaths] = useState([])
  const [Labas, setLabas] = useState([])

  function getTheIndexPage() {
    dispatch.NPublics[NPublics.EGetIndexPage]()
  }

  // 生命周期
  useEffect(() => {
    getTheIndexPage()
    Taro.hideTabBar()
    let donggua_remind_next = Taro.getStorageSync('donggua_remind_next')
    if (donggua_remind_next === false) {
      setShowRemind(false)
    }
  }, [])

  useEffect(() => {
    if (indexPage) {
      setBannerImages(indexPage.Banners)
      setPaths(indexPage.Paths)
      setLabas(indexPage.Labas)
    }
  }, [indexPage])

  const closeRemind = () => {
    if (showRemindNext) {
      Taro.setStorageSync('donggua_remind_next', false)
    }
    setShowRemind(false)
  }
  return (
    <BaseLayout menuIndex={0} title='冬瓜回收' navigationBar={false} footer>
      <NavCustomBar mainTitle='冬瓜回收' needBackIcon={false} />
      <View className='home_page'>
        {/* 轮播广告位 */}
        <View className='banner-section'>
          <Swiper
            className='banner-swiper'
            indicatorDots
            indicatorColor='rgba(255, 255, 255, 0.6)'
            indicatorActiveColor='#15b381'
            autoplay
            interval={3000}
            circular
          >
            {BannerImages.map((banner, index) => (
              <SwiperItem key={index}>
                <View className='banner-item' onClick={() => Taro.navigateTo({ url: banner.url })}>
                  <Image className='banner-image' src={banner.img} mode='aspectFill' />
                </View>
              </SwiperItem>
            ))}
          </Swiper>
        </View>

        {/* 滚动信息区域 */}
        <View className='scrolling-section'>
          <View className='scrolling-container'>
            <View className='scrolling-icon'>
              <Image src='https://oss.dongguahuishou.com/images/laba.png' style={{ width: '100%', height: '15px' }} mode='aspectFit' />
            </View>
            <View className='scrolling-content'>
              <Text className='scrolling-text' userSelect>
                {Labas.map(laba => `${laba.text}     `).join(' | ')}
                {/* 添加空格和分隔符，确保滚动时文字间有间隔 */}
              </Text>
            </View>
          </View>
        </View>

        {/* 主要服务分类 */}
        <View className='main-categories-section'>
          <ScrollView className='categories-scroll' scrollX showScrollbar={false} enableFlex>
            {Paths.map((item, index) => (
              <View key={index} className='category-item' onClick={() => Taro.navigateTo({ url: '/pages/appliances/electronics/index' })}>
                <Image className='category-icon' src={item.img} mode='aspectFit' lazyLoad />
                <Text className='category-name'>{item.name}</Text>
              </View>
            ))}
          </ScrollView>
        </View>

        {/* 预约按钮区域 */}
        <View className='booking-section'>
          <View className='booking-button' onClick={() => Taro.navigateTo({ url: '/pages/appliances/electronics/index' })}>
            <Text className='booking-text'>预约上门回收</Text>
          </View>
          <Text className='booking-desc'>*现在预约，即可上门回收 免费拆卸，无任何隐形费用</Text>
        </View>

        {/* 回收流程区域 */}
        <View className='process-section'>
          <Text className='section-title'>回收流程</Text>
          <View className='process-steps'>
            <View className='process-line'></View>
            <View className='process-step'>
              <View className='icon-wrapper'><View className='icon'>📝</View></View>
              <Text className='title'>在线下单</Text>
              <Text className='desc'>选择品类{"\n"}填写信息</Text>
            </View>
            <View className='process-step'>
              <View className='icon-wrapper'><View className='icon'>📞</View></View>
              <Text className='title'>专人联系</Text>
              <Text className='desc'>确认时间{"\n"}预约上门</Text>
            </View>
            <View className='process-step'>
              <View className='icon-wrapper'><View className='icon'>🚚</View></View>
              <Text className='title'>上门回收</Text>
              <Text className='desc'>免费搬运{"\n"}专业拆卸</Text>
            </View>
            <View className='process-step'>
              <View className='icon-wrapper'><View className='icon'>💰</View></View>
              <Text className='title'>现场结算</Text>
              <Text className='desc'>当场验收{"\n"}立即到账</Text>
            </View>
          </View>
        </View>

        {/* 用户评价区域 */}
        <View className='review-section'>
          <Text className='section-title'>用户评价</Text>
          <ScrollView className='review-list' scrollX showScrollbar={false} enableFlex>
            {UserReviews.map((review, index) => (
              <View key={index} className='review-card'>
                <View className='review-user'>
                  <Text className='review-username'>{review.username}</Text>
                  <View className='review-rating'>
                    {Array(review.rating).fill().map((_, i) => (
                      <Text key={i} className='star'>⭐</Text>
                    ))}
                  </View>
                </View>
                <Text className='review-text' userSelect>{review.content}</Text>
              </View>
            ))}
          </ScrollView>
        </View>



        {/* 招募区域 */}
        <View className='recruitment-section'>
          <View className='recruitment-card'>
            <View className='recruitment-content'>
              <Text className='recruitment-title'>{RecruitmentInfo.title}</Text>
              <Text className='recruitment-desc'>{RecruitmentInfo.desc}</Text>
              <View className='benefits-list'>
                {RecruitmentInfo.benefits.map((benefit, index) => (
                  <View key={index} className='benefit-item'>
                    <Text className='benefit-dot'></Text>
                    <Text className='benefit-text'>{benefit}</Text>
                  </View>
                ))}
              </View>
              <View className='recruitment-button' onClick={() =>
                Taro.navigateTo({
                  url: `/pages/webview/index?url=${encodeURIComponent(RecruitmentInfo.url)}`
                })}>
                {RecruitmentInfo.buttonText}
              </View>
            </View>
            <Image className='recruitment-image' src='https://oss.dongguahuishou.com/images/worker.png' mode='aspectFit' />
          </View>
        </View>


        {/* 公众号区域
        <View className='official-account-section'>
          <Text className='section-title'>关注我们</Text>
          <View className='official-account-card'>
            <View className='official-account-content'>
              <Text className='official-account-title'>{OfficialAccountInfo.title}</Text>
              <Text className='official-account-desc'>{OfficialAccountInfo.desc}</Text>
              <View className='official-account-wrapper'>
                <OfficialAccount className='official-account-component' />
              </View>
              <View className='divider'>
                <View className='divider-line'></View>
                <Text className='divider-text'>或</Text>
                <View className='divider-line'></View>
              </View>
              <View className='scan-text'>扫描二维码关注</View>
            </View>
            <View className='qrcode-container'>
              <Image className='qrcode-image' src={OfficialAccountInfo.qrcode} mode='aspectFit' />
              <Text className='qrcode-caption'>长按识别二维码</Text>
            </View>
          </View>
        </View> */}
        {showRemind ? (
          <View className='remind_wrapper'>
            <View className='message_wrapper'>
              <View className='remindTitle'>
                {/* <View><Image src={'https://c.evergreenrecycle.cn/hanhan/banma.png'} style={{ width: '15%', height: '8%' ,padding:5}} /></View> */}
                {'冬瓜回收用户使用协议与隐私政策'}
              </View>
              <View className='content_wrapper'>
                {E.SecreteRule?.map((kind, index) => {
                  return (
                    <Fragment key={index + 1}>
                      <View className='remind_title'>{kind.title}</View>
                      {index !== 2 && index !== 3 && index !== 5
                        ? kind.content?.map((remind, mark) => (
                          <View className='remind_text' userSelect key={remind + mark}>
                            {remind}
                          </View>
                        ))
                        : kind.content?.map((remind, mark) => (
                          <View className='remind_text' userSelect key={mark}>
                            {remind.title}
                            {remind.content?.map((content2) => (
                              // eslint-disable-next-line react/jsx-key
                              <View className='remind_text2' userSelect>{content2}</View>
                            ))}
                          </View>
                        ))}
                    </Fragment>
                  )
                }
                )}
              </View>
              <View className='know_button_wrapper'>
                <Radio
                  checked={showRemindNext}
                  onClick={() => {
                    setShowRemindNext(!showRemindNext)
                  }}
                  className='radio'
                  style={!showRemind ? { display: 'none' } : undefined}
                >
                  下次不再显示
                </Radio>
                <View
                  className='know_button'
                  onClick={() => {
                    closeRemind()
                  }}
                >
                  我知道了
                </View>
              </View>
            </View>
          </View>
        ) : null}
      </View>
    </BaseLayout>
  )
}

