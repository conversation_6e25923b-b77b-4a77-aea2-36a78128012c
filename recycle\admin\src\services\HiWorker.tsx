import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'

export async function getWorkerReview(payload: any) {
  return requestPost('/HiWorker/workerReview', payload)
}
export async function getWorkerMaintain(payload: any) {
  return requestPost('/HiWorker/workerMaintain', payload)
}
export async function putWorkerMaintain(payload: any) {
  return requestPut(`/HiWorker/workerMaintain/${payload.id}`, payload)
}
export async function getFullTimeWorker(payload: any) {
  return requestGet('/HiWorker', payload)
}
export async function postWorkerWallet(payload: any) {
  return requestPost('/HiWorkerCharge', payload)
}
export async function getWorkerWalletLog(payload: any) {
  return requestGet('/HiWalletlog', payload)
}
export async function getDetailWorker(payload: any) {
  return requestGet(`/HiWorker/${payload.id}`)
}
export async function getAddress(payload: any) {
  return requestGet('/address', payload)
}
export async function changeWorkerUse(payload: any) {
  return requestPut(`/HiWorker/${payload.id}`, payload)
}
export async function addWorkAddress(payload: any) {
  return requestPost('HiWorker/addWorkAddress', payload)
}
export async function removeWorkAddress(payload: any) {
  return requestPost('HiWorker/removeWorkAddress', payload)
}
export async function getTownList(payload: any) {
  return requestPost('HiWorker/getTown', payload)
}
export async function getWorkerArealist(payload: any) {
  return requestGet(`getWorkerArea/${payload.id}`, payload)
}
export async function getTheWorkerList(payload: any) {
  return requestPost('HiWorker', payload)
}
export async function postWorkerInsure(payload: any) {
  return requestPost('workerInsure', payload)
}
export async function getWorkerInsure(payload: any) {
  return requestGet('workerInsure', payload)
}
export async function putWorkerInsure(payload: any) {
  return requestPut(`workerInsure/${payload.id}`, payload)
}
export async function delWorkerInsure(payload: any) {
  return requestDelete(`workerInsure/${payload.id}`, payload)
}
export async function getWorkerPaylist(payload: any) {
  return requestGet('/HiWorkerPaylist', payload)
}

export async function postWorkerRefund(payload: any) {
  return requestPost('masterPayRefund', payload)
}
export async function workerAreaEdit(payload: any) {
  return requestPost('workerArea', payload)
}
export async function getWorkerSelected(payload: any) {
  return requestGet('workerArea', payload)
}
export async function workerDeleteArea(payload: any) {
  return requestDelete(`workerArea/${payload.workerID}`, payload)
}
export async function deleteWorker(payload: any) {
  return requestDelete(`HiWorker/${payload.id}`, payload)
}
export async function deleteCoWorker(payload: any) {
  return requestDelete(`JDCoWorker/${payload.id}`, payload)
}

export async function putcoworker(payload: any) {
  return requestPut(`HiWorker/coworker/${payload.id}`, payload)
}
export async function postcoworker(payload: any) {
  return requestPost('HiWorker/coworker', payload)
}