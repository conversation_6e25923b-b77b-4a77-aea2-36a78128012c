'use strict'

const Model = use('Model')
const Database = use('Database')

//上门师傅信息
class YCWorkerWalletLog extends Model {
  static get table() {
    return 'yc_worker_wallet_log'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return null
  }
  worker() {
    return this.belongsTo('App/Models/YCWorker', 'workerID', 'id')
  }
  order() {
    return this.belongsTo('App/Models/YCOrder', 'orderID', 'id')
  }
}

module.exports = YCWorkerWalletLog
