import Taro, {checkIsSupportFacialRecognition } from '@tarojs/taro'
import React, { Component } from 'react'
import {connect} from 'react-redux'
import { View, Image, Button, Text, Checkbox } from '@tarojs/components'
import { AtModal } from 'taro-ui'
import './question.less'
import E from '../../config/E'

class Question extends Component {
  constructor() {
    super(...arguments)
    this.state = {
      currentIndex: null,
      locale: 'zh_CN'
    }
  }

  config = {}

  //-----------------------生命周期-------------------------//
  componentWillMount() {
  }

  componentDidMount() {
    const locale = Taro.getStorageSync('locale')
    this.setState({ locale })
    Taro.setNavigationBarTitle({ title: locale == 'en' ? 'FAQ' : '常规问题解答' })
  }

  componentWillUnmount() {}

  componentDidShow() {}

  componentDidHide() {}

  //-----------------------事件-------------------------//
  seeDetail(index) {
    this.setState({
      currentIndex: this.state.currentIndex === index ? null : index
    })
  }

  //-----------------------渲染-------------------------//
  render() {
    let { currentIndex, locale } = this.state
    return (
      <View className="successfulOrder">
        {E.Questions.map((value, index) => (
          <View className="item_wrapper">
            <View
              className="click_wrapper"
              style={index === currentIndex ? { border: 'none' } : null}
              onClick={() => {
                this.seeDetail(index)
              }}>
              <Text>
                { locale == 'en' ? value.enTitle : value.title }
              </Text>
              <Image src={require('./../../assets/icon/youjiantou.png')} className={`operate_item_image ${index === currentIndex ? 'rotate' : ''}`} />
            </View>
            {index === currentIndex ? (
              <View className="content_wrapper">
                {
                  locale == 'en' ?
                  value.enList.map((content, mark) => (
                    <View className="content">
                      {content}
                    </View>
                  )) :
                  value.list.map((content, mark) => (
                    <View className="content">
                      {content}
                    </View>
                  ))
                }
                {/* {value.list.map((content, mark) => (
                  <View className="content">
                    {content}
                  </View>
                ))} */}
              </View>
            ) : null}
          </View>
        ))}
      </View>
    )
  }
}

export default connect(({}) => ({}))(Question)
