import React, { useCallback, useEffect, useState } from 'react'
import { BellOutlined, LogoutOutlined, SettingOutlined, UserOutlined } from '@ant-design/icons'
import { Ava<PERSON>, Badge, Divider, Drawer, Spin, Tabs } from 'antd'
import HeaderDropdown from './HeaderDropdown'
import { NUser, EUserLogout, NOrder, EGetNotice } from '../../common/action'
import styles from './index.module.less'
import { effect, useConnect } from 'dva17'
import images from '../../assets/images'
const { TabPane } = Tabs;
/**
 * 退出登录，并且将当前的 url 保存
 */

export default () => {
  const { currentUser } = useConnect(NUser)
  const { notices } = useConnect(NOrder)
  const [visible, setVisible] = useState<any>(false)
  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    effect(NOrder, EGetNotice, {})
    return () => {

    }
  }, [])
  /*--------------------- 响应 ---------------------*/
  /*--------------------- 渲染 ---------------------*/
  const loading = (
    <span className={`${styles.action} ${styles.account}`}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    </span>
  )

  if (!currentUser) {
    return loading
  }
  console.log(notices);

  const message = notices && notices.data ? notices.data.map((vo: any, index: number) => {
    return ({ id: index, msg: `订单号:${vo.order?.orderNo} ,${vo.whoCancel},原因:${vo.cancelReason}` })
  }) : []
  function callback(key: any) {
    console.log(key);
  }
  const menuHeaderDropdown = (
    <div className="notice-card" style={{ width: 300 }}>
      <Tabs defaultActiveKey="1" onChange={callback} centered>
        <TabPane tab="通知" key="1">
          {message.map((item: any) => (
            <li key={item.id}>
              <Avatar icon={<UserOutlined />} />
              {item.msg}
              <Divider />
            </li>
          ))}
        </TabPane>
        <TabPane tab="消息" key="2">
        </TabPane>
      </Tabs>
    </div>
  )
  return (
    <HeaderDropdown overlay={menuHeaderDropdown}>
      <span className={`${styles.action} ${styles.account}`}>
        <span className={`${styles.name} anticon`}>
          <Badge count={notices?.total} size="small">
            <BellOutlined style={{ fontSize: '16px' }} onClick={() => { }} />
          </Badge>
        </span>
      </span>
    </HeaderDropdown>
  )
}
