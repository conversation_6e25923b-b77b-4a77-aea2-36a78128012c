import { requestGet, requestPatch, requestPost } from "../utils/request"

export async function getOrder(payload) {
  return requestGet("order", payload)
}
export async function getQRCode(id) {
  return requestGet("qrcode/" + id)
}
export async function posrOrder(payload) {
  return requestPost("order", payload)
}

export async function postPay(payload) {
  return requestPost("pay", payload)
}
export async function getPay(id) {
  return requestGet(`pay/${id}`)
}

export async function getGoods() {
  return requestGet(`goods`)
}
