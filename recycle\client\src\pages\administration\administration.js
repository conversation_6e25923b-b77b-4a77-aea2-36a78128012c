import Taro from '@tarojs/taro'
import React, { Component } from 'react'
import T from "../../config/T"
import { connect } from 'react-redux'
import { View, Image, Button, Text, Input, Radio, RadioGroup } from '@tarojs/components'
import { AtModal } from 'taro-ui'
import './administration.less'

class Administration extends Component {
  constructor() {
    super(...arguments)
    this.state = {
      user: null,
      select: false
    }
  }

  config = {}

  //-----------------------生命周期-------------------------//
  componentWillMount() {
    this.setState({ user: Taro.getStorageSync('userInfo') && JSON.parse(Taro.getStorageSync('userInfo')) })
  }

  componentDidMount() {
    const locale = Taro.getStorageSync('locale')
    let select = Taro.getCurrentInstance().router.params.select
    if (select) {
      this.setState({ select: true })
    }
    let { user } = this.state
    this.props.dispatch({
      type: 'NUserAddress/EGetUserAddressList',
      payload: {
        id: user.id,
        sort: 'desc'
      }
    })
    Taro.setNavigationBarTitle({ title: locale == 'en' ? 'Address Manager' : '地址管理' })
  }

  componentWillReceiveProps(nextProps, nextContext) {
    if (!this.props.isGetAddressList && nextProps.isGetAddressList) {
      this.setState({ addressList: nextProps.addressList })
    }
  }

  componentWillUnmount() { }

  componentDidShow() { }

  componentDidHide() { }

  //-----------------------事件-------------------------//
  // 选择
  singlechange(e) {
    let user = this.state.user
    let list = this.props.addressList
    let which = Number(e.detail.value)
    list.forEach((address, index) => {
      if (address.id === which) {
        this.props.dispatch({
          type: 'NUserAddress/ESetDefault',
          payload: {
            addressID: address.id,
            id: user.id
          }
        })
      }
    })
  }

  // 编辑
  editAddressItem(value) {
    if (value) {
      Taro.navigateTo({ url: `/pages/address/address?edit=${value}` })
    }
  }

  // 删除
  delAddressItem(id) {
    let { user } = this.state
    this.props.dispatch({
      type: 'NUserAddress/EDeleteAddress',
      payload: {
        id,
        userID: user.id
      }
    })
  }

  searchWord(e) {
    let { user } = this.state
    if (e.target.value) {
      this.props.dispatch({
        type: 'NUserAddress/ESearchAddress',
        payload: {
          id: user.id,
          realname: e.target.value,
          mobile: e.target.value
        }
      })
    } else {
      this.props.dispatch({
        type: 'NUserAddress/EGetUserAddressList',
        payload: {
          id: user.id
        }
      })
    }
  }

  selectAddress(e, value) {
    let { select } = this.state
    let dataset = e.target.dataset
    if (select && dataset.set) {

      this.props.dispatch({
        type: 'NOrder/ESelectAddress',
        payload: {
          ...value
        }
      })

    }
  }

  //-----------------------渲染-------------------------//
  render() {
    //const {addressList} = this.state
    let { addressList, isAddItem } = this.props
    // const {haveChosenClothing, haveChosenPage} = this.props.oldGoods
    return (
      <View className="administration">
        <View className="administration_input">
          <Input type="text" placeholder={T.administration.inputTxt} className="input_" onInput={this.searchWord} maxLength={18} />
        </View>
        <RadioGroup color="#15b381" onChange={this.singlechange.bind(this)}>
          {addressList
            ? addressList.map((v, i) => (
              <View
                key={i + v}
                className="address_box"
                onClick={e => {
                  this.selectAddress(e, v)
                }}
                data-set="mark">
                <View className="address_name_phone">
                  <Text className="address_name">{v.realname}</Text>
                  <Text>{v.mobile}</Text>
                </View>
                <View className="address_information">
                  <Text className="information">{v.province + v.city + v.district + v.subDistrct + v.address}</Text>
                </View>
                <View className="address_edit">
                  <Radio className="address_Radio_text" value={v.id} checked={v.isDefault === 1 ? true : false}>
                    {T.administration.defaultAdd}
                  </Radio>
                  <View className="address_edit_del">
                    <View
                      className="addressEditDel"
                      onClick={() => {
                        this.editAddressItem(JSON.stringify(v))
                      }}>
                      <Image src={require('./../../assets/icon/bianji.png')} className="address_Icon" />
                      <Text className="address_text">
                        {T.administration.edit}
                      </Text>
                    </View>
                    <View
                      className="addressEditDel"
                      onClick={() => {
                        this.delAddressItem(v.id)
                      }}>
                      <Image src={require('./../../assets/icon/lajitong.png')} className="address_Icon" />
                      <Text className="address_text">
                        {T.administration.delete}
                      </Text>
                    </View>
                  </View>
                </View>
              </View>
            ))
            : null}
        </RadioGroup>

        <View
          className="administration_add_address"
          onClick={() => {
            Taro.navigateTo({ url: '/pages/address/address' })
          }}>
          <View className="text_wrapper">
            <Text>
              {T.administration.addAddress}
            </Text>
          </View>
          {isAddItem ? <View className="add_item"></View> : null}
        </View>
      </View>
    )
  }
}

export default connect(({ NUser: { userInfo }, NUserAddress: { addressList, isGetAddressList }, NSystem: { isAddItem } }) => ({
  user: userInfo,
  isAddItem,
  addressList,
  isGetAddressList
}))(Administration)
