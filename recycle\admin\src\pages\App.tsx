import React, { useEffect, useState } from 'react'
import { effect, useConnect } from 'dva17'
import { BrowserRouter as Router, Routes, Route, Link, useNavigate, useLocation } from 'react-router-dom';
import ProLayout, { MenuDataItem, PageContainer, PageLoading, ProSettings } from '@ant-design/pro-layout'
import { PageName, UserState } from '../common/enum'
import OrderManage from './OrderManage';
import { Tabs } from 'antd';
import 'antd/dist/antd.css';
import defaultProps from './Routes'
import RightContent from '../components/RightContent'
import MenuFooter from '../components/MenuFooter'
import { EUserAutoLogin, NUser } from '../common/action'
import Login from './Login'
import Authorized, { reloadAuthorized } from '../utils/Authorized/Authorized'
import { SiderMenuProps } from '@ant-design/pro-layout/lib/components/SiderMenu/SiderMenu'
import GlobalWatermark from '../components/GlobalWatermark'
import './App.less'

interface Pane {
  title: string;
  key: string;
  content: JSX.Element;
}

export default () => {
  const [activeKey, setActiveKey] = useState<string>('/');
  const [panes, setPanes] = useState<Pane[]>([]);
  const [collapsed, setCollapsed] = useState<boolean>(true); // 默认折叠侧边栏
  const location = useLocation()
  const navigation = useNavigate()
  const [settings, setSetting] = useState<Partial<ProSettings> | undefined>({ fixSiderbar: true, menuHeaderRender: false, menu: { defaultOpenAll: true } })
  const { status, currentUser } = useConnect(NUser)

  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    effect(NUser, EUserAutoLogin)
  }, [])

  /*--------------------- 响应 ---------------------*/
  const addPane = (route: { path: string; element: JSX.Element; name: string }) => {
    // console.log(route,"routes");

    const exists = panes.find((pane) => pane.key === route.path);
    if (!exists) {
      // 查找路由中对应的组件
      let routeComponent;
      defaultProps.route.routes.forEach((mainRoute) => {
        if (mainRoute.path === route.path) {
          routeComponent = mainRoute.component;
        } else if (mainRoute.routes) {
          mainRoute.routes.forEach((subRoute) => {
            if (subRoute.path === route.path) {
              routeComponent = subRoute.component;
            }
          });
        }
      });

      // 如果找到了组件，添加到panes中
      if (routeComponent) {
        setPanes([...panes, { title: route.name, key: route.path, content: routeComponent }]);
      } else {
        setPanes([...panes, { title: route.name, key: route.path, content: route.element || <></> }]);
      }
    }
    setActiveKey(route.path);
    navigation(route.path);
  };
  const menuDataRender = (menuList: MenuDataItem[]): MenuDataItem[] =>
    menuList.map((item) => {
      const localItem = {
        ...item,
        children: item.children ? menuDataRender(item.children) : undefined,
      };
      return Authorized.check(item.authority, localItem, null) as MenuDataItem;
    });
  const menuFooterRender = (props: SiderMenuProps | undefined) => {
    return <MenuFooter collapsed={props?.collapsed} />
  }

  const menuItemRender = (menuItemProps: any, defaultDom: boolean | React.ReactChild | React.ReactFragment | React.ReactPortal | null | undefined) => {
    // console.log(item,1)
    return (
      <a
        onClick={() => {
          navigation(menuItemProps?.path)
          addPane(menuItemProps || "")
        }}>
        {defaultDom}
      </a>
    )
  }

  const onChange = (key: string) => {
    setActiveKey(key);
    navigation(key);
  };

  const onEdit = (targetKey: any, action: 'add' | 'remove') => {
    if (action === 'remove') {
      const newPanes = panes.filter((pane) => pane.key !== targetKey);
      setPanes(newPanes);
      if (newPanes.length) {
        setActiveKey(newPanes[0].key);
        navigation(newPanes[0].key);
      } else {
        setActiveKey('/');
        navigation('/');
      }
    }
  };

  // 将所有Tab内容都保留在DOM中，通过CSS控制显示/隐藏
  const renderAllPanes = () => {
    return (
      <div className="tab-content-container">
        {panes.map((pane) => (
          <div
            key={pane.key}
            style={{
              display: activeKey === pane.key ? 'block' : 'none',
              width: '100%',
              height: '100%'
            }}>
            {pane.content}
          </div>
        ))}
      </div>
    );
  };

  const items = panes.map((pane) => ({
    label: pane.title,
    key: pane.key,
    closable: true,
  }));
  /*--------------------- 渲染 ---------------------*/
  if (status == UserState.loading) {
    return <PageLoading />
  } else if (status == UserState.login) {
    return <Login />
  } else {
    return (
      <>
        <GlobalWatermark />
        <div className="app-container">
          <ProLayout
            {...defaultProps}
            {...settings}
            location={{
              pathname: location.pathname,
            }}
            collapsed={collapsed}
            onCollapse={setCollapsed}
            defaultCollapsed={true}
            siderWidth={200}
            disableContentMargin={false}
            menuFooterRender={menuFooterRender}
            onMenuHeaderClick={(e: any) => console.log(e)}
            menuDataRender={menuDataRender}
            menuItemRender={menuItemRender}
            rightContentRender={() => <RightContent />}
            waterMarkProps={{
              content: '冬瓜回收管理系统',
              fontColor: 'rgba(0, 0, 0, 0.06)',
              fontSize: 16,
              zIndex: 9999,
              rotate: -22,
              gapX: 100,
              gapY: 100,
            }}>
            <PageContainer>
              <Tabs
                activeKey={activeKey}
                onChange={onChange}
                type="editable-card"
                hideAdd
                onEdit={onEdit}
                items={items}
              />
              {/* 使用新的渲染方法替代原来的方式 */}
              {renderAllPanes()}
              {/* 保留路由系统，但不在视图中直接显示其渲染结果 
               这样可以确保路由仍然正常工作，但不会导致组件重新挂载 */}
              <div style={{ display: 'none', height: 0, overflow: 'hidden' }}>
                <Routes location={location.pathname}>
                  {defaultProps.route.routes.map(({ name, path, component, routes }: any, i: any) => {
                    return (
                      <Route key={i} path={path} element={component}>
                        {routes &&
                          routes.map(({ name, path: path2, component, routes }: any, j: any) => {
                            let subPath = path2.slice(path.length + 1)
                            // console.log('subPath: ', subPath)
                            return <Route key={j} path={subPath} element={component} />
                          })}
                      </Route>
                    )
                  })}
                </Routes>
              </div>
            </PageContainer>
          </ProLayout>
        </div>
      </>
    );
  }
}
