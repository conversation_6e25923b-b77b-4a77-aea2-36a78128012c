import { Modal, Table, Tag, Button, DatePicker, Input, Space, Form } from 'antd'
import { useEffect, useState } from 'react'
import { getCNChargeList } from '../../services/price'
import dayjs from 'dayjs'

interface CNChargeListModalProps {
  visible: boolean;
  onClose: () => void;
}

const CNChargeListModal = ({ visible, onClose }: CNChargeListModalProps) => {
  const [form] = Form.useForm();
  const [CNChargeList, setCNChargeList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState<number>(0)
  const [totalInfoFee, setTotalInfoFee] = useState<number>(0)
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });

  useEffect(() => {
    if (visible) {
      fetchCNChargeList();
    }
  }, [visible]);

  const fetchCNChargeList = async (params: any = {}) => {
    setLoading(true);
    try {
      const searchParams = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...params
      };

      const response = await getCNChargeList(searchParams);
      setCNChargeList(response?.data || []);
      setPagination(prev => ({
        ...prev,
        total: response?.total || 0
      }));
      setTotalCount(response?.total || 0);
      setTotalInfoFee(response?.totalInfoFee || 0);

    } catch (error) {
      console.error('获取佣金修改记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    form.validateFields().then(values => {
      const searchParams = {
        ...values,
        startDate: values.dateRange?.[0]?.format('YYYY-MM-DD'),
        endDate: values.dateRange?.[1]?.format('YYYY-MM-DD'),
      };
      delete searchParams.dateRange;

      setPagination(prev => ({ ...prev, current: 1 }));
      fetchCNChargeList(searchParams);
    });
  };

  const handleReset = () => {
    form.resetFields();
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchCNChargeList();
  };



  const columns = [
    {
      title: '补扣时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (createdAt: any) => (
        <span>{createdAt ? dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss') : '-'}</span>
      ),
    },
    {
      title: '订单号',
      dataIndex: 'orderNo',
      key: 'orderNo',
      width: 150,
      // 正确展示订单号，raw 实际为当前行数据
      render: (_: any, record: any) => <span>{record?.order?.orderNo || '-'}</span>,
    },
    {
      title: '师傅',
      dataIndex: 'worker',
      key: 'worker',
      width: 120,
      render: (_: any, record: any) => <span>{record?.worker?.workerName}</span>,
    },
    {
      title: '补扣金额',
      dataIndex: 'money',
      key: 'money',
      width: 120,
      render: (money: any) => <span>¥{money}</span>,
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      width: 240,
      render: (_: any, record: any) => <span>{record?.order?.address}</span>,
    },
    {
      title: '品类',
      dataIndex: 'wasteType',
      key: 'wasteType',
      width: 120,
      render: (_: any, record: any) => <span>{record?.order?.type}</span>,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      render: (remark: string) => <span>{remark || '-'}</span>,
    },
  ];

  return (
    <Modal
      title="菜鸟补扣记录"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1400}
      destroyOnClose
    >
      <Form
        form={form}
        layout="inline"
        style={{ marginBottom: 16 }}
      >
        <Form.Item name="dateRange" label="导入日期">
          <DatePicker.RangePicker />
        </Form.Item>
        <Form.Item name="orderNo" label="订单号">
          <Input placeholder="请输入订单号" style={{ width: 240 }} allowClear />
        </Form.Item>
        <Form.Item name="wasteType" label="品类">
          <Input placeholder="请输入品类" style={{ width: 120 }} allowClear />
        </Form.Item>
        <Form.Item name="workerName" label="师傅">
          <Input placeholder="请输入师傅" style={{ width: 120 }} allowClear />
        </Form.Item>
        <Form.Item name="address" label="地址">
          <Input placeholder="请输入地址" style={{ width: 240 }} allowClear />
        </Form.Item>
        <Form.Item>
          <Space>
            <Button type="primary" onClick={handleSearch}>
              搜索
            </Button>
            <Button onClick={handleReset}>
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
      <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
          订单量: {totalCount && totalCount.toLocaleString()}
        </span>
        <span style={{ fontWeight: 'bold', color: '#52c41a' }}>
          信息费合计: ¥{totalInfoFee && totalInfoFee.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
        </span>
      </div>
      <Table
        columns={columns}
        loading={loading}
        dataSource={CNChargeList}

        rowKey={(record) => record.id || JSON.stringify(record)}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          onChange: (page, pageSize) => {
            setPagination(prev => ({ ...prev, current: page, pageSize: pageSize || 20 }));
            fetchCNChargeList({ page, pageSize });
          }
        }}
      />
    </Modal>
  );
};

export default CNChargeListModal;