import { Modal, Form, Input, Button, Table, message, InputNumber, Select, Space } from 'antd'
import { useEffect, useState } from 'react'
import { SourceLevels, TypeLevels } from '../../common/enum'
import { batchUpdateCommission } from '../../services/price'

interface BatchCommissionModalProps {
    visible: boolean;
    onClose: () => void;
    onSuccess: () => void;
    selectedRows?: any[];
}

const BatchCommissionModal = ({ visible, onClose, onSuccess, selectedRows = [] }: BatchCommissionModalProps) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [batchData, setBatchData] = useState<any[]>([]);

    useEffect(() => {
        if (visible && selectedRows.length > 0) {
            setBatchData(selectedRows.map(row => ({
                ...row,
                newPrice: row.price
            })));
        }
    }, [visible, selectedRows]);

    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            setLoading(true);
            // 批量修改逻辑
            const updateList = batchData.map(item => ({
                id: item.id,
                price: item.newPrice,
                area: item.area,
                type: item.type,
                source: item.source,
                remark: values.remark || '批量修改'
            }));
            console.log(updateList);
            // 这里需要调用批量更新API
            await batchUpdateCommission({ updateList });

            message.success('批量修改佣金成功！');
            onSuccess();
            onClose();
        } catch (error) {
            console.error('批量修改失败:', error);
            message.error('批量修改失败，请重试');
        } finally {
            setLoading(false);
        }
    };

    const handlePriceChange = (value: number, record: any, index: number) => {
        const newData = [...batchData];
        newData[index] = { ...newData[index], newPrice: value };
        setBatchData(newData);
    };

    const handleBatchPriceChange = () => {
        form.validateFields(['batchPrice']).then(values => {
            const newData = batchData.map(item => {
                let newPrice = item.price;
                newPrice = values.batchPrice || item.price;
                return { ...item, newPrice };
            });
            setBatchData(newData);
        });
    };

    const columns = [
        {
            title: '区域',
            dataIndex: 'area',
            key: 'area',
        },
        {
            title: '品类',
            dataIndex: 'type',
            key: 'type',
        },
        {
            title: '平台',
            dataIndex: 'source',
            key: 'source',
        },
        {
            title: '原价格',
            dataIndex: 'price',
            key: 'price',
            render: (price: number) => `¥${price}`,
        },
        {
            title: '新价格',
            key: 'newPrice',
            render: (record: any, _: any, index: number) => (
                <InputNumber
                    value={record.newPrice}
                    onChange={(value) => handlePriceChange(value || 0, record, index)}
                    formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={value => value!.replace(/\¥\s?|(,*)/g, '')}
                    style={{ width: '100%' }}
                />
            ),
        },
    ];

    return (
        <Modal
            title="批量修改佣金"
            open={visible}
            onCancel={onClose}
            footer={null}
            width={1000}
            destroyOnClose
        >
            <Form
                form={form}
                layout="vertical"
            >
                <div style={{ marginBottom: 16 }}>
                    <Space>
                        <span>批量设置:</span>
                        <Form.Item name="batchPrice" style={{ margin: 0, display: 'inline-block' }}>
                            <InputNumber
                                placeholder="输入统一价格"
                                formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                parser={value => value!.replace(/\¥\s?|(,*)/g, '')}
                                style={{ width: 150 }}
                            />
                        </Form.Item>
                        <Button type="primary" onClick={handleBatchPriceChange}>
                            应用到所有
                        </Button>
                    </Space>
                </div>

                <Table
                    columns={columns}
                    dataSource={batchData}
                    rowKey="id"
                    pagination={false}
                    scroll={{ y: 300 }}
                    size="small"
                />

                <Form.Item
                    name="remark"
                    label="修改原因"
                    style={{ marginTop: 16 }}
                >
                    <Input.TextArea
                        rows={3}
                        placeholder="请输入修改原因（可选）"
                    />
                </Form.Item>

                <div style={{ textAlign: 'right', marginTop: 16 }}>
                    <Space>
                        <Button onClick={onClose}>
                            取消
                        </Button>
                        <Button type="primary" loading={loading} onClick={handleSubmit}>
                            确认修改 ({batchData.length}项)
                        </Button>
                    </Space>
                </div>
            </Form>
        </Modal>
    );
};

export default BatchCommissionModal;