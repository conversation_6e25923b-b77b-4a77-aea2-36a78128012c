'use strict'

const _ = require('lodash')
const moment = require('moment')
const Env = use('Env')
const { Worker, WorkerDistrict, WorkerArea, WorkerWalletLog, Order, ReqLog, WorkerPay, Company, JDWorker, HiWorker } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { judgeAuthority } = require('../../../Services/UserService')
const { CryptUtil } = require('../../../Util')
const Database = use('Database')

//上门师傅信息
class WorkerController {
  async index({ request, response }) {
    const { adminUser } = request
    let { companyID } = adminUser
    let { current = 1, pageSize = 10, lowWallet, companyNameID, hightWallet, leadID, wallet = 'descend', forbidden, level, isUse, sort = 'desc', mobile, workerName } = request.all()
    companyID = parseInt(companyID)
    let query = Worker.query()
      .whereNull('managerID')
      .with('assistants', (b) => b.select('id', 'workerName', 'managerID', 'mobile', 'createdAt', 'forbidden'))
      .with('insure').with('company', (b) => b.select('id', 'companyName', 'province')).with('order')
    if (level) {
      query.where('level', level)
    } else if (leadID) {
      query.where('leadID', leadID)
    }
    if (lowWallet && hightWallet) {
      query.whereBetween('wallet', [lowWallet, hightWallet])
    }
    if (forbidden) {
      query.where('forbidden', forbidden)
    }
    if (wallet == 'descend') {
      query.orderBy('wallet', 'desc')
      // console.log(wallet)

    } else if (wallet == 'ascend') {
      query.orderBy('wallet', 'asc')
      // console.log(wallet)
    }
    if (companyID && companyID !== 36) {
      query.where('companyID', companyID)
    }
    if (companyNameID) {
      query.where('companyID', companyNameID)
    }
    if (isUse) {
      query.where('isUse', isUse)
    }
    if (mobile) {
      query.whereRaw('mobile like ?', [`%${mobile}%`])
    }
    if (workerName) {
      query.whereRaw('workerName like ?', [`%${workerName}%`])
    }
    let vo = await query.paginate(current, pageSize)
    response.json(vo)
  }
  async walletlog({ request, response, params }) {
    let { workerID, current = 1, pageSize = 10, } = request.all()
    // 合并消费记录
    let query = WorkerWalletLog.query()
      .where('workerID', workerID)
      .select('workerID', 'id', 'createdAt', 'money', 'remark', 'orderID').with('order')
      .orderBy('createdAt', 'desc')
    let vo = await query.paginate(current, pageSize)
    response.json(vo)
  }
  async workerReview({ request, response }) {
    let { companyID = 0, sort = 'desc', current = 1, pageSize = 10, workerName } = request.all()
    companyID = parseInt(companyID)
    let query = Worker.query().with('company').with('insure')
      .whereNotNull('idCardNo')
      .where('isUse', E.WorkerAccountStatus.Init)
    if (companyID && companyID !== 36) {
      query.where('companyID', companyID)
    }
    if (workerName) {
      query.whereRaw('workerName like ?', [`%${workerName}%`])
    }
    let vo = await query.orderBy('id', sort).paginate(current, pageSize)
    response.json(vo)
  }

  // 添加小工
  async addAssistant({ request, response }) {
    try {
      const { assistantName, mobile, workerID, password } = request.all()

      // 验证参数
      if (!assistantName || !mobile || !workerID) {
        throw ERR.INVALID_PARAMS
      }

      // 检查手机号是否已存在
      const existingAssistant = await Worker.query()
        .whereNotNull('managerID')
        .where('mobile', mobile)
        .first()

      if (existingAssistant) {
        return response.status(400).json({
          success: false,
          message: '该手机号已被注册'
        })
      }

      // 检查所属师傅是否存在
      const worker = await Worker.find(workerID)
      if (!worker) {
        return response.status(400).json({
          success: false,
          message: '所属师傅不存在'
        })
      }
      let passwd = mobile.slice(-6) || password
      let enPw = CryptUtil.md5(CryptUtil.encryptData256(passwd, Env.get('APP_KEY')))

      // 创建小工账号
      const assistant = await Worker.create({
        workerName: assistantName,
        mobile,
        managerID: workerID,
        isUse: '同意',
        forbidden: '1',
        companyID: worker.companyID,
        password: enPw, // 默认密码为手机号后6位
        wallet: 0 // 钱包余额默认为0
      })

      // 返回成功信息
      response.json({
        success: true,
        message: '添加小工成功',
        data: assistant
      })
    } catch (error) {
      console.error('添加小工失败:', error)
      response.status(500).json({
        success: false,
        message: '添加小工失败',
        error: error.message
      })
    }
  }

  // 获取小工订单
  async getAssistantOrders({ request, response }) {
    try {
      const { assistantID, current = 1, pageSize = 10 } = request.all()

      if (!assistantID) {
        throw ERR.INVALID_PARAMS
      }

      // 查询关联了小工的订单
      const query = Order.query()
        .where('workerID', assistantID)
        .with('manager', (builder) => {
          builder.select('id', 'workerName', 'mobile')
        })
        .orderBy('createdAt', 'desc')

      const orders = await query.paginate(current, pageSize)

      response.json(orders)
    } catch (error) {
      console.error('获取小工订单失败:', error)
      response.status(500).json({
        success: false,
        message: '获取小工订单失败',
        error: error.message
      })
    }
  }
  async which({ request, response }) {
    let { subDistrct, type, name, province, typeName, userMobile, orderID, county, townID } = request.all()
    const { adminUser } = request
    let { companyID } = adminUser
    companyID = parseInt(companyID)
    let query = Worker.query().with('order',
      builder => {
        builder.where('status', '进行中')
        return builder
      })
      .select('id', 'workerName', 'isUse', 'mobile', 'city', 'type', "wallet")
      .whereNotNull('workerName')
      .where('isUse', '同意')
      .where('forbidden', '1')
    if (userMobile && orderID) {
      let orderOg = await Order.query()
        .select('id', 'workerID', 'userMobile')
        .where('userMobile', userMobile)
        .whereNotNull('workerID')
        .whereNot('id', orderID)
        .first()
      // console.log(orderOg, "orderOg")
      if (orderOg) {
        query.where('id', orderOg.toJSON().workerID)
          .select(Database.raw(`${orderOg.toJSON().id} as orderID`))
      } else {
        let workerIds
        if (townID) {
          let area = await WorkerArea.query()
            .select('id', 'workerID', "companyID", "name", "code", "detailName").where('code', townID)
            .where('detailName', typeName).fetch()
          workerIds = _.uniq(area.rows.map((vo) => vo.workerID))
        }
        if (((workerIds && workerIds.length === 0) || !workerIds) && subDistrct && typeName) {
          let prearea = await WorkerArea.query()
            .select('id', 'workerID', "companyID", "name", "code", "detailName").where('area', county)
            .where('name', subDistrct)
            .where('detailName', typeName).fetch()
          workerIds = prearea.rows.map((vo) => vo.workerID)
        }
        query.where('id', 'IN', _.uniq(workerIds))
      }
    }
    if (name) {
      query.whereRaw('workerName like ?', [`%${name}%`])
    }
    if (companyID && companyID !== 36) {
      query.where('companyID', companyID)
    }
    let vo = await query.fetch()
    let workerlist = vo.toJSON()
    let companyList = []
    if ((companyID === 36 || companyID === 0) && province === "广东省") {
      let tmpdata = await Company.query().where('forbidden', '1')
        .select(Database.raw('id as companyID,forbidden,companyName as workerName,"-2" as id'))
        .fetch()
      companyList = tmpdata.toJSON()
    }
    let res = companyList.concat(workerlist)
    response.json(res)
  }

  async addWorkAddress({ request, response }) {
    let { workerid, province, city, district, subDistrct } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台 回收人员信息' })
    if (!workerid || !province || !city || !district || !subDistrct) {
      throw ERR.INVALID_PARAMS
    }
    let vo = await WorkerDistrict.create({ workerid, province, city, district, subDistrct })
    response.json(vo)
  }

  async removeWorkAddress({ request, params, response }) {
    //let vo = await Worker.find(params.id)
    let { workerid, subDistrct } = request.all()
    if (!workerid || !subDistrct) {
      throw ERR.INVALID_PARAMS
    }
    let vo = await WorkerDistrict.query()
      .where('workerid', workerid)
      .where('subDistrct', subDistrct)
      .first()
    if (!vo) {
      throw ERR.RESTFUL_DELETE_ID
    }
    await vo.delete()
    response.json(vo)
  }

  async getTown({ request, response }) {
    let { workerid, district, wasteTypeID } = request.all()
    if (!workerid || !district) {
      throw ERR.INVALID_PARAMS
    }
    let vo = await WorkerDistrict.query()
      .where('workerid', workerid)
      .where('district', district)
      .fetch()
    // if(wasteTypeID){
    // 	vo = vo.where('wasteTypeID',wasteTypeID)
    // }
    response.json(vo)
  }

  async show({ request, params, response }) {
    let vo = await Worker.query()
      .where('id', params.id)
      .with('company')
      .first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }

  async store({ request, response }) {
    let { adminUser } = request
    let { companyID, search } = request.all()
    let query = Worker.query().where('isUse', E.WorkerAccountStatus.Agree)
    if (companyID) {
      query.where('companyID', companyID)
    }
    if (search) {
      query.whereRaw('workerName like ?', [`%${search}%`])
    }
    let vo = await query
      .orderBy('id', 'desc')
      .select('id', 'workerName')
      .fetch()
    response.json(vo)
  }
  async update({ request, params, response }) {
    let { type, companyID, mobile, isjd, isHi } = request.all()
    let { adminUser } = request
    // let result = await judgeAuthority(adminUser, '回收人员编辑')
    // if (!result) {
    //   throw ERR.USER_ROLE_NO_PRIVILEGE
    // }
    let vo = await Worker.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    if (mobile) {
      let passwd = mobile.slice(-6)
      let enPw = CryptUtil.md5(CryptUtil.encryptData256(passwd, Env.get('APP_KEY')))
      _.assign(vo, request.all(), { password: enPw })
    } else {
      _.assign(vo, request.all())
    }

    await vo.save()
    response.json(vo)

    if (isjd) {
      await JDWorker.create({
        ..._.omit(vo.toJSON(), ['isjd', 'isHi', 'managerID']),
      })
    }
    if (isHi) {
      await HiWorker.create({
        ..._.omit(vo.toJSON(), ['isjd', 'isHi', 'managerID']),
      })
    }
    if (!vo.toJSON().isjd) {
      await JDWorker.query().where('id', vo.id).delete()
    }
    if (!vo.toJSON().isHi) {
      await HiWorker.query().where('id', vo.id).delete()
    }
    if (companyID) {
      await WorkerArea.query().where('workerID', params.id).update({ companyID: companyID })
      await WorkerWalletLog.query().where('workerID', params.id).update({ companyID: companyID })
      await Order.query().where('workerID', params.id).update({ companyID: companyID })
    }
  }
  async chargeWallet({ request, params, response }) {
    let { chargeNum, workerID } = request.all()
    let vo = await Worker.find(workerID)
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    vo.wallet += chargeNum
    await vo.save()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台充值' })
    await WorkerPay.create({ workerID: workerID, totalPay: chargeNum * 100, status: '完成', finishAt: new Date(), transactionID: 1, type: '系统' })
    return { chargeNum, workerID }
  }
}

module.exports = WorkerController
