import Taro from '@tarojs/taro'
import React, { Component } from 'react'
import T from '../../config/T'

import { connect } from 'react-redux'
import { <PERSON>, <PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>, PickerViewColumn } from '@tarojs/components'
import { AtModal } from 'taro-ui'
import './chooseAddress.less'
import dayjs from 'dayjs'
import { NOld<PERSON>oods, NOrder, NUserAddress, NUser } from '../../config/constants'

class ChooseAddress extends Component {
  constructor() {
   // super(...arguments)
    this.state = {
      province: null,
      provinceID: 0,
      city: null,
      cityID: 0,
      area: null,
      areaID: 0,
      street: null,
      streetID: 0,
    }
  }

  config = {}

  //-----------------------生命周期-------------------------//
  componentWillMount() {}

  componentDidMount() {}

  /*componentWillReceiveProps(nextProps, nextContext) {
    }*/

  componentWillUnmount() {}

  componentDidShow() {}

  componentDidHide() {}

  //-----------------------事件-------------------------//
  // 省列表
  provincial_list = async num => {
    await this.props.dispatch.NUser[NUser.EGetAddress]({
      code: num,
    })
  }

  // 地址选择
  chooseAddress = async e => {
    const val = await e.detail.value
    let { provinceID, cityID, areaID } = this.state
    const { provinceList, cityList, areaList, streetList, levelTowAddressShow } = await this.props.user
    if (levelTowAddressShow) {
      // 省
      if (!(provinceID == val[0])) {
        await this.provincial_list(provinceList[val[0]].code)
        await this.setState({ provinceID: val[0] })
      }
      // 市
      if (!(cityID == val[1])) {
        await this.provincial_list(cityList[val[1]].code)
        await this.setState({ cityID: val[1] })
      }

      // 区
      if (!(areaID == val[2])) {
        await this.provincial_list(areaList[val[2]].code)
        await this.setState({ areaID: val[2] })
      }

      await this.setState({
        province: provinceList[val[0]].name,
        city: cityList[val[1]].name,
        area: areaList[val[2]].name,
      })
    } else {
      // 街道
      await this.provincial_list(streetList[val[0]].code)
      await this.setState({
        street: streetList[val[0]].name,
      })
    }
  }
  // 提交地址
  submitAddress = async () => {
    let { province, city, area, street } = await this.state

    const { provinceList, cityList, areaList, streetList } = await this.props.user
    if (!(province && province.length > 0)) {
      province = await provinceList[0].name
    }
    if (!(city && city.length > 0)) {
      city = await cityList[0].name
    }
    if (!(area && area.length > 0)) {
      area = await areaList[0].name
    }
    if (!(street && street.length > 0)) {
      street = await streetList[0].name
    }


    await this.props.dispatch.NUser[NUser.ESetState]({
      province,
      city,
      area,
      street,
      chooseAddressShow: false,
      levelTowAddressShow: true,
    })
  }
  //-----------------------渲染-------------------------//
  render() {
    const { provinceList, cityList, areaList, streetList, chooseAddressShow, levelTowAddressShow } = this.props.user
    return (
      <View className="ChooseAddress">
        <View className="mask">{/* 压屏 */}</View>

        <View className="ChooseAddress_day_time">
          <View className="ChooseAddress_day_time_title">
            <View
              className="ChooseAddress_day_time_title_button"
              style={{ marginLeft: '5vw' }}
              onClick={() => {
                this.props.dispatch.NUser[NUser.ESetState]({
                  chooseAddressShow: !chooseAddressShow,
                  levelTowAddressShow: true,
                })
              }}
            >
              取消
            </View>

            <View className="ChooseAddressTitle">
              <Text className="ChooseAddressTitle_text">选择地址</Text>
              {/* <Text  className='ChooseAddressTitle_text'>{dayjs().add((daysListId-0+1), 'days').format('LL')}</Text> */}
            </View>

            <View
              className="ChooseAddress_day_time_title_button"
              style={{ marginRight: '5vw' }}
              onClick={() => {
                if (levelTowAddressShow) {
                  this.props.dispatch.NUser[NUser.ESetState]({
                    levelTowAddressShow: !levelTowAddressShow,
                  })
                } else {
                  this.submitAddress()
                }
              }}
            >
              完成
            </View>
          </View>

          <PickerView
            value="北京"
            className="PickerView_box"
            onChange={e => {
              this.chooseAddress(e)
            }}
          >
            {/* 省  */}
            {levelTowAddressShow ? (
              <PickerViewColumn>
                {provinceList.map(item => {
                  return <View className="PickerView_box_item">{item.name}</View>
                })}
              </PickerViewColumn>
            ) : null}
            {/* 市 */}
            {levelTowAddressShow ? (
              <PickerViewColumn>
                {cityList.map(item => {
                  return <View className="PickerView_box_item">{item.name}</View>
                })}
              </PickerViewColumn>
            ) : null}

            {/* 区 */}
            {levelTowAddressShow ? (
              <PickerViewColumn>
                {areaList.map(item => {
                  return <View className="PickerView_box_item">{item.name}</View>
                })}
              </PickerViewColumn>
            ) : null}

            {/* 街道 */}
            {levelTowAddressShow ? null : (
              <PickerViewColumn>
                {streetList.map(item => {
                  return <View className="PickerView_box_item">{item.name}</View>
                })}
              </PickerViewColumn>
            )}
          </PickerView>
        </View>
      </View>
    )
  }
}
export default connect(({ NUser }) => ({ user: NUser }))(ChooseAddress)
