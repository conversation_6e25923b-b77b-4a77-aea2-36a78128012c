'use strict'

const _ = require('lodash')
const moment = require('moment')

const { PriceSet, Order, Company, PriceSetLog } = require('../../../Models')
const { ERR, E } = require('../../../../../constants')
const { CryptUtil } = require('../../../Util')
const OrderService = require('../../../Services/OrderService')
const Env = use('Env')

//定价表
class PriceSetController {
  async index({ request, response }) {
    let { source, type, area, current = 1, pageSize = 100, sort = 'asc', companyID } = request.all()
    let query = PriceSet.query().with('company')
    if (source) {
      query.whereRaw('source like ?', [`%${source}%`])
    }
    if (type) {
      query.whereRaw('type like ?', [`%${type}%`])
    }
    if (area) {
      query.whereRaw('area like ?', [`%${area}%`])
    }
    if (companyID) {
      const company = await Company.query().where('companyName', 'like', `%${companyID}%`).first()
      if (company) {
        query.where('companyID', company.id)
      }
    }
    let vo = await query.orderBy('id', sort).paginate(current, pageSize)
    response.json(vo)
  }
  //创建
  async store({ request, response }) {
    let { adminUser } = request
    let { type, price, area, source, companyID } = request.all()
    if (!type || !price) {
      throw ERR.INVALID_PARAMS
    }
    let vo = await PriceSet.create({
      type,
      price,
      area,
      source,
      companyID
    })
    response.json(vo)
    await OrderService.logPriceUpdate({
      action: 'create',
      type,
      area,
      source,
      price,
      id: vo.id,
      adminID: adminUser.id,
      remark: '后台创建佣金配置'
    })
  }
  //更新
  async update({ request, params, response }) {
    let { adminUser } = request
    let { price, type, area, source } = request.all()
    let vo = await PriceSet.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    await OrderService.logPriceUpdate({
      action: 'update',
      type,
      area,
      source,
      price,
      id: vo.id,
      adminID: adminUser.id,
      remark: '后台更新佣金配置'
    })
    _.assign(vo, request.all())
    await OrderService.updateOrderPrices(area, type, source, price)
    await vo.save()
    response.json(vo)
  }
  //删除
  async destroy({ request, params, response }) {
    let { adminUser } = request
    let vo = await PriceSet.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_DELETE_ID
    } else {
      await OrderService.logPriceUpdate({
        action: 'delete',
        type: vo.type,
        area: vo.area,
        source: vo.source,
        price: vo.price,
        id: vo.id,
        adminID: adminUser.id,
        remark: '后台删除佣金配置'
      })
      await vo.delete()
    }
    response.json(vo)
  }
  async bundlePriceSet({ request, response }) {
    let { ids, price } = request.all()
    if (ids.length === 0) {
      response.json({ msg: '无数据', code: 200 })
    } else {
      await _.forEach(ids, async function (vo) {
        let priceVo = await PriceSet.find(vo)
        priceVo.price = price
        await priceVo.save()
      })
    }
    response.json({ msg: 'ok', code: 200 })
  }
  async getPriceSetLog({ request, response, params }) {
    let { page, pageSize, source, type, area, } = request.all()
    let query = PriceSetLog.query().with('order').with('operator')
    if (source) {
      query.whereRaw('before_data->>"$.source" like ?', [`%${source}%`])
    }
    if (type) {
      query.whereRaw('before_data->>"$.type" like ?', [`%${type}%`])
    }
    if (area) {
      query.whereRaw('before_data->>"$.area" like ?', [`%${area}%`])
    }
    let logList = await query.orderBy('createdAt', 'desc').paginate(page, pageSize)
    response.json(logList)
  }

  async bundlePriceSetUpdate({ request, response }) {
    let { adminUser } = request
    let { updateList } = request.all()
    if (updateList.length === 0) {
      response.json({ msg: '无数据', code: 200 })
    } else {
      // console.log(updateList)

      await _.forEach(updateList, async function (vo) {
        let priceVo = await PriceSet.find(vo.id)
        priceVo.price = vo.price
        try {
          await OrderService.logPriceUpdate({
            action: 'update',
            type: vo.type,
            area: vo.area,
            source: vo.source,
            price: vo.price,
            id: vo.id,
            adminID: adminUser.id,
            remark: vo.remark
          })
        } catch (error) {
          console.log(error)
          return error
        }
        try {
          await OrderService.updateOrderPrices(vo.area, vo.type, vo.source, vo.price)
          await priceVo.save()
        } catch (error) {
          console.log(error)
          return error
        }
      })
    }
    response.json({ msg: 'ok', code: 200 })
  }

}

module.exports = PriceSetController
