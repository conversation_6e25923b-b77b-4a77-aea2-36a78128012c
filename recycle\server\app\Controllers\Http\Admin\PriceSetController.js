'use strict'

const _ = require('lodash')
const moment = require('moment')

const { PriceSet, Order, Company } = require('../../../Models')
const { ERR, E } = require('../../../../../constants')
const { CryptUtil } = require('../../../Util')
const Env = use('Env')

//定价表
class PriceSetController {
  async index({ request, response }) {
    let { source, type, area, current = 1, pageSize = 100, sort = 'asc', companyID } = request.all()
    let query = PriceSet.query().with('company')
    if (source) {
      query.whereRaw('source like ?', [`%${source}%`])
    }
    if (type) {
      query.whereRaw('type like ?', [`%${type}%`])
    }
    if (area) {
      query.whereRaw('area like ?', [`%${area}%`])
    }
    if (companyID) {
      const company = await Company.query().where('companyName', 'like', `%${companyID}%`).first()
      if (company) {
        query.where('companyID', company.id)
      }
    }
    let vo = await query.orderBy('id', sort).paginate(current, pageSize)
    response.json(vo)
  }
  //创建
  async store({ request, response }) {
    let { type, price, area, source, companyID } = request.all()
    if (!type || !price) {
      throw ERR.INVALID_PARAMS
    }
    let vo = await PriceSet.create({
      type,
      price,
      area,
      source,
      companyID
    })
    response.json(vo)
  }
  //更新
  async update({ request, params, response }) {
    let { price, type, area, source } = request.all()
    let vo = await PriceSet.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
    if (area === '合肥市') {
      await Order.query()
        .where('type', type)
        .where('from', source)
        .whereIn('status', ['预订', '进行中', '师傅撤回', '系统撤回'])
        .where('city', '合肥市')
        .update({ infoFee: price, commission: price })
    } else if (area === '广东省') {
      await Order.query()
        .where('from', source)
        .where('type', type)
        .whereIn('status', ['预订', '进行中', '师傅撤回', '系统撤回'])
        .where('province', '广东省')
        .update({ infoFee: price, commission: price })
    } else {
      await Order.query()
        .where('from', source)
        .where('type', type)
        .whereIn('status', ['预订', '进行中', '师傅撤回', '系统撤回'])
        .where('province', area)
        .update({ infoFee: price, commission: price })
    }
  }
  //删除
  async destroy({ request, params, response }) {
    let vo = await PriceSet.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_DELETE_ID
    } else {
      await vo.delete()
    }
    response.json(vo)
  }
  async bundlePriceSet({ request, response }) {
    let { ids, price } = request.all()
    if (ids.length === 0) {
      response.json({ msg: '无数据', code: 200 })
    } else {
      await _.forEach(ids, async function (vo) {
        let priceVo = await PriceSet.find(vo)
        priceVo.price = price
        await priceVo.save()
      })
    }
    response.json({ msg: 'ok', code: 200 })
  }
}

module.exports = PriceSetController
