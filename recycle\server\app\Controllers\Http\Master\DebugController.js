"use strict";

const moment = require("moment");
const Env = use('Env')

const { <PERSON><PERSON><PERSON><PERSON>, Worker } = require("../../../Models");
const { ERR, E } = require("../../../../../../constants");
const { CryptUtil } = require("../../../Util");

//订单废品关联表
class DebugController {
  async store({ request, response }) {
    let { phoneList } = request.all()
    phoneList.forEach(async function (itm) {
      try {
        let vo = await Worker.query().where('mobile', itm).first()
        let passwd = itm.slice(-6)
        console.log(passwd);
        let enPw = CryptUtil.md5(CryptUtil.encryptData256(passwd, Env.get('APP_KEY')))
        vo.password = enPw
        await vo.save()
      } catch (error) {
        console.log(error);
      }
    })
    return { code: "ok" }
  }
}

module.exports = DebugController;
