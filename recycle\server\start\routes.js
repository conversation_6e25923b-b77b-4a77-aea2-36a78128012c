'use strict'

/*
|--------------------------------------------------------------------------
| Routes
|--------------------------------------------------------------------------
*/

/** @type {typeof import('@adonisjs/framework/src/Route/Manager')} */
const Route = use('Route')

Route.get('/', () => {
  return { greeting: 'r2.0' }
})
const _ = require('lodash')
require('../app/routes_client')
require('../app/routes_admin')
require('../app/routes_app')
require('../app/routes_master')
const {} = require('../app/Models/')
// 定时任务重置每日任务
const schedule = require('node-schedule')
const Excel = require('exceljs')
const axios = require('axios')
// 回收，每日早八点，德邦下单服务
schedule.scheduleJob('0 0 8 * * *', async function () {
  console.log('定时')
})
// 每天早上8点，整改过期短信通知
schedule.scheduleJob('0 0 8 * * *', async () => {
  return false
})
Route.get('/md5test', async (response) => {
  return 1
})

Route.get('/revisedata', async (response) => {
  return 1
})

Route.get('/importexcel', async (response) => {
  return 1
})
Route.get('/importreportexcel', async (response) => {
  return 1
})
Route.get('/baidutest', async (response) => {
  const tianqi = await axios.get(
    'http://api.map.baidu.com/weather/v1/?district_id=310112&data_type=all&ak=R9fcr1fyZDIjeZo85vUAZSNXP3rnKT8v'
  )
  console.log('tianqi: ', tianqi)
  return tianqi.data
})
