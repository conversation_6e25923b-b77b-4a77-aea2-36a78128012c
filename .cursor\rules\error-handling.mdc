---
description: 错误处理和日志记录规范
---

# 错误处理和日志记录规范

## 错误处理原则

### 早期返回模式
```javascript
// ❌ 避免深层嵌套
const processOrder = (order) => {
  if (order) {
    if (order.status === 'pending') {
      if (order.items && order.items.length > 0) {
        // 处理订单...
        return { success: true };
      } else {
        return { success: false, error: '订单没有商品项' };
      }
    } else {
      return { success: false, error: '订单状态不是待处理' };
    }
  } else {
    return { success: false, error: '订单不存在' };
  }
};

// ✅ 使用早期返回
const processOrder = (order) => {
  // 前置条件验证
  if (!order) {
    return { success: false, error: '订单不存在' };
  }
  
  if (order.status !== 'pending') {
    return { success: false, error: '订单状态不是待处理' };
  }
  
  if (!order.items || order.items.length === 0) {
    return { success: false, error: '订单没有商品项' };
  }
  
  // 处理订单...
  return { success: true };
};
```

### 错误类型定义
```javascript
// 自定义错误类型
class AppError extends Error {
  constructor(message, code = 'UNKNOWN_ERROR', statusCode = 500) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.statusCode = statusCode;
  }
}

// 业务错误
class BusinessError extends AppError {
  constructor(message, code = 'BUSINESS_ERROR', statusCode = 400) {
    super(message, code, statusCode);
  }
}

// 验证错误
class ValidationError extends AppError {
  constructor(message, fields = {}, code = 'VALIDATION_ERROR') {
    super(message, code, 400);
    this.fields = fields;
  }
}

// 认证错误
class AuthenticationError extends AppError {
  constructor(message = '未登录或登录已过期', code = 'AUTHENTICATION_ERROR') {
    super(message, code, 401);
  }
}

// 授权错误
class AuthorizationError extends AppError {
  constructor(message = '无操作权限', code = 'AUTHORIZATION_ERROR') {
    super(message, code, 403);
  }
}

// 资源不存在错误
class NotFoundError extends AppError {
  constructor(resource = '资源', code = 'NOT_FOUND_ERROR') {
    super(`${resource}不存在`, code, 404);
  }
}
```

## 异步错误处理

### Promise 错误处理
```javascript
// 使用 async/await 和 try/catch
const getOrderDetails = async (orderId) => {
  try {
    // 验证参数
    if (!orderId) {
      throw new ValidationError('订单ID不能为空');
    }
    
    // 数据库查询
    const order = await Order.find(orderId);
    
    if (!order) {
      throw new NotFoundError('订单');
    }
    
    // 权限检查
    if (!hasPermission(order)) {
      throw new AuthorizationError();
    }
    
    // 关联查询
    const items = await OrderItem.query()
      .where('order_id', order.id)
      .fetch();
      
    return {
      ...order.toJSON(),
      items: items.toJSON()
    };
  } catch (error) {
    // 错误分类处理
    if (error instanceof ValidationError) {
      // 参数验证错误
      logger.warn('订单详情查询参数错误', {
        orderId,
        error: error.message,
        fields: error.fields
      });
    } else if (error instanceof NotFoundError) {
      // 资源不存在错误
      logger.info('查询不存在的订单', {
        orderId,
        error: error.message
      });
    } else {
      // 未预期的错误
      logger.error('获取订单详情失败', {
        orderId,
        error: error.message,
        stack: error.stack
      });
    }
    
    // 重新抛出错误
    throw error;
  }
};

// 在控制器中处理
class OrderController {
  async show({ params, response }) {
    try {
      const order = await getOrderDetails(params.id);
      return response.json({ code: 200, data: order });
    } catch (error) {
      // 根据错误类型返回不同状态码
      const statusCode = error.statusCode || 500;
      const code = error.code || 'UNKNOWN_ERROR';
      const message = error.message || '服务器错误';
      
      return response.status(statusCode).json({
        code: statusCode,
        error: code,
        message
      });
    }
  }
}
```

### 异步错误处理工具
```javascript
// 包装异步路由处理器
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 使用示例
router.get('/orders/:id', asyncHandler(async (req, res) => {
  const order = await getOrderDetails(req.params.id);
  res.json({ code: 200, data: order });
}));

// Promise 链式调用错误处理
const fetchOrderData = (orderId) => {
  return getOrder(orderId)
    .then(order => {
      return getOrderItems(order.id)
        .then(items => ({ ...order, items }));
    })
    .catch(error => {
      logger.error('获取订单数据失败', { orderId, error });
      throw error;
    });
};
```

## 前端错误处理

### React 错误边界
```jsx
// ErrorBoundary.tsx
import React, { Component, ErrorInfo, ReactNode } from 'react';

interface ErrorBoundaryProps {
  fallback?: ReactNode;
  onError?: (error: Error, info: ErrorInfo) => void;
  children: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, info: ErrorInfo) {
    console.error('组件错误:', error, info);
    
    // 调用错误处理函数
    this.props.onError?.(error, info);
    
    // 发送错误日志到服务器
    reportError(error, info);
  }

  render() {
    if (this.state.hasError) {
      // 使用传入的回退UI或默认错误显示
      return this.props.fallback || (
        <div className="error-container">
          <h2>页面出现错误</h2>
          <p>请刷新页面或联系客服</p>
          <button onClick={() => window.location.reload()}>
            刷新页面
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// 使用示例
const App = () => {
  return (
    <ErrorBoundary
      onError={(error, info) => {
        console.log('捕获到错误:', error);
      }}
      fallback={<ErrorFallback />}
    >
      <OrderList />
    </ErrorBoundary>
  );
};
```

### 小程序错误处理
```javascript
// app.js 全局错误处理
App({
  onError(error) {
    console.error('小程序全局错误:', error);
    
    // 上报错误
    this.reportError(error);
  },
  
  // API 调用错误处理
  request(options) {
    return new Promise((resolve, reject) => {
      Taro.request({
        ...options,
        success: (res) => {
          // 请求成功但业务失败
          if (res.data.code !== 200) {
            const error = new Error(res.data.message || '请求失败');
            error.code = res.data.code;
            error.data = res.data;
            
            this.handleApiError(error, options.url);
            reject(error);
            return;
          }
          
          resolve(res.data);
        },
        fail: (err) => {
          this.handleApiError(err, options.url);
          reject(err);
        }
      });
    });
  },
  
  handleApiError(error, url) {
    console.error('API调用失败:', url, error);
    
    // 处理特定错误
    if (error.code === 401) {
      // 清除登录状态
      Taro.removeStorageSync('token');
      
      // 重定向到登录页
      Taro.navigateTo({ url: '/pages/login/index' });
    }
    
    // 上报错误
    this.reportError(error, { type: 'api', url });
  },
  
  reportError(error, extra = {}) {
    // 发送错误报告到服务器
    Taro.request({
      url: 'https://api.example.com/errors',
      method: 'POST',
      data: {
        message: error.message || String(error),
        stack: error.stack,
        time: new Date().toISOString(),
        page: getCurrentPageUrl(),
        user: Taro.getStorageSync('userId'),
        system: Taro.getSystemInfoSync(),
        ...extra
      }
    }).catch(e => {
      console.error('错误上报失败:', e);
    });
  }
});

// 获取当前页面路径
function getCurrentPageUrl() {
  const pages = Taro.getCurrentPages();
  const currentPage = pages[pages.length - 1];
  return currentPage ? currentPage.route : '';
}
```

## 后端错误处理

### 全局错误处理中间件
```javascript
// app/Exceptions/Handler.js
'use strict'

const BaseExceptionHandler = use('BaseExceptionHandler');
const Logger = use('Logger');

class ExceptionHandler extends BaseExceptionHandler {
  async handle(error, { request, response }) {
    // 请求信息
    const requestInfo = {
      url: request.url(),
      method: request.method(),
      ip: request.ip(),
      headers: request.headers(),
      body: request.all()
    };
    
    // 处理自定义错误类型
    if (error.name === 'ValidationError') {
      return response.status(400).json({
        code: 400,
        error: 'VALIDATION_ERROR',
        message: error.message,
        fields: error.fields || {}
      });
    }
    
    if (error.name === 'AuthenticationError') {
      return response.status(401).json({
        code: 401,
        error: 'AUTHENTICATION_ERROR',
        message: error.message || '未登录或登录已过期'
      });
    }
    
    if (error.name === 'AuthorizationError') {
      return response.status(403).json({
        code: 403,
        error: 'AUTHORIZATION_ERROR',
        message: error.message || '无操作权限'
      });
    }
    
    if (error.name === 'NotFoundError') {
      return response.status(404).json({
        code: 404,
        error: 'NOT_FOUND_ERROR',
        message: error.message || '资源不存在'
      });
    }
    
    // 处理数据库错误
    if (error.code === 'ER_DUP_ENTRY') {
      return response.status(400).json({
        code: 400,
        error: 'DUPLICATE_ENTRY',
        message: '数据已存在，请勿重复添加'
      });
    }
    
    // 记录未处理的错误
    Logger.error('未处理的异常:', {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      request: requestInfo
    });
    
    // 生产环境隐藏错误细节
    const isProduction = process.env.NODE_ENV === 'production';
    
    return response.status(500).json({
      code: 500,
      error: 'SERVER_ERROR',
      message: isProduction ? '服务器内部错误' : error.message,
      ...(isProduction ? {} : { stack: error.stack })
    });
  }
  
  async report(error, { request }) {
    // 记录所有错误
    Logger.error('应用错误:', {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      request: {
        url: request.url(),
        method: request.method(),
        ip: request.ip()
      }
    });
  }
}

module.exports = ExceptionHandler;
```

### 参数验证错误处理
```javascript
// app/Validators/Order.js
'use strict'

const { ValidationError } = use('App/Exceptions');

class OrderValidator {
  get rules() {
    return {
      user_id: 'required|integer|exists:users,id',
      items: 'required|array|min:1',
      'items.*.category_id': 'required|integer|exists:categories,id',
      'items.*.quantity': 'required|integer|min:1',
      address: 'required|string|max:200',
      appointment_time: 'required|date|after:now'
    };
  }
  
  get messages() {
    return {
      'user_id.required': '用户ID不能为空',
      'user_id.exists': '用户不存在',
      'items.required': '订单项不能为空',
      'items.array': '订单项必须是数组',
      'items.min': '订单项至少需要一项',
      'items.*.category_id.required': '分类ID不能为空',
      'items.*.category_id.exists': '分类不存在',
      'items.*.quantity.required': '数量不能为空',
      'items.*.quantity.min': '数量必须大于0',
      'address.required': '地址不能为空',
      'address.max': '地址不能超过200个字符',
      'appointment_time.required': '预约时间不能为空',
      'appointment_time.date': '预约时间格式不正确',
      'appointment_time.after': '预约时间必须在当前时间之后'
    };
  }
  
  async fails(errorMessages) {
    const errors = errorMessages.reduce((result, error) => {
      const field = error.field;
      result[field] = error.message;
      return result;
    }, {});
    
    throw new ValidationError('表单验证失败', errors);
  }
}

module.exports = OrderValidator;
```

## 日志规范

### 日志级别定义
```javascript
// 日志级别使用规范
const Logger = use('Logger');

// ERROR：严重错误，导致系统不可用
Logger.error('数据库连接失败', {
  error: error.message,
  stack: error.stack,
  database: config.database
});

// WARN：警告信息，可能导致系统异常
Logger.warn('用户多次登录失败', {
  userId: user.id,
  attempts: attempts,
  ip: request.ip()
});

// INFO：重要业务信息，记录关键操作
Logger.info('用户完成订单', {
  userId: user.id,
  orderId: order.id,
  amount: order.total_amount
});

// DEBUG：调试信息，用于开发环境
Logger.debug('API响应详情', {
  url: request.url(),
  params: request.all(),
  response: response.data
});
```

### 结构化日志
```javascript
// 日志格式规范
const logData = {
  // 上下文信息
  context: {
    requestId: request.id(),     // 请求唯一标识
    timestamp: new Date(),       // 日志时间
    environment: process.env.NODE_ENV, // 环境
    service: 'order-service'     // 服务名称
  },
  
  // 用户信息
  user: {
    id: user?.id,
    type: user?.type,            // 用户类型
    ip: request.ip()
  },
  
  // 操作信息
  operation: {
    name: 'create_order',        // 操作名称
    status: 'success',           // 操作状态
    duration: 120,               // 操作耗时(ms)
  },
  
  // 详细数据
  data: {
    orderId: order.id,
    items: items.length,
    amount: order.total_amount
  },
  
  // 错误信息(如果有)
  error: error ? {
    message: error.message,
    code: error.code,
    stack: error.stack
  } : undefined
};

Logger.info('创建订单', logData);
```

### 日志配置示例
```javascript
// config/app.js
logLevel: process.env.LOG_LEVEL || 'info',

// config/logger.js
module.exports = {
  transport: 'file',
  
  // 文件输出
  file: {
    driver: 'file',
    filename: 'adonis.log',
    level: process.env.LOG_LEVEL || 'info'
  },
  
  // 控制台输出
  console: {
    driver: 'console',
    level: process.env.LOG_LEVEL || 'info'
  },
  
  // 生产环境可配置远程日志服务
  production: {
    driver: 'http',
    endpoint: process.env.LOG_ENDPOINT,
    level: 'info'
  }
};
```

## 监控告警系统

### 前端错误监控
```javascript
// 前端错误监控系统
class ErrorMonitor {
  constructor(options = {}) {
    this.apiEndpoint = options.apiEndpoint || '/api/errors';
    this.appName = options.appName || 'client-app';
    this.enabled = options.enabled !== false;
    this.maxErrors = options.maxErrors || 10;
    this.errorCount = 0;
    
    if (this.enabled) {
      this.init();
    }
  }
  
  init() {
    // 捕获 JS 错误
    window.addEventListener('error', (event) => {
      this.captureError({
        type: 'javascript',
        message: event.message,
        stack: event.error?.stack,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      });
      
      return false;
    }, true);
    
    // 捕获未处理的 Promise 错误
    window.addEventListener('unhandledrejection', (event) => {
      this.captureError({
        type: 'promise',
        message: event.reason?.message || 'Unhandled Promise Rejection',
        stack: event.reason?.stack,
        detail: event.reason
      });
      
      return false;
    });
    
    // 捕获资源加载错误
    document.addEventListener('error', (event) => {
      const target = event.target;
      if (target.tagName === 'IMG' || target.tagName === 'SCRIPT' || target.tagName === 'LINK') {
        this.captureError({
          type: 'resource',
          element: target.tagName.toLowerCase(),
          source: target.src || target.href,
          message: `Failed to load ${target.tagName.toLowerCase()}`
        });
      }
    }, true);
    
    console.log('错误监控系统已初始化');
  }
  
  captureError(errorInfo) {
    if (this.errorCount >= this.maxErrors) {
      return;
    }
    
    this.errorCount++;
    
    // 获取用户和环境信息
    const metadata = this.getMetadata();
    
    // 发送错误报告
    const report = {
      app: this.appName,
      timestamp: new Date().toISOString(),
      error: errorInfo,
      ...metadata
    };
    
    // 发送到服务器
    fetch(this.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(report),
      // 使用 keepalive 确保页面卸载时也能发送
      keepalive: true
    }).catch(e => {
      console.error('Error report failed:', e);
    });
    
    console.warn('Captured error:', errorInfo);
  }
  
  getMetadata() {
    return {
      url: window.location.href,
      userAgent: navigator.userAgent,
      screenSize: `${window.screen.width}x${window.screen.height}`,
      viewportSize: `${window.innerWidth}x${window.innerHeight}`,
      userId: this.getUserId(),
      sessionId: this.getSessionId()
    };
  }
  
  getUserId() {
    // 从存储中获取用户ID
    return localStorage.getItem('userId') || 'anonymous';
  }
  
  getSessionId() {
    // 获取或创建会话ID
    let sessionId = sessionStorage.getItem('sessionId');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('sessionId', sessionId);
    }
    return sessionId;
  }
}

// 初始化错误监控
const errorMonitor = new ErrorMonitor({
  apiEndpoint: 'https://api.example.com/errors',
  appName: 'recycle-client'
});
```

### 后端性能监控
```javascript
// 性能监控中间件
class PerformanceMonitor {
  async handle({ request }, next) {
    const start = process.hrtime.bigint();
    const startMemory = process.memoryUsage();
    
    try {
      // 执行路由处理器
      await next();
    } finally {
      const end = process.hrtime.bigint();
      const endMemory = process.memoryUsage();
      
      // 计算执行时间(毫秒)
      const duration = Number(end - start) / 1000000;
      
      // 计算内存使用变化
      const memoryDiff = {
        rss: endMemory.rss - startMemory.rss,
        heapTotal: endMemory.heapTotal - startMemory.heapTotal,
        heapUsed: endMemory.heapUsed - startMemory.heapUsed
      };
      
      // 记录性能数据
      this.recordPerformance({
        path: request.url(),
        method: request.method(),
        duration,
        memoryDiff,
        timestamp: new Date()
      });
    }
  }
  
  recordPerformance(data) {
    // 性能阈值检查(超过100ms记录警告)
    if (data.duration > 100) {
      Logger.warn('API性能警告', {
        path: data.path,
        method: data.method,
        duration: `${data.duration}ms`
      });
    }
    
    // 保存性能数据
    // 可以存储到数据库或发送到专门的监控服务
  }
}
```