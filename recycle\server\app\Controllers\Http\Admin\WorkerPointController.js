'use strict'

const _ = require('lodash')
const moment = require('moment')

const { WorkerPoint, WorkerPay, Worker, WorkerWalletLog } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { OrderService, ExcelService } = require('../../../Services')

//人员画像
class WorkerPointController {
  async index({ request, response }) {
    let { current = 1, pageSize = 10, name, phone, status } = request.all()
    let query = WorkerPoint.query()
    if (status) {
      query.where('status', status)
    }
    let data = await query.paginate(current, pageSize)
    response.json(data)
  }
  async show({ request, params, response }) {
    let vo = await WorkerPoint.query().where('workerID', params.id)
      .orderBy('createdAt', 'desc')
      .first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    let log = await WorkerPoint.query().where('workerID', params.id)
      .orderBy('createdAt', 'asc')
      .fetch()
    response.json({ data: vo, log })
  }
  async store({ request, response }) {
    const { workerID, point, count, completeness, finishCount, handingCount, overCount,
      satisfaction, safe, standard, complaint, ontime, praise, goodCount, badCount, complaintCount } = request.all()
    if (!workerID) {
      throw ERR.INVALID_PARAMS
    }
    let vo = await WorkerPoint.create({
      workerID, point, count, completeness, finishCount, handingCount, overCount,
      satisfaction, safe, standard, complaint, ontime, praise, goodCount, badCount, complaintCount
    })
    response.json(vo)
  }
  async update({ request, params, response }) {
    const { workerID, point, count, completeness, finishCount, handingCount, overCount,
      satisfaction, safe, standard, complaint, ontime, praise, goodCount, badCount, complaintCount } = request.all()
    let vo = await WorkerPoint.query().where('workerID', params.id).orderBy('createdAt', 'desc')
      .first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
  }
  async exportMasterXLS({ request, params, response }) {
    let query = Worker.query().with('company')
      .with('assistants', (b) => b.select('id', 'workerName', 'managerID', 'mobile', 'createdAt', 'forbidden'))
      .where('isUse', '同意')
    let vo = await query.orderBy('createdAt', 'desc').fetch()
    let data = []
    vo = vo.toJSON()
    vo.forEach((value, index) => {
      let arrInner = []
      arrInner.push(value.id)
      arrInner.push(value.workerName)
      arrInner.push(value.mobile)
      arrInner.push(value.wallet)
      arrInner.push(value.company ? value.company.companyName : '未知')
      arrInner.push(value.managerID ? '是' : '否')
      arrInner.push(value.company ? value.company.mobile : '未知')
      arrInner.push(parseInt(value.forbidden) ? '正常' : '停用')
      arrInner.push(moment(value && value.createdAt).format('YYYY-MM-DD HH:mm'))
      arrInner.push(value.assistants ? value.assistants.map(vo => vo.workerName).join(',') : '未知')
      data.push(arrInner)
    })
    let theTitle = [
      'ID',
      '师傅名称',
      '师傅电话',
      '余额',
      '公司',
      '是否小工',
      '公司电话',
      '状态',
      '注册时间',
      '小工师傅',
    ]
    let theWidth = [
      { wch: 14 },
      { wch: 16 },
      { wch: 22 },
      { wch: 14 },
      { wch: 14 },
      { wch: 8 },
      { wch: 22 },
      { wch: 14 },
      { wch: 22 },
      { wch: 22 },
    ]
    let fileTitle = '师傅统计-' + `${moment().format('YYYYMMDDHH')}`
    await ExcelService.generateExcel(fileTitle, theTitle, data, response, { '!cols': theWidth })
  }
  //订单数据导出接口
  async exportXLS({ request, params, response }) {
    let { status, workerID, type, workerName, source, current = 1, transactionID, pageSize = 10,
      finishStartDate, finishEndDate,
    } = request.all()
    let query = WorkerPay.query().with('worker')
    if (status) {
      if (status === 'all') { } else {
        query.where('status', status)
      }
    }
    if (finishStartDate && finishEndDate) {
      query.where('finishAt', '>=', moment(finishStartDate).toDate()).where('finishAt', '<=', moment(finishEndDate).add(1, 'd').toDate())
    }
    if (source) {
      if (source === 'all') { } else {
        query.where('source', source)
      }
    }
    if (type) {
      if (type === 'all') { } else {
        query.where('type', type)
      }
    }
    if (workerID) {
      query.where('workerID', workerID)
    }
    if (transactionID) {
      query.where('transactionID', 'like', `%${transactionID}%`)
    }
    if (workerName) {
      let workers = await Worker.query().where('workerName', 'like', `%${workerName}%`).select('id', 'workerName').fetch()
      let workerIDs = workers.rows.map((vo) => vo.id)
      query.where('workerID', 'IN', _.uniq(workerIDs))
    }
    let vo = await query.orderBy('finishAt', 'desc').fetch()
    let data = []
    vo.rows.forEach((value, index) => {
      let arrInner = []
      arrInner.push(value.id)
      arrInner.push(value.type)
      arrInner.push(value.source)
      arrInner.push(value.$relations.worker ? value.$relations.worker.workerName : '未知')
      arrInner.push(value.$relations.worker ? value.$relations.worker.mobile : '未知')
      arrInner.push(value.$relations.worker ? value.$relations.worker.companyID : '未知')
      arrInner.push(value ? parseInt(value.totalPay) / 100 : null)
      arrInner.push(value ? parseInt(value.refundRMB) / 100 : null)
      arrInner.push(value.status)
      arrInner.push(moment(value && value.finishAt).format('YYYY-MM-DD HH:mm'))
      arrInner.push(moment(value && value.refundAt).format('YYYY-MM-DD HH:mm'))
      data.push(arrInner)
    })
    let theTitle = [
      'ID',
      '类型',
      '来源',
      '师傅',
      '师傅手机号',
      '公司ID',
      '支付金额',
      '退款金额',
      '状态',
      '完成时间',
      '退款时间',
    ]
    let theWidth = [
      { wch: 14 },
      { wch: 16 },
      { wch: 14 },
      { wch: 16 },
      { wch: 12 },
      { wch: 12 },
      { wch: 12 },
      { wch: 12 },
      { wch: 22 },
      { wch: 22 },
      { wch: 22 },
    ]
    let fileTitle = '押金统计-' + `${moment().format('YYYYMMDDHH')}`
    await ExcelService.generateExcel(fileTitle, theTitle, data, response, { '!cols': theWidth })
  }
  async workerPaylist({ request, response, params }) {
    let { status, workerID, type, workerName, source, current = 1, transactionID, pageSize = 10,
      finishStartDate, finishEndDate,
    } = request.all()
    let query = WorkerPay.query().with('worker').with('refundVo')
    if (status) {
      if (status === 'all') { } else {
        query.where('status', status)
      }
    }
    if (finishStartDate && finishEndDate) {
      query.where('finishAt', '>=', moment(finishStartDate).toDate()).where('finishAt', '<=', moment(finishEndDate).add(1, 'd').toDate())
    }
    if (source) {
      if (source === 'all') { } else {
        query.where('source', source)
      }
    }
    if (type) {
      if (type === 'all') { } else {
        query.where('type', type)
      }
    }
    if (workerID) {
      query.where('workerID', workerID)
    }
    if (transactionID) {
      query.where('transactionID', 'like', `%${transactionID}%`)
    }
    if (workerName) {
      let workers = await Worker.query().where('workerName', 'like', `%${workerName}%`).select('id', 'workerName').fetch()
      let workerIDs = workers.rows.map((vo) => vo.id)
      query.where('workerID', 'IN', _.uniq(workerIDs))
    }
    let data = await query.orderBy('finishAt', 'desc').paginate(current, pageSize)
    return data
  }
  async masterPayRefund({ request, response }) {
    let { id, reason = "后台退押金", amount } = request.all()
    let res = await OrderService.refundMasterPay(id, reason, amount)
    return res
  }
  async exportWalletXLS({ request, response }) {
    let { workerID } = request.all()
    let data = await WorkerPay.query().where('workerID', workerID)
      .where('status', E.OrderStatus.Completed)
      .whereNotNull('finishAt')
      .orderBy('finishAt', 'desc')
      .fetch()
    // return data
    // 合并消费记录
    let orderLog = await WorkerWalletLog.query()
      .where('workerID', workerID)
      .select('workerID', 'id', 'createdAt', 'money', 'orderID').with('order',
        builder => {
          builder.select('id', 'orderNo', 'status', 'address')
          return builder
        })
      .fetch()

    let resJson = orderLog.toJSON().map((vo, index) => {
      vo.updatedAt = vo.createdAt
      vo.totalPay = parseInt(vo.money) * 100
      return vo
    })
    let res = resJson.concat(data.toJSON())
    let returnVO = _.orderBy(res, ['updatedAt'], ['desc'])
    // console.log(returnVO);
    let query = await Worker.find(workerID)
    // console.log(query.toJSON());
    // return returnVO
    let redata = []
    returnVO.forEach((value, index) => {
      let arrInner = []
      arrInner.push(value.id)
      arrInner.push(query.toJSON() && query.toJSON().workerName)
      arrInner.push(value.workerID)
      arrInner.push(value.totalPay ? value.totalPay / 100 : value.money)
      arrInner.push(value.order ? '扣款' : '充值')
      arrInner.push(value.order ? value.order.orderNo : value.transactionID)
      arrInner.push(value.updatedAt)
      redata.push(arrInner)
    })
    let theTitle = [
      'ID',
      '师傅名称',
      '师傅ID',
      '金额',
      '类型',
      '单号',
      '更新时间',
    ]
    let theWidth = [
      { wch: 8 },
      { wch: 10 },
      { wch: 8 },
      { wch: 10 },
      { wch: 10 },
      { wch: 22 },
      { wch: 22 },
    ]
    let fileTitle = '师傅钱包统计-' + `${query.toJSON().workerName}` + `${moment().format('YYYYMMDDHH')}`
    await ExcelService.generateExcel(fileTitle, theTitle, redata, response, { '!cols': theWidth })
  }
}

module.exports = WorkerPointController
