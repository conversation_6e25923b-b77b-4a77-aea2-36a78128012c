'use strict'

const _ = require('lodash')
const moment = require('moment')
const { Order, Company } = require('../../../Models')
const array = require('../../../../public/address')
const { E } = require('../../../../../../constants')

//省市区街道级联
class AddressController {
  async store({ request, response }) {
    let { code } = request.all()
    let province = code.substring(0, 2)
    let provinceMark
    array.forEach((value, index) => {
      if (value.code === province) {
        provinceMark = index
        return
      }
    })
    let cities = array[provinceMark].children
    let cityMark
    cities.forEach((value, index) => {
      if (value.code === code) {
        cityMark = index
        return
      }
    })
    // console.log('city', cities[cityMark])
    let city = cities[cityMark].children
    let list = []
    city.forEach((value) => {
      // console.log('value', value)
      value.children.forEach((sub) => {
        let obj = sub
        obj.area = value.name
        obj.areaCode = value.code
        list.push(obj)
      })
    })
    return list
  }

  async index({ request, response }) {
    let { code, codeArr } = request.all()
    let length = 0
    if (code) { length = code.length }
    let address = []
    if (!code && !codeArr) {
      //获取省份
      array.forEach((value, index) => {
        address.push({
          name: value.name,
          code: value.code
        })
      })
    }
    if (length === 2) {
      //省份下城市
      array.forEach((value, index) => {
        if (value.code === code) {
          //address.push(value)
          value.children.forEach((city, cityIndex) => {
            address.push({
              name: city.name,
              code: city.code
            })
          })
        }
      })
    } else if (length === 4) {
      //城市下区
      let province = code.substring(0, 2)
      array.forEach((value, index) => {
        if (value.code === province) {
          value.children.forEach((city, cityIndex) => {
            if (city.code === code) {
              city.children.forEach((area, areaIndex) => {
                address.push({
                  name: area.name,
                  code: area.code
                })
              })
            }
          })
        }
      })
    } else if (length === 6) {
      //区下街道
      let province = code.substring(0, 2)
      let cityCode = code.substring(0, 4)
      array.forEach((value, index) => {
        if (value.code === province) {
          value.children.forEach((city, cityIndex) => {
            if (city.code === cityCode) {
              city.children.forEach((area, areaIndex) => {
                if (area.code === code) {
                  area.children.forEach((street, streetIndex) => {
                    address.push({
                      name: street.name,
                      code: street.code
                    })
                  })
                }
              })
            }
          })
        }
      })
    }
    if (codeArr) {
      if (codeArr[0].length === 6) {
        codeArr.forEach((codeItem) => {
          //区下街道6位
          let province = codeItem.substring(0, 2)
          let cityCode = codeItem.substring(0, 4)
          array.forEach((value, index) => {
            if (value.code === province) {
              value.children.forEach((city, cityIndex) => {
                if (city.code === cityCode) {
                  city.children.forEach((area, areaIndex) => {
                    if (area.code === codeItem) {
                      area.children.forEach((street, streetIndex) => {
                        address.push({
                          name: street.name,
                          code: street.code
                        })
                      })
                    }
                  })
                }
              })
            }
          })
        })
      } else if (codeArr[0].length === 4) {
        codeArr.forEach((codeItem) => {
          //城市下区 
          let province = codeItem.substring(0, 2)
          array.forEach((value, index) => {
            if (value.code === province) {
              value.children.forEach((city, cityIndex) => {
                if (city.code === codeItem) {
                  city.children.forEach((area, areaIndex) => {
                    address.push({
                      name: area.name,
                      code: area.code
                    })
                  })
                }
              })
            }
          })
        })
      } else if (codeArr[0].length === 2) {
        codeArr.forEach((codeItem) => {
          //省份下城市
          array.forEach((value, index) => {
            if (value.code === codeItem) {
              //address.push(value)
              value.children.forEach((city, cityIndex) => {
                address.push({
                  name: city.name,
                  code: city.code
                })
              })
            }
          })
        })
      }
    }
    return address
  }
  //获取总共完成订单
  async all({ request, response }) {
    let { companyID } = request.all()
    let orderList = await Order.query().where('companyID', companyID).where('status', E.OrderStatus.Completed).fetch()
    let total = 0
    _.forEach(orderList.rows, async function (order) {
      let a = order.actualMoney ? order.actualMoney : 0
      let b = order.commission ? order.commission : 0
      let money = 0
      if (order.waste_1st_ID === 4) {
        money = a
      } else {
        money = a + b
      }
      total += money
    })
    let company = await Company.find(companyID)
    company.estimateTotal = total
    company.completeTotal = orderList.rows.length
    await company.save()
    return company
  }
}

module.exports = AddressController
