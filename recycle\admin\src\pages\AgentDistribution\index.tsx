import { <PERSON><PERSON>, Image, Button, Modal, Form, notification, Input, Switch } from 'antd'
import type { ActionType, ProColumns } from '@ant-design/pro-table'
import ProTable, { TableDropdown } from '@ant-design/pro-table'
import { PlusOutlined, } from '@ant-design/icons'
import ProCard from '@ant-design/pro-card'
import { effect } from 'dva17'
import { useEffect, useRef, useState } from 'react'
import { NSalesman, EGetList, EPut, EPost } from '../../common/action'
import { useForm } from 'antd/es/form/Form'
import { useNavigate } from 'react-router-dom'
import { ProList } from '@ant-design/pro-components'
export default function AgentDistribution() {
  const navigation = useNavigate()
  const [form] = useForm()
  const actionRef = useRef<ActionType>()
  const [visibleCreateModal, setVisibleCreateModal] = useState(false)
  const [visibleOrderCount, setVisibleOrderCount] = useState(false)
  const [orderList, setOrderList] = useState<OrderCount[]>([])
  const [modaltitle, setModaltitle] = useState('')
  const [putData, setPutData] = useState<any>(null)
  /*--------------------- 生命周期 ---------------------*/
  const columns: ProColumns<Item>[] = [
    {
      title: '展开',
      width: '80',
      copyable: false,
      search: false,
      ellipsis: false,
    },
    {
      title: 'id',
      width: '80',
      copyable: false,
      ellipsis: false,
      search: false,
      dataIndex: 'id',
    },
    {
      title: '职务',
      width: '80',
      copyable: false,
      search: false,
      ellipsis: false,
      render: (_, record) => {
        let rowkey
        if (record.superior_id) {
          rowkey = '地推专员'
        } else {
          rowkey = '地推经理'
        }
        return rowkey
      },
    },
    {
      title: '姓名',
      dataIndex: 'name',
      search: false,
      width: '80',
      copyable: false,
      ellipsis: false,
    },
    {
      title: '电话',
      dataIndex: 'phone',
      width: '80',
      search: false,
      copyable: false,
      ellipsis: false,
    },
    {
      title: '结算时间',
      dataIndex: 'createdAt',
      copyable: false,
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            startDate: value[0],
            endDate: value[1],
          }
        },
      },
    },
    {
      title: '经理姓名',
      dataIndex: 'name',
      copyable: false,
      hideInTable: true,
    },
    {
      title: '专员人数',
      search: false,
      width: '30',
      copyable: false,
      ellipsis: false,
      render: (_, row) => {
        if (row.salesman) {
          return row.salesman.length
        } else {
          return null
        }
      },
    },
    {
      title: '推荐人数',
      search: false,
      width: '50',
      copyable: false,
      ellipsis: false,
      render: (_, row) => {
        if (row.userCount) {
          return row.userCount.count
        } else {
          return 0
        }
      },
    },
    {
      title: '推荐订单',
      search: false,
      width: '50',
      copyable: false,
      ellipsis: false,
      render: (_, row) => {
        if (row.orderCount.length) {
          return row.orderCount.reduce((pre, cur) => {
            return pre + cur.count
          }, 0)
        } else {
          return 0
        }
      },
    },
    {
      title: '分享码',
      search: false,
      dataIndex: 'img_url',
      width: '200px',
      copyable: false,
      ellipsis: false,
      valueType: 'image',
      fieldProps: {
        style: {
          width: '80px',
          height: '80px',
          objectFit: 'cover',
        },
      },
    },

    {
      title: '状态',
      dataIndex: 'status',
      search: false,
      copyable: false,
      ellipsis: false,
      valueType: 'select',
      valueEnum: {
        1: {
          text: '启用',
          status: 'Success',
          // disabled: true,
        },
        0: {
          text: '停用',
          status: 'Error',
        },
      },
    },
    {
      title: '操作',
      // dataIndex: '',
      search: false,
      width: '140',
      copyable: false,
      ellipsis: false,
      render: (_, row) => (
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <a
            style={{ flex: 1 }}
            onClick={() => {
              // navigation(`/OverView/${row.id}`)
              setOrderList(row.orderCount)
              setVisibleOrderCount(true)
            }}>
            推荐订单
          </a>
          {row.superior_id ? (
            <a style={{ flex: 1 }}></a>
          ) : (
            <a
              style={{ flex: 1 }}
              onClick={() => {
                setModaltitle('添加地推专员')
                setPutData(row)
                setVisibleCreateModal(true)
              }}>
              添加地推专员
            </a>
          )}
          <a
            style={{ flex: 1 }}
            onClick={() => {
              row.superior_id ? setModaltitle('编辑地推专员') : setModaltitle('编辑地推经理')
              form.setFieldsValue({ name: row.name, status: row.status, phone: row.phone })
              setPutData(row)
              setVisibleCreateModal(true)
            }}>
            编辑
          </a>
        </div>
      ),
    },
  ]
  useEffect(() => { }, [])
  /*--------------------- 响应 ---------------------*/
  const refreshPage = () => {
    if (actionRef.current) {
      actionRef.current.reload()
    }
  }
  /*--------------------- 渲染 ---------------------*/
  return (
    <div>
      <ProCard>
        <ProTable<Item>
          actionRef={actionRef}
          columns={columns}
          request={async (params = {}, sorter) => {
            return (await effect(NSalesman, EGetList, {
              ...params,
              ...sorter,
            })) as any
          }}
          pagination={{
            pageSize: 10,
          }}
          dateFormatter="string"
          headerTitle=""
          expandable={{ childrenColumnName: 'salesman' }}
          rowKey={record => {
            let rowkey
            if (record.superior_id) {
              rowkey = record.superior_id + '_' + record.id
            } else {
              rowkey = record.id
            }

            return rowkey
          }}
          toolBarRender={() => [
            <Button
              type="primary"
              onClick={() => {
                setModaltitle('添加地推经理')
                form.resetFields()
                setVisibleCreateModal(true)
              }}>
              <PlusOutlined />
              添加地推经理
            </Button>,
          ]}
        />

        <Modal
          title={modaltitle}
          open={visibleCreateModal}
          onOk={() => { }}
          onCancel={() => {
            setVisibleCreateModal(false)
          }}
          destroyOnClose
          width={400}
          footer={null}>
          <Form
            form={form}
            initialValues={{ status: 1 }}
            onFinish={values => {
              if (modaltitle == '添加地推经理') {
                effect(NSalesman, EPost, values).then(() => {
                  setVisibleCreateModal(false)
                  notification.success({
                    message: '成功！',
                    description: '创建成功',
                    duration: 2,
                  })
                  refreshPage()
                })
              } else if (modaltitle == '编辑地推经理') {
                values.id = putData.id
                effect(NSalesman, EPut, values).then(() => {
                  setVisibleCreateModal(false)
                  notification.success({
                    message: '成功！',
                    description: '修改成功',
                    duration: 2,
                  })
                  refreshPage()
                })
              } else if (modaltitle == '添加地推专员') {
                values.superior_id = putData.id
                effect(NSalesman, EPost, values).then(() => {
                  setVisibleCreateModal(false)
                  notification.success({
                    message: '成功！',
                    description: '创建成功',
                    duration: 2,
                  })
                  refreshPage()
                })
              } else if (modaltitle == '编辑地推专员') {
                values.id = putData.id
                values.superior_id = putData.superior_id
                effect(NSalesman, EPut, values).then(() => {
                  setVisibleCreateModal(false)
                  notification.success({
                    message: '成功！',
                    description: '修改成功',
                    duration: 2,
                  })
                  refreshPage()
                })
              }
              form.resetFields()
            }}
            onFinishFailed={err => {
              if (err.errorFields.length == 0) {
                notification.error({
                  message: '',
                  description: '',
                })
              }
            }}
            autoComplete="off">
            <Form.Item label="姓名" name="name" rules={[{ required: true, message: '请输入正确的名称!' }]}>
              <Input style={{ width: '90%' }} />
            </Form.Item>
            <Form.Item label="手机" name="phone" rules={[{ required: true, message: '请输入正确的手机!' }]}>
              <Input style={{ width: '90%' }} />
            </Form.Item>
            <Form.Item name="status" label="状态" rules={[{ required: true }]} valuePropName="checked">
              <Switch />
            </Form.Item>
            <Form.Item wrapperCol={{ offset: 10, span: 16 }}>
              <Button type="primary" htmlType="submit">
                提交
              </Button>
            </Form.Item>
          </Form>
        </Modal>

        <Modal
          title={'推荐订单列表'}
          width={400}
          footer={null}
          open={visibleOrderCount}
          onOk={() => { }}
          onCancel={() => {
            setOrderList([])
            setVisibleOrderCount(false)
          }}>
          <ProCard>
            <ProList<OrderCount>
              dataSource={orderList}
              metas={{
                title: {
                  dataIndex: 'wasteType',
                },
                // avatar: {
                //   dataIndex: 'count',
                // },
                // description: {
                //   dataIndex: 'count',
                // },
                content: {
                  dataIndex: 'wasteType',
                  render: (text, data, index) => (
                    <a
                      onClick={() => {
                        navigation(`/ClientManage/ClientOrder/${data.salesman_id}`)
                      }}>
                      {data.count}单
                    </a>
                  ),
                },
              }}></ProList>
          </ProCard>
        </Modal>
      </ProCard>
    </div>
  )
}

interface Item {
  id: number
  superior_id?: any
  name: string
  phone: string
  img_url: string
  status: number
  createdAt: string
  updatedAt: string
  salesman: Salesman[]
  userCount?: UserCount
  orderCount: OrderCount[]
}

interface OrderCount {
  salesman_id: number
  wasteType: string
  count: number
}

interface UserCount {
  salesman_id: number
  count: number
}

interface Salesman {
  id: number
  superior_id: number
  name: string
  img_url: string
  phone: string
  status: number
  createdAt: string
  updatedAt: string
  userCount?: any
  orderCount: any[]
}
