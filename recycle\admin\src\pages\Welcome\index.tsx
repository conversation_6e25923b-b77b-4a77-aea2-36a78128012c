import { useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { reducer, useConnect } from 'dva17'
import { LikeOutlined } from '@ant-design/icons'
import { Button, Descriptions, Result, Avatar, Space, Statistic } from 'antd'
import ProLayout, { <PERSON><PERSON><PERSON>r, SettingDrawer } from '@ant-design/pro-layout'

import images from '../../assets/images'
import styles from './index.module.less'

import { NUser, RAdd, RSetState } from '../../common/action'

export default () => {
  const { count } = useConnect(NUser) //使用state中model数据

  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    console.log('Home start') //页面启动
    return () => {
      console.log('Home end') //页面结束
      reducer(NUser, RSetState, { count: 0 }) //清除count
    }
  }, [])

  /*--------------------- 响应 ---------------------*/
  const onCountClick = () => {
    reducer(NUser, RAdd, {}) //使用reducer更新models数据
  }

  /*--------------------- 渲染 ---------------------*/
  return (
    <PageContainer
      content={
        <Descriptions size="small" column={2}>
          <Descriptions.Item label="创建人">张三1</Descriptions.Item>
          <Descriptions.Item label="联系方式">
            <a>421421</a>
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">2017-01-10</Descriptions.Item>
          <Descriptions.Item label="更新时间">2017-10-10</Descriptions.Item>
          <Descriptions.Item label="备注">中国浙江省杭州市西湖区古翠路</Descriptions.Item>
        </Descriptions>
      }
      tabList={[
        {
          tab: '基本信息',
          key: 'base',
        },
        {
          tab: '详细信息',
          key: 'info',
        },
      ]}
      extraContent={
        <Space size={24}>
          <Statistic title="Feedback" value={1128} prefix={<LikeOutlined />} />
          <Statistic title="Unmerged" value={93} suffix="/ 100" />
        </Space>
      }
      extra={[
        <Button key="3">操作</Button>,
        <Button key="2">操作</Button>,
        <Button key="1" type="primary">
          主操作
        </Button>,
      ]}
      footer={[
        <Button key="3">重置</Button>,
        <Button key="2" type="primary">
          提交
        </Button>,
      ]}>
      <div
        style={{
          height: '120vh',
        }}>
        <Result
          status="404"
          style={{
            height: '100%',
            background: '#fff',
          }}
          title="Hello World"
          subTitle="Sorry, you are not authorized to access this page."
          extra={<Button type="primary">Back Home</Button>}
        />
      </div>
    </PageContainer>
  )
}
