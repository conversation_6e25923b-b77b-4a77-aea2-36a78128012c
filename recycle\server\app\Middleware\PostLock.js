'use strict'

const moment = require('moment')

const { ERR, E } = require('../../../../constants')
const USER_ORDER_LOCK = {}

class PostLock {
  async handle({ request }, next) {
    console.log('----------> PostLock 1', request.url())
    // call next to advance the request
    let { worker } = request
    let userID = worker.id
    // 自锁机制 START
    let key = userID + ':' + request.url()
    let lock = USER_ORDER_LOCK[key]
    let now = moment().unix()
    if (lock && now - lock < 200) {
      throw ERR.POST_LOCK
    } else {
      USER_ORDER_LOCK[key] = now
    }
    try {
      await next()
      console.log('----------> PostLock 2', key)
      delete USER_ORDER_LOCK[key]
    } catch (e) {
      console.log('----------> PostLock 2', key)
      delete USER_ORDER_LOCK[key]
      throw e
    }
  }
}

module.exports = PostLock
