{"name": "adonis-api-app", "version": "4.1.0", "adonis-version": "4.1.0", "description": "Adonisjs boilerplate for API server with pre-configured JWT", "main": "index.js", "scripts": {"start": "node server.js", "dev": "adonis serve --dev", "test": "node ace test", "example-test": "node ace test --glob /**/unit/example.spec.js"}, "keywords": ["ad<PERSON><PERSON><PERSON><PERSON>", "adonis-app"], "author": "", "license": "UNLICENSED", "private": true, "dependencies": {"@adonisjs/ace": "^5.0.8", "@adonisjs/auth": "^3.0.7", "@adonisjs/bodyparser": "^2.0.5", "@adonisjs/cors": "^1.0.7", "@adonisjs/fold": "^4.0.9", "@adonisjs/framework": "^5.0.9", "@adonisjs/ignitor": "^2.0.8", "@adonisjs/lucid": "^6.1.3", "@adonisjs/vow": "^1.0.17", "@alicloud/sms-sdk": "^1.1.6", "adonis": "^0.9.0", "ali-oss": "^6.1.0", "axios": "^0.18.0", "co-wechat-api": "^3.9.1", "co-wechat-oauth": "^2.0.1", "exceljs": "^4.1.1", "fs-extra": "^7.0.1", "hasha": "^3.0.0", "jsonwebtoken": "^8.5.0", "lodash": "^4.17.15", "md5": "^2.2.1", "moment": "^2.24.0", "mysql": "^2.16.0", "node-fecth": "^0.0.1-security", "node-schedule": "^1.3.2", "node-xlsx": "^0.15.0", "qrcode": "^1.3.3", "random-int": "^1.0.0", "shortid": "^2.2.14", "tenpay": "^2.1.18", "wechatpay-node-v3": "^2.0.0", "weixin-pay": "^1.1.7", "xlsx": "^0.18.5"}, "autoload": {"App": "./app"}}