/**
 * Created by <PERSON>(qq:24242811) on 2019/1/10.
 */
const Route = use('Route')
const { UserService, WXService } = require('./Services')
const Helpers = use('Helpers')
const { Order } = require('./Models')
const axios = require('axios')
const moment = require('moment')
// 无需登录
Route.group(() => {
  Route.resource('user', 'UserController') //用户
  Route.post('user/register', 'UserController.register')
  Route.post('user/login', 'UserController.login')
  Route.post('user/getWxMobile', 'UserController.getWxMobile')
  Route.post('uploadIdCard', async ({ request, params, response }) => {
    const profilePic = request.file('user_idcard', {
      types: ['image'],
      size: '2mb'
    })

    await profilePic.move(Helpers.tmpPath('uploads'), {
      name: 'custom-name.jpg',
      overwrite: true
    })

    if (!profilePic.moved()) {
      // return profilePic.error()
      response.json(profilePic.error())
    }
    response.json('File moved')
    // return 'File moved'
  })

  Route.resource('waste1stCategory', 'Waste1stCategoryController') //废品一级分类
  Route.get('waste1stCategory/:id/get2ndCategory', 'Waste1stCategoryController.get2ndCategory')
  Route.get('getThirdList', 'Waste1stCategoryController.getThirdList')
  Route.resource('waste2ndCategory', 'Waste2ndCategoryController') //废品二级分类
  Route.get('waste2ndCategory/:id/getWaste', 'Waste2ndCategoryController.getWaste')
  Route.resource('waste', 'WasteController') //废品
  Route.resource('wasteType', 'WasteTypeController') //废品类型
  Route.resource('attributeType', 'AttributeTypeController') //废品属性类型
  Route.resource('attribute', 'AttributeController') //废品属性
  Route.any('hook/pay', 'PayController.hookWechatPay') //微信支付回调

  Route.get('orderWorker', async ({ request, params, response }) => {
    let vo = await Order.query().with('worker').first()
    // console.log(vo)
    response.json(vo)
  })

  Route.resource('discovery', 'DiscoveryController') //帖子相关

  Route.get('indexPage', 'IndexController.index')
  Route.get('getPrice', 'IndexController.getPrice')
})
  .prefix('client/v1')
  .namespace('Client')

// 需要登录
Route.group(() => {
  //用户
  Route.resource('user', 'UserController') //用户
  Route.get('testSync', 'UserController.testSync')
  Route.post('createOrder', 'OrderController.clientPostOrder')
  Route.resource('userAddress', 'UserAddressController') //用户地址
  Route.get('orderDetail/:id', 'OrderController.showDetail') //获取订单详情
  Route.resource('order', 'OrderController') // 订单
  Route.post('order/:id/updateImage', 'OrderController.updateImage') //上传订单图片
  Route.post('order/:id/updateStatus', 'OrderController.updateStatus') //更新订单状态
  Route.post('order/userList', 'OrderController.userList') //获取某用户某状态订单集
  Route.post('order/getOrderList', 'OrderController.getOrderList') //获取订单列表
  Route.put('order/update/:id', 'OrderController.updateInfo')
  Route.resource('orderRating', 'OrderRatingController') //订单评价
  Route.resource('orderWaste', 'OrderWasteController') //订单废品关联表
  Route.resource('discoveryUpvote', 'DiscoveryUpvoteController') //点赞相关
  Route.resource('pay', 'PayController') //支付
  Route.get('user/:id/userAddress', 'UserController.userAddress') //地址列表
  Route.post('user/:id/searchUserAddress', 'UserController.searchUserAddress') //地址搜索
  Route.post('user/:id/setDefaultUserAddress', 'UserController.setDefaultUserAddress') //设置默认地址
  Route.post('order/complaint', 'OrderController.complaint') //订单投诉
  Route.get('getWorker', 'OrderRatingController.getWorker')
  Route.post('evaluate', 'UserController.postEvaluate')
  Route.post('user/phone', 'UserController.getPhone') // 获取手机
  Route.get('clientEstimate', 'ClientEstimateController.getEstimate') //客户估价
})
  .prefix('client/v1')
  .namespace('Client')
  .middleware('userAuth')
  
  Route.group(() => {
    // 认证相关路由
    Route.post('code', 'AuthController.getCode')
    Route.post('loginByCode', 'AuthController.checkPhone')
    Route.post('login', 'AuthController.login')
    Route.post('register', 'AuthController.register')
    Route.post('loginBypasswd', 'AuthController.loginBypasswd')
  }).prefix('api/auth')
  
// 文件上传接口
Route.group(() => {
  Route.post('file', 'FileController.file')
  Route.post('sms', 'SendSmsController.smsSend') //短信提醒
  Route.resource('address', 'AddressController') //地址
}).prefix('client/v1')


