/* utils/locales.js */
const zh_CN = {
  // layout
  home: '首页',
  about: '发现',
  user: '个人中心',
  // index/index
  selectTitle: '选择您需要回收的品类',
  recycleTitle: '回收服务流程',
  recycleStep1: '选择类型',
  recycleStep2: '提交回收',
  recycleStep3: '上门回收',
  recycleStep4: '发放权益',
  recycleTips: '在回收前，请先将回收的垃圾进行分类',
  FlowText1: '选择您要回收的垃圾类型。',
  FlowText2: '拍照并填写提交您的预约。',
  FlowText3: '上门回收人员会根据您的预约时间上门回收，并与您当场确认订单完成情况。',
  FlowText4: '根据上门回收人员与您确认的订单，获得相应的权益。',
  // public/public
  topTitle1: '全部',
  // topTitle2: '公益',
  topTitle3: '活动',
  topTitle4: '媒体报道',
  // oldGoods/oldGoods
  desc: '回收说明',
  oldGoods: {
    publicRecycling: ' ',
    recycling: '一键回收',
    select_1: '选择类型',
    select_2: '已选择类型',
  },

  // uploadImg/uploadImg
  uploadImg: {
    uploadtxt: '点击拍照或上传',
    select: '已选回收类型',
    minumun: '5公斤起收',
    rule: '起收规则',
    book: '立即预约',
    noImage: '还未上传回收物品照片-点击上传',
  },

  //order/index
  order: {
    addInfoTxt: '添加上门信息',
    addressBook: '地址簿',
    selectTime: '选择时间',
    selectPromoter: '选择促销员',
    selectTimeVisit: '选择上门时间',
    selectPromoterVisit: '选择门店促销员',
    remarks: '备注',
    remarksDesc: '在此输入备注内容',
    words: '字',
    submit: '提交预约',
  },

  // administration/administration
  administration: {
    inputTxt: '输入联系人/手机号查询',
    defaultAdd: '设置为默认上门地址',
    edit: '编辑',
    delete: '删除',
    addAddress: '新增地址',
  },

  // address/address
  addressPage: {
    addWechatTxt: '添加微信地址',
    name: '姓名',
    mr: '先生',
    ms: '女士',
    phone: '电话',
    ProvinceCityDistrict: '省市区',
    street: '街道',
    address: '地址（详细到门牌号）',
    save: '保存地址',
  },

  // successfulOrder/successfulOrder
  successfulOrderPage: {
    status_1: '预约',
    status_2: '等待上门',
    status_3: '完成',
    success: '预约成功',
    contact_1: '24小时内会有上门回收人员与您联系,',
    contact_2: '请确保预留的电话保持畅通',
    viewDetails: '查看预约详情',
    backHome: '返回首页',
    cancel: '取消预约',
  },

  // attributeSelect/attributeSelect  选择大家具属性页
  attributeSelectPage: {
    reminder: '提示：大件回收需回收人员上门拆卸，成本较高，需要您支付回收费用',
    specificPrice: '具体价格以实物为准',
    pay: '订单完成后，我们将向您支付家电回收的残值费用',
    appraisal: '估价',
  },

  // orderManage/orderManage 订单列表
  orderManagePage: {
    appointmentTime: '预约上门时间',
    spotRecycling: '定点回收',
    orderNo: '订单号',
    appraisal: '预估价格',
    toBeAssessed: '待估价',
    dealPrice: '成交价格',
    publicRecycling: ' ',
    toBePaid: '待支付',
    recyclerMan: '回收人',
  },

  // orderDetail/orderDetail
  orderDetail: {
    orderCancel: '订单已取消',
    orderCompleted: '订单已完成',
    orderInProgress: '订单进行中',
    orderBooked: '已预约-等待派单',
    cancelOrder: '取消订单',
    cancelReason: '取消原因',
    appointmentTime: '预约时间',
    complained: '已投诉',
    complaint: '投诉',
    fixedPointTime: '定时定点',
    recycler: '回收员',
    contact: '联系',
    evaluation: '评价',
    info: '预约信息',
    spotRecycling: '定点回收',
    pickUp: '收起',
    total: '总计数量/重量',
    totalExpenditure: '总计支出',
    totalRevenue: '总计收益',
    publicRecycling: ' ',
    upload: '上传照片',
    orderInfo: '订单信息',
    orderNo: '订单号',
    orderTime: '下单时间',
    cancelTime: '取消时间',
    completeTime: '完成时间',
    toBePaid: '订单待支付',
    dealPrice: '成交价格',
    pay: '支付',
    usedClothes: '旧衣旧书(单位/千克)',
    appliances: '大家电(单位/台)',
    waste: '生活废品(单位/千克)',
    furniture: '大家具(单位/件)',
  },

  // 旧衣旧书说明弹框
  usedClothesModal: {
    clothesAndBookTitle: '旧衣、旧书回收说明',
    clothesTitle: '旧衣说明',
    clothesContent_1: '1、 您下单后，我们将安排快递人员上门回收，快递工作人员与您联系确认具体回收时间',
    clothesContent_2: '2、 回收时间：9：00-18：00；',
    clothesContent_3: '3、 如果您对服务不满意，可以在“我的”个人中心“客服与投诉”予以投诉，您可以直接电话或留言投诉；',
    clothesContent_4: '4、 回收的物品我们将进行环保处理，避免二次污染和资源浪费；',
    clothesContent_5: '5、 我们提供专业化的废旧物品回收信息平台，您的支持是我们最大的动力；',
    booksTitle: '旧书说明',
    booksContent_1:
      '1、 不接收高中以下教材、教辅、考试考证类辅导书、单独磁带光碟；不接收报刊、杂志类等时效性刊物；不接收非法出版物、盗版、严重污染、破损、影响阅读的书籍；',
    booksContent_2: '2、 您下单后，我们将安排快递人员上门回收，快递工作人员与您联系确认具体回收时间；',
    booksContent_3: '3、 回收时间：9：00-18：00；',
    booksContent_4: '4、 如果您对服务不满意，可以在“我的”个人中心“客服与投诉”予以投诉，您可以直接电话或留言投诉；',
    booksContent_5: '5、 回收的物品我们将进行环保处理，避免二次污染和资源浪费；',
    booksContent_6: '6、 我们提供专业化的废旧物品回收信息平台，您的支持是我们最大的动力；',
    otherTitle: '其他',
    otherContent_1: '1、 您下单后，我们将安排回收人员上门回收，回收人员与您联系确认具体回收时间；',
    otherContent_2: '2、 回收时间：9：00-18：00；',
    otherContent_3: '3、 如果您对服务不满意，可以在“我的”个人中心“客服与投诉”予以投诉，您可以直接电话或留言投诉；',
    otherContent_4: '4、 回收的物品我们将进行环保处理，避免二次污染和资源浪费；',
    otherContent_5: '5、 我们提供专业化的废旧物品回收信息平台，您的支持是我们最大的动力；',
    ok: '我知道了',

    wasteTitle: '生活废品说明',
    appliancesTitle: '家电类回收说明',
    furnitureTitle: '家具类回收说明',
  },

  // user/user
  address: '地址管理',
  customer: '咨询客服',
  question: '常规问题解答',
  userAbout: '关于冬瓜回收',
  qrcode: '定点回收二维码',
  languageSetting: '语言设置',
  booked: '已预约',
  inProgress: '进行中',
  completed: '已完成',
  canceled: '已取消',
  chinese: '中文',
  english: '英文',

  // 时间选择器
  timeSelect: {
    chooseTime: '选择时间',
    cancel: '取消',
    submit: '确认',
  },

  // 定点回收二维码
  qrcodePage: {
    title: '定点回收个人二维码',
    desc: '说明：携带所要回收的物品，定时前往固定站点，向回收人员出示此码进行扫描，由回收人员新建并填写订单相关内容完成该笔订单。',
  },

  // 取消
  cancelPage: {
    cancelTitle: '取消',
    cancelReason: '选择或填写取消订单的原因',
    complaintTitle: '投诉',
    complaintReason: '选择或填写投诉原因',
    others: '其他原因',
    remark: '在此输入备注内容',
    cancelSubmit: '确认取消',
    complaintSubmit: '确认投诉',
  },

  // 评价页面
  reamrkPage: {
    recyclingPersonnel: '回收人员',
    dissatisfied: '不满意',
    general: '一般',
    satisfaction: '满意',
    otherComments: '其他评价',
    remark: '在此输入备注内容',
    submit: '确认评价',
    rated: '已评价',
  },
}

export default zh_CN
