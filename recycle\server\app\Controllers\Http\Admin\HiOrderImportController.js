'use strict'

const _ = require("lodash");
const moment = require("moment");
const { <PERSON><PERSON><PERSON><PERSON>, Worker, Hi<PERSON>rderMaintain, HiCOWorker, HiWorkerWalletLog, HiWorkerMaintain, ReqLog, HiPrice, HiWorker } = require('../../../Models')
const XLSX = require('xlsx'); // 使用 xlsx 库支持 .xls 和 .xlsx 格式
const ExcelJS = require('exceljs'); // 保留 ExcelJS 用于导出功能

const { ERR } = require("../../../../../constants");
const ExcelService = require('../../../Services/ExcelService');
const fs = require("fs-extra");
const Helpers = use("Helpers");
const Database = use('Database')

/**
 * 嗨回收订单导入控制器
 * 专门处理嗨回收平台的订单导入、导出和维护功能
 */
class HiOrderImportController {

    /**
     * 获取嗨回收订单列表
     */
    async index({ request, response }) {
        let {
            createdAt, workTime,
            current = 1, pageSize = 10, status = "完成", orderNo,
            waste_1st_type, userMobile, userName, workerName, from = "嗨回收",
            startDate, endDate,
            workStartDate, workEndDate, orderType, type, finishedAt,
            finishStartDate, finishEndDate, model, offTime,
            offStartDate, offEndDate, countName, countPhone,
            address, province, city
        } = request.all()

        let query = HiOrder.query().with('company')
        let totalQuery = HiOrder.query()

        // 默认查询嗨回收平台订单

        if (status) {
            query.where('status', status)
            totalQuery.where('status', status)
        }

        if (orderType) {
            query.where('orderType', orderType)
            totalQuery.where('orderType', orderType)
        }

        if (model) {
            query.where('model', model)
            totalQuery.where('model', model)
        }

        if (countName) {
            let worker = await Worker.query().where('workerName', countName).first()
            if (worker) {
                query.where('workerID', worker.id)
                totalQuery.where('workerID', worker.id)
            } else {
                query.where('countName', 'LIKE', `%${countName}%`)
                totalQuery.where('countName', 'LIKE', `%${countName}%`)
            }
        }

        if (countPhone) {
            query.where('countPhone', 'LIKE', `%${countPhone}%`)
            totalQuery.where('countPhone', 'LIKE', `%${countPhone}%`)
        }

        if (startDate && endDate) {
            query.where('createdAt', '>=', moment(startDate).toDate())
                .where('createdAt', '<=', moment(endDate).add(1, 'd').toDate())
            totalQuery.where('createdAt', '>=', moment(startDate).toDate())
                .where('createdAt', '<=', moment(endDate).add(1, 'd').toDate())
        }

        if (workStartDate && workEndDate) {
            query.where('workTime', '>=', moment(workStartDate).toDate())
                .where('workTime', '<=', moment(workEndDate).add(1, 'd').toDate())
            totalQuery.where('workTime', '>=', moment(workStartDate).toDate())
                .where('workTime', '<=', moment(workEndDate).add(1, 'd').toDate())
        }

        if (finishStartDate && finishEndDate) {
            query.where('finishedAt', '>=', moment(finishStartDate).toDate())
                .where('finishedAt', '<=', moment(finishEndDate).add(1, 'd').toDate())
            totalQuery.where('finishedAt', '>=', moment(finishStartDate).toDate())
                .where('finishedAt', '<=', moment(finishEndDate).add(1, 'd').toDate())
        }

        if (offStartDate && offEndDate) {
            query.where('offTime', '>=', moment(offStartDate).toDate())
                .where('offTime', '<=', moment(offEndDate).add(1, 'd').toDate())
            totalQuery.where('offTime', '>=', moment(offStartDate).toDate())
                .where('offTime', '<=', moment(offEndDate).add(1, 'd').toDate())
        }

        if (province) {
            query.whereRaw('province like ?', [`%${province}%`])
            totalQuery.whereRaw('province like ?', [`%${province}%`])
        }

        if (city) {
            query.whereRaw('city like ?', [`%${city}%`])
            totalQuery.whereRaw('city like ?', [`%${city}%`])
        }

        if (workerName) {
            query.where('wokerName', 'LIKE', `%${workerName}%`)
            totalQuery.where('wokerName', 'LIKE', `%${workerName}%`)
        }

        if (orderNo) {
            query.whereRaw('orderNo like ?', [`%${orderNo}%`])
            totalQuery.whereRaw('orderNo like ?', [`%${orderNo}%`])
        }

        if (type) {
            query.whereRaw('type like ?', [`%${type}%`])
            totalQuery.whereRaw('type like ?', [`%${type}%`])
        }

        if (waste_1st_type) {
            query.where('waste_1st_ID', waste_1st_type)
            totalQuery.where('waste_1st_ID', waste_1st_type)
        }

        if (userMobile) {
            query.whereRaw('userMobile like ?', [`%${userMobile}%`])
            totalQuery.whereRaw('userMobile like ?', [`%${userMobile}%`])
        }

        if (userName) {
            query.whereRaw('userName like ?', [`%${userName}%`])
            totalQuery.whereRaw('userName like ?', [`%${userName}%`])
        }

        if (address) {
            query.whereRaw('address like ?', [`%${address}%`])
            totalQuery.whereRaw('address like ?', [`%${address}%`])
        }

        // 排序处理
        this.applySorting(query, { createdAt, workTime, finishedAt, offTime, address })

        let vo = await query.with('worker').paginate(current, pageSize)
        let totalInfoFee = await totalQuery.sum('infoFee as totalFee')

        response.json({
            ...vo.toJSON(),
            totalInfoFee: totalInfoFee[0].totalFee || 0
        })
    }

    /**
     * 应用排序
     */
    applySorting(query, { createdAt, workTime, finishedAt, offTime, address }) {
        const sortFields = { createdAt, workTime, finishedAt, offTime, address }

        Object.keys(sortFields).forEach(field => {
            if (sortFields[field] === 'descend') {
                query.orderBy(field, 'desc')
            } else if (sortFields[field] === 'ascend') {
                query.orderBy(field, 'asc')
            }
        })
    }

    /**
     * 获取单个嗨回收订单详情
     */
    async show({ request, params, response }) {
        let vo = await HiOrder.query()
            .where('id', params.id)
            .first()

        if (!vo) {
            throw ERR.RESTFUL_GET_ID
        }

        response.json(vo)
    }

    /**
     * 更新嗨回收订单
     */
    async update({ request, params, response }) {
        let vo = await HiOrder.find(params.id)
        if (!vo) {
            throw ERR.RESTFUL_UPDATE_ID
        }

        _.assign(vo, request.all())
        await vo.save()
        response.json(vo)
    }

    /**
     * 删除嗨回收订单
     */
    // async destroy({ request, params, response }) {
    //     let vo = await HiOrder.find(params.id)
    //     if (!vo) {
    //         throw ERR.RESTFUL_DELETE_ID
    //     }

    //     // 如果有师傅关联，需要处理钱包扣款
    //     if (vo.countPhone) {
    //         let worker = await Worker.query().where('mobile', vo.countPhone).first()
    //         if (worker) {
    //             const deductionAmount = vo.infoFee ? parseInt(vo.infoFee) * parseInt(1) : 0;
    //             worker.wallet += deductionAmount
    //             await worker.save()
    //         }
    //     }

    //     await vo.delete()
    //     response.json(vo)
    // }


    /**
     * 解析Excel文件 - 支持 .xls 和 .xlsx 格式
     * @param {string} filePath 文件路径
     * @param {string} fileName 文件名
     * @returns {Array} 解析后的JSON数据
     */
    async parseExcelFile(filePath, fileName) {
        const fileExtension = fileName.toLowerCase().split('.').pop();

        console.log(`开始解析Excel文件: ${fileName}, 格式: ${fileExtension}`);

        if (fileExtension === 'xls') {
            // 使用 XLSX 库处理 .xls 格式文件
            return this.parseXlsFile(filePath);
        } else if (fileExtension === 'xlsx') {
            // 使用 ExcelJS 处理 .xlsx 格式文件 (保持原有逻辑)
            return this.parseXlsxFile(filePath);
        } else {
            throw new Error(`不支持的文件格式: ${fileExtension}`);
        }
    }

    /**
     * 使用 XLSX 库解析 .xls 文件
     * @param {string} filePath 文件路径
     * @returns {Array} 解析后的JSON数据
     */
    parseXlsFile(filePath) {
        try {
            // 读取工作簿
            const workbook = XLSX.readFile(filePath);

            // 获取第一个工作表名称
            const sheetName = workbook.SheetNames[0];
            if (!sheetName) {
                throw new Error('Excel文件中没有找到工作表');
            }

            // 获取工作表
            const worksheet = workbook.Sheets[sheetName];
            if (!worksheet) {
                throw new Error('无法读取Excel文件的第一个工作表');
            }

            // 将工作表转换为JSON
            const rawData = XLSX.utils.sheet_to_json(worksheet, {
                header: 1, // 使用数组格式，第一行作为标题
                defval: '' // 空单元格的默认值
            });

            if (rawData.length <= 1) {
                throw new Error('Excel文件中没有数据行（除了标题行）');
            }

            // 获取标题行
            const headers = rawData[0];

            // 验证必需的列是否存在
            const requiredColumns = ['工单编号', '师傅姓名'];
            const missingColumns = requiredColumns.filter(col => !headers.includes(col));
            if (missingColumns.length > 0) {
                throw new Error(`Excel文件缺少必需的列: ${missingColumns.join(', ')}`);
            }

            // 将数据转换为对象数组
            const jsonData = [];
            for (let i = 1; i < rawData.length; i++) {
                const row = rawData[i];
                const rowData = {};
                let hasData = false;

                // 将每一行数据转换为对象
                headers.forEach((header, index) => {
                    if (header && row[index] !== undefined && row[index] !== null && row[index] !== '') {
                        rowData[header] = row[index];
                        hasData = true;
                    }
                });

                // 只添加有数据的行
                if (hasData) {
                    jsonData.push(rowData);
                }
            }

            console.log(`成功解析 .xls 文件，共 ${jsonData.length} 条有效数据`);
            return jsonData;

        } catch (error) {
            console.error('解析 .xls 文件失败:', error);
            throw new Error(`解析 .xls 文件失败: ${error.message}`);
        }
    }

    /**
     * 使用 ExcelJS 解析 .xlsx 文件
     * @param {string} filePath 文件路径
     * @returns {Array} 解析后的JSON数据
     */
    async parseXlsxFile(filePath) {
        try {
            // 使用 ExcelJS 解析 .xlsx 文件
            const workbook = new ExcelJS.Workbook();
            await workbook.xlsx.readFile(filePath);

            // 检查工作簿是否有工作表
            if (!workbook.worksheets || workbook.worksheets.length === 0) {
                throw new Error('Excel文件中没有找到工作表');
            }

            const worksheet = workbook.worksheets[0];

            // 检查工作表是否有效
            if (!worksheet) {
                throw new Error('无法读取Excel文件的第一个工作表');
            }

            // 检查工作表是否有数据行
            if (worksheet.rowCount <= 1) {
                throw new Error('Excel文件中没有数据行（除了标题行）');
            }

            // 获取标题行并验证必需的列
            const headerRow = worksheet.getRow(1);
            const headers = [];
            headerRow.eachCell((cell, colNumber) => {
                headers[colNumber] = cell.value;
            });

            // 验证必需的列是否存在
            const requiredColumns = ['工单编号', '师傅姓名'];
            const missingColumns = requiredColumns.filter(col => !headers.includes(col));
            if (missingColumns.length > 0) {
                throw new Error(`Excel文件缺少必需的列: ${missingColumns.join(', ')}`);
            }
            // 将工作表数据转换为 JSON
            const jsonData = [];
            worksheet.eachRow((row, rowNumber) => {
                if (rowNumber > 1) {
                    const rowData = {};
                    let hasData = false;
                    row.eachCell((cell, colNumber) => {
                        const headerValue = worksheet.getCell(1, colNumber).value;
                        if (headerValue && cell.value !== null && cell.value !== undefined) {
                            rowData[headerValue] = cell.value;
                            hasData = true;
                        }
                    });
                    // 只添加有数据的行
                    if (hasData) {
                        jsonData.push(rowData);
                    }
                }
            });
            console.log(`成功解析 .xlsx 文件，共 ${jsonData.length} 条有效数据`);
            return jsonData;

        } catch (error) {
            console.error('解析 .xlsx 文件失败:', error);
            throw new Error(`解析 .xlsx 文件失败: ${error.message}`);
        }
    }

    /**
     * 解析嗨回收订单行数据
     */
    parseHiOrderRow(row) {
        // 这里需要根据嗨回收的Excel格式来解析
        // 假设的字段映射，需要根据实际Excel格式调整
        const data = {
            orderNo: this.getCellValue(row, 1), // 订单号
            userName: this.getCellValue(row, 2), // 客户姓名
            userMobile: this.getCellValue(row, 3), // 客户手机
            address: this.getCellValue(row, 4), // 地址
            type: this.getCellValue(row, 5), // 废品类型
            infoFee: this.getCellValue(row, 6), // 信息费
            wokerName: this.getCellValue(row, 7), // 师傅姓名
            workerPhone: this.getCellValue(row, 8), // 师傅电话
            from: '嗨回收', // 来源
            status: '完成', // 默认状态
            companyID: 47, // 默认公司ID，需要根据实际情况调整
            createdAt: new Date(),
            updatedAt: new Date()
        };

        // 验证必填字段
        if (!data.orderNo || !data.userName || !data.userMobile) {
            throw new Error('订单号、客户姓名、客户手机为必填字段');
        }

        return data;
    }

    /**
     * 获取单元格值
     */
    getCellValue(row, columnIndex) {
        const cell = row.getCell(columnIndex);
        return cell ? cell.value : '';
    }

    /**
     * 获取嗨回收待维护订单列表
     */
    async hiOrderMaintainList({ request, response }) {
        let { status = "待维护", orderNo, current = 1, pageSize = 10, remark } = request.all()
        let vo = HiOrderMaintain.query().where('status', status)

        if (orderNo) {
            vo.where('orderNo', 'LIKE', `%${orderNo}%`)
        }
        if (remark) {
            vo.where('remark', 'LIKE', `%${remark}%`)
        }

        vo = await vo.paginate(current, pageSize)
        response.json(vo)
    }

    /**
     * 更新嗨回收订单维护状态
     */
    async hiOrderMaintainUpdate({ request, params, response }) {
        let vo = await HiOrderMaintain.find(params.id)
        if (!vo) {
            throw ERR.RESTFUL_UPDATE_ID
        }

        _.assign(vo, request.all())
        await vo.save()
        response.json(vo)
    }

    /**
     * 导出嗨回收订单数据
     */
    async exportXLS({ request, response }) {
        let {
            createdAt, workTime, status = "完成", orderNo,
            waste_1st_type, userMobile, userName, workerName, from,
            startDate, endDate, workStartDate, workEndDate,
            orderType, type, countName, countPhone,
            finishStartDate, finishEndDate,
            offStartDate, offEndDate, lowPrice, hightPrice,
            address, price, city, source, province
        } = request.all()

        // 创建Excel工作簿和工作表
        let workbook = new ExcelJS.Workbook()
        let worksheet = workbook.addWorksheet('嗨回收订单数据')

        // 设置列头和列宽
        let font = { name: 'Times New Roman', size: 12 }
        worksheet.columns = [
            { header: '订单号', key: 'orderNo', width: 18, style: { font } },
            { header: '结算人', key: 'countName', width: 15, style: { font } },
            { header: '四级分类', key: 'type', width: 15, style: { font } },
            { header: '来源', key: 'from', width: 12, style: { font } },
            { header: '客户姓名', key: 'userName', width: 15, style: { font } },
            { header: '联系电话', key: 'userMobile', width: 15, style: { font } },
            { header: '信息费', key: 'infoFee', width: 12, style: { font } },
            { header: '地址', key: 'address', width: 35, style: { font } },
            { header: '省', key: 'province', width: 15, style: { font } },
            { header: '市', key: 'city', width: 15, style: { font } },
            { header: '区', key: 'town', width: 15, style: { font } },
            { header: '县', key: 'county', width: 15, style: { font } },
            { header: '预约时间', key: 'workTime', width: 20, style: { font } },
            { header: '回收人员', key: 'wokerName', width: 15, style: { font } },
            { header: '工作电话', key: 'workerPhone', width: 15, style: { font } },
            { header: '完成时间', key: 'finishedAt', width: 20, style: { font } },
            { header: '揽收时间', key: 'offTime', width: 20, style: { font } },
            { header: '下单时间', key: 'createdAt', width: 20, style: { font } },
            { header: '同步类型', key: 'model', width: 20, style: { font } },
            { header: '备注', key: 'remark', width: 25, style: { font } },
        ]

        try {
            // 构建查询条件
            const query = this.buildExportQuery({
                orderType, type, status, startDate, endDate, workStartDate, workEndDate,
                province, lowPrice, hightPrice, orderNo, from, waste_1st_type,
                userMobile, userName, address, price, city, source, countPhone,
                offStartDate, offEndDate, finishStartDate, finishEndDate, workerName, countName
            });

            const countResult = await query.count('* as total');
            const totalCount = countResult[0].total;

            console.log(`开始导出嗨回收订单，共${totalCount}条记录`);

            if (totalCount === 0) {
                const fileName = `嗨回收订单数据${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`;
                await workbook.xlsx.writeFile(`./${fileName}`);
                return response.attachment(`./${fileName}`);
            }

            // 分批查询数据
            const batchSize = 2000;
            const totalBatches = Math.ceil(totalCount / batchSize);
            let rowCount = 1;

            for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                const batchQuery = this.buildExportQuery({
                    orderType, type, status, startDate, endDate, workStartDate, workEndDate,
                    province, lowPrice, hightPrice, orderNo, from, waste_1st_type,
                    userMobile, userName, address, price, city, source, countPhone,
                    offStartDate, offEndDate, finishStartDate, finishEndDate, workerName, countName
                });

                const batchData = await batchQuery
                    .with('worker')
                    .offset(batchIndex * batchSize)
                    .limit(batchSize)
                    .fetch();

                // 添加数据到Excel
                for (const item of batchData.rows) {
                    worksheet.addRow({
                        orderNo: item.orderNo,
                        from: item.from,
                        userName: item.userName,
                        countName: item.worker && item.worker.workerName || item.wokerName || '',
                        type: item.type,
                        userMobile: item.userMobile,
                        infoFee: item.infoFee ? parseInt(item.infoFee) : 0,
                        address: `${item.address}`,
                        workTime: item.workTime ? moment(item.workTime).format('YYYY-MM-DD HH:mm') : '',
                        workerPhone: item.workerPhone || '',
                        wokerName: item.wokerName || '',
                        province: item.province || '',
                        city: item.city || '',
                        town: item.town || '',
                        county: item.county || '',
                        finishedAt: item.finishedAt ? moment(item.finishedAt).format('YYYY-MM-DD HH:mm') : '',
                        offTime: item.offTime ? moment(item.offTime).format('YYYY-MM-DD HH:mm') : '',
                        createdAt: item.createdAt ? moment(item.createdAt).format('YYYY-MM-DD HH:mm') : '',
                        model: item.model || '',
                        remark: item.remark || '',
                    });
                    rowCount++;
                }

                console.log(`已处理${rowCount - 1}/${totalCount}条记录，完成度: ${Math.round(((rowCount - 1) / totalCount) * 100)}%`);
            }

            const fileName = `嗨回收订单数据${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`;
            await workbook.xlsx.writeFile(`./${fileName}`);
            console.log(`导出完成，文件名: ${fileName}`);

            return response.attachment(`./${fileName}`);
        } catch (error) {
            console.error('导出过程中发生错误:', error);
            return response.status(500).json({
                code: 500,
                message: '导出失败',
                error: error.message
            });
        }
    }

    /**
     * 构建导出查询条件
     */
    buildExportQuery(params) {
        const {
            orderType, type, status, startDate, endDate, workStartDate, workEndDate,
            province, lowPrice, hightPrice, orderNo, from, waste_1st_type,
            userMobile, userName, address, price, city, source, countPhone,
            offStartDate, offEndDate, finishStartDate, finishEndDate, workerName, countName
        } = params;

        let query = HiOrder.query();

        // 默认查询嗨回收订单

        if (status) query.where('status', status);
        if (orderType) query.where('orderType', orderType);
        if (type) query.whereRaw('type like ?', [`%${type}%`]);
        if (orderNo) query.whereRaw('orderNo like ?', [`%${orderNo}%`]);
        if (userMobile) query.whereRaw('userMobile like ?', [`%${userMobile}%`]);
        if (userName) query.whereRaw('userName like ?', [`%${userName}%`]);
        if (address) query.whereRaw('address like ?', [`%${address}%`]);
        if (province) query.whereRaw('province like ?', [`%${province}%`]);
        if (city) query.whereRaw('city like ?', [`%${city}%`]);
        if (workerName) query.where('wokerName', 'LIKE', `%${workerName}%`);
        if (countPhone) query.where('countPhone', 'LIKE', `%${countPhone}%`);
        if (waste_1st_type) query.where('waste_1st_ID', waste_1st_type);

        // 日期范围查询
        if (startDate && endDate) {
            query.where('createdAt', '>=', moment(startDate).toDate())
                .where('createdAt', '<=', moment(endDate).add(1, 'd').toDate());
        }
        if (workStartDate && workEndDate) {
            query.where('workTime', '>=', moment(workStartDate).toDate())
                .where('workTime', '<=', moment(workEndDate).add(1, 'd').toDate());
        }
        if (finishStartDate && finishEndDate) {
            query.where('finishedAt', '>=', moment(finishStartDate).toDate())
                .where('finishedAt', '<=', moment(finishEndDate).add(1, 'd').toDate());
        }
        if (offStartDate && offEndDate) {
            query.where('offTime', '>=', moment(offStartDate).toDate())
                .where('offTime', '<=', moment(offEndDate).add(1, 'd').toDate());
        }

        return query;
    }

    /**
     * 导出嗨回收待维护订单
     */
    async exportHiOrderMaintain({ request, response }) {
        try {
            let { status = "待维护", orderNo, remark } = request.all()
            let query = HiOrderMaintain.query().where('status', status)

            if (orderNo) {
                query.where('orderNo', 'LIKE', `%${orderNo}%`)
            }
            if (remark) {
                query.where('remark', 'LIKE', `%${remark}%`)
            }

            let vo = await query.fetch()
            let data = []

            vo.rows.forEach((value, index) => {
                let arrInner = []
                arrInner.push(value.id)
                arrInner.push(value.orderNo)
                arrInner.push(value.status)
                arrInner.push(value.remark || '')
                arrInner.push(moment(value.createdAt).format('YYYY-MM-DD HH:mm:ss'))
                arrInner.push(moment(value.updatedAt).format('YYYY-MM-DD HH:mm:ss'))
                data.push(arrInner)
            })

            let theTitle = [
                'ID',
                '订单号',
                '状态',
                '备注',
                '创建时间',
                '更新时间'
            ]

            let theWidth = [
                { wch: 8 },
                { wch: 20 },
                { wch: 10 },
                { wch: 30 },
                { wch: 22 },
                { wch: 22 }
            ]

            let fileTitle = '嗨回收待维护订单-' + `${moment().format('YYYY-MM-DD-HH-mm')}`
            await ExcelService.generateExcel(fileTitle, theTitle, data, response, { '!cols': theWidth })
        } catch (error) {
            console.error('导出过程中发生错误:', error)
            return response.status(500).json({
                code: 500,
                message: '导出失败',
                error: error.message
            })
        }
    }

    async exportXLS({ request, response }) {
        let { status = "待维护", orderNo, remark } = request.all()
        let query = HiOrderMaintain.query().where('status', status)
        let vo = await query.fetch()
        let data = []
        vo.rows.forEach((value, index) => {
            let arrInner = []
            arrInner.push(value.id)
            arrInner.push(value.orderNo)
            arrInner.push(value.status)
            arrInner.push(moment(value.createdAt).format('YYYY-MM-DD HH:mm:ss'))
            arrInner.push(moment(value.updatedAt).format('YYYY-MM-DD HH:mm:ss'))
            data.push(arrInner)
        })
        let theTitle = [
            'ID',
            '订单号',
            '状态',
            '创建时间',
            '更新时间'
        ]
        let theWidth = [
            { wch: 8 },
            { wch: 20 },
            { wch: 10 },
            { wch: 22 },
            { wch: 22 }
        ]
        let fileTitle = '嗨回收待维护订单-' + `${moment().format('YYYY-MM-DD-HH-mm')}`
        await ExcelService.generateExcel(fileTitle, theTitle, data, response, { '!cols': theWidth })
        return response.attachment(`./${fileTitle}`);

    }



    /**
   * 导入嗨回收订单数据
   */
    async uploadHiOrder({ request, response }) {
        let file = request.file("file");
        if (!file) {
            throw { error: 12001, message: "文件不存在" };
        }

        let { subtype, clientName } = file;

        // 验证文件类型 - 允许上传 .xlsx 和 .xls 格式
        const allowedTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
            'application/vnd.ms-excel', // .xls
            'application/x-excel', // .xls 的另一种 MIME 类型
            'application/x-msexcel', // .xls 的另一种 MIME 类型
            'application/excel', // 另一种可能的类型
            'application/x-ms-excel', // 另一种可能的类型
            'application/msexcel', // 另一种可能的类型
            'application/x-excel', // Excel的其他MIME类型
            'application/x-msexcel',
            'application/spreadsheet' // 电子表格通用类型
        ];

        // 添加调试日志，记录实际文件类型
        console.log('上传文件信息:', {
            fileName: clientName,
            fileType: file.type,
            subtype: subtype,
            fileSize: file.size
        });

        // 上传文件到本地
        let tmpName = (moment().format("x") + "." + clientName).toLowerCase();
        fs.ensureDirSync(Helpers.tmpPath());
        await file.move(Helpers.tmpPath(), { name: tmpName });
        if (!file.moved()) {
            throw { error: 12002, message: "文件上传失败" };
        }
        try {
            // 解析excel - 支持 .xls 和 .xlsx 格式
            const filePath = Helpers.tmpPath(tmpName);
            const jsonData = await this.parseExcelFile(filePath, clientName);

            // 验证是否有有效数据
            if (jsonData.length === 0) {
                throw new Error('Excel文件中没有有效的数据行');
            }

            // 批量处理数据，每1000条为一批
            const batchSize = 1000;
            const totalBatches = Math.ceil(jsonData.length / batchSize);
            let successCount = 0;
            let errorCount = 0;
            let noWorkerCount = 0;
            console.log(`开始导入数据，共${jsonData.length}条，分${totalBatches}批处理`);
            for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                const startIndex = batchIndex * batchSize;
                const endIndex = Math.min(startIndex + batchSize, jsonData.length);
                const currentBatch = jsonData.slice(startIndex, endIndex);
                console.log(`处理第${batchIndex + 1}/${totalBatches}批，${startIndex + 1}-${endIndex}条`);
                // 使用事务处理一批数据
                await Database.transaction(async (trx) => {
                    // 收集需要批量创建的数据
                    let orderBatch = [];
                    let workerMaintainBatch = [];
                    let hiOrderMaintainBatch = [];
                    let walletLogBatch = [];
                    let reqLogBatch = [];

                    // 预处理一批数据
                    for (const item of currentBatch) {
                        try {
                            // 验证必填字段
                            if (!item.工单编号) {
                                console.warn('跳过没有工单编号的行:', item);
                                errorCount++;
                                continue;
                            }

                            // 订单表里查找是否已创建
                            const order = await HiOrder.findBy({ orderNo: item.工单编号 });
                            if (order) {
                                // 订单已存在，跳过
                                console.log(`订单 ${item.工单编号} 已存在，跳过`);
                                continue;
                            }
                            // 查找师傅
                            let worker = await Worker.findBy({ workerName: item.师傅姓名, forbidden: 1, isUse: "同意", isHi: 1 });
                            if (!worker) {
                                // co师傅表里查找
                                let coworker = await HiCOWorker.findBy({ workerName: item.师傅姓名 });
                                if (!coworker) {
                                    // 记录未找到师傅的情况
                                    noWorkerCount++;
                                    workerMaintainBatch.push({
                                        orderNo: item.工单编号,
                                        name: item.师傅姓名,
                                        status: '待维护'
                                    });
                                    hiOrderMaintainBatch.push({
                                        orderNo: item.工单编号,
                                        status: '待维护',
                                        remark: `未找到该师傅:${item.师傅姓名}`
                                    });
                                    reqLogBatch.push({
                                        req: `未找到该师傅:${item.师傅姓名}`,
                                        source: '嗨回收导入'
                                    });

                                    // 继续处理下一条
                                    continue;
                                } else {
                                    worker = await Worker.findBy({ id: coworker.managerID });
                                }
                            }
                            // 获取价格
                            // 来源-品类-地址
                            //4组来源（+活动）-10组分类（品类+类型）-江浙皖、沪（区）
                            let getPrice;
                            // console.log(item.旧机品类, item.旧机类型, item.省, item.市, item.区, item.工单来源, ">>>>>>>>>>>>>>>>>>>>>");
                            if ((item.工单来源).includes('京东SAAS')) {
                                if (item.活动 === '估价') {
                                    if (item.旧机品类 === '空调') {
                                        getPrice = await HiPrice.findBy({ type: item.旧机类型, source: "京东SAAS估价", province: item.省 });
                                    } else if (item.旧机类型 === '燃气热水器') {
                                        getPrice = await HiPrice.findBy({ type: "燃气热水器", source: "京东SAAS估价", province: item.省 });
                                    } else {
                                        getPrice = await HiPrice.findBy({ type: item.旧机品类, source: "京东SAAS估价", province: item.省 });
                                    }
                                } else {
                                    if (item.旧机品类 === '空调') {
                                        getPrice = await HiPrice.findBy({ type: item.旧机类型, source: "京东SAAS一口价", province: item.省, city: item.市 });
                                    } else if (item.旧机类型 === '燃气热水器') {
                                        getPrice = await HiPrice.findBy({ type: "燃气热水器", source: "京东SAAS一口价", province: item.省, city: item.市 });
                                    } else if (item.旧机品类 === '电视') {
                                        getPrice = await HiPrice.findBy({ type: item.旧机类型, source: "京东SAAS一口价", province: item.省, city: item.市 });
                                    } else if (item.旧机品类 === '洗衣机') {
                                        getPrice = await HiPrice.findBy({ type: item.旧机类型, source: "京东SAAS一口价", province: item.省, city: item.市 });
                                    } else {
                                        getPrice = await HiPrice.findBy({ type: item.旧机品类, source: "京东SAAS一口价", province: item.省, city: item.市 });
                                    }
                                }
                            } else if ((item.工单来源).includes('拼多多') || (item.工单来源).includes('抖音')) {
                                if (item.旧机品类 == '空调') {
                                    if (item.省 === '上海市') {
                                        getPrice = await HiPrice.findBy({ type: item.旧机类型, source: "拼多多抖音", province: item.省, area: item.区 });
                                    } else {
                                        getPrice = await HiPrice.findBy({ type: item.旧机类型, source: "拼多多抖音", province: item.省 });
                                    }
                                } else if (item.旧机类型 == '燃气热水器') {
                                    if (item.省 === '上海市') {
                                        getPrice = await HiPrice.findBy({ type: '燃气热水器', source: "拼多多抖音", province: item.省, area: item.区 });
                                    } else {
                                        getPrice = await HiPrice.findBy({ type: '燃气热水器', source: "拼多多抖音", province: item.省 });
                                    }
                                } else {
                                    if (item.省 === '上海市') {
                                        getPrice = await HiPrice.findBy({ type: item.旧机品类, source: "拼多多抖音", province: item.省, area: item.区 });
                                    } else {
                                        getPrice = await HiPrice.findBy({ type: item.旧机品类, source: "拼多多抖音", province: item.省 });
                                    }
                                }
                            } else if ((item.工单来源).includes('小米')) {
                                if (item.旧机品类 == '空调' || item.旧机品类 == '热水器' ||  item.旧机品类 == '洗衣机') {
                                    getPrice = await HiPrice.query().where('source', '小米送拆装一体').where('type', 'like', `%${item.旧机类型}%`).where('province', item.省).first();
                                } else {
                                    getPrice = await HiPrice.query().where('source', '小米送拆装一体').where('type', 'like', `%${item.旧机品类}%`).where('province', item.省).first();
                                }
                            } else {
                                if (item.旧机品类 == '空调') {
                                    if (item.省 === '上海市') {
                                        getPrice = await HiPrice.findBy({ type: item.旧机类型, source: "其他", province: item.省, area: item.区 });
                                    } else {
                                        getPrice = await HiPrice.findBy({ type: item.旧机类型, source: "其他", province: item.省 });
                                    }
                                } else if (item.旧机品类 == '燃气热水器') {
                                    if (item.省 === '上海市') {
                                        getPrice = await HiPrice.findBy({ type: '燃气热水器', source: "其他", province: item.省, area: item.区 });
                                    } else {
                                        getPrice = await HiPrice.findBy({ type: '燃气热水器', source: "其他", province: item.省 });
                                    }
                                } else {
                                    if (item.省 === '上海市') {
                                        getPrice = await HiPrice.findBy({ type: item.旧机品类, source: "其他", province: item.省, area: item.区 });
                                    } else {
                                        getPrice = await HiPrice.findBy({ type: item.旧机品类, source: "其他", province: item.省 });
                                    }
                                }
                            }
                            const deductionAmount = getPrice ? parseInt(getPrice.toJSON().price) : "none";
                            if (deductionAmount === "none") {
                                reqLogBatch.push({
                                    req: `未找到该订单价格:${item.工单编号}`,
                                    source: '嗨回收导入'
                                });
                                hiOrderMaintainBatch.push({
                                    orderNo: item.工单编号,
                                    status: '待维护',
                                    remark: `未找到该订单价格:${item.工单编号}`
                                });
                                continue;
                            }
                            let type = item.旧机品类 + item.旧机类型
                            // 准备订单数据
                            const orderData = {
                                from: item.工单来源,
                                infoFee: deductionAmount !== "none" ? deductionAmount : 0,
                                orderNo: item.工单编号,
                                userName: item.客户姓名,
                                userMobile: item.客户手机,
                                province: item.省,
                                city: item.市,
                                town: item.区,
                                county: item['街道/乡镇'],
                                address: item.客户地址,
                                type: type,
                                remark: item.工单备注,
                                brand: item.工单品牌,
                                wokerName: item.师傅姓名,
                                workerPhone: 11111111111,
                                model: item.回收方式,
                                keywords: item.工单来源 + item.客户地址 + item.客户手机 + item.客户姓名 + item.工单编号 + item.旧机品类 + item.旧机类型 + item.回收方式,
                                price: item.实际回收价格,
                                SKU: item.旧机规格,
                                companyID: 36,
                                status: worker ? "完成" : "无师傅",
                                workerID: worker ? worker.id : null,
                                countName: worker ? worker.workerName : item.师傅姓名,
                                countPhone: worker ? worker.mobile : 11111111111,
                                takeTime: item.接单时间 ? moment(item.接单时间).subtract(8, 'hours').format('YYYY-MM-DD HH:mm') : null,
                                offTime: item.派单时间 ? moment(item.派单时间).subtract(8, 'hours').format('YYYY-MM-DD HH:mm') : null,
                                workTime: item.预约时间 ? moment(item.预约时间).subtract(8, 'hours').format('YYYY-MM-DD HH:mm') : null,
                                finishedAt: item.完工时间 ? moment(item.完工时间).subtract(8, 'hours').format('YYYY-MM-DD HH:mm') : null,
                                // createdAt: item.下单时间 ? moment(item.下单时间).subtract(8, 'hours').format('YYYY-MM-DD HH:mm') : null
                            };
                            orderBatch.push(orderData);
                            // 如果有师傅，准备扣款记录
                            if (worker && deductionAmount !== "none") {
                                // console.log(deductionAmount);
                                // 更新师傅钱包余额
                                await Worker.query()
                                    .where({ id: worker.id })
                                    .transacting(trx)
                                    .update({ wallet: Database.raw(`wallet - ${deductionAmount}`) });

                                // 准备钱包日志数据
                                walletLogBatch.push({
                                    orderNo: item.工单编号, // 使用订单号关联，后续更新为订单ID
                                    workerID: worker.id,
                                    money: -(deductionAmount),
                                    remark: `${item.师傅姓名}嗨回收订单完成扣款`
                                });
                            }
                            successCount++;
                        } catch (error) {
                            console.error(`处理订单 ${item.工单编号} 时出错:`, error);
                            errorCount++;
                            // 继续处理下一条
                        }
                    }

                    // 批量创建订单
                    if (orderBatch.length > 0) {
                        const createdOrders = await HiOrder.createMany(orderBatch, trx);
                        console.log(`批量创建了 ${createdOrders.length} 条订单`);

                        // 更新钱包日志中的订单ID
                        if (walletLogBatch.length > 0) {
                            // 获取刚创建的订单ID和订单号的映射
                            const orderMap = {};
                            for (const order of createdOrders) {
                                orderMap[order.orderNo] = order.id;
                            }

                            // 更新钱包日志中的订单ID
                            for (const log of walletLogBatch) {
                                log.orderID = orderMap[log.orderNo];
                                delete log.orderNo; // 删除临时字段
                            }

                            // 批量创建钱包日志
                            await HiWorkerWalletLog.createMany(walletLogBatch, trx);
                        }
                    }

                    // 批量创建师傅维护记录
                    if (workerMaintainBatch.length > 0) {
                        // 对workerMaintainBatch按phone字段去重
                        const uniqueWorkerMaintain = workerMaintainBatch.filter((item, index, self) =>
                            index === self.findIndex((t) => t.phone === item.phone)
                        );
                        // 更新workerMaintainBatch为去重后的数组
                        workerMaintainBatch = uniqueWorkerMaintain;
                        await HiWorkerMaintain.createMany(workerMaintainBatch, trx);
                    }
                    // 批量创建请求日志
                    if (reqLogBatch.length > 0) {
                        await ReqLog.createMany(reqLogBatch, trx);
                    }
                    if (hiOrderMaintainBatch.length > 0) {
                        await HiOrderMaintain.createMany(hiOrderMaintainBatch, trx);
                    }
                });

                // 每批处理完成后输出进度
                console.log(`第${batchIndex + 1}批处理完成，进度: ${Math.round((endIndex / jsonData.length) * 100)}%`);
            }

            // 导入完成后输出统计信息
            console.log(`导入完成，总数据: ${jsonData.length}, 成功: ${successCount}, 失败: ${errorCount}, 无师傅: ${noWorkerCount}`);

            response.json({
                code: 200,
                msg: "导入成功",
                data: {
                    total: jsonData.length,
                    success: successCount,
                    error: errorCount,
                    noWorker: noWorkerCount
                }
            });
        } catch (e) {
            console.error('导入过程中发生错误:', e);

            // 清理临时文件
            try {
                await fs.remove(Helpers.tmpPath(tmpName));
            } catch (cleanupError) {
                console.error('清理临时文件失败:', cleanupError);
            }

            // 返回更友好的错误信息
            const errorMessage = e.message || e.toString();
            return response.status(500).json({
                code: 500,
                message: '导入失败',
                error: errorMessage,
                details: e
            });
        } finally {
            // 确保清理临时文件
            try {
                await fs.remove(Helpers.tmpPath(tmpName));
            } catch (cleanupError) {
                console.error('最终清理临时文件失败:', cleanupError);
            }
        }
    }

}

module.exports = HiOrderImportController 