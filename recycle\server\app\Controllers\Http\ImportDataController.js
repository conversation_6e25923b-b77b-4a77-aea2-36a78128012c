"use strict";

const _ = require("lodash");
const moment = require("moment");
const { <PERSON>, JDWorkerWalletLog, JDOrder, JDCOWorker, JDPrice, ReqLog, JDWorkerMaintain, JDOrderMaintain } = require('../../Models')
const fs = require("fs-extra");
const Helpers = use("Helpers");
const Database = use('Database')
const ExcelJS = require('exceljs')
//订单废品关联表
class ImportDataController {
  async importData({ request, response }) {
    let file = request.file("file");
    if (!file) {
      throw { error: 12001, message: "文件不存在" };
    }
    let { subtype, clientName } = file;
    // 上传文件到本地
    let tmpName = (moment().format("x") + "." + clientName).toLowerCase();
    fs.ensureDirSync(Helpers.tmpPath());
    await file.move(Helpers.tmpPath(), { name: tmpName });
    if (!file.moved()) {
      throw { error: 12002, message: "文件刪除失败" };
    }

    try {
      // 解析excel
      const workbook = new ExcelJS.Workbook()
      const filePath = Helpers.tmpPath(tmpName)
      await workbook.xlsx.readFile(filePath)
      const worksheet = workbook.worksheets[0]

      // 将工作表数据转换为 JSON
      const jsonData = []
      worksheet.eachRow((row, rowNumber) => {
        if (rowNumber > 1) {
          const rowData = {}
          row.eachCell((cell, colNumber) => {
            rowData[worksheet.getCell(1, colNumber).value] = cell.value
          })
          jsonData.push(rowData)
        }
      })

      // 批量处理数据，每1000条为一批
      const batchSize = 1000;
      const totalBatches = Math.ceil(jsonData.length / batchSize);
      let successCount = 0;
      let errorCount = 0;
      let noWorkerCount = 0;

      console.log(`开始导入数据，共${jsonData.length}条，分${totalBatches}批处理`);

      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        const startIndex = batchIndex * batchSize;
        const endIndex = Math.min(startIndex + batchSize, jsonData.length);
        const currentBatch = jsonData.slice(startIndex, endIndex);

        console.log(`处理第${batchIndex + 1}/${totalBatches}批，${startIndex + 1}-${endIndex}条`);

        // 使用事务处理一批数据
        await Database.transaction(async (trx) => {
          // 收集需要批量创建的数据
          let orderBatch = [];
          let workerMaintainBatch = [];
          let walletLogBatch = [];
          let reqLogBatch = [];

          // 预处理一批数据
          for (const item of currentBatch) {
            try {
              // 订单表里查找是否已创建
              const order = await JDOrder.findBy({ orderNo: item.服务工单号 });
              if (order) {
                // 订单已存在，跳过
                continue;
              }

              // 查找师傅
              let worker = await Worker.findBy({ mobile: item.工作电话, forbidden: 1, isUse: "同意" });
              if (!worker) {
                // co师傅表里查找
                let coworker = await JDCOWorker.findBy({ mobile: item.工作电话 });
                if (!coworker) {
                  // 记录未找到师傅的情况
                  noWorkerCount++;
                  workerMaintainBatch.push({
                    orderNo: item.服务工单号,
                    name: item.师傅姓名,
                    phone: item.工作电话,
                    status: '待维护'
                  });
                  await JDOrderMaintain.create({
                    orderNo: item.服务工单号,
                    status: '待维护',
                    remark: `未找到该师傅:${item.师傅姓名}`
                  });
                  reqLogBatch.push({
                    req: `未找到该师傅:${item.师傅姓名}`,
                    source: '京东回收导入'
                  });

                  // 继续处理下一条
                  continue;
                } else {
                  worker = await Worker.findBy({ id: coworker.managerID });
                }
              }

              // 获取价格
              let getPrice;
              if (item.以旧换新模式 == '送取不同步') {
                if (item.省 === '江苏' && item.市 === '宿迁市') {
                  getPrice = await JDPrice.findBy({ type: "不同步", city: '宿迁市', four: item.旧机四级品类 });
                } else if (item.省 === '江苏' && item.市 === '盐城市') {
                  getPrice = await JDPrice.findBy({ type: "不同步", city: '盐城市', four: item.旧机四级品类 });
                } else if (item.省 === '江苏' && item.市 === '徐州市') {
                  getPrice = await JDPrice.findBy({ type: "不同步", city: '徐州市', four: item.旧机四级品类 });
                } else if (item.省 === '江苏' && item.市 === '淮安市') {
                  getPrice = await JDPrice.findBy({ type: "不同步", city: '淮安市', four: item.旧机四级品类 });
                } else if (item.省 === '江苏' && item.市 === '连云港市') {
                  getPrice = await JDPrice.findBy({ type: "不同步", city: '连云港市', four: item.旧机四级品类 });
                } else if (item.省 === '江苏' && item.市 === '南通市') {
                  getPrice = await JDPrice.findBy({ type: "不同步", city: '南通市', four: item.旧机四级品类 });
                }
              }

              const deductionAmount = getPrice ? parseInt(getPrice.toJSON().price) : "none";
              if (deductionAmount === "none") {
                reqLogBatch.push({
                  req: `未找到该订单价格:${item.服务工单号}`,
                  source: '京东回收导入'
                });
                await JDOrderMaintain.create({
                  orderNo: item.服务工单号,
                  status: '待维护',
                  remark: `未找到该订单价格:${item.服务工单号}`
                });
                continue;
              }

              // 准备订单数据
              const orderData = {
                from: '京东回收',
                infoFee: deductionAmount !== "none" ? deductionAmount : 0,
                orderNo: item.服务工单号,
                userName: item.用户姓名,
                userMobile: item.用户电话,
                province: item.省,
                city: item.市,
                town: item['区/县'],
                county: item['街道/乡镇'],
                address: item.用户地址,
                type: item.旧机四级品类,
                remark: item.备注,
                brand: item.品牌,
                wokerName: item.师傅姓名,
                workerPhone: item.工作电话,
                model: item.以旧换新模式,
                keywords: item.品牌 + item.用户地址 + item.用户电话 + item.用户姓名 + item.服务工单号 + item.旧机四级品类 + item.以旧换新模式,
                price: item.旧机预估价,
                SKU: item.主品产品名称,
                companyID: 47,
                status: worker ? "完成" : "无师傅",
                workerID: worker ? worker.id : null,
                countName: worker ? worker.workerName : item.师傅姓名,
                countPhone: worker ? worker.mobile : item.工作电话,
                takeTime: item.接单时间 ? moment(item.接单时间).subtract(8, 'hours').format('YYYY-MM-DD HH:mm') : null,
                offTime: moment().format('YYYY-MM-DD HH:mm'),
                workTime: item.上门时间 ? moment(item.上门时间).subtract(8, 'hours').format('YYYY-MM-DD HH:mm') : null,
                finishedAt: item.完工时间 ? moment(item.完工时间).subtract(8, 'hours').format('YYYY-MM-DD HH:mm') : null,
                createdAt: item.下传时间 ? moment(item.下传时间).subtract(8, 'hours').format('YYYY-MM-DD HH:mm') : null
              };
              orderBatch.push(orderData);

              // 如果有师傅，准备扣款记录
              if (worker && deductionAmount !== "none") {
                // 更新师傅钱包余额
                await Worker.query()
                  .where({ id: worker.id })
                  .transacting(trx)
                  .update({ wallet: Database.raw(`wallet - ${deductionAmount}`) });

                // 准备钱包日志数据
                walletLogBatch.push({
                  orderNo: item.服务工单号, // 使用订单号关联，后续更新为订单ID
                  workerID: worker.id,
                  money: -(deductionAmount),
                  remark: `${item.师傅姓名}订单完成扣款`
                });
              }

              successCount++;
            } catch (error) {
              console.error(`处理订单 ${item.服务工单号} 时出错:`, error);
              errorCount++;
              // 继续处理下一条
            }
          }

          // 批量创建订单
          if (orderBatch.length > 0) {
            const createdOrders = await JDOrder.createMany(orderBatch, trx);
            console.log(`批量创建了 ${createdOrders.length} 条订单`);

            // 更新钱包日志中的订单ID
            if (walletLogBatch.length > 0) {
              // 获取刚创建的订单ID和订单号的映射
              const orderMap = {};
              for (const order of createdOrders) {
                orderMap[order.orderNo] = order.id;
              }

              // 更新钱包日志中的订单ID
              for (const log of walletLogBatch) {
                log.orderID = orderMap[log.orderNo];
                delete log.orderNo; // 删除临时字段
              }

              // 批量创建钱包日志
              await JDWorkerWalletLog.createMany(walletLogBatch, trx);
            }
          }

          // 批量创建师傅维护记录
          if (workerMaintainBatch.length > 0) {
            // 对workerMaintainBatch按phone字段去重
            const uniqueWorkerMaintain = workerMaintainBatch.filter((item, index, self) =>
              index === self.findIndex((t) => t.phone === item.phone)
            );
            // 更新workerMaintainBatch为去重后的数组
            workerMaintainBatch = uniqueWorkerMaintain;
            await JDWorkerMaintain.createMany(workerMaintainBatch, trx);
          }

          // 批量创建请求日志
          if (reqLogBatch.length > 0) {
            await ReqLog.createMany(reqLogBatch, trx);
          }
        });

        // 每批处理完成后输出进度
        console.log(`第${batchIndex + 1}批处理完成，进度: ${Math.round((endIndex / jsonData.length) * 100)}%`);
      }

      // 导入完成后输出统计信息
      console.log(`导入完成，总数据: ${jsonData.length}, 成功: ${successCount}, 失败: ${errorCount}, 无师傅: ${noWorkerCount}`);

      response.json({
        code: 200,
        msg: "导入成功",
        data: {
          total: jsonData.length,
          success: successCount,
          error: errorCount,
          noWorker: noWorkerCount
        }
      });
    } catch (e) {
      console.error(e);
      throw { error: 12003, message: e };
    }
  }
  fetchValue([x1, y1], sources) {
    return sources[y1 - 1][x1 - 1];
  }
  fetchRectangleData([x1, y1], [x2, y2], sources) {
    let ret = [];
    for (let y = y1; y <= y2; y++) {
      let row = sources[y - 1].slice(x1 - 1, x2);
      ret.push(row);
    }
    return ret;
  }
  parseData1(sources) {
    let result = [];
    sources.forEach((row, index) => {
      if (index == 0) {
        // 第一行
        for (let i in row) {
          if (i !== 0) {
            result[i - 1] = { name: row[i], attributes: {} };
          }
        }
      } else {
        let sortName = null;
        for (let i in row) {
          if (i == 0) {
            // 分类名
            sortName = row[i];
          } else {
            // 分类值
            result[i - 1].attributes[sortName] = row[i].split(/、|，|,/g);
          }
        }
      }
    });
    return result;
  }

}

module.exports = ImportDataController;
