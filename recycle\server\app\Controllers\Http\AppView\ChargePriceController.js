'use strict'

const _ = require('lodash')
const { <PERSON>q<PERSON><PERSON>,Worker, ChargePrice, Order, AppPay } = require('../../../Models/index')
const { ERR, E } = require('../../../../../../constants')
const WXService = require('../../../Services/WXService')
const Config = require('../../../Util/Config')

class ChargePriceController {
  async index({ request, response }) {
    let { current = 1, pageSize = 10, area, sort = 'desc' } = request.all()
    let workerID = request.worker && request.worker.id
    let query = ChargePrice.query()
    if (area) {
      query.whereRaw('area like ?', [`%${area}%`])
    }
    let vo = await query.orderBy('id', sort).paginate(current, pageSize)
    let inprocessWallets = 0
    inprocessWallets = await Order.query()
      .where('workerID', workerID)
      .where('status', E.OrderStatus.InProgress)
      .getSum('infoFee')
    response.json({ ...vo.toJSON(), inprocessWallets })
  }
  async createScheme({ request, response }) {
    let { amount, userId, paymentMethod, orderNo } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: 'APP创建scheme' })
    let worker = await Worker.find(userId)
    let scheme = await WXService.createScheme(orderNo, amount, Config.WEAPP.AppID, Config.WEAPP.AppSecret)
    if (scheme) {
      response.json({
        code: 0,
        message: 'success',
        data: scheme
      })
    } else {
      response.json({
        code: 1,
        message: 'failed',
        data: null
      })
    }
  }

}

module.exports = ChargePriceController
