import { Routes, Route, HashRouter } from 'react-router-dom'
import { Provider } from 'react-redux'
import { bindJWTToken, initModels, initRequest } from '../utils/dva17'
import models from '../models'
import Config from '../config/Config'

import Home from './Home'
import Login from './Login'
import { Toast } from 'antd-mobile'

initRequest(
  Config.SERVER_HOME,
  (status, message) => {
    if (401 == status) {
      localStorage.removeItem('token')
      location = '#/user/login'
    } else {
      console.warn(message) //TODO.请求异常
      Toast.show({
        content: message?.message || '',
      })
    }
  },
  false //是否打印request记录
)
bindJWTToken(localStorage.getItem('token'))

export default () => {
  /*--------------------- 生命周期 ---------------------*/
  /*--------------------- 响应 ---------------------*/
  /*--------------------- 渲染 ---------------------*/
  return (
    <Provider store={initModels(models, false /*是否打印dva17记录 */)}>
      <HashRouter>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/user/login" element={<Login />} />
        </Routes>
      </HashRouter>
    </Provider>
  )
}
