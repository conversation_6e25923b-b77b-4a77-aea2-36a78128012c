'use strict'

const Model = use('Model')

//佣金修改记录
class PriceSetLog extends Model {
  static get primaryKey() {
    return 'id'
  }
  static get table() {
    return 'price_set_log'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return null
  }
  getAfterData(value) {
    return value ? JSON.parse(value) : {}
  }
  getBeforeData(value) {
    return value ? JSON.parse(value) : {}
  }
  
  order() {
    return this.belongsTo('App/Models/Order', 'orderID', 'id')
  }
  
  operator() {
    return this.belongsTo('App/Models/AdminUser', 'adminID', 'id').select('id', 'name', 'username')
  }
}

module.exports = PriceSetLog 