import Taro, { requirePlugin } from '@tarojs/taro'
import React, { Component } from 'react'
import { connect } from 'react-redux'
import { View, Image, Button, Text, Checkbox } from '@tarojs/components'
import { AtModal, AtTextarea } from 'taro-ui'
import './remarkPage.less'
import E from '../../config/E'
import dayjs from 'dayjs'
import T from '../../config/T'
import 'taro-ui/dist/style/components/textarea.scss'

class RemarkPage extends Component {
  constructor() {
    super(...arguments)
    this.state = {
      locale: 'zh_CN',
      whoCancel: false,
      selectReason: '',
      reason: '',
      orderID: null,
      manage: null,
      selectIndex: null,
      expressionIndex: 3,
    }
  }

  config = {}

  //-----------------------生命周期-------------------------//
  componentWillMount() {
    let orderID = Taro.getCurrentInstance().router.params.orderID
    this.setState({
      orderID,
    })
    this.props.dispatch({
      type: 'NOrder/ERemarkOrder',
      payload: {
        id: orderID,
      },
    })
    this.props
      .dispatch({
        type: 'NOrder/EGetRating',
        payload: {
          orderID,
        },
      })
      .then(res => {
        if (res.workerRatingText) {
          this.setState({ expressionIndex: res.workerRating, reason: res.workerRatingText })
        }
      })
  }

  componentDidMount() {
    const locale = Taro.getStorageSync('locale')
    this.setState({ locale })
  }

  componentWillUnmount() {}

  componentDidShow() {}

  componentDidHide() {}

  //-----------------------事件-------------------------//
  selectReasonFun(selectReason, index) {
    if (this.props.ratingText) {
      return
    }
    this.setState({
      selectReason,
      selectIndex: index,
    })
  }

  submit() {
    let { whoCancel, orderID, reason, manage, selectReason, expressionIndex } = this.state
    let { companyID, workerID } = this.props.orderRemark
    let remark = selectReason && reason ? selectReason + ',' + reason : selectReason ? selectReason : reason
    this.props.dispatch({
      type: 'NOrder/ECreateOrderRating',
      payload: {
        orderID,
        companyID,
        workerID,
        workerRating: expressionIndex,
        workerRatingText: remark,
      },
    })
  }

  selectExpression(index) {
    if (this.props.ratingText) {
      return
    }
    //workerRating   workerRatingText
    this.setState({
      expressionIndex: index,
    })
  }

  //-----------------------渲染-------------------------//
  render() {
    const { whoCancel, reason, selectIndex, selectReason, expressionIndex, locale } = this.state
    const { orderRemark, ratingText } = this.props
    console.log(orderRemark)
    return orderRemark ? (
      <View className="cancleOrder">
        <View className="recycler">
          <View className="recycler_avatar">
            <Text>{orderRemark ? orderRemark.worker.workerName.substr(orderRemark.worker.workerName.length - 1, 1) : ''}</Text>
          </View>
          <View className="recycler_operate">
            <Text>{orderRemark.worker.workerName}</Text>
            <View>{orderRemark.worker.mobile}</View>
          </View>
        </View>
        <View className="expression_wrapper">
          <View
            className="item_wrapper"
            onClick={() => {
              this.selectExpression(1)
            }}
          >
            {expressionIndex === 1 ? (
              <Image src={require(`../../assets/icon/not_satisfaction_active.png`)} className="expression" />
            ) : (
              <Image src={require(`../../assets/icon/not_satisfaction.png`)} className="expression" />
            )}

            <Text style={expressionIndex === 1 ? { color: '#15b381' } : null}>{T.reamrkPage.dissatisfied}</Text>
          </View>
          <View
            className="item_wrapper"
            onClick={() => {
              this.selectExpression(2)
            }}
          >
            {expressionIndex === 2 ? (
              <Image src={require(`../../assets/icon/general_active.png`)} className="expression" />
            ) : (
              <Image src={require(`../../assets/icon/general.png`)} className="expression" />
            )}

            <Text style={expressionIndex === 2 ? { color: '#15b381' } : null}>{T.reamrkPage.general}</Text>
          </View>
          <View
            className="item_wrapper"
            onClick={() => {
              this.selectExpression(3)
            }}
          >
            {expressionIndex === 3 ? (
              <Image src={require(`../../assets/icon/satisfaction_active.png`)} className="expression" />
            ) : (
              <Image src={require(`../../assets/icon/satisfaction.png`)} className="expression" />
            )}

            <Text style={expressionIndex === 3 ? { color: '#15b381' } : null}>{T.reamrkPage.satisfaction}</Text>
          </View>
        </View>

        <View className="content_wrapper">
          <View className="top_select">
            {E[['OrderComplaint', 'OrderMedium', 'OrderRating'][expressionIndex - 1]].map((vo, index) => (
              <Text
                onClick={() => {
                  this.selectReasonFun(vo.zh_CN, index)
                }}
                key={index + vo}
                style={index === selectIndex ? { color: '#ffffff', background: '#15b381' } : null}
              >
                {locale == 'en' ? vo.en : vo.zh_CN}
              </Text>
            ))}
          </View>
          <Text className="input_title">{T.reamrkPage.otherComments}：</Text>
          <AtTextarea
            className="detail_input"
            count={false}
            value={reason}
            maxLength={100}
            onChange={value => {
              this.setState({
                reason: value,
              })
            }}
            placeholder={T.reamrkPage.remark}
            placeholderStyle="font-size: 12px;color: #7C8696;"
          />
          <View
            className="submit_button"
            style={(reason || selectReason) && expressionIndex && !ratingText ? { background: '#15b381' } : null}
            onClick={() => {
              console.log('提交评价')
              if ((reason || selectReason) && expressionIndex && !ratingText) {
                this.submit()
              }
            }}
          >
            {!ratingText ? T.reamrkPage.submit : T.reamrkPage.rated}
          </View>
        </View>
      </View>
    ) : null
  }
}
export default connect(({ NOrder: { orderRemark, ratingText } }) => ({
  orderRemark,
  ratingText,
}))(RemarkPage)
