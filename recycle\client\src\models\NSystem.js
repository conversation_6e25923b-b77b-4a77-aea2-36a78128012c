//old

import Taro from '@tarojs/taro'
import { NSystem } from '../config/constants'
const { EWxGetSystemInfo, EWxGetMenuButtonBoundingClientRect, EJudgeIsAddItem, ESetState } = NSystem
export default {
  state: {
    firstPageShow: true,
    systemInfo: null,
    MenuButtonInfo: null,
    isAddItem: null,
  },
  effects: dispatch => ({
    async [EWxGetSystemInfo](payload, rootState) {
      const response = await Taro.getSystemInfo()
      await this.RSetState({ systemInfo: response })
    },
    async [EWxGetMenuButtonBoundingClientRect](payload, rootState) {
      // const response = await wx.getMenuButtonBoundingClientRect()
      // await this.RSetState({ MenuButtonInfo: response })
    },
    async [EJudgeIsAddItem](payload, rootState) {
      var systemInfo
      Taro.getSystemInfo({
        success: res => {
          if (res.system.indexOf('iOS') >= 0 && res.screenHeight > 780) {
            systemInfo = true
          } else {
            systemInfo = false
          }
        },
      })
      await this.RSetState({ isAddItem: systemInfo })
    },
    async [ESetState](payload, rootState) {
      await this.RSetState({ ...payload })
    },
  }),

  reducers: {
    RSetState(state, payload) {
      return {
        ...state,
        ...payload,
      }
    },
  },
}
