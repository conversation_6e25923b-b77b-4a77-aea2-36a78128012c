import { Modal, Button, Image, notification, Input } from 'antd';
import { useEffect, useState } from 'react';
import { effect } from 'dva17';
import { NWorker, EChangeWorkerUse } from '../../common/action';
import styles from './index.module.less';

const { Search } = Input;

interface WorkerDetailModalProps {
  visible: boolean;
  worker: any;
  onClose: () => void;
  onRefresh: () => void;
}

const WorkerDetailModal = ({ visible, worker, onClose, onRefresh }: WorkerDetailModalProps) => {
  const [inputMobile, setInputMobile] = useState('');

  const handleChangeUse = async (params: any) => {
    await effect(NWorker, EChangeWorkerUse, { id: worker.id, ...params })
      .then(() => {
        notification.success({
          message: "成功",
          description: '修改成功！',
          duration: 2
        })
        onRefresh();
        onClose();
      });
  }

  return (
    <Modal
      title="师傅明细"
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>关闭</Button>
      ]}
      width={700}
    >
      <div className={styles.item_wrapper}>
        <div className={styles.item} style={{ marginTop: 30 }}>
          <span className={styles.item_title}>兼京东：</span>
          <div className={styles.item_content}>{worker?.isjd ? '是' : '否'}</div>
          <Button type='primary' onClick={() => handleChangeUse({ isjd: !worker?.isjd })}>
            切换
          </Button>
        </div>
        <div className={styles.item} style={{ marginTop: 30 }}>
          <span className={styles.item_title}>兼嗨回收：</span>
          <div className={styles.item_content}>{worker?.isHi ? '是' : '否'}</div>
          <Button type='primary' onClick={() => handleChangeUse({ isHi: !worker?.isHi })}>
            切换
          </Button>
        </div>
        <div className={styles.item} style={{ marginTop: 30, paddingBottom: 10 }}>
          <span className={styles.item_title}>姓名：</span>
          <div className={styles.item_content}>{worker?.workerName}</div>
        </div>
        
        <div className={styles.item}>
          <span className={styles.item_title}>手机号：</span>
          <div className={styles.item_content}>{worker?.mobile}</div>
          <input 
            placeholder="请输入新手机号"
            style={{ width: 160, height: 32, marginRight: 10, paddingLeft: 8 }}
            onChange={(e) => setInputMobile(e.target.value)}
          />
          <Button type="primary" onClick={() => {
            if (inputMobile) {
              handleChangeUse({ mobile: inputMobile });
            } else {
              notification.warning({
                message: "提示",
                description: '请输入新手机号',
                duration: 2
              });
            }
          }}>
            提交
          </Button>
        </div>
        
        <div className={styles.item}>
          <span className={styles.item_title}>地址：</span>
          <div className={styles.item_content}>
            {worker?.province}{worker?.city}{worker?.district}{worker?.subDistrct}{worker?.address}
          </div>
        </div>
        
        <div className={styles.item}>
          <span className={styles.item_title}>身份证号码：</span>
          <div className={styles.item_content}>{worker?.idCardNo}</div>
        </div>
        
        <div className={styles.item}>
          <span className={styles.item_title}>身份证图片：</span>
          <div className={styles.item_content}>
            <Image src={worker?.idCardImg} style={{ width: 200, height: 200 }} />
            <Image src={worker?.idCardBackImg} style={{ width: 200, height: 200 }} />
          </div>
        </div>
        
        <div className={styles.item}>
          <span className={styles.item_title}>保险：</span>
          <div className={styles.item_content}>{worker?.insure ? '已提交' : '未提交'}</div>
        </div>
        
        <div className={styles.item}>
          <Button type='primary' onClick={() => handleChangeUse({ mobile: worker?.mobile })}>
            重置密码
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default WorkerDetailModal; 