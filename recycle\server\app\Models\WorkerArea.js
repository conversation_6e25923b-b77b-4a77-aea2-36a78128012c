'use strict'

const Model = use('Model')

//上门师傅信息
class WorkerArea extends Model {
  static get table() {
    return 'worker_area'
  }
  static get createdAtColumn() {
    return null
  }
  static get updatedAtColumn() {
    return 'updatedAt'
  }

  company() {
    return this.belongsTo('App/Models/Company', 'companyID', 'id')
  }
  worker() {
    return this.belongsTo('App/Models/Worker', 'workerID', 'id')
  }
  address() {
    return this.belongsTo('App/Models/SysArea', 'areaCode', 'areaCode')
      .select('id', 'areaCode', 'name', 'provinceCode', 'cityCode')
      .where('level', '3')
      .with('province')
      .with('city')
  }
  typeInfo() {
    return this.hasMany('App/Models/WorkerArea', 'code', 'code')
      .select('type', 'typeName', 'code', 'name', 'area', 'areaCode', 'detail', 'detailName')
  }
}

module.exports = WorkerArea
