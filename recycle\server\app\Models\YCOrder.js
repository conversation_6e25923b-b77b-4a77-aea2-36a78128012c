'use strict'

const Model = use('Model')

//订单
class YCOrder extends Model {
  static get table() {
    return 'yc_order'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return null
  }
  static get updatedAtColumn() {
    return null
  }
  worker() {
    return this.belongsTo('App/Models/Worker', 'workerID', 'id')
  }
  company() {
    return this.belongsTo('App/Models/Company', 'companyID', 'id')
  }
  sms() {
    return this.belongsTo('App/Models/LogMsg', 'id', 'orderID')
  }
}

module.exports = YCOrder
