const moment = require('moment')
const _ = require('lodash')
const axios = require('axios')
const crypto = require('crypto')
const fs = require('fs')
const WXPay = require('weixin-pay')
const wechatpay = require('wechatpay-node-v3')
const Env = use('Env')
const Helpers = use('Helpers')
const ObjectUtil = require('./ObjectUtil')
const { LogExternalApiRequest } = require('../Models')
const { ERR, E, Config } = require('../../../../constants')

const APP_MCH_ID = Env.get('APP_MCH_ID')
const APP_PARTNER_KEY = Env.get('APP_PARTNER_KEY')
const APP_CHECK_ID = Env.get('APP_CHECK_ID')
const HOME_PATH = Env.get('HOME_PATH')
const { Configs } = require('../Util')
const tokenResultDict = {}
const WXBizDataCrypt = require('./WXBizDataCrypt')

const APP_ID = Configs.WEAPP.AppID
const wxpay = WXPay({
  appid: APP_ID,
  mch_id: APP_MCH_ID,
  partner_key: APP_PARTNER_KEY, //微信商户平台API密钥
  pfx: fs.readFileSync(Helpers.resourcesPath('pay/apiclient_cert.p12')),
})

const wxpayMaster = WXPay({
  appid: Configs.WEAPP.AppID,
  mch_id: APP_MCH_ID,
  partner_key: APP_PARTNER_KEY, //微信商户平台API密钥
  pfx: fs.readFileSync(Helpers.resourcesPath('pay/apiclient_cert.p12')),
})
// 使用框架
const companyPay = new wechatpay({
  appid: APP_ID,
  mchid: APP_MCH_ID,
  publicKey: fs.readFileSync(Helpers.resourcesPath('pay/apiclient_cert.pem')), // 公钥
  privateKey: fs.readFileSync(Helpers.resourcesPath('pay/apiclient_key.pem')), // 秘钥
  key: APP_PARTNER_KEY,
})

const WXService = {
  fetchPhoneNumber(encryptedData, iv, sessionKey) {
    let pc = new WXBizDataCrypt(APP_CHECK_ID, sessionKey)
    return pc.decryptData(encryptedData, iv)
  },

  clientFetchPhoneNumber(encryptedData, iv, sessionKey) {
    let pc = new WXBizDataCrypt(APP_ID, sessionKey)
    return pc.decryptData(encryptedData, iv)
  },
  // envVersion要打开的小程序版本。正式版为 release，体验版为 trial，开发版为 develop
  /** 生成小程序码 */
  async getwxacode({ AppID, AppSecret, data }) {
    let access_token = await this.getAccessToken(AppID, AppSecret)

    // console.log(access_token)
    let res = await axios.post(
      `https://api.weixin.qq.com/wxa/getwxacode?access_token=${access_token}`,
      data,
      {
        responseType: 'arraybuffer',
      },
      { 'content-type': 'image/jpeg' }
    )
    return res
  },
  async code2Session(code, appid, secret) {
    let res = await this.request(E.HttpMethod.Get, 'jscode2session', {
      appid,
      secret,
      js_code: code,
      grant_type: 'authorization_code',
    })
    return res
  },
  async getMobileNumber(code, encryptedData, iv) {
    let resSession = await this.code2Session(code)

    let sessionKey = resSession.result.session_key
    // console.log(sessionKey)
    // console.log(sessionKey.length)
    // console.log(encryptedData)
    // console.log(iv)
    await this.sleep(50)
    let res = await this.decryptData(encryptedData, iv, sessionKey)
    return res
  },
  async decryptData(encryptedData, iv, sessionKey) {
    // base64 decode
    // console.log(encryptedData)
    // console.log(iv)
    // console.log(sessionKey)
    var sessionKey = Buffer.from(sessionKey, 'base64')
    encryptedData = Buffer.from(encryptedData, 'base64')
    iv = Buffer.from(iv, 'base64')

    try {
      // 解密
      var decipher = crypto.createDecipheriv('aes-128-cbc', sessionKey, iv)
      // 设置自动 padding 为 true，删除填充补位
      decipher.setAutoPadding(true)
      var decoded = decipher.update(encryptedData, 'binary', 'utf8')
      decoded += decipher.final('utf8')

      decoded = JSON.parse(decoded)
    } catch (err) {
      console.log(err)
      throw new Error('Illegal Buffer')
    }

    if (decoded.watermark.appid !== APP_ID) {
      console.log(decoded.watermark.appid)
      throw new Error('Illegal Buffer')
    }

    return decoded
  },
  sleep(time) {
    return new Promise((resolve) => setTimeout(resolve, time))
  },
  async request(method, action, data) {
    let option = {
      url: 'https://api.weixin.qq.com/sns/' + action,
      method,
    }
    if (data) {
      switch (method.toLowerCase()) {
        case E.HttpMethod.Get:
        case E.HttpMethod.Delete:
          option.params = data
          break
        case E.HttpMethod.Post:
        case E.HttpMethod.Patch:
        case E.HttpMethod.Put:
          option.data = data
          break
      }
    }
    // console.log('REQ CLIENT:', option)

    try {
      let result = await axios.request(option)
      if (200 == result.status) {
        // console.log('ACK CLIENT:', action, result.data)
        if (result.data.errcode) {
          return { error: _.assign(ERR.API_ERROR, result.data) }
        } else {
          return { result: result.data }
        }
      } else {
        // console.log('ERR CLIENT:', action, result.error)
        return { error: ERR.API_ERROR }
      }
    } catch (e) {
      // console.log('ERR CLIENT:', action, e)
      return { error: ERR.API_ERROR }
    }
  },

  async getAccessToken(AppID, AppSecret) {
    let tokenResult = tokenResultDict[AppID] || {}
    // 检查token是否存在且未过期(2小时内)
    if (!tokenResult.token || tokenResult.expire <= moment().unix()) {
      try {
        let res = await axios.post(`https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${AppID}&secret=${AppSecret}`)
        let { access_token, expires_in } = res.data
        
        // 设置过期时间为2小时(7200秒)减去200秒缓冲时间
        tokenResultDict[AppID] = {
          expire: moment().unix() + expires_in - 200,
          token: access_token,
          updateTime: moment().format('YYYY-MM-DD HH:mm:ss')
        }
        console.log('[new token]', tokenResultDict[AppID])
      } catch (error) {
        console.error('获取access_token失败:', error)
        throw new Error('获取微信access_token失败')
      }
    }
    return tokenResultDict[AppID].token
  },

  //微信订阅消息
  async sendWechat(AppID, AppSecret, openid, waste_1st_ID, createdAt, orderNO, status, page, messageID) {
    let content = Configs.Content
    content.template_id = messageID
    content.page = page
    content.miniprogram = {
      appid: AppID,
      pagepath: page,
    }
    content.touser = openid
    content.data.thing2.value =
      waste_1st_ID === 1 ? '旧衣旧书' : waste_1st_ID === 2 ? '生活废品' : waste_1st_ID === 3 ? '大家电' : '大家具'
    content.data.date8.value = moment(createdAt).format('YYYY年MM月DD日 HH:mm')
    content.data.character_string5.value = orderNO
    content.data.thing4.value = status
    let access_token = await this.getAccessToken(AppID, AppSecret)
    let res = await axios.post(`https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=${access_token}`, content)
    // console.log(res.data, content, '服务号消息')
    return res.data
  },
  async sendClientWechat(AppID, AppSecret, openid, waste_1st_ID, createdAt, orderNO, status, page, messageID) {
    let content = Configs.ClientContent
    content.template_id = messageID
    content.page = page
    content.miniprogram = {
      appid: AppID,
      pagepath: page,
    }
    content.touser = openid
    content.data.character_string5.value = orderNO
    content.data.thing2.value = '大家电'
    content.data.time14.value = moment(createdAt).format('YYYY年MM月DD日 HH:mm')
    content.data.thing3.value = status
    let access_token = await this.getAccessToken(AppID, AppSecret)
    let res = await axios.post(`https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=${access_token}`, content)
    // console.log(res.data, content, '服务号消息')
    return res.data
  },

  // 微信支付
  async createPay(appId, openid, amount, itemName, orderNo, notify_url) {
    let total_fee = Env.get('NODE_ENV') != 'production' ? 10 : amount
    // total_fee = 10
    //回调的路径
    let params = {
      spbill_create_ip: '**************',
      openid,
      body: itemName,
      detail: itemName,
      out_trade_no: orderNo.toString(),
      // out_trade_no: orderNo,
      total_fee,
      notify_url,
    }
    // console.log('1111wexinpay debug', params)
    let ret
    if (appId === APP_ID) {
      //用户支付
      ret = await new Promise((resolve, reject) => {
        wxpay.getBrandWCPayRequestParams(params, function (err, result) {
          // console.log('getBrandWCPayRequestParams', err, result)
          if (err) {
            reject(err)
          } else {
            resolve(result)
          }
        })
      })
    } else {
      // 师傅支付
      // console.log('师傅支付: ')
      ret = await new Promise((resolve, reject) => {
        wxpayMaster.getBrandWCPayRequestParams(params, function (err, result) {
          // console.log('getBrandWCPayRequestParams', err, result)
          if (err) {
            reject(err)
          } else {
            resolve(result)
          }
        })
      })
    }

    await LogExternalApiRequest.create({
      url: 'createWechatPay',
      headers: notify_url,
      request: JSON.stringify(params),
      response: JSON.stringify(ret),
    })
    return ret
  },

  //逆向支付（商家支付）
  async businessPay(appId, openid, amount, theTitle, orderNO) {
    const out_batch_no = ObjectUtil.getUuid()
    let dataBody = {
      appid: appId,
      out_batch_no: out_batch_no,
      batch_name: theTitle,
      batch_remark: theTitle,
      total_amount: parseInt(amount),
      total_num: 1,
      transfer_detail_list: [
        {
          out_detail_no: orderNO.toString(),
          transfer_amount: parseInt(amount),
          transfer_remark: theTitle,
          openid,
          // user_name: theTitle //收款用户姓名 金额 > 2000 必传
        },
      ],
    }
    try {
      let result = await companyPay.batches_transfer(dataBody)
      console.log(result, 'result')
      await LogExternalApiRequest.create({
        url: 'pay user',
        request: JSON.stringify(dataBody),
        response: JSON.stringify(result),
      })
      return result
    } catch (error) {
      await LogExternalApiRequest.create({
        url: 'businessPayError',
        request: JSON.stringify(dataBody),
        response: error.toString(),
      })
      return error.toString()
    }
  },

  // 师傅退款
  async createMaterRefund(orderNo, payAmount, refundAmount, transactionID) {
    let notify_url = `${HOME_PATH}/master/v1/hook/refund`
    var params = {
      appid: Configs.WEAPP.AppID,
      mch_id: Env.get('APP_MCH_ID'),
      out_refund_no: orderNo,
      total_fee: payAmount, // 原支付金额
      refund_fee: refundAmount, // 退款金额
      transaction_id: transactionID,
      notify_url: notify_url,
    }
    console.warn('[createRefund 1]', params)
    if (Env.get('NODE_ENV') != 'production') {
      return null
    } else {
      console.warn('[createRefund 2]')
      const ret = await new Promise((resolve, reject) => {
        wxpayMaster.refund(params, function (err, result) {
          console.warn('[createRefund 3]', err, result)
          if (err) {
            reject(err)
          } else {
            resolve(result)
          }
        })
      })
      await LogExternalApiRequest.create({
        url: 'createWechatH5Refund',
        headers: null,
        request: JSON.stringify(params),
        response: JSON.stringify(ret),
      })
      return ret
    }
  },

  async createNonceStr() {
    return Math.random().toString(36).substr(2, 15)
  },
    // 微信scheme
    async createScheme(orderNo, amount, AppID, AppSecret) {
      let content =
      {
        "jump_wxa":
        {
          "path": "pages/workerRecharge/index",
          "query": `orderNo=${orderNo}&amount=${amount}`,
          "env_version": "release" // 开发环境 develop 体验版 trial 正式版 release
        },
        "is_expire": false,
      }
      let access_token = await this.getAccessToken(AppID, AppSecret)
  
      try {
        let res = await axios.post(`https://api.weixin.qq.com/wxa/generatescheme?access_token=${access_token}`, content)
        return res.data
      } catch (error) {
        console.log(error)
        return error
      }
    },
}
module.exports = WXService
