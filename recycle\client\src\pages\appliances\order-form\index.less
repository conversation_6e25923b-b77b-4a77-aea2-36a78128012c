@import '../../../styles/variables.less';

.order-form {
  background: @background-light;
  min-height: 100vh;
  width: 100%;
}

.header {
  background: @gradient-primary;
  height: 120rpx;
  display: flex;
  align-items: center;
  padding: 0 @spacing-lg;
  color: @text-white;
  box-shadow: @shadow-light;
}

.back-btn {
  background: none;
  border: none;
  color: @text-white;
  font-size: @font-lg;
  padding: 0;
  margin-right: @spacing-md;
  line-height: 1;
  width: auto;
  transition: @transition-fast;
  
  &:active {
    opacity: 0.7;
  }
}

.header-title {
  font-size: @font-lg;
  font-weight: 600;
}

.content {
  height: calc(100vh - 120rpx);
  overflow-y: auto;
  padding-bottom: @spacing-lg;
}

.summary-card {
  background: @background;
  margin: @spacing-lg;
  border-radius: @radius-lg;
  padding: @spacing-lg;
  box-shadow: @shadow-light;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: @gradient-primary;
    border-radius: @radius-lg @radius-lg 0 0;
  }
}

.summary-title {
  font-size: @font-lg;
  font-weight: 600;
  margin-bottom: @spacing-lg;
  color: @primary-color;
  padding-top: @spacing-sm;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: @spacing-md;
  padding-bottom: @spacing-md;
  border-bottom: 1rpx solid @border-light;
  
  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }
}

.label {
  color: @text-secondary;
  font-size: @font-md;
}

.value {
  font-weight: 600;
  color: @text-primary;
  font-size: @font-md;
  
  &.price {
    color: @primary-color;
    font-size: @font-lg;
    font-weight: bold;
  }
}

.form-section {
  background: @background;
  margin: 0 @spacing-lg @spacing-lg;
  border-radius: @radius-lg;
  padding: @spacing-lg;
  box-shadow: @shadow-light;
}

.section-title {
  font-size: @font-lg;
  font-weight: 600;
  margin-bottom: @spacing-lg;
  color: @primary-color;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -@spacing-xs;
    left: 0;
    width: 60rpx;
    height: 6rpx;
    background: @gradient-primary;
    border-radius: @radius-xs;
  }
}

.form-group {
  margin-bottom: @spacing-lg;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: block;
  margin-bottom: @spacing-sm;
  font-weight: 600;
  color: @text-primary;
  font-size: @font-md;
}

.required {
  color: @warning-color;
  margin-left: 4rpx;
}

.form-input {
  width: 100%;
  padding: @spacing-md @spacing-lg;
  border: 2rpx solid @border-color;
  border-radius: @radius-md;
  font-size: @font-md;
  background: @background;
  color: @text-primary;
  transition: all @transition-normal;
  
  &:focus {
    border-color: @primary-color;
    box-shadow: 0 0 0 4rpx rgba(21, 179, 129, 0.1);
    background: @background;
  }
  
  &::placeholder {
    color: @text-light;
  }
  
  &.error {
    border-color: @error-color;
  }
}

// 复选框组
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: @spacing-sm;
}

.checkbox-item {
  display: flex;
  align-items: center;
  padding: @spacing-md @spacing-lg;
  border: 2rpx solid @border-color;
  border-radius: @radius-md;
  background: @background;
  color: @text-primary;
  transition: all @transition-normal;
  cursor: pointer;
  position: relative;
  
  &:before {
    content: '';
    width: 32rpx;
    height: 32rpx;
    border: 2rpx solid @border-color;
    border-radius: 6rpx;
    margin-right: @spacing-md;
    background: @background;
    transition: all @transition-normal;
  }
  
  &.checked {
    border-color: @primary-color;
    background: rgba(21, 179, 129, 0.05);
    
    &:before {
      background: @primary-color;
      border-color: @primary-color;
      background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
      background-size: 20rpx;
      background-repeat: no-repeat;
      background-position: center;
    }
  }
  
  &:active {
    transform: scale(0.98);
  }
}

// 单选框组
.radio-group {
  display: flex;
  gap: @spacing-md;
}

.radio-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: @spacing-md;
  border: 2rpx solid @border-color;
  border-radius: @radius-md;
  background: @background;
  color: @text-primary;
  transition: all @transition-normal;
  cursor: pointer;
  
  &.active {
    border-color: @primary-color;
    background: @primary-color;
    color: @text-white;
  }
  
  &:active {
    transform: scale(0.98);
  }
}

// 备注预览
.remark-preview {
  margin-top: @spacing-sm;
  padding: @spacing-sm @spacing-md;
  background: rgba(21, 179, 129, 0.05);
  border: 1rpx solid rgba(21, 179, 129, 0.2);
  border-radius: @radius-sm;
  font-size: @font-sm;
  color: @text-secondary;
  line-height: 1.4;
}

.picker-view {
  width: 100%;
  padding: @spacing-md @spacing-lg;
  border: 2rpx solid @border-color;
  border-radius: @radius-md;
  font-size: @font-md;
  background: @background;
  color: @text-primary;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all @transition-normal;
  cursor: pointer;
  
  &.error {
    border-color: @warning-color;
  }
  
  &:active {
    background: @background-light;
  }
}

.picker-arrow {
  color: @text-secondary;
  font-size: 24rpx;
  transform: scaleY(0.6);
}

.tips {
  background: linear-gradient(135deg, @secondary-color, rgba(240, 249, 244, 0.8));
  margin: 0 @spacing-lg @spacing-lg;
  padding: @spacing-lg;
  border-radius: @radius-md;
  border-left: 8rpx solid @success-color;
  box-shadow: @shadow-light;
}

.tips-title {
  font-weight: 600;
  color: @primary-dark;
  margin-bottom: @spacing-sm;
  font-size: @font-md;
}

.tips-content {
  font-size: @font-sm;
  color: @text-secondary;
  line-height: 1.6;
}

.submit-btn {
  background: @gradient-primary;
  color: @text-white;
  padding: @spacing-lg;
  border: none;
  border-radius: 50rpx;
  width: calc(100% - @spacing-xl);
  margin: 0 @spacing-lg @spacing-lg;
  font-size: @font-lg;
  font-weight: 600;
  transition: all @transition-normal;
  box-shadow: @shadow-medium;
  
  &:active {
    transform: translateY(2rpx);
    box-shadow: @shadow-light;
  }
  
  &[disabled] {
    background: @background-gray;
    color: @text-light;
    transform: none;
    box-shadow: none;
  }
}

// 页面加载动画
.summary-card,
.form-section,
.tips {
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 为不同元素添加延迟动画
.summary-card { animation-delay: 0.1s; }
.form-section { animation-delay: 0.2s; }
.tips { animation-delay: 0.3s; }