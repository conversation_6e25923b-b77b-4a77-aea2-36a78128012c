import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'

export async function getCompanylist({payload}:any) {
  return requestGet(`/company`,payload);
}
export async function delCompanyItem({payload}:any) {
  return requestDelete(`/company/${payload.id}`);
}
export async function getAddress({payload}:any) {
  return requestGet(`/address`,payload);
}
export async function creatCompany({payload}:any) {
  return requestPost(`/company`,payload);
}
export async function editCompany({payload}:any) {
  return requestPut(`/company/${payload.id}`,payload);
}
