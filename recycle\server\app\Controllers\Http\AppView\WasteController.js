'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Waste, CompanySelfWastePrice } = require('../../../Models')
const { ERR, E } = require('../../../../../constants')

//废品
class WasteController {
  async index({ request, response }) {
    let { secondCateID, source = E.OrderSource.CaiNiao, type = E.Permission.Primary } = request.all()
    if (!secondCateID) {
      throw ERR.INVALID_PARAMS
    }
    let query = await Waste.query()
      .where('type', type)
      .where('source', source)
      .where('secondCateID', secondCateID)
      .fetch()
    response.json(query)
  }
  async show({ request, params, response }) {
    let { source = E.OrderSource.CaiNiao, type = E.Permission.Primary } = request.all()
    if (['超级管理员', '管理'].includes(type)) {
      type = '初级'
    }
    let vo = await Waste.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    // let data = await Waste.query()
    //   .where('type', type)
    //   .where('source', source)
    //   .where('secondCateID', vo.secondCateID)
    //   .first()
    response.json(vo)
  }
  async store({ request, response }) {
    let worker = request.worker
    let vo = await CompanySelfWastePrice.query()
      .where('companyID', worker.companyID)
      .where('priceType', 2)
      .fetch()
    response.json(vo)
  }
}

module.exports = WasteController
