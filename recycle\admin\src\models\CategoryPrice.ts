import { effect, requestDelete, requestGet, requestPost, requestPut } from 'dva17'
import { EDelete, EGet, EChangePlusPrice, EGetAttribute, EGetAttributeType, EPost, EPut, ESaveCommission, NCategoryPrice, NCompany, RAdd, RReset, RSetState, EGetCommission, EPostCommission, EPutCommission, EDelCommission, EPutbundlePrice, EPostBundleClientPrice, EPutClientPrice, EDelClientPrice, EPostClientPrice, EGetClientPrice } from '../common/action'
import { adapterPaginationResult } from '../common/utils'
import { getAttributeType, getAttribute, saveCommmisson, changePlusPrice, getCommission, postCommission, putCommission, delCommission, postBundlePrice, postBundleClientPrice, putClientCommission, delClientCommission, postClientCommission, getClientCommission, getClientEstimate, postClientEstimate, putClientEstimate, deleteClientEstimate } from '../services/price'

export default {
  namespace: NCategoryPrice,
  state: {
    buttons: [[], [], [], []],
    buttonIndexes: [-1, -1, -1, -1],
    editOne: null,
    attributeType: null,
    attribute: null,
    isSave: false,
    priceChange: false,
  },
  reducers: {
    [RSetState](state: any, payload: any) {
      return { ...state, ...payload }
    },
    [RReset](state: { count: any }, payload: any) {
      return { buttons: [[], [], [], []], buttonIndexes: [-1, -1, -1, -1], editOne: null }
    },
  },
  effects: {
    //  标准CURD示例
    async [EGet]({ state, payload: { level, id, index, type, source, area } }: any, { reducer }: any) {
      switch (level) {
        case 0:
          {
            let { data } = await requestGet('Waste1stCategory', {})
            reducer(RSetState, { buttons: state.buttons.map((v: any, i: number) => (i == level ? data : v)) })
          }
          break
        case 1:
          {
            let data = await requestGet('Waste1stCategory/' + id, {})
            reducer(RSetState, {
              buttons: state.buttons.map((v: any, i: number) => (i == level ? data : v)),
              buttonIndexes: state.buttonIndexes.map((v: number, i: number) => (i == level - 1 ? index : -1)),
            })
          }
          break
        case 2:
          {
            let data = await requestGet('waste2ndCategory/' + id, { type, source, area })
            reducer(RSetState, {
              buttons: state.buttons.map((v: any, i: number) => (i == level ? data : v)),
              buttonIndexes: state.buttonIndexes.map((v: number, i: number) => (i == level - 1 ? index : i >= level ? -1 : v)),
            })
          }
          break
        case 3:
          await effect(NCategoryPrice, EGetAttributeType, { id: id })
          reducer(RSetState, {
            buttonIndexes: state.buttonIndexes.map((v: number, i: number) => (i == level - 1 ? index : i >= level ? -1 : v)),
            editOne: state.buttons[2][index],
          })
          break
      }
    },
    //获取属性type
    async [EGetAttributeType]({ payload }: any, { reducer }: any) {
      const response = await getAttributeType(payload)
      reducer(RSetState, { attributeType: response })
    },
    //获取属性值
    async [EGetAttribute]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { attribute: null })
      const response = await getAttribute(payload)
      reducer(RSetState, { attribute: response })
    },
    //    佣金 or 基准价格
    async [ESaveCommission]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isSave: false })
      let res = await saveCommmisson(payload)
      reducer(RSetState, { isSave: true })
    },
    //改变属性价格
    async [EChangePlusPrice]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { priceChange: false })
      let res = await changePlusPrice(payload)
      reducer(RSetState, { priceChange: true })
    },
    //价格表
    async [EGetCommission]({ payload }: any, { reducer }: any) {
      let res = await getCommission(payload)
      return adapterPaginationResult(res)
    },
    async [EPostCommission]({ payload }: any, { reducer }: any) {
      let res = await postCommission(payload)
    },
    async [EPutbundlePrice]({ payload }: any, { reducer }: any) {
      let res = await postBundlePrice(payload)
    },
    async [EPutCommission]({ payload }: any, { reducer }: any) {
      let res = await putCommission(payload)
    },
    async [EDelCommission]({ payload }: any, { reducer }: any) {
      let res = await delCommission(payload)
    },

    //C端佣金
    async [EGetClientPrice]({ payload }: any, { reducer }: any) {
      let res = await getClientCommission(payload)
      return adapterPaginationResult(res)
    },
    async [EPostBundleClientPrice]({ payload }: any, { reducer }: any) {
      let res = await postBundleClientPrice(payload)
    },
    async [EPutClientPrice]({ payload }: any, { reducer }: any) {
      let res = await putClientCommission(payload)
    },
    async [EDelClientPrice]({ payload }: any, { reducer }: any) {
      let res = await delClientCommission(payload)
    },
    async [EPostClientPrice]({ payload }: any, { reducer }: any) {
      let res = await postClientCommission(payload)
    },

    // 获取客户端估价列表
    async ['EGetClientEstimate']({ payload }: any, { reducer }: any) {
      const response = await getClientEstimate(payload);
      return adapterPaginationResult(response);
    },

    // 创建客户端估价
    async ['EPostClientEstimate']({ payload }: any, { reducer }: any) {
      return await postClientEstimate(payload);
    },

    // 更新客户端估价
    async ['EPutClientEstimate']({ payload }: any, { reducer }: any) {
      const { id, ...data } = payload;
      return await putClientEstimate(payload);
    },

    // 删除客户端估价
    async ['EDeleteClientEstimate']({ payload }: any, { reducer }: any) {
      return await deleteClientEstimate(payload);
    },

  },
}
