import Taro, { useRouter } from '@tarojs/taro'
import { WebView } from '@tarojs/components'
import { useState, useEffect } from 'react'

/**
 * WebView页面组件
 * 用于在小程序内打开H5页面
 * 通过url参数传入要打开的网页地址
 */
export default function WebViewPage() {
  const router = useRouter()
  const [url, setUrl] = useState('')
  
  useEffect(() => {
    if (router.params && router.params.url) {
      setUrl(decodeURIComponent(router.params.url))
    }
  }, [router.params])
  
  return <WebView src={url} />
}
