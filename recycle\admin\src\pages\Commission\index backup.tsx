import { Button, Tabs, Tag, Space, message, Input, Select, notification } from 'antd'
import ProCard from '@ant-design/pro-card'
import { effect, reducer, requestGet, useConnect } from 'dva17'
import { useEffect, useState } from 'react'
import { EGet, ESaveCommission, NCategoryPrice, RReset, RSetState } from '../../common/action'
import { AreaList, orderSources, WorkerLevels } from '../../common/enum'
import { computeAuthority } from '../../utils/Authorized/authority'
const { Option } = Select

export default () => {
  const NAMES = ['选择大类型', '选择品类', '选择类型']
  const { buttons, buttonIndexes, editOne } = useConnect(NCategoryPrice)
  const [editPrice, setEditPrice] = useState<any>(0)
  let [saveList, setSaveList] = useState<any>([])
  const [type, setType] = useState<any>('中级')
  const [area, setArea] = useState<any>('浙江省')
  const [source, setSource] = useState<any>('菜鸟回收')
  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    if (editOne) {
      setEditPrice(editOne.commission)
    }
  }, [editOne])
  useEffect(() => {
    effect(NCategoryPrice, EGet, { level: 0 })
    return () => {
      reducer(NCategoryPrice, RReset)
    }
  }, [])
  useEffect(() => {
    let tempButtons = buttons
    tempButtons[2] = []
    effect(NCategoryPrice, RSetState, { buttons: tempButtons, editOne: null, })
  }, [buttonIndexes[0]])

  useEffect(() => {
    let tempButtons = buttons
    tempButtons[3] = []
    effect(NCategoryPrice, RSetState, { buttons: tempButtons, editOne: null, })
  }, [buttonIndexes[1]])
  /*--------------------- 响应 ---------------------*/
  const openNotificationWithIcon = () => {
    notification.success({
      message: '成功！',
      description: '修改成功',
      duration: 2
    })
  }
  const templeSaveList = (value: number, id: number) => {
    let listobj = [{ id: id, commission: value }]
    setEditPrice(value)
    setSaveList(listobj)
  }
  /*--------------------- 渲染 ---------------------*/
  return (
    <>
      <ProCard layout="default" bordered style={{ fontSize: '16px' }}>
        区域: <Select
          style={{ width: 150, marginRight: 10 }}
          defaultValue={area}
          onChange={async (e: string) => {
            setArea(e)
            reducer(NCategoryPrice, RReset)
            await effect(NCategoryPrice, EGet, { level: 0 })
          }}>{
            AreaList.map((vo: string, index: number) => {
              return (<Option key={vo}>{vo}</Option>)
            })}</Select>
        订单来源: <Select
          defaultValue={source}
          style={{ width: 150, marginRight: 10 }}
          onChange={async (e: string) => {
            setSource(e)
            reducer(NCategoryPrice, RReset)
            await effect(NCategoryPrice, EGet, { level: 0 })
          }}>{
            orderSources.map((vo: string, index: number) => {
              return (<Option key={vo}>{vo}</Option>)
            })}</Select>
        <Button
          type='primary'
          style={{ right: '25px', position: 'absolute' }}
          disabled={!computeAuthority('佣金编辑')}
          onClick={async () => {
            if (saveList.length >= 1) {
              await effect(NCategoryPrice, ESaveCommission, { list: saveList }).then((res) => {
                openNotificationWithIcon()
              })
            }
          }}>保存</Button>
      </ProCard>
      {NAMES.map((name: string, level: number) => {
        if (buttons[level].length) {
          return (
            <ProCard key={level} layout="default" bordered>
              {NAMES[level]}：
              {buttons[level].map((vo: any, i: number) => (
                <Button
                  style={{ borderRadius: '4px', margin: '20px 10px 0', cursor: 'pointer', fontSize: '16px', lineHeight: '6px' }}
                  type={i == buttonIndexes[level] ? 'primary' : undefined}
                  key={i}
                  onClick={() => {
                    effect(NCategoryPrice, EGet, { level: level + 1, id: vo.id, index: i, type, source, area })
                  }}>
                  {vo.name}
                </Button>
              ))}
            </ProCard>
          )
        }
      })}
      {editOne && (
        <ProCard layout="default" bordered>
          回收物佣金：
          <Input placeholder="输入系统信息费用"
            disabled={!computeAuthority('佣金编辑')}
            onChange={(e: any) => {
              templeSaveList(e?.target?.value, editOne.id)
            }}
            value={editPrice} style={{ width: 100 }} />
          {
            (buttonIndexes[0] === 0 || buttonIndexes[0] === 1) ? "元/KG" :
              (buttonIndexes[0] === 2 ? "元/台" : "%")
          }
        </ProCard>
      )}
    </>
  )
}
