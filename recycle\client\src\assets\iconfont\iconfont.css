/*


*/
/* @font-face {
  font-family: "iconfont";
  src: url("iconfont.eot?t=1584502290336");
  src: url("iconfont.eot?t=1584502290336#iefix") format("embedded-opentype"),
    
      url("data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAATwAAsAAAAACZAAAASiAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCDQAqGDIVAATYCJAMYCw4ABCAFhG0HWBtuCMgOJZHAwABgoGlmPHyu/XtukoXJ+8gKuOyAJaqyBlQ1vlWywrFRFQaFpP353/YewFfwXq8CV6G9DPRFxQWdObwuUreWdYS49VfHX1x/r2nqeMDq2re4vJfWG6VTJoDhAAWolHKNShPOgZyo3zK81qUu6DKBakMXxDUuLQ8iFWRWIF5ahWqIjBmUEkbo1W3OxgLxDsL0aSZ5C8Bb9/fjN+giEiRNBrLw9uOhdTDg+553KZTvv0+ZTQJhfzaw28g4BArxJtfxGuUnDlGqfL3CFaDqJen7nrcX3155l/J/QKEkS1akqvq/PLKiERLRAlGMYfp1ebN3QpMg8/0iQeH7FSw7/UgGguBHikECTdtj9Tj+EI+AfjgwFEmW91tGkUM7JcTWT0hSJIzdEwrRgbRF9ZY0uEMHgugA8Gl3d5XzZbl+28VSw/ZLHxXquXvVBwn/2FCtKB3ICwaNQeCTu4JvO8Mcl02d9Kj83n77lh3+ndJ237Z8D7eKFdBnH7V2sBZbL/etF3SzF9dnljbc+Ai0kkwUytiZIT5w48EB/8GHN6Xr9/f79t275qIX1RskR9pJEEvGNLHLcn6CEoMHHRYKlfUAOnrdCRWouOCpweBYvtOSQL26betPMwwZRzHSWX8dTTCFJE34d8z+R7YB1EwLqOeBun5mU1V7Y9qbd09vvPuTZqxsn2zqUGHUG5h/GnNuunmgIyNnAk7EE3I8FEGJx2jM/0YGvRF6flYpM8eO2zCklCzNaZ2pHDNOaR2WW2pohBzL6i2vtyxU18hAlmwYPHZM3pDWuUenzdTs6LCjo6k/AD5fdx2EAenDwPh+SrPBgjC4GfHdKMSSFkmykIBsZD0i1ih8d3VVELpPJUzhVX2qwht3seTm2rqMMZCDAkXj9HTC5G3f+gms5lbDE84dBiO7b1XhbGxC7DnzCDY84mfPAHXsGBWA0ruLUjrhDFkkSHGAol4ozYHsxbTS4D3mNRwYhGFoss+da7ekJINAB0NTr9P75PRJ/uUzmsxYubJVmL5ZJ+4Q17npEOOsQvct95PR3IpXCXzCOjPr1QquarhbfzSMAxj+5n0UBYaz+Rw1AGD4kUdRsWCIKcxcKpivUF3/ur/xdXbwUmVcn989owD4tvRoousfzhaoL/h1lGThPyas2VYUMTG2RWptVgi0zkZCEj9Uw7/lmfGyL9V87VJCbz6EpLMOWW+TXOiH0Bg5A63eRagOmLaPzBJRokQB+6YiCFP2QjLhA2RTbpML/QU0FnyG1lRQUN2JpANHtsNKDxZRNUZ1bM1o1lrrcvKGGdNeeQ4SvPZqMSp2mQVI9FjDrFalSaZNyInEPpZ4Rgg6jHmWF10OlkPXIbvdxbpF1zBUi1UWjN291Go+751UtS4H0K4SIdUwpA6rxmgsq1ouTrzdHaNT98+BCLzsqoklLWXeAojIw9o/S0tFU4NsUjhrtZzLcI8RBDoYxlPTeCIXBxYHNsSuJ1xY7vxxwyC1MBVLi5hbL2rcia+rUI2vclzlHqjIy9sgCYpQEGFEOBEB9SmqFnFX3mq3RzuQx1MtoMhqhdeDREWN116jsLgcCAAAAA==")
      format("woff2"),
    url("iconfont.woff?t=1584502290336") format("woff"),
    url("iconfont.ttf?t=1584502290336") format("truetype"),
    
      url("iconfont.svg?t=1584502290336#iconfont") format("svg");
} */

.icon {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-heart-fill:before {
  content: "\e849";
}

.icon-message-fill:before {
  content: "\e84a";
}

.icon-user:before {
  content: "\e7ae";
}

.icon-bulb:before {
  content: "\e7c3";
}

.icon-home:before {
  content: "\e7c6";
}
