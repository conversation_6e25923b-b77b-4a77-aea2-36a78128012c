const moment = require('moment')
const _ = require('lodash')
const Env = use('Env')
const { WorkerPayRefund, WorkerPay, Worker, User, OrderOperate } = require('../Models')
const WXService = require('./WXService');
const E = require('../../../constants/E');
const { Configs } = require('../Util');

const PAY_REFUND_ID_PREFIX = 'donggua_master_refund_'

const OrderService = {
  // 师傅微信退款
  async refundMasterPay(orderID, reason, amount = 0) {
    console.log('[退款]', orderID, amount)
    let pay = await WorkerPay.query().where('id', orderID).whereNotNull('transactionID').orderBy('refundAt', 'asc').first()
    if (pay) {
      let { id, workerID, totalPay, refundRMB, transactionID } = pay
      let needRefundRMB = amount || totalPay - refundRMB
      console.log('needRefundRMB: ', needRefundRMB)
      if (needRefundRMB) {
        let payRefund = await WorkerPayRefund.create({
          workerID,
          payID: id,
          refundRMB: needRefundRMB,
          totalRMB: totalPay,
          reason
        })
        pay.refundRMB += needRefundRMB
        console.log('pay.refundRMB: ', pay.refundRMB)
        await pay.save()
        // 发起微信退款
        let ret = await WXService.createMaterRefund(PAY_REFUND_ID_PREFIX + payRefund.id, totalPay, needRefundRMB, transactionID)
        console.log(ret, "师傅押金退款")
      }
    }
    return pay
  },
  //通知师傅
  //模板消息 师傅端订单提示
  async _remindMaster(worker, vo, status = E.OrderStatus.Reservation) {
    let page = `/pages/index/index`
    let messageID = Configs.MasterMessageID
    try {
      await WXService.sendWechat(
        Configs.WEAPP.AppID,
        Configs.WEAPP.AppSecret,
        worker.openid,
        vo.waste_1st_ID,
        vo.createdAt,
        vo.orderNo,
        status,
        page,
        messageID
      )
    } catch (error) {
      console.log(error);
    }
  },
  //记录订单操作及订阅消息的推送
  async _record(vo, status) {
    let user = await User.find(vo.userID)
    let page = `/pages/orderDetail/orderDetail?orderNumber=${vo.id}&manage=${status}`
    let messageID = Configs.ClientMessageID1
    WXService.sendClientWechat(
      Configs.Client.AppID,
      Configs.Client.AppSecret,
      user.openid,
      vo.waste_1st_ID,
      vo.createdAt,
      vo.orderNO,
      status,
      page,
      messageID
    )
    let record = await OrderOperate.create({ orderID: vo.id, status, info: '用户', companyID: vo.companyID, workerID: vo.workerID })
  }
}

module.exports = OrderService
