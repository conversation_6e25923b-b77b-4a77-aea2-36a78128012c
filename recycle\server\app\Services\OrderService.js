const moment = require('moment')
const _ = require('lodash')
const Env = use('Env')
const { WorkerPayRefund, WorkerPay, User, OrderOperate, PriceSetLog, Order, PriceSet } = require('../Models')
const WXService = require('./WXService');
const E = require('../../../constants/E');
const { Configs } = require('../Util');

const PAY_REFUND_ID_PREFIX = 'donggua_master_refund_'

const OrderService = {
  // 师傅微信退款
  async refundMasterPay(orderID, reason, amount = 0) {
    console.log('[退款]', orderID, amount)
    let pay = await WorkerPay.query().where('id', orderID).whereNotNull('transactionID').orderBy('refundAt', 'asc').first()
    if (pay) {
      let { id, workerID, totalPay, refundRMB, transactionID } = pay
      let needRefundRMB = amount || totalPay - refundRMB
      console.log('needRefundRMB: ', needRefundRMB)
      if (needRefundRMB) {
        let payRefund = await WorkerPayRefund.create({
          workerID,
          payID: id,
          refundRMB: needRefundRMB,
          totalRMB: totalPay,
          reason
        })
        pay.refundRMB += needRefundRMB
        console.log('pay.refundRMB: ', pay.refundRMB)
        await pay.save()
        // 发起微信退款
        let ret = await WXService.createMaterRefund(PAY_REFUND_ID_PREFIX + payRefund.id, totalPay, needRefundRMB, transactionID)
        console.log(ret, "师傅押金退款")
      }
    }
    return pay
  },
  //通知师傅
  //模板消息 师傅端订单提示
  async _remindMaster(worker, vo, status = E.OrderStatus.Reservation) {
    let page = `/pages/index/index`
    let messageID = Configs.MasterMessageID
    try {
      await WXService.sendWechat(
        Configs.WEAPP.AppID,
        Configs.WEAPP.AppSecret,
        worker.openid,
        vo.waste_1st_ID,
        vo.createdAt,
        vo.orderNo,
        status,
        page,
        messageID
      )
    } catch (error) {
      console.log(error);
    }
  },
  //记录订单操作及订阅消息的推送
  async _record(vo, status) {
    let user = await User.find(vo.userID)
    let page = `/pages/orderDetail/orderDetail?orderNumber=${vo.id}&manage=${status}`
    let messageID = Configs.ClientMessageID1
    WXService.sendClientWechat(
      Configs.Client.AppID,
      Configs.Client.AppSecret,
      user.openid,
      vo.waste_1st_ID,
      vo.createdAt,
      vo.orderNO,
      status,
      page,
      messageID
    )
    let record = await OrderOperate.create({ orderID: vo.id, status, info: '用户', companyID: vo.companyID, workerID: vo.workerID })
  },


  /**
   * 记录价格更新日志
   * @param {Object} params 价格更新参数
   */
  async logPriceUpdate(params) {
    const { action, type, area, source, level, price, id, adminID, remark } = params;

    // 获取旧数据（如果存在）
    let oldData = null;
    if (action === "update" || action === "delete") {
      const existingRecord = await PriceSet.find(id);
      if (existingRecord) {
        oldData = existingRecord.toJSON();
      }
    }

    // 准备新数据
    const newData = action !== "delete" ? { id, type, area, source, level, price } : null;

    // 根据操作类型记录日志
    if (action === "create") {
      await PriceSetLog.create({
        priceSetID: id,
        adminID: adminID,
        action: 'create',
        before_data: null,
        after_data: JSON.stringify(newData),
        remark: '后台创建佣金配置'
      });
    } else if (action === "update") {
      await PriceSetLog.create({
        priceSetID: id,
        adminID: adminID,
        action: 'update',
        before_data: JSON.stringify(oldData),
        after_data: JSON.stringify(newData),
        remark: remark || '后台更新佣金配置'
      });
    } else if (action === "delete") {
      await PriceSetLog.create({
        priceSetID: id,
        adminID: adminID,
        action: 'delete',
        before_data: JSON.stringify(oldData),
        after_data: null,
        remark: '后台删除佣金配置'
      });
    }
  },

  /**
   * 更新订单价格的独立函数
   * @param {string} area - 地区名称
   * @param {string} type - 订单类型
   * @param {string} source - 订单来源
   * @param {number} price - 新价格
   */
  async updateOrderPrices(area, type, source, price) {
    const validStatuses = ['进行中', '预订', '系统撤回', '师傅撤回']
    const updateData = { infoFee: price, commission: price }

    // 特殊地区处理
    if (area === "合肥市") {
      await Order.query()
        .whereIn('status', validStatuses)
        .where('city', '合肥市')
        .where('type', type)
        .where('from', source)
        .update(updateData)
      return
    } else if (area === "广州市" || area === "深圳市" || area === "珠海市" || area === "佛山市" || area === "东莞市" || area === "中山市" || area === "江门市" || area === "湛江市" || area === "茂名市" || area === "肇庆市" || area === "惠州市" || area === "梅州市" || area === "汕尾市" || area === "河源市" || area === "阳江市" || area === "清远市" || area === "潮州市" || area === "揭阳市" || area === "云浮市") {
      await Order.query()
        .whereIn('status', validStatuses)
        .where('city', area)
        .where('type', type)
        .where('from', source)
        .update(updateData)
      return
    } else if (area == "安徽省") {
      await Order.query()
        .whereIn('status', validStatuses)
        .where('province', "安徽省")
        .whereNot('city', "合肥市")
        .where('type', type)
        .where('from', source)
        .update(updateData)
      return
    } else if (area == "浙江省" || area == "江苏省" || area == "内蒙古自治区" || area == "宁夏回族自治区" || area == "甘肃省") {
      await Order.query()
        .whereIn('status', validStatuses)
        .where('province', area)
        .where('type', type)
        .where('from', source)
        .update(updateData)
      return
    }
    // 默认处理：按城市更新
    await Order.query()
      .whereIn('status', validStatuses)
      .where('city', area)
      .where('type', type)
      .where('from', source)
      .update(updateData)
  }
}

module.exports = OrderService
