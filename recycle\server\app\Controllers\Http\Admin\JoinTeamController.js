'use strict'

const _ = require('lodash')
const moment = require('moment')

const { JoinTeam } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

class JoinTeamController {
	async index({ request, response }) {
		let { current = 1, pageSize = 10, name, company, phone, forbidden } = request.all()
		let query = JoinTeam.query()
		if (name) {
			query.where('name', 'like', `%${name}%`)
		}
		if (company) {
			query.where('company', 'like', `%${company}%`)
		}
		if (phone) {
			query.where('phone', 'like', `%${phone}%`)
		}
		if (forbidden) {
			query.where('forbidden', forbidden)
		}
		let vo = await query.paginate(current, pageSize)
		response.json(vo)
	}
	async show({ request, params, response }) {
		let vo = await JoinTeam.query().where('id', params.id).fetch()
		if (!vo) {
			throw ERR.RESTFUL_GET_ID
		}
		response.json(vo)
	}
	async store({ request, response }) {
		let { name } = request.all()
		if (!name) {
			throw ERR.INVALID_PARAMS
		}
		let vo = await JoinTeam.create(request.all())
		response.json(vo)
	}
	async update({ request, params, response }) {
		let vo = await JoinTeam.find(params.id)
		if (!vo) {
			throw ERR.RESTFUL_UPDATE_ID
		}
		_.assign(vo, request.all())
		await vo.save()
		response.json(vo)
	}
	async destroy({ request, params, response }) {
		let vo = await JoinTeam.find(params.id)
		if (!vo) {
			throw ERR.RESTFUL_DELETE_ID
		}
		await vo.delete()
		response.json(vo)
	}
}

module.exports = JoinTeamController
