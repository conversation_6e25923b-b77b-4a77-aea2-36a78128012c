'use strict'

const _ = require('lodash')
const moment = require('moment')

const {
  Order,
  User,
  Worker,
  OrderWaste,
  OrderCancel,
  WorkerWalletLog,
  OrderLog,
  ReqLog,
  MetalPrice,
} = require('../../../Models/index')
const { ERR, E } = require('../../../../../../constants')
const Config = require('../../../Util/Config')
const { callBack } = require('../../../Util/DebugUtil')
const { _remindMaster } = require('../../../Services/OrderService')
const { SpecCODElIST, actions, OrderStatus } = require('../../../../../constants/E')
const Database = use('Database')

//订单
class OrderController {


  //扇形图数据 某个区间完成订单
  async chart({ request, response }) {
    let { startDate, endDate, page = 1, perPage = 10 } = request.all()
    let workerID = request.worker.id
    if (!workerID) {
      throw ERR.RESTFUL_GET_ID
    }
    let finish
    let finishList
    if (startDate && endDate) {
      finish = await Order.query()
        .where('workerID', workerID)
        .where('status', E.OrderStatus.Completed)
        .where('finishedAt', '>=', moment(startDate).toDate())
        .where('finishedAt', '<=', moment(endDate).add(1, 'd').toDate())
        .paginate(page, perPage)
      finishList = await Order.query().select('id', 'type', 'status', 'commission', 'infoFee', 'createdAt', 'finishedAt', 'workTime', 'workerID')
        .where('workerID', workerID)
        .where('status', E.OrderStatus.Completed)
        .where('finishedAt', '>=', moment(startDate).toDate())
        .where('finishedAt', '<=', moment(endDate).add(1, 'd').toDate())
        .fetch()
    } else {
      finish = await Order.query().where('workerID', workerID).where('status', E.OrderStatus.Completed).paginate(page, perPage)
      finishList = await Order.query().select('id', 'type', 'status', 'commission', 'infoFee', 'createdAt', 'finishedAt', 'workTime', 'workerID')
        .where('workerID', workerID).where('status', E.OrderStatus.Completed).fetch()
    }
    response.json({ count: finish.pages.total, finishList })
  }
  //订单详情
  async show({ request, params, response }) {
    let { worker } = request
    let { companyID, type } = worker
    let query = await Order.find(params.id)
    if (!query) {
      throw ERR.RESTFUL_GET_ID
    }
    let vo = await Order.query().where('id', params.id).with('cancel').with('doneInfo').with('worker').first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    vo = vo.toJSON()
    let trackList = []
    let trackerList = _.filter(SpecCODElIST, { type: vo.type })
    if (trackerList.length < 2) {
      trackerList = _.filter(SpecCODElIST, { type: "空调" })
    }
    trackerList.map((item) => {
      trackList.push(item.Ch)
    })
    let showScan = true
    if (vo.type === "电热水器" || vo.type === "燃气热水器" || vo.type === "空气热泵热水器") {
      showScan = false
    }
    response.json({ ...vo, trackList, showScan })
  }

  async orderPrice({ request, response, params }) {
    let { worker } = request
    let { status = OrderStatus.InProgress } = request.all()
    let data = await Order.query().where('status', status).where(worker.id).getSum('infoFee')
    return data
  }
  async comfirmOrder({ request, response }) {
    let { worker } = request
    let { companyID } = worker
    let { id, remuneration, imageUrl, infoFee, payMoney, remark, sign, unit, isManager } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: 'APP 管理订单确认' })
    imageUrl = JSON.stringify(imageUrl)
    let data = await Order.find(id)
    if (data.toJSON().status === OrderStatus.Cancelled || data.toJSON().status === OrderStatus.Completed) {
      throw ERR.UNLOCK
    }
    let uniqOrder = await WorkerWalletLog.query().where("orderID", id).where("isClient", 0).first()
    if (uniqOrder) {
      throw ERR.INVALID_PARAMS;
    }
    let workerVO
    let moneyRes
    if (isManager) {
      workerVO = await Worker.find(isManager)
      await WorkerWalletLog.create({
        workerID: isManager,
        money: -infoFee,
        remuneration,
        commission: infoFee,
        imageUrl,
        payMoney,
        companyID,
        remark: `小工${worker.workerName}完成订单`,
        sign,
        unit,
        orderID: id,
      })
    } else {
      workerVO = await Worker.find(worker.id)
      await WorkerWalletLog.create({
        workerID: worker.id,
        money: -infoFee,
        remuneration,
        commission: infoFee,
        imageUrl,
        payMoney,
        companyID,
        remark: remark || '师傅完成订单',
        sign,
        unit,
        orderID: id,
      })
    }
    moneyRes = parseFloat(workerVO.toJSON().wallet) - parseInt(infoFee)
    workerVO.wallet = moneyRes
    await workerVO.save()
    data.status = '完成'
    data.infoFee = infoFee
    data.commission = infoFee
    data.finishedAt = moment().format('YYYY-MM-DD HH:mm:ss')
    await data.save()
    await OrderLog.create({
      status: E.OrderLogStatus.Completed,
      orderID: id,
      workerID: worker.id,
      content: remark,
    })
    response.json(workerVO)
    data = data.toJSON()
    await ReqLog.create({ res: JSON.stringify(workerVO), source: 'APP 管理订单确认' })
    let content = {
      "tracesList": [{
        "logisticProviderID": data.cpCode,
        "extendFields": "",
        "traces": [{
          "extendFields": [
            { "value": 1, "key": "cnRecycleType", "desc": "回收类型" },
            { "value": data.apprizeAmount, "key": "apprizeAmount", "desc": "回收金额" }
          ],
          "country": "China", "city": data.city,
          "facilityType": "1",
          "facilityName": Config.companySelfName,
          "tz": "+8",
          "action": actions.TMS_CHECK.name,
          "desc": actions.TMS_CHECK.remark, "contacter": worker.workerName,
          "contactPhone": worker.mobile, "outBizCode": moment().format('x'),
          "time": moment().format('YYYY-MM-DD HH:mm:ss')
        }], "txLogisticID": data.orderNo, "mailNos": data.mailNo
      }]
    }
    if (data.from === "菜鸟回收") {
      await callBack("TRACEPUSH", data.cpCode, content)
    }
    await _remindMaster(workerVO, data, E.OrderStatus.Completed)
  }

  //订单创建
  async store({ request, response }) {
    let {
      userID,
      estimatedMoney,
      waste_1st_ID,
      orderWastes,
      userName,
      from,
      sentinelAddress,
      unit,
      wasteType,
      commission,
      remuneration,
    } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: 'APP 管理订单创建' })
    let user = await User.findBy({ id: userID })
    if (!user) {
      throw ERR.RESTFUL_GET_ID
    }
    let worker = request.worker
    if (!userID || !waste_1st_ID || !orderWastes || !userName || !from || !sentinelAddress || !wasteType) {
      throw ERR.INVALID_PARAMS
    }
    let workTime = moment(new Date()).format('YYYY-MM-DD') + ' ' + '13:00'
    const createData = {
      userID,
      workerID: worker.id,
      companyID: worker.companyID,
      waste_1st_ID,
      wasteType,
      estimatedMoney,
      userName,
      from,
      sentinelAddress,
      unit,
      workTime,
      remuneration,
      commission,
    }
    if (userData.salesman_id) {
      createData.salesman_id = userData.salesman_id
    }

    let vo = await Order.create(createData)
    vo.status = E.OrderStatus.InProgress
    vo.orderNo = moment().format('YYYYMMDD') + vo.id
    if (orderWastes && orderWastes.length > 0) {
      orderWastes.forEach((waste, index) => {
        waste.orderID = vo.id
      })
      await vo.orderWastes().delete()
      orderWastes.forEach(async function (itm) {
        let orderWaste = new OrderWaste()
        _.assign(orderWaste, itm)
        await orderWaste.save()
      })
    }
    await vo.save()
    response.json(vo)
    await ReqLog.create({ res: JSON.stringify(vo), source: 'APP 管理订单创建' })
  }



  async getRevenue({ request, response }) {
    let { worker } = request
    let { companyID, type } = worker
    let { startDate, endDate, platform } = request.all()
    if (worker.isUse !== "同意" || worker.type !== "管理") {
      throw ERR.AUTH_FAILED
    }
    let query = Order.query()
      .select(Database.raw('id,commission,createdAt '))
      .whereNot('status', '取消')
      .whereBetween('createdAt', [moment(startDate).add(1, 'days').format('YYYY-MM-DD'), moment(endDate).format('YYYY-MM-DD')])
    switch (type) {
      case '超级管理员':
        break
      case '管理':
        query.where('companyID', companyID)
        break
      case '师傅':
        return false
      default:
        break
    }
    if (platform) {
      if (platform === '全部' || platform === 'null' || platform === '0' || platform === 'undefined') {
        console.log('ALL')
      } else {
        query.where('from', platform)
      }
    }
    let revenue = await query.fetch()
    let count = await query.getCount()
    let countAll = await query.getSum('commission')
    return { revenues: revenue, len: count, countAll }
  }
  async managerOrder({ request, response }) {
    let { startDate, endDate, platform, delay, page = 1, perPage = 10, cancel, status } = request.all()
    let query = Order.query().with('worker').with('cancel')
    let { worker } = request
    delay = parseInt(delay)
    cancel = parseInt(cancel)
    if (worker.isUse !== "同意" || worker.type !== "管理") {
      throw ERR.AUTH_FAILED
    }
    if (cancel) {
      // 已取消+退单订单
      query.where('workerID', null)
    }
    if (delay) {
      query.where('workTime', '<', moment().format('YYYY-MM-DD'))
    }
    if (status) {
      query.where('status', status)
    }
    if (startDate && endDate) {
      query.whereBetween('createdAt', [moment(startDate).add(1, 'days').format('YYYY-MM-DD'), moment(endDate).format('YYYY-MM-DD')])
    }
    if (platform) {
      if (platform === '全部' || platform === 'null' || platform === '0' || platform === 'undefined') {
        console.log('ALL')
      } else {
        query.where('from', platform)
      }
    }
    let revenue = await query.paginate(page, perPage)
    response.json(revenue)
  }



  // 已测试通过
  // 师傅订单列表
  async index({ request, response }) {
    let { page = 1, perPage = 20, workDate, startDate, endDate, status, orderNo, sort = 'desc', whoCancel, date, keyword } = request.all()
    let workerID = request.worker && request.worker.id
    workDate = parseInt(workDate)
    if (!workerID) {
      return ERR.USER_NOT_EXISTS
    }
    let query = Order.query().where('workerID', workerID)
    if (status) {
      if (status === E.OrderStatus.Pending) {
        query.where('status', E.OrderStatus.Reservation)
        if (startDate && endDate) {
          query.whereBetween('createdAt', [moment(startDate).format('YYYY-MM-DD'), moment(endDate).add(1, 'days').format('YYYY-MM-DD')])
        }
      } else if (status === E.OrderStatus.Dispatched) {
        let orderLog = await OrderLog.query()
          .select('status', 'orderID', 'content', 'createdAt', 'workerID')
          .where('workerID', workerID)
          .whereIn('status', [E.OrderLogStatus.Remark, E.OrderLogStatus.Call, E.OrderLogStatus.RESCHEDULE])
          .fetch()

        let orderLogList = orderLog.rows.map((item) => {
          return item.orderID
        })
        // 去重
        orderLogList = [...new Set(orderLogList)]
        query.where('status', E.OrderStatus.InProgress).whereNotIn('id', orderLogList)
        if (startDate && endDate) {
          query.whereBetween('workTime', [moment(startDate).format('YYYY-MM-DD'), moment(endDate).add(1, 'days').format('YYYY-MM-DD')])
        }
      } else if (status === E.OrderStatus.InProgress) {
        let orderLog = await OrderLog.query()
          .select('status', 'orderID', 'content', 'createdAt', 'workerID')
          .where('workerID', workerID)
          .whereIn('status', [E.OrderLogStatus.Remark, E.OrderLogStatus.Call, E.OrderLogStatus.RESCHEDULE])
          .fetch()
        let orderLogList = orderLog.rows.map((item) => {
          return item.orderID
        })
        // 去重
        orderLogList = [...new Set(orderLogList)]
        query.where('status', E.OrderStatus.InProgress).whereIn('id', orderLogList)
        if (startDate && endDate) {
          query.whereBetween('workTime', [moment(startDate).format('YYYY-MM-DD'), moment(endDate).add(1, 'days').format('YYYY-MM-DD')])
        }
      } else if (status === E.OrderStatus.TransferOrder) {
        throw ERR.INVALID_PARAMS
      } else {
        query.where('status', status).with('doneInfo')
        if (startDate && endDate) {
          query.whereBetween('finishedAt', [moment(startDate).format('YYYY-MM-DD'), moment(endDate).add(1, 'days').format('YYYY-MM-DD')])
        }
      }
    } else if (orderNo) {
      query.where('orderNo', orderNo)
    } else if (whoCancel) {
      let cancelList = await OrderCancel.query().where('workerID', workerID).where('whoCancel', whoCancel).fetch()
      let orderList = []
      cancelList.rows.forEach((vlaue) => {
        orderList.push(vlaue.orderID)
      })
      query = Order.query().whereIn('id', orderList)
    } else {
      query.orderBy('id', 'desc')
    }
    if (keyword) {
      query.whereRaw('keywords like ?', [`%${keyword}%`])
    }
    if (date) {
      query.whereBetween('workTime', [moment(date).format('YYYY-MM-DD'), moment(date).add(1, 'days').format('YYYY-MM-DD')])
    }
    if (status === E.OrderStatus.InProgress) {
      switch (workDate) {
        case 0:
          query.where('workTime ', '<=', moment().add(1, 'days').format('YYYY-MM-DD'))
          break;
        case 1:
          query.whereBetween('workTime', [moment().add(1, 'days').format('YYYY-MM-DD'), moment().add(2, 'days').format('YYYY-MM-DD')])
          break;
        case 2:
          query.whereBetween('workTime', [moment().add(2, 'days').format('YYYY-MM-DD'), moment().add(60, 'days').format('YYYY-MM-DD')])
          break;
        default:
          break;
      }
      query.orderBy('workTime', 'asc')
    } else if (status === E.OrderStatus.Completed) {
      query.orderBy('finishedAt', 'desc')
    } else {
      query.orderBy('id', sort)
    }
    let vo = await query.with('worker').with('cancel').paginate(page, perPage)
    response.json(vo)
  }
  // 获取订单数量
  async getOrderCount({ request, response }) {
    let { startDate, endDate, keyword } = request.all()
    let { worker } = request
    let orderLog = await OrderLog.query()
      .select('status', 'orderID', 'content', 'createdAt', 'workerID')
      .where('workerID', worker.id)
      .whereIn('status', [E.OrderLogStatus.Remark, E.OrderLogStatus.Call, E.OrderLogStatus.RESCHEDULE])
      .fetch()

    let orderLogList = orderLog.rows.map((item) => {
      return item.orderID
    })
    // 去重
    orderLogList = [...new Set(orderLogList)]

    let orderCount = {
      pending: 0,
      contacting: 0,
      in_progress: 0,
      completed: 0
    }
    // 待接单
    let pcount = Order.query()
      .where('workerID', worker.id)
      .where('status', E.OrderStatus.Reservation)
    if (startDate && endDate) {
      pcount.whereBetween('createdAt', [moment(startDate).format('YYYY-MM-DD'), moment(endDate).add(1, 'days').format('YYYY-MM-DD')])
    }
    if (keyword) {
      pcount.whereRaw('keywords like ?', [`%${keyword}%`])
    }
    orderCount.pending = await pcount.getCount()
    // 联系中
    let ccount = Order.query()
      .where('workerID', worker.id)
      .whereNotIn('id', orderLogList)
      .where('status', E.OrderStatus.InProgress)
    if (startDate && endDate) {
      ccount.whereBetween('workTime', [moment(startDate).format('YYYY-MM-DD'), moment(endDate).add(1, 'days').format('YYYY-MM-DD')])
    }
    if (keyword) {
      ccount.whereRaw('keywords like ?', [`%${keyword}%`])
    }
    orderCount.contacting = await ccount.getCount()
    // 进行中
    let icount = Order.query()
      .whereIn('id', orderLogList)
      .where('workerID', worker.id)
      .where('status', E.OrderStatus.InProgress)
    if (startDate && endDate) {
      icount.whereBetween('workTime', [moment(startDate).format('YYYY-MM-DD'), moment(endDate).add(1, 'days').format('YYYY-MM-DD')])
    }
    if (keyword) {
      icount.whereRaw('keywords like ?', [`%${keyword}%`])
    }
    orderCount.in_progress = await icount.getCount()
    // 已完成
    let completedCount = Order.query()
      .where('workerID', worker.id)
      .where('status', E.OrderStatus.Completed)
    if (startDate && endDate) {
      completedCount.whereBetween('finishedAt', [moment(startDate).format('YYYY-MM-DD'), moment(endDate).add(1, 'days').format('YYYY-MM-DD')])
    }
    if (keyword) {
      completedCount.whereRaw('keywords like ?', [`%${keyword}%`])
    }
    orderCount.completed = await completedCount.getCount()

    return orderCount
  }
  // 获取首页数据
  async getIndexData({ request, response }) {
    let workerID = request.worker.id
    console.log("workerID", workerID);

    const data = {
      copperPrice: 54.45,
      copperChange: 0.5,
      aluminumPrice: 18.25,
      pendingOrders: 5,
      contactOrders: 3,
      inProgressOrders: 8,
      statistics: {
        completedOrders: 112,
        cancelledOrders: 8,
        inProgressOrders: 8,
        typeList: [
          { value: 80, name: '空调' },
          { value: 25, name: '冰箱' },
          { value: 20, name: '洗衣机' },
          { value: 15, name: '电视' },
          { value: 10, name: '其他' }
        ],
      },
      calendar: [
        { date: '09/15', count: 5 },
        { date: '09/16', count: 3 },
        { date: '09/17', count: 2 },
        { date: '09/18', count: 0 },
        { date: '09/19', count: 1 },
      ]
    }

    const copperPrice = await MetalPrice.query().orderBy('createdAt', 'desc').first()
    const aluminumPrice = await MetalPrice.query().orderBy('createdAt', 'desc').first()
    data.copperPrice = parseFloat(copperPrice.toJSON().average)
    data.copperChange = parseFloat(copperPrice.toJSON().vchange)
    data.aluminumPrice = parseFloat(aluminumPrice.toJSON().average)

    let orderLog = await OrderLog.query()
      .select('status', 'orderID', 'content', 'createdAt', 'workerID')
      .where('workerID', workerID)
      .whereIn('status', [E.OrderLogStatus.Remark, E.OrderLogStatus.Call, E.OrderLogStatus.RESCHEDULE])
      .fetch()

    let orderLogList = orderLog.rows.map((item) => {
      return item.orderID
    })
    // 去重
    orderLogList = [...new Set(orderLogList)]
    let orderCount = {
      pending: 0,
      contacting: 0,
      in_progress: 0,
    }
    // 待接单
    orderCount.pending = await Order.query()
      .where('workerID', workerID)
      .where('status', E.OrderStatus.Reservation)
      .getCount()
    // 联系中
    orderCount.contacting = await Order.query()
      .where('workerID', workerID)
      .whereNotIn('id', orderLogList)
      .where('status', E.OrderStatus.InProgress)
      .getCount()
    // 进行中
    orderCount.in_progress = await Order.query()
      .whereIn('id', orderLogList)
      .where('workerID', workerID)
      .where('status', E.OrderStatus.InProgress)
      .getCount()
    // 已完成
    orderCount.completed = await Order.query()
      .where('workerID', workerID)
      .where('status', E.OrderStatus.Completed)
      .getCount()

    data.pendingOrders = orderCount.pending
    data.contactOrders = orderCount.contacting
    data.inProgressOrders = orderCount.in_progress
    // 本月取消订单
    let cancelOrdercount = await Order.query().where('workerID', workerID)
      .where('status', E.OrderStatus.Cancelled)
      .whereBetween('createdAt', [moment().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
      moment().endOf('month').format('YYYY-MM-DD HH:mm:ss')])
      .select(Database.raw('COUNT(*) AS count'))
      .first()

    data.statistics.cancelledOrders = cancelOrdercount.toJSON() ? cancelOrdercount.toJSON().count : 0
    // 本月完成订单
    let completedOrdercount = await Order.query().where('workerID', workerID)
      .whereBetween('finishedAt', [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')])
      .where('status', E.OrderStatus.Completed)
      .getCount()
    data.statistics.completedOrders = completedOrdercount
    let calendar = await Order.query()
      .select(
        Database.raw('DATE_FORMAT(createdAt, "%Y-%m-%d") as date, COUNT(*) as count,status')
      )
      .whereNotIn('status', [E.OrderStatus.Cancelled, E.OrderStatus.Completed])
      .where('workerID', workerID)
      .whereBetween('createdAt', [moment().startOf('week').format('YYYY-MM-DD'), moment().endOf('week').format('YYYY-MM-DD')])
      .groupBy('date')
      .orderBy('date', 'asc')
      .fetch()
    data.calendar = calendar

    let typeList = await Order.query().where('workerID', workerID)
      .select(Database.raw('type as name,COUNT(*) as value,status,createdAt,workerID'))
      .whereNotIn('status', [E.OrderStatus.Cancelled])
      .whereBetween('createdAt', [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')])
      .groupBy('name')
      .fetch()
    data.statistics.typeList = typeList
    response.json(data)
  }
  // 订单备注
  async orderRemark({ request, response }) {
    let workerID = request.worker.id
    let { id, remark } = request.all()
    let vo = await OrderLog.create({
      status: E.OrderLogStatus.Remark,
      orderID: id,
      workerID: workerID,
      content: remark,
    })
    response.json(vo)
  }
  // 师傅拨打电话
  async orderCall({ request, response }) {
    let workerID = request.worker.id
    let { id } = request.all()
    let vo = await OrderLog.create({
      status: E.OrderLogStatus.Call,
      orderID: id,
      workerID: workerID,
      content: '拨打客户电话',
    })
    response.json(vo)
  }
  //订单撤回
  async orderBack({ request, response }) {
    let { worker } = request
    let { id: workerID } = worker
    let { whoCancel = '师傅撤销', cancelReason, orderID } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: 'APP 管理撤回订单' })
    let order = await Order.find(orderID)
    if (!order) {
      throw ERR.RESTFUL_GET_ID
    }
    if (order.toJSON().status === E.OrderStatus.Cancelled || order.toJSON().status === E.OrderStatus.Completed) {
      return ({ error: 4003, message: '订单已取消或已完成，请刷新页面' })
    }
    order.workerID = null
    order.status = E.OrderStatus.MasterReturn
    let cancel = await OrderCancel.create({ orderID, workerID, cancelReason, whoCancel, companyID: order.companyID, userID: order.userID })
    await order.save()
    response.json(cancel)
    order = order.toJSON()
    await ReqLog.create({ res: JSON.stringify(order), source: 'APP 管理撤回订单' })
    let workerInfo = await Worker.find(workerID)
    await _remindMaster(workerInfo, order, E.OrderLogStatus.Recall)
    await OrderLog.create({
      status: E.OrderLogStatus.Recall,
      orderID,
      workerID: worker.id,
      content: cancelReason,
    })
  }
  // 获取订单日志
  async getOrderLog({ request, response, params }) {
    let { worker } = request
    if (!params.id) {
      throw ERR.API_ERROR
    }
    let logList = await OrderLog.query().with('user').with('worker').with('creater').with('order')
      .where('orderID', params.id)
      .where('workerID', worker.id)
      .orderBy('createdAt', 'asc')
      .fetch()
    response.json(logList)
  }
  //订单数据更新
  async update({ request, params, response }) {
    let { status, whoCancel, cancelReason, workTime } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: 'APP 管理订单数据更新' })
    let workerID = request.worker.id
    let { worker } = request
    let companyID = worker.companyID
    let vo = await Order.query().where('id', params.id).first()
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    if (vo.toJSON().status === E.OrderStatus.Cancelled || vo.toJSON().status === E.OrderStatus.Completed) {
      return ({ error: 4003, message: '订单已取消或已完成，请刷新页面' })
    }

    // // 判断是否是空调品类订单且准备接单，检查师傅是否有保险
    // if (status === E.OrderStatus.InProgress && vo.type === '空调') {
    //   // 查询师傅是否有保险
    //   const workerInsure = await WorkerInsure.query().where('workerID', workerID).first()
    //   if (!workerInsure || !workerInsure.files || workerInsure.files.length === 0) {
    //     return ({ error: 4005, message: '您没有上传保险文件，无法接空调品类订单' })
    //   }
    // }
    if (status === E.OrderStatus.InProgress || status === E.OrderStatus.Completed || status === E.OrderStatus.Cancelled) {
      if (status === E.OrderStatus.Cancelled) {
        let cancel = await OrderCancel.create({ orderID: vo.id, workerID, cancelReason, whoCancel, companyID: vo.companyID })
      }
    }
    let obj = request.all()
    delete obj.list
    delete obj.cancelReason
    delete obj.whoCancel

    if (status === E.OrderStatus.InProgress) {
      obj.workerID = workerID
    }
    if (whoCancel === E.CancleOrderStatus.MasterRevoke) {
      let cancel = await OrderCancel.create({ orderID: vo.id, workerID, cancelReason, whoCancel, companyID: vo.companyID })
    }
    if (workTime) {
      workTime = moment(workTime).format('YYYY-MM-DD HH:mm:ss')
      obj.workTime = workTime
      await OrderLog.create({
        status: E.OrderLogStatus.RESCHEDULE,
        orderID: vo.id,
        workerID: workerID,
        content: "改约到" + workTime,
      })
      let cvo = {
        "tracesList": [{
          "logisticProviderID": vo.cpCode, "extendFields": "", "traces": [{
            "country": "China", "city": vo.city, "tz": "+8",
            "remark": "用户改约", "province": vo.province,
            "extendFields": [{
              "value": 1,
              "key": "cnRecycleType",
              "desc": "回收类型"
            },
            {
              "value": workTime && moment(workTime).format('YYYY-MM-DD'), "key": "reserveTime",
              "desc": "预约上门时间"
            }], "action": "TMS_RESCHEDULE",
            "facilityName": Config.companySelfName, "facilityType": "1", "contacter": worker.workerName,
            "outBizCode": moment().format('x'),
            "time": moment().format('YYYY-MM-DD HH:mm:ss'), "contactPhone": worker.mobile, "desc": "预约上门时间"
          }], "txLogisticID": vo.orderNo, "mailNos": vo.mailNo
        }]
      }
      if (vo.from === "菜鸟回收") {
        callBack('TRACEPUSH', vo.cpCode, cvo)
      }
    }
    _.assign(vo, obj)
    await vo.save()
    response.json(vo)
    await ReqLog.create({ res: JSON.stringify(vo), source: 'APP 管理订单数据更新' })
    //  师傅接单
    if (status === E.OrderStatus.InProgress) {
      await OrderLog.create({
        status: E.OrderLogStatus.AcceptOrder,
        orderID: vo.id,
        workerID: worker.id,
        content: '师傅接单',
      })
      vo.takeTime = moment().format('YYYY-MM-DD HH:mm:ss')
      await vo.save()
    }
  }
  // 师傅改派
  async orderChange({ request, response }) {
    let { worker } = request
    if (worker.isUse !== "同意") {
      throw ERR.AUTH_FAILED
    }
    let { orderID, companyID, workerID, remark } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: 'APP 管理订单变更' })
    let query = Order.query().where('id', orderID)
    let order = await query.update({ companyID, workerID })
    let vo = await Worker.find(workerID)
    response.json(vo)
    let data = await OrderLog.create({
      status: E.OrderLogStatus.TransferOrder,
      orderID,
      workerID: worker.id,
      content: `管理员:${worker.workerName}转派给师傅${vo.workerName} 备注:` + remark,
    })
    // 短信通知用戶改派了
    let user = await User.find(order.userID)
    if (user) {
      await Sms.send({
        phone: user.mobile,
        message: `您的订单已改派给师傅${vo.workerName}，请注意查收。`
      })
    }
    response.json(data)
  }

  // 获取订单地址
  async getOrderAddressList({ request, response }) {
    let { worker } = request
    let order = await Order.query()
      .select('id', 'address', 'status', 'createdAt', 'workerID', 'type', 'userName', 'userMobile', 'orderNo')
      .where('workerID', worker.id)
      .whereIn('status', [E.OrderStatus.InProgress, E.OrderStatus.Dispatched])
      .fetch()
    if (!order) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(order)
  }
}

module.exports = OrderController
