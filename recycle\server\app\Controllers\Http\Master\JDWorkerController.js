'use strict'

const _ = require('lodash')
const moment = require('moment')
const Env = use('Env')

const { JDWorker } = require('../../../Models')
const { ERR, E } = require('../../../../constants')
const { WXService } = require('../../../Services')
const { CryptUtil, Configs } = require('../../../Util')

//上门师傅信息
class JDWorkerController {
  async index({ request, response }) {
    let { page = 1, perPage = 10, name, isUse = E.WorkerAccountStatus.Agree } = request.all()
    let worker = request.worker
    let query = JDWorker.query().with('company', builder => {
      builder.select('id', 'companyName', 'city')
      return builder
    })
    if (!worker.companyID) {
      throw ERR.USER_EXISTS
    }
    if (worker.type !== "超级管理员") {
      query.where('companyID', worker.companyID)
    }
    if (name) {
      query.select('id', 'type', 'mobile', 'workerName', 'companyID', 'wallet', 'city').where('workerName', 'like', `%${name}%`)
    }
    let vo = await query.where('isUse', E.WorkerAccountStatus.Agree).fetch()
    response.json(vo)
  }
  async login({ request, response }) {
    let { openID, code, token } = request.all()
    let vo = null
    if (code) {
      let { result, error } = await WXService.code2Session(code, Configs.JDMaster.AppID, Configs.JDMaster.AppSecret)
      if (error) {
        throw error
      } else {
        openID = result.openid
        vo = await JDWorker.findBy('openid', result.openid)
      }
    } else if (openID) {
      vo = await JDWorker.findBy('openid', openID)
    } else if (token) {
      const payload = CryptUtil.jwtDecode(request.header('Authorization') || request.input('token'))
      vo = await JDWorker.find(payload.userID)
    }
    if (!vo) {
      response.json({
        token: 0,
        openID
      })
    } else {
      response.json({
        token: CryptUtil.jwtEncode({ userID: vo.id }),
        openID,
        user: vo
      })
    }
  }
  async register({ request, response }) {
    try {
      let { openid } = request.all()
      if (!openid) {
        throw ERR.INVALID_PARAMS
      }
      let isReg = await JDWorker.findBy('openid', openid)
      if (isReg) {
        throw ERR.USER_EXISTS
      }

      let vo = await JDWorker.create({ openid })
      response.json({
        token: CryptUtil.jwtEncode({ userID: vo.id }),
        user: vo
      })
    } catch (e) {
      response.json(e)
    }
  }
  async show({ request, params, response }) {
    let vo = await JDWorker.query().where('id', params.id).first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
  //worker信息更新
  async update({ request, params, response }) {
    // console.log(params);
    let { mobile, workerName } = request.all()
    let vo = await JDWorker.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    if (mobile) {
      let passwd = mobile.slice(-6)
      let enPw = CryptUtil.md5(CryptUtil.encryptData256(passwd, Env.get('APP_KEY')))
      _.assign(vo, request.all(), { isUse: E.WorkerAccountStatus.Init, password: enPw })
    } else {
      _.assign(vo, request.all(), { isUse: E.WorkerAccountStatus.Init })
    }
    await vo.save()
    response.json(vo)
  }

  async loginBypasswd({ request, response }) {
    let { phone, passwd, newPasswd } = request.all()
    let enPw = CryptUtil.md5(CryptUtil.encryptData256(passwd, Env.get('APP_KEY')))
    let vo = await JDWorker.query().where('mobile', phone).where('password', enPw).first()
    if (!vo) {
      throw ERR.SQL_DUP_NAME_OR_PASSWORD
    }
    if (newPasswd) {
      vo.password = CryptUtil.md5(CryptUtil.encryptData256(newPasswd, Env.get('APP_KEY')))
      await vo.save()
    }
    response.json({
      token: CryptUtil.jwtEncode({ userID: vo.id }),
      openID: vo.openid,
      user: vo
    })
  }
}

module.exports = JDWorkerController
