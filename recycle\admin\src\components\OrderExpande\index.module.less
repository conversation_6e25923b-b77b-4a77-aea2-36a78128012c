.item_wrapper {
  .item_wrapper_reasonList {
    display: flex;
    flex-wrap: wrap;

    .reasonList_item {
      border: 1px solid #1890ff;
      border-radius: 3px;
      padding: 5px 10px;
      cursor: pointer;
      margin-right: 15px;
    }
  }
  .item_log {
    display: inline-block;
    width: 90px;
    text-align: right;
    font-weight: 600;
    font-weight: bolder;
    cursor: pointer;
    text-decoration:underline;
    color: #1890ff;
  }
  .item {
    display: flex;
    margin-bottom: 20px;
    letter-spacing: 0.8px;

    // flex-wrap: wrap;
    //border-bottom: 1px solid #cccccc;
    &:last-child {
      border-bottom: none;
    }

    .item_title {
      display: inline-block;
      width: 90px;
      text-align: right;
      font-weight: 600;
    }

    .item_content {
      display: flex;
      flex: 1;
      flex-wrap: wrap;

      .item_pic {
        height: 100px;
        width: 100px;
        border-radius: 4px;
        margin-right: 10px;
        margin-bottom: 10px;
        box-shadow: 0 2px 4px #cccccc;
      }

      .item_content_type {
        display: inline-block;
        width: 110px;
        height: 34px;
        line-height: 34px;
        text-align: center;
        border-radius: 4px;
        border: 1px solid #1890ff;
        color: #1890ff;
        margin: 0 8px 8px 0;
      }

      .waste_wrapper {
        width: 100%;

        .waste_wrapper_title {
          width: 100%;
          display: flex;
          font-weight: 600;

          .title {
            width: 50%;
            text-align: center;
          }
        }
      }

      .content_wrapper {
        width: 100%;
        display: flex;
        margin: 10px 0;

        .name {
          width: 50%;
          text-align: center;
        }

        .price {
          width: 50%;
          text-align: center;
        }
      }
    }
  }
}
