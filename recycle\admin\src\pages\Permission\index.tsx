import { PlusOutlined, TeamOutlined } from '@ant-design/icons'
import { Button, Tabs, Tag, Space, message, Switch, Select, notification, Modal, Input, Tree } from 'antd'
import type { ActionType, ProColumns } from '@ant-design/pro-table'
import ProTable, { TableDropdown } from '@ant-design/pro-table'
import ProCard from '@ant-design/pro-card'
import { effect, requestGet, useConnect } from 'dva17'
import { useEffect, useRef, useState } from 'react'
import { EGet, NAdminUser, NUser } from '../../common/action'
import { BasicHave, WorkerLevels } from '../../common/enum'
import { createAccount, editAccount, getPermissionContent, savePermission } from '../../services/permission'
import ProForm, { ProFormInstance, ProFormSelect, ProFormText } from '@ant-design/pro-form'
import { computeAuthority } from '../../utils/Authorized/authority'
const { Option } = Select
const { TreeNode } = Tree
type Item = {
  id: number
  username: string
  name: string
  authority: {
    name: string
    id: number
  }
  forbidden: number
  createdAt: string
  company: {
    companyName: string
  }
}


export default () => {
  const [currentGrade, setCurrentGrade] = useState<string>('初级')
  const [showData, setShowData] = useState<any>(null)
  const { currentUser } = useConnect(NUser)
  const [visible, setVisible] = useState<boolean>(false)
  const [visibleEdit, setVisibleEdit] = useState<boolean>(false)
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(false)
  const [expandedKeys, setExpandedKeys] = useState<any>(['订单管理', '回收价格管理', '佣金管理', '回收人员管理', '服务商查看', '账户的查看', '数据查询', '押金管理', '帖子操作'])
  const [checkedKeys, setCheckedKeys] = useState<any>([])
  const [selectedKeys, setSelectedKeys] = useState<any>([])
  const [treeData, setTreeData] = useState<any>([])
  const [permissionContent, setPermissionContent] = useState<any>(null)
  const actionRef = useRef<ActionType>()
  const formRef = useRef<ProFormInstance>();
  const columns: ProColumns<Item>[] = [
    { title: '账户', dataIndex: 'username', copyable: false, ellipsis: true },
    { title: '姓名', dataIndex: 'name', copyable: false, ellipsis: true },
    {
      title: '公司', dataIndex: 'company', copyable: false, ellipsis: true,
      render: (_, row) => (
        <>
          {row?.company?.companyName}
        </>
      ),
    },
    {
      title: '权限',
      dataIndex: 'authority',
      copyable: false,
      ellipsis: true,
      search: false,
      render: (_, { id, authority: { name } }) => (
        <Select
          onChange={async (e: any) => {
            await editAccount({ id: id, permission: e }).then(() => {
              noticePut()
            })
          }} defaultValue={name} style={{ width: 120 }}>
          {WorkerLevels.map((v, i) => (
            <Option key={i} value={v}>
              {v}
            </Option>
          ))}
        </Select>
      ),
    },
    {
      title: '是否启用',
      dataIndex: 'forbidden',
      copyable: false,
      ellipsis: true,
      search: false,
      render: (_, { forbidden, id }) => <Switch
        checkedChildren="启动" unCheckedChildren="停用"
        defaultChecked={Boolean(forbidden)}
        disabled={!computeAuthority('权限编辑') || currentUser.level == '服务商'}
        onChange={
          async (e: any) => {
            await editAccount({ id: id, forbidden: e ? 1 : 0 }).then(() => {
              noticePut()
            })
          }}
      />,
    },
    { title: '创建时间', dataIndex: 'createdAt', copyable: false, ellipsis: true, search: false },
    {
      title: '操作',
      width: '30%',
      copyable: false,
      ellipsis: true,
      search: false,
      render: (_, row) => (
        <>
          <Button onClick={() => {
            handleEdit(row)
          }}>重置账户</Button>
        </>
      ),
    },
  ]
  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    if (permissionContent) {
      let value = JSON.parse(permissionContent.key)
      setCheckedKeys(value)
    }
  }, [permissionContent])
  /*--------------------- 响应 ---------------------*/
  const showModalPermission = async () => {
    let permissionContent = await getPermissionContent({ name: '初级' })
    setPermissionContent(permissionContent);
    setTreeData(BasicHave)
    setVisibleEdit(true)
  }
  const noticePut = () => {
    notification.success({
      message: '成功！',
      description: '修改成功',
      duration: 2
    })
    refreshPage()
  }
  const refreshPage = () => {
    if (actionRef.current) {
      actionRef.current.reload()
    }
  }
  const handleEdit = async (row: any) => {
    setVisible(true)
    setShowData(row)
  }
  const handleOK = async () => {
    const val2 = await formRef.current?.validateFieldsReturnFormatValue?.();
    console.log('validateFieldsReturnFormatValue:', val2);
    if (!val2.password || !val2.newPassword || !val2.username || !val2.permission) {
      message.error({ content: '请重新输入!' })
    } else if (val2.newPassword !== val2.password) {
      message.error({ content: '密码不正确! 请重新输入!' })
    } else {
      if (showData) {
        await editAccount({ ...val2, id: showData.id })
      } else {
        await createAccount(val2)
      }
      setVisible(false)
    }
    noticePut()
  }

  const gradeChange = async (e: any) => {
    console.log('e', e)
    setCurrentGrade(e)
    let permissionContent = await getPermissionContent({ name: e })
    setPermissionContent(permissionContent);
  }
  const handleSave = async () => {
    console.log('save')
    console.log({
      name: currentGrade,
      key: JSON.stringify(checkedKeys)
    })
    await savePermission({
      name: currentGrade,
      key: JSON.stringify(checkedKeys)
    }).then(() => {
      message.success('提交成功')
      setVisibleEdit(false)
    })
  }
  //tree
  const onExpand = (expandedKeys: any) => {
    console.log('onExpand', expandedKeys)
    setAutoExpandParent(false)
    setExpandedKeys(expandedKeys)
  }

  const onCheck = (checkedKeys: any) => {
    console.log('onCheck', checkedKeys)
    setCheckedKeys(checkedKeys)
  }

  const onSelect = (selectedKeys: any, info: any) => {
    console.log('onSelect', selectedKeys, info)
    setSelectedKeys(selectedKeys)
  }

  const renderTreeNodes = (data: any) =>
    data.map((item: any) => {
      if (item.children) {
        return (
          <TreeNode title={item.title} key={item.key} >
            {renderTreeNodes(item.children)}
          </TreeNode>
        )
      }
      return <TreeNode key={item.key} {...item} />
    })

  /*--------------------- 渲染 ---------------------*/
  return (
    <ProCard>
      <ProTable<Item>
        actionRef={actionRef}
        columns={columns}
        request={async (params = {}) => (await effect(NAdminUser, EGet, params)) as any}
        pagination={{
        }}
        rowKey="id"
        dateFormatter="string"
        headerTitle=""
        toolBarRender={() => [
          <Button key="3" type="primary"
            disabled={!computeAuthority('权限编辑') || currentUser.level == '服务商'}
            onClick={() => {
              showModalPermission()
            }}>
            <TeamOutlined />
            权限配置
          </Button>,
          <Button key="3" type="primary"
            disabled={!computeAuthority('账户新建与编辑') || currentUser.level == '服务商'}
            onClick={() => {
              setVisible(true)
              setShowData(null)
            }}>
            <PlusOutlined />
            新建
          </Button>,
        ]}
      />
      <Modal
        destroyOnClose={true}
        open={visible}
        title={`${showData ? '编辑账号' : '新建账号'}`}
        onCancel={() => {
          setVisible(false)
          setShowData(null)
        }}
        onOk={async () => {
          await handleOK()
        }}
      >
        <div style={{ width: '100%' }}>
          <ProForm
            submitter={false}
            formRef={formRef}
          >
            <ProFormText
              initialValue={showData ? (showData.name) : null}
              width="md"
              name="name"
              label="姓名"
              tooltip="最长为 24 位"
              placeholder="请输入姓名"
            />
            <ProFormText
              initialValue={showData ? (showData.username) : null}
              width="md"
              name="username"
              label="账号"
              tooltip="最长为 24 位"
              placeholder="请输入账号"
            />
            <ProFormText.Password
              name="password"
              width="md"
              label="密码"
              placeholder="请输入密码"
            />
            <ProFormText.Password
              name="newPassword"
              width="md"
              label="确认密码"
              placeholder="请确认密码"
            />
            <ProFormSelect
              initialValue={showData ? WorkerLevels[parseInt(showData.authorityID) - 1] : null}
              options={
                WorkerLevels.map((vo: any) => {
                  return {
                    value: vo,
                    label: vo,
                  }
                })
              }
              name="permission"
              width="md"
              label="等级"
            />
          </ProForm>
        </div>
      </Modal>
      <Modal
        destroyOnClose
        title='权限配置'
        open={visibleEdit}
        footer={null}
        onCancel={() => setVisibleEdit(false)}
      >
        <div>
          <span>权限等级：</span>
          <Select style={{ width: 300 }}
            disabled={!computeAuthority('账户新建与编辑')}
            onChange={(e) => gradeChange(e)} defaultValue="初级">
            {WorkerLevels.map((value: any, index: number) => (
              <Select.Option
                label={value}
                value={value} key={value}>
                {value}
              </Select.Option>
            ))}
          </Select>
          <h3 style={{ marginTop: 20 }}>权限</h3>
          <Tree
            checkable
            onExpand={onExpand}
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            onCheck={onCheck}
            checkedKeys={checkedKeys}
            onSelect={onSelect}
            selectedKeys={selectedKeys}>
            {renderTreeNodes(treeData)}
          </Tree>
          <Button
            disabled={!computeAuthority('账户新建与编辑')}
            style={{ marginTop: 20, background: '#3' }}
            onClick={() => {
              handleSave()
            }}>
            保存
          </Button>
        </div>
      </Modal>
    </ProCard>
  )
}
