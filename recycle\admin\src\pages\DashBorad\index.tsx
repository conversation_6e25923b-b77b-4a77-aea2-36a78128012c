// import * as echarts from 'echarts';
import type { ProColumns } from '@ant-design/pro-table'
import ProCard from '@ant-design/pro-card'
import { effect, reducer, useConnect } from 'dva17'
import { useEffect, useRef, useState } from 'react'
import { EGet, EGetCharts, EGetData, EGetLineCharts, EGetList, NCollection, NCompany, NOrder, NWorker, RSetState, EGetHiProvinceCharts, EGetHiChannelCharts, EGetHiCategoryCharts } from '../../common/action'
import { Line, Pie } from '@ant-design/charts';
import { Card, Col, DatePicker, Row, Select, Statistic, Divider } from 'antd'
import {
  ShoppingCartOutlined,
  WalletOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  BarChartOutlined,
  DollarOutlined,
  RiseOutlined,
  FallOutlined,
  ShopOutlined,
  TeamOutlined,
  AppstoreOutlined,
  TagOutlined
} from '@ant-design/icons'
import styles from './index.module.less'
import { provinceList } from '../../common/enum'

const { Option } = Select
const RangePicker: any = DatePicker.RangePicker;

export default () => {
  const { dashData, chartData, lineData, lastSearch, hiProvinceData, hiChannelData, hiCategoryData } = useConnect(NCollection)
  const { companyList } = useConnect(NCompany)
  const [selectProvince, setSelectProvince] = useState<any>(null)
  const [selectComp, setSelectComp] = useState<any>(null)

  const configpie = {
    forceFit: true,
    title: {
      visible: true,
    },
    description: {
      visible: true,
    },
    radius: 0.8,
    data: chartData,
    angleField: 'count',
    colorField: 'type',
    label: {
      text: (d: any) => `${d.type}\n ${d.count}`,
      position: 'outside',
      textAlign: 'center',
    },
    legend: {
      color: {
        title: 'type',
        position: 'left',
        rowPadding: 5,
      },
    },
  };

  const configline = {
    title: {
      visible: true,
    },
    description: {
      visible: true,
    },
    padding: 'auto',
    forceFit: true,
    data: lineData,
    xField: 'date',
    yField: 'count',
    legend: { position: 'top' },
    colorField: 'type',
    responsive: true,
    slider: {
      x: {},
      // y: { labelFormatter: '~s' },
    },
  };

  // 嗨回收省份分布图表配置
  const hiProvinceConfig = {
    forceFit: true,
    radius: 0.8,
    data: hiProvinceData,
    angleField: 'count',
    colorField: 'province',
    label: {
      text: (d: any) => `${d.province}\n ${d.count}`,
      position: 'outside',
      textAlign: 'center',
    },
    legend: {
      color: {
        title: '省份',
        position: 'left',
        rowPadding: 5,
      },
    },
  };

  // 嗨回收渠道分布图表配置  
  const hiChannelConfig = {
    forceFit: true,
    radius: 0.8,
    data: hiChannelData,
    angleField: 'count',
    colorField: 'channel',
    label: {
      text: (d: any) => `${d.channel}\n ${d.count}`,
      position: 'outside',
      textAlign: 'center',
    },
    legend: {
      color: {
        title: '渠道',
        position: 'left',
        rowPadding: 5,
      },
    },
  };

  // 嗨回收品类分布图表配置
  const hiCategoryConfig = {
    forceFit: true,
    radius: 0.8,
    data: hiCategoryData,
    angleField: 'count',
    colorField: 'category',
    label: {
      text: (d: any) => `${d.category}\n ${d.count}`,
      position: 'outside',
      textAlign: 'center',
    },
    legend: {
      color: {
        title: '品类',
        position: 'left',
        rowPadding: 5,
      },
    },
  };

  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    effect(NCollection, EGetData, {})
    effect(NCollection, EGetCharts, {})
    effect(NCollection, EGetLineCharts, {})
    effect(NCollection, EGetHiProvinceCharts, {})
    effect(NCollection, EGetHiChannelCharts, {})
    effect(NCollection, EGetHiCategoryCharts, {})
    effect(NCompany, EGet, { pageSize: 50, forbidden: 1 })

    return () => {
      reducer(NCollection, RSetState, { lastSearch: {} })
    }
  }, [])
  /*--------------------- 响应 ---------------------*/
  // 省市选择
  function funSelectProvince(e: any) {
    setSelectProvince(e)
    effect(NCollection, EGetCharts, { ...lastSearch, province: e })
    effect(NCollection, EGetLineCharts, { ...lastSearch, province: e })
  }
  // 公司
  function funSelectComp(e: any) {
    setSelectComp(e)
    effect(NCollection, EGetCharts, { ...lastSearch, companyID: e })
    effect(NCollection, EGetLineCharts, { ...lastSearch, companyID: e })
  }
  /*--------------------- 渲染 ---------------------*/
  const tstyle = { fontSize: 35, fontWeight: 'bolder' }
  const dstyle = { fontWeight: 'bolder' }
  return (
    <ProCard>
      <Row gutter={[24, 24]}>
        {/* 核心业务指标 */}
        <Col span={24}>
          <div className="section-title">
            <AppstoreOutlined className="title-icon" />
            <span>核心业务指标</span>
          </div>
          <Row gutter={[24, 24]}>
            <Col span={6}>
              <Card className="stat-card core-metric" hoverable>
                <Statistic
                  title={
                    <div className="stat-title">
                      <ShoppingCartOutlined className="stat-icon shopping" />
                      <span>今日进单</span>
                    </div>
                  }
                  value={dashData?.todayCount}
                  suffix="单"
                  valueStyle={{ color: '#1890ff' }}
                />
                <div className="stat-footer">
                  <span>累计</span>
                  <span className="value highlight-blue">{dashData?.totalCount}单</span>
                </div>
              </Card>
            </Col>
            <Col span={6}>
              <Card className="stat-card core-metric" hoverable>
                <Statistic
                  title={
                    <div className="stat-title">
                      <CheckCircleOutlined className="stat-icon success" />
                      <span>今日完单</span>
                    </div>
                  }
                  value={dashData?.CompletedCount}
                  suffix="单"
                  valueStyle={{ color: '#52c41a' }}
                />
                <div className="stat-footer">
                  <span>昨日完单</span>
                  <span className="value highlight-green">{dashData?.yedCompletedCount}单</span>
                  <Divider type="vertical" />
                  <span>累计完单</span>
                  <span className="value highlight-green">{dashData?.totalCompletedCount}单</span>
                </div>
              </Card>
            </Col>
            <Col span={6}>
              <Card className="stat-card core-metric" hoverable>
                <Statistic
                  title={
                    <div className="stat-title">
                      <ClockCircleOutlined className="stat-icon warning" />
                      <span>待处理</span>
                    </div>
                  }
                  value={dashData?.PendingCount}
                  suffix="单"
                  valueStyle={{ color: '#faad14' }}
                />
                <div className="stat-footer">
                  <span>进行中</span>
                  <span className="value highlight-warning">{dashData?.InProgressCount}单</span>
                </div>
              </Card>
            </Col>
            <Col span={6}>
              <Card className="stat-card core-metric" hoverable>
                <Statistic
                  title={
                    <div className="stat-title">
                      <CloseCircleOutlined className="stat-icon danger" />
                      <span>今日取消</span>
                    </div>
                  }
                  value={dashData?.CancelledCount}
                  suffix="单"
                  valueStyle={{ color: '#ff4d4f' }}
                />
                <div className="stat-footer">
                  <span>昨日取消</span>
                  <span className="value highlight-red">{dashData?.yedCancelledCount}单</span>
                </div>
              </Card>
            </Col>
          </Row>
        </Col>

        {/* 财务指标 */}
        <Col span={24}>
          <div className="section-title">财务指标</div>
          <Row gutter={[24, 24]}>
            <Col span={8}>
              <Card className="stat-card" hoverable>
                <Statistic
                  title="今日充值"
                  value={dashData?.todayCharge / 100}
                  prefix={<WalletOutlined />}
                  suffix="元"
                  precision={0}
                  valueStyle={{ color: '#3f8600' }}
                />
                <div className="stat-footer">
                  <span>本周充值</span>
                  <span className="value">￥{dashData?.weekCharge / 100}</span>
                </div>
              </Card>
            </Col>
            <Col span={8}>
              <Card className="stat-card" hoverable>
                <Statistic
                  title="本月完工率"
                  value={dashData?.mcomplete}
                  prefix={<BarChartOutlined />}
                  suffix="%"
                  valueStyle={{ color: '#3f8600' }}
                />
                <div className="stat-footer">
                  <span>本月取消率</span>
                  <span className="value">{dashData?.mcancel}%</span>
                </div>
              </Card>
            </Col>
            <Col span={8}>
              <Card className="stat-card" hoverable >
                <Statistic
                  title="今日完工金额"
                  value={dashData?.channelAmount}
                  prefix={<DollarOutlined />}
                  suffix="元"
                  precision={0}
                  valueStyle={{ color: '#3f8600' }}
                />
                <div className="stat-footer">
                  <span>本月累计完工</span>
                  <span className="value">￥{dashData?.monthChannelAmount}</span>
                </div>
              </Card>
            </Col>
          </Row>
        </Col>

        {/* 数据趋势分析 */}
        <Col span={24}>
          <Row gutter={[24, 24]}>
            <Col span={16}>
              <Card
                className={'alignCard'}
                title={'品类分布'}
                extra={
                  <div className="filter-container">
                    <Select
                      allowClear
                      value={selectComp}
                      style={{ width: 200 }}
                      onChange={funSelectComp}>
                      {companyList.map((val: any, index: any) => (
                        <Option key={index} value={val.id}>{val.companyName}</Option>
                      ))}
                    </Select>
                    <Select
                      allowClear
                      value={selectProvince}
                      style={{ width: 200 }}
                      onChange={funSelectProvince}>
                      {provinceList.map((province: any, index: any) => (
                        <Option key={index} value={province.name}>{province.name}</Option>
                      ))}
                    </Select>
                    <RangePicker
                      onChange={async (value: any, dateString: any) => {
                        if (dateString[1]) {
                          await effect(NCollection, EGetCharts, { ...lastSearch, startDate: dateString[0], endDate: dateString[1] })
                        } else {
                          await effect(NCollection, EGetCharts, {})
                        }
                      }}
                    />
                  </div>
                }
              >
                <Pie {...configpie} style={{ height: '400px' }} />
              </Card>
            </Col>
            <Col span={8}>
              <div className={styles.card_box} style={{ display: 'flex', flexDirection: 'column', gap: 10 }}>
                <Card className="stat-card channel-card" hoverable title={
                  <div className="channel-title">
                    <span>京东</span>
                  </div>
                }>
                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <Statistic
                        title="今日订单量"
                        value={dashData?.JDtodayOrderCount || 0}
                        prefix={<ShoppingCartOutlined />}
                        suffix="单"
                        valueStyle={{ color: '#3f8600', fontSize: '20px' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="今日完工金额"
                        value={dashData?.JDchannelAmount}
                        prefix={<DollarOutlined />}
                        suffix="元"
                        precision={0}
                        valueStyle={{ color: '#3f8600', fontSize: '20px' }}
                      />
                    </Col>
                  </Row>
                  <Divider style={{ margin: '12px 0' }} />
                  <div className="stat-footer">
                    <div className="footer-item">
                      <span>本月订单量</span>
                      <span className="value">{dashData?.JDmonthOrderCount || 0}单</span>
                    </div>
                    <div className="footer-item">
                      <span className="amount">月完工金额：￥{dashData?.JDmonthChannelAmount}</span>
                    </div>
                  </div>
                </Card>
                <Card className="stat-card channel-card" hoverable title={
                  <div className="channel-title">
                    <span>自营</span>
                  </div>
                }>
                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <Statistic
                        title="今日订单量"
                        value={dashData?.ZYtodayOrderCount || 0}
                        prefix={<ShoppingCartOutlined />}
                        suffix="单"
                        valueStyle={{ color: '#3f8600', fontSize: '20px' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="今日完工金额"
                        value={dashData?.ZYchannelAmount}
                        prefix={<DollarOutlined />}
                        suffix="元"
                        precision={0}
                        valueStyle={{ color: '#3f8600', fontSize: '20px' }}
                      />
                    </Col>
                  </Row>
                  <Divider style={{ margin: '12px 0' }} />
                  <div className="stat-footer">
                    <div className="footer-item">
                      <span>本月订单量</span>
                      <span className="value">{dashData?.ZYmonthOrderCount || 0}单</span>
                    </div>
                    <div className="footer-item">
                      <span className="amount">月完工金额：￥{dashData?.ZYmonthChannelAmount}</span>
                    </div>
                  </div>
                </Card>
                <Card className="stat-card channel-card" hoverable title={
                  <div className="channel-title">
                    <span>嗨回收</span>
                  </div>
                }>
                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <Statistic
                        title="今日订单量"
                        value={dashData?.HiTodayOrderCount || 0}
                        prefix={<ShoppingCartOutlined />}
                        suffix="单"
                        valueStyle={{ color: '#3f8600', fontSize: '20px' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="今日完工金额"
                        value={dashData?.HiChannelAmount}
                        prefix={<DollarOutlined />}
                        suffix="元"
                        precision={0}
                        valueStyle={{ color: '#3f8600', fontSize: '20px' }}
                      />
                    </Col>
                  </Row>
                  <Divider style={{ margin: '12px 0' }} />
                  <div className="stat-footer">
                    <div className="footer-item">
                      <span>本月订单量</span>
                      <span className="value">{dashData?.HiMonthOrderCount || 0}单</span>
                    </div>
                    <div className="footer-item">
                      <span className="amount">月完工金额：￥{dashData?.HiMonthChannelAmount}</span>
                    </div>
                  </div>
                </Card>
              </div>
            </Col>


            {/* 嗨回收数据分布分析 */}
            <Col span={24} className={styles['hi-distribution']}>
              <div className={styles['distribution-section']}>
                <div className={styles['section-header']}>
                  <BarChartOutlined className={styles['header-icon']} />
                  <span className={styles['header-title']}>嗨回收数据分布分析</span>
                </div>
                <Row gutter={[24, 24]}>
                  <Col span={8}>
                    <Card
                      className={`${styles['distribution-card']} ${styles['alignCard']}`}
                      title={'省份分布'}
                      extra={
                        <div className={styles['filter-container']}>
                          <RangePicker
                            onChange={async (value: any, dateString: any) => {
                              if (dateString[1]) {
                                await effect(NCollection, EGetHiProvinceCharts, { ...lastSearch, startDate: dateString[0], endDate: dateString[1] })
                              } else {
                                await effect(NCollection, EGetHiProvinceCharts, {})
                              }
                            }}
                          />
                        </div>
                      }
                    >
                      <div className={styles['chart-container']}>
                        <Pie {...hiProvinceConfig} style={{ height: '300px' }} />
                      </div>
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card
                      className={`${styles['distribution-card']} ${styles['alignCard']}`}
                      title={'渠道分布'}
                      extra={
                        <div className={styles['filter-container']}>
                          <RangePicker
                            onChange={async (value: any, dateString: any) => {
                              if (dateString[1]) {
                                await effect(NCollection, EGetHiChannelCharts, { ...lastSearch, startDate: dateString[0], endDate: dateString[1] })
                              } else {
                                await effect(NCollection, EGetHiChannelCharts, {})
                              }
                            }}
                          />
                        </div>
                      }
                    >
                      <div className={styles['chart-container']}>
                        <Pie {...hiChannelConfig} style={{ height: '300px' }} />
                      </div>
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card
                      className={`${styles['distribution-card']} ${styles['alignCard']}`}
                      title={'品类分布'}
                      extra={
                        <div className={styles['filter-container']}>
                          <RangePicker
                            onChange={async (value: any, dateString: any) => {
                              if (dateString[1]) {
                                await effect(NCollection, EGetHiCategoryCharts, { ...lastSearch, startDate: dateString[0], endDate: dateString[1] })
                              } else {
                                await effect(NCollection, EGetHiCategoryCharts, {})
                              }
                            }}
                          />
                        </div>
                      }
                    >
                      <div className={styles['chart-container']}>
                        <Pie {...hiCategoryConfig} style={{ height: '300px' }} />
                      </div>
                    </Card>
                  </Col>
                </Row>
              </div>
            </Col>
            <Col span={24}>
              <Card
                className={'alignCard'}
                title={'业务趋势'}
                extra={
                  <div className="filter-container">
                    <Select
                      allowClear
                      value={selectComp}
                      style={{ width: 200 }}
                      onChange={funSelectComp}>
                      {companyList.map((val: any, index: any) => (
                        <Option key={index} value={val.id}>{val.companyName}</Option>
                      ))}
                    </Select>
                    <Select
                      value={selectProvince}
                      allowClear
                      style={{ width: 200 }}
                      onChange={funSelectProvince}>
                      {provinceList.map((province: any, index: any) => (
                        <Option key={index} value={province.name}>{province.name}</Option>
                      ))}
                    </Select>
                    <RangePicker
                      onChange={async (value: any, dateString: any) => {
                        if (dateString[1]) {
                          await effect(NCollection, EGetLineCharts, { ...lastSearch, startDate: dateString[0], endDate: dateString[1] })
                        } else {
                          await effect(NCollection, EGetLineCharts, {})
                        }
                      }}
                    />
                  </div>
                }
              >
                <Line {...configline} style={{ height: '400px' }} />
              </Card>
            </Col>
          </Row>
        </Col>
      </Row>
    </ProCard>
  )
}