---
globs: *.ts,*.tsx
description: TypeScript + React + Ant Design 开发规范（管理后台）
---

# TypeScript + React + Ant Design 规范

## 适用范围
- `recycle/admin/` - 后台管理系统

## 代码风格
- 使用 TypeScript 进行开发，优先使用 interfaces 而非 types
- 避免使用 enums，使用 maps 提供更好的类型安全性
- 使用函数式、声明式编程模式
- 使用描述性变量名称，带辅助动词 (如 isLoading, hasError)
- 优先使用 function 关键字定义纯函数

## 组件开发
- 使用函数组件配合 TypeScript 接口
- 组件文件结构：导出组件、子组件、辅助函数、静态内容、类型定义
- 目录命名使用小写加破折号 (如 auth-wizard)
- 优先使用命名导出

## 错误处理
- 在函数开始处理错误和边界情况
- 使用早期返回避免深层嵌套 if 语句
- 使用守卫子句处理前置条件和无效状态
- 避免不必要的 else 语句，使用 if-return 模式

## Ant Design Pro 组件
- 使用 ProTable, ProForm 等 pro-components
- 配合 DVA 进行状态管理
- 统一的表单验证和数据处理

## 示例组件结构
```typescript
interface OrderManageProps {
  loading?: boolean;
  dataSource: Order[];
}

export function OrderManage({ loading = false, dataSource }: OrderManageProps) {
  // 早期返回处理边界情况
  if (!dataSource?.length) {
    return <Empty description="暂无数据" />;
  }

  return (
    <ProTable<Order>
      columns={columns}
      dataSource={dataSource}
      loading={loading}
    />
  );
}
```

## 关键文件路径
- 页面组件：[recycle/admin/src/pages/](mdc:recycle/admin/src/pages/)
- 业务组件：[recycle/admin/src/components/](mdc:recycle/admin/src/components/)
- 数据模型：[recycle/admin/src/models/](mdc:recycle/admin/src/models/)
- 服务接口：[recycle/admin/src/services/](mdc:recycle/admin/src/services/)