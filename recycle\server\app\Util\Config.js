const Configs = {
  WEAPP: {
    //师傅端的appId 及 appSecret
    AppID: 'wxf328c677f1460502',
    AppSecret: '********************************'
  },
  Client: {
    //用户端
    AppID: 'wx78f1a38d505aac56',
    AppSecret: '********************************'
  },
  ClientMessageID1: 'KhwysVMAAUVoYCIWLK29Yn2EopVXu3missGIQMPfaJ0',
  MasterMessageID: 'AGY008C2JnxXczgjDYLV3jObd4cQ2r3wsaBCrzb9xA4',
  // CaiNiaoLink: 'http://linkdaily.tbsandbox.com/bifrost/link.do',//彩虹橋
  CaiNiaoLink: "http://link.cainiao.com/gateway/link.do",//正式
  // CaiNiaoLink: " https://prelink.cainiao.com/gateway/link.do",//预发布
  // ASKey: '5N187N7W8Mb899Z8S6YB11I0j4HUNL0V', //彩虹橋
  ASKey: '177A5Ss9KCpJ9rq6Q2xFPrOn2jLJs315',//正式/预发布
  companySelfName:"憬橙环保",
  companySelfHuman:"王银生",
  companySelfPhone:"15216778005",
  sendCode:"SMS_470200157",//普通热线  
  // sendCode:"SMS_470090163",//    400熱綫  
  callNumber:'4001861266',
  YUNQIXUN_USER_NAME:"11211",
  YUNQIXUN_PASSWORD:"TGmL4i7i",
  totalPay: 20500,
  ClientContent: {
    touser: '',
    template_id: '',
    page: 'index',
    // miniprogram_state: 'trial',
    lang: 'zh_CN',
    scene: '100',
    title: '订单状态提醒',
    data: {
      character_string5: {
        value: '*********'
      },
      thing2: {
        value: '大家电'
      },
      time14: {
        value: '2019年10月1日'
      },
      thing3: {
        value: '进行中'
      }
    }
  },
  Content: {
    touser: '',
    template_id: '',
    page: 'index',
    // miniprogram_state: 'trial',
    lang: 'zh_CN',
    scene: '100',
    title: '订单状态提醒',
    data: {
      character_string5: {
        value: '*********'
      },
      thing2: {
        value: '大家电'
      },
      date8: {
        value: '2019年10月1日'
      },
      thing4: {
        value: '进行中'
      }
    }
  }
}
module.exports = Configs
