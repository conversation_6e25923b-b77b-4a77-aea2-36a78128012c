'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Waste } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//订单废品关联表
class OrderWasteController {
  async index({ request, response }) {
    let { current = 1, pageSize = 10 } = request.all()
    let query = OrderWaste.query()
    let vo = await query.paginate(current, pageSize)
    response.json(vo)
  }
  async show({ request, params, response }) {
    let vo = await OrderWaste.query()
      .where('id', params.id)
      .first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
  async store({ request, response }) {
    let { list } = request.all()
    // console.log('list', list)
    _.forEach(list, async function(value) {
      let vo = await Waste.find(value.id)
      _.assign(vo, value)
      await vo.save()
    })
    response.json({ result: 'ok' })
  }
  async update({ request, params, response }) {
    let { list } = request.all()
    return list
  }
  async destroy({ request, params, response }) {
    let vo = await OrderWaste.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_DELETE_ID
    }
    await vo.delete()
    response.json(vo)
  }
}

module.exports = OrderWasteController
