import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'

export async function getOrderList(payload:any) {
  return requestGet('/order', payload)
}
export async function changeOrderStatus(payload:any) {
  return requestPut(`/order/${payload.id}`, payload)
}
export async function changeWorker(payload:any) {
  return requestPut(`/changeWorker/${payload.id}`, payload)
}
export async function changeOrderImportStatus(payload:any) {
  return requestPut(`/orderimport/${payload.id}`, payload)
}

export async function orderMaintainUpdate(payload:any) {
  return requestPut(`/orderMaintainUpdate/${payload.id}`, payload)
}
export async function orderMaintainList(payload:any) {
  return requestGet('/orderMaintainList', payload)
}
  
export async function getWhichWorkers(payload:any) {
  return requestPost('/worker/whichWorkers', payload)
}
export async function sendSMS(payload:any) {
  return requestPost('sms', payload)
}
export async function getWorkerDetail(payload:any) {
  return requestGet(`worker/${payload.id}`)
}
export async function orderBack(payload:any) {
  return requestPost('order/orderBack', payload)
}
export async function orderSend(payload:any) {
  return requestPost('order/orderSend', payload)
}
export async function postNewOrder(payload:any) {
  return requestPost('orderNew', payload)
}
export async function postConfirmOrder(payload:any) {
  return requestPost('comfirmOrder', payload)
}
export async function seeComplaint(payload:any) {
  return requestPost('order/complaints', payload)
}
export async function devideOrder(payload:any) {
  return requestPost('order/devideOrder', payload)
}
export async function seeRating(payload:any) {
  return requestPost('order/rating', payload)
}
export async function getOrderDetail(payload:any) {
  return requestGet(`order/${payload.id}`, payload)
}
export async function getOrderCount(payload:any) {
  return requestGet('orderCount', payload)
}
export async function getOrderLog(payload:any) {
  return requestGet(`orderLog/${payload.id}`, payload)
}

export async function getNotice(payload:any) {
  return requestGet(`noticeOrder`, payload)
}

export async function remindOrder(payload:any) {
  return requestPut(`/remind/${payload.id}`, payload)
}

export async function getPrice(payload:any) {
  return requestGet(`JDPrice`, payload)
}

export async function putPrice(payload:any) {
  return requestPut(`/JDPrice/${payload.id}`, payload)
}

export async function postPrice(payload:any) {
  return requestPost(`JDPrice`, payload)
}

export async function getRecyclePrice(payload:any) {
  return requestGet(`JDRecyclePrice`, payload)
}
export async function deletePrice(payload:any) {
  return requestDelete(`JDPrice/${payload.id}`);
}

export async function deleteRecyclePrice(payload:any) {
  return requestDelete(`JDRecyclePrice`, payload)
}
export async function putRecyclePrice(payload:any) {
  return requestPut(`/JDRecyclePrice/${payload.id}`, payload)
}

export async function postRecyclePrice(payload:any) {
  return requestPost(`JDRecyclePrice`, payload)
} 
// 批量取消订单
export async function cancelBatchOrders(payload:any) {
  return requestPost('admin/cancelBundle', payload)
}

export async function getYCPrice(payload:any) {
  return requestGet(`YCPrice`, payload)
}

export async function putYCPrice(payload:any) {
  return requestPut(`/YCPrice/${payload.id}`, payload)
}

export async function postYCPrice(payload:any) {    
  return requestPost(`YCPrice`, payload)
} 