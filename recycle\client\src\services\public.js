import { requestGet, requestPut, requestPost, requestDelete } from '../utils/request'


export async function getUserDiscoveryList(payload) {
  return requestGet('discovery', payload)
}
export async function replayTheContent(payload) {
  return requestPost('discovery', payload)
}
export async function changeUpvote(payload) {
  return requestPost('discoveryUpvote', payload)
}
export async function getUpvoteList() {
  return requestGet('discoveryUpvote')
}
export async function getWalletInfo(payload) {
  return requestGet('walletInfo', payload)
}
export async function postWallet(payload) {
  return requestPost('wallet/cashOut', payload)
}
export async function postPoint(payload) {
  return requestPost('point/cashOut', payload)
}