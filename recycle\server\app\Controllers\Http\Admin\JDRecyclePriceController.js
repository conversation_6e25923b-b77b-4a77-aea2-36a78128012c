'use strict'

const _ = require('lodash')
const moment = require('moment')

const { JDPrice } = require('../../../Models')
const { ERR } = require('../../../../../../constants')
const Excel = require('exceljs')
class JDRecyclePriceController {
  async index({ request, response }) {
    let { type = "纯回收",town, current = 1, pageSize = 10, thrid, lowPrice, hightPrice, price, city, sort = 'desc', four, province, level } = request.all()
    let query = JDPrice.query()
    if (type) {
      query.where('type', type)
    }

    if (level) {
      if (level === 'null') {
        query.whereNull('level')
      } else {
        query.where('level', level)
      }
    }
    if (province) {
      query.where('province', province)
    }
    if (lowPrice && hightPrice) {
      query.whereBetween('price', [lowPrice, hightPrice])
    }
    if (price === 'descend') {
      query.orderBy('price', 'desc')
    } else if (price === 'ascend') {
      query.orderBy('price', 'asc')
    } else {
      query.orderBy('id', sort)
    }
    if (city) {
      query.where('city', city)
    }
    if (town) {
      query.whereRaw('town like ?', [`%${town}%`])
    }
    if(thrid){
      query.whereRaw('thrid like ?', [`%${thrid}%`])
    }
    if (four) {
      query.whereRaw('four like ?', [`%${four}%`])
    }
    let vo = await query.paginate(current, pageSize)
    response.json(vo)
  }
  async update({ request, params, response }) {
    let vo = await JDPrice.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
  }
  async store({ request, response }) {
    let { type, thrid, four, province, city, town, level, price } = request.all()
    let vo = await JDPrice.create({ type, thrid, four, province, city, town, level, price })
    response.json(vo)
  }
  async destroy({ request, params, response }) {
    let vo = await JDPrice.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    await vo.delete()
    response.json(vo)
  }
  async exportTable({ request, response }) {
    let { type = "纯回收", town,thrid, lowPrice, hightPrice, price, city, sort = 'desc', four, province } = request.all()
    let query = JDPrice.query()

    if (type) {
      query.where('type', type)
    }
    if (province) {
      query.where('province', province)
    }
    if (lowPrice && hightPrice) {
      query.whereBetween('price', [lowPrice, hightPrice])
    }
    if (price === 'descend') {
      query.orderBy('price', 'desc')
    } else if (price === 'ascend') {
      query.orderBy('price', 'asc')
    } else {
      query.orderBy('id', sort)
    }
    if (city) {
      query.where('city', city)
    }
    if (town) {
      query.whereRaw('town like ?', [`%${town}%`])
    }
    if (thrid) {
      query.whereRaw('thrid like ?', [`%${thrid}%`])
    }
    if (four) {
      query.whereRaw('four like ?', [`%${four}%`])
    }

    const vo = await query.fetch()
    let data = vo.toJSON()

    // 创建Excel工作簿和工作表
    let workbook = new Excel.Workbook()
    let worksheet = workbook.addWorksheet('Sheet 1')

    // 设置列头和列宽
    let font = { name: 'Times New Roman', size: 12 }
    worksheet.columns = [
      { header: '同步类型', key: 'type', width: 15, style: { font } },
      { header: '三级品类', key: 'thrid', width: 20, style: { font } },
      { header: '四级品类', key: 'four', width: 20, style: { font } },
      { header: '省份', key: 'province', width: 15, style: { font } },
      { header: '城市', key: 'city', width: 15, style: { font } },
      { header: '区', key: 'town', width: 15, style: { font } },
      { header: '城市等级', key: 'level', width: 12, style: { font } },
      { header: '信息费', key: 'price', width: 12, style: { font } }
    ]

    // 添加数据行
    let rowDownload = data.map(async (item) => {
      worksheet.addRow({
        type: item.type,
        thrid: item.thrid,
        four: item.four,
        province: item.province,
        city: item.city,
        town: item.town,
        level: item.level,
        price: item.price
      })
    })

    // 生成文件名
    const fileName = `价格信息表${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`
    rowDownload = await Promise.all(rowDownload)
    // 写入文件并返回
    await workbook.xlsx.writeFile(`./${fileName}`)
    return response.attachment(`./${fileName}`)
  }
}

module.exports = JDRecyclePriceController
