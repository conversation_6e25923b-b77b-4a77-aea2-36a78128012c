.attributeSelect {
  width: 100vw;
  height: 100vh;
  background-color: #f5f6f8;
  display: flex;
  flex-direction: column;
  .which {
    width: 100%;
    height: 280px;
    background: #ffffff;
    display: flex;
    align-items: center;
    padding: 0 40px;
    .left_wrapper {
      height: 180px;
      width: 170px;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      > image {
        height: 100%;
        background-size: contain;
      }
    }
    .right_wrapper {
      margin-top: -20px;
      margin-left: 30px;
      > View {
        font-size: 24px;
        color: #999999;
        letter-spacing: 1.2px;
        line-height: 36px;
        &:first-child {
          font-weight: 700;
          font-size: 28px;
          color: #444444;
          margin-bottom: 28px;
        }
        Text {
          color: #e01818;
          margin-right: 10px;
        }
      }
    }
  }
  .wrapper {
    width: 100vw;
    padding-left: 40px;
    padding-right: 40px;
    background: #ffffff;
    .devide {
      white-space: nowrap;
      overflow: hidden;
      width: 100%;
      .demo-text-1 {
        height: 90px;
        line-height: 90px;
        position: relative;
        font-size: 26px;
        font-weight: 700;
        color: #999999;
        flex-shrink: 0;
        display: inline-block;
        //width: 25%;
        text-align: center;
        > Text {
          margin: 0 20px;
        }
        .show_which {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          bottom: 10px;
          width: 27px;
          height: 6px;
          background: url('https://oss.evergreenrecycle.cn/donggua/client/images/activate.png') no-repeat center;
          background-size: cover;

          // width: 100%;
          // background: linear-gradient(to right, rgba(119, 220, 143, 1) 0% , rgba(21, 179, 129, 1) 100%);
        }
      }
      .showed {
        color: #444444;
      }
    }
    .move {
      transform: translateX(-100px);
    }
  }

  .select_wrapper {
    //flex: 1;
    background: #f5f6f8;
    display: flex;
    flex-wrap: wrap;
    margin-top: 80px;
    width: 100%;
    padding: 0 40px;
    .select_item {
      width: 320px;
      height: 72px;
      line-height: 72px;
      text-align: center;
      margin-bottom: 30px;
      font-size: 26px;
      color: #444444;
      background: #ffffff;
      &:nth-child(even) {
        margin-left: 30px;
      }
    }
    .selected {
      color: #15b381;
      position: relative;
      top: 0;
      &::after {
        position: absolute;
        bottom: 0;
        right: 0;
        content: '';
        width: 50px;
        height: 50px;
        background: url('https://oss.evergreenrecycle.cn/donggua/client/images/gouxuan.png') center;
        background-size: 50px 50px;
      }
    }
  }

  .test-h {
    flex: 1;
    background: #ffffff;
    padding: 0 30px;
    .select_wrapper {
      display: flex;
      flex-wrap: wrap;
      margin-top: 30px;
      .select_item {
        width: 46%;
        height: 64px;
        border: 1px solid #cccccc;
        line-height: 64px;
        text-align: center;
        margin-bottom: 20px;
        border-radius: 4px;
        font-size: 24px;
        background: #ffffff;
        &:nth-child(even) {
          margin-left: 8%;
        }
      }
      .selected {
        background: #15b381;
        color: #ffffff;
        border: none;
      }
    }
  }
}

.oldGoods_bottom {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  background-color: #ffffff;
  .top_wrapper {
    width: 100%;
    height: 120px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .bottom_wrapper {
    height: 32px;
    width: 100%;
  }
  .oldGoods_bottom_shoppingCar {
    width: 50vw;
    height: 100%;
    display: flex;
    align-items: center;
    > Text {
      margin-left: 60px;
      font-size: 30px;
      font-weight: 700;
      color: #7c8696;
    }
  }
  .oldGoods_bottom_text {
    width: 346px;
    height: 84px;
    border-radius: 42px;
    background-color: #e5e5e5;
    background: url('../../assets/icon/gujia.png') center no-repeat;
    background-size: 106px 48px;
    &.canClick {
      background-color: #15b381;
    }
  }
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
  display: none;
}
