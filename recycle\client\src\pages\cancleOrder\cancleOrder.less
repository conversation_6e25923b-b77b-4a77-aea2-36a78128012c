.cancleOrder {
  width: 100vw;
  height: 100vh;
  background: #f5f6f8;
  display: flex;
  flex-direction: column;
  align-items: center;
  letter-spacing: 2px;
  padding-bottom: 66px;
  box-shadow: 0px 2px 11px #e9ebed inset;
  .img {
    width: 240px;
    height: 240px;
    margin-top: 100px;
    margin-bottom: 32px;
  }
  .title {
    font-size: 32px;
    color: #444444;
    font-weight: 700;
  }
  .title2 {
    font-size: 24px;
    color: #556073;
    margin-top: 16px;
    margin-bottom: 62px;
  }
  .content_wrapper {
    flex: 1;
    width: 660px;
    background: #ffffff;
    padding: 44px 36px;
    position: relative;
    .top_select {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      > Text {
        height: 40px;
        line-height: 40px;
        padding: 0px 22px;
        border-radius: 20px;
        font-size: 22px;
        letter-spacing: 1px;
        margin-bottom: 20px;
        margin-right: 20px;
        color: #ffffff;
        background: linear-gradient(to right, rgba(119, 220, 143, 1) 0% , rgba(21, 179, 129, 1) 100%);
      }
    }
    .input_title {
      display: inline-block;
      padding-top: 30px;
      font-size: 26px;
      color: #444444;
      font-weight: 700;
    }
    .detail_input {
      border: none;
      padding-left: 0;
    }
    .at-textarea__textarea {
      font-size: 24px;
      color: #333333;
      line-height: 36px;
    }
    .submit_button {
      width: 588px;
      height: 88px;
      background: #e5e5e5;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 30px;
      color: #808080;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 42px;
      letter-spacing: 2px;
      border-radius: 44px;
      &.isClick {
        background: #15b381;
        color: #fff;
      }
    }
  }
}
