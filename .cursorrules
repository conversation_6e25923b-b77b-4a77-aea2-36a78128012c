
### always response with Chinese
###项目名称：家电回收
###项目描述：家电回收平台，用户可以通过平台进行家电回收，平台会根据家电的类型、品牌、型号、新旧程度等因素进行评估，给出回收价格，用户可以选择是否回收，如果选择回收，平台会安排工作人员上门回收，回收后，平台会进行处理，处理后，平台会进行销售，销售后，平台会进行售后服务。
###项目目录：
    - recycle
        - master 用taro.js+react.js开发的微信小程序 家电回收师傅端操作小程序
        - client 用taro.js+react.js 开发的微信小程序、H5   家电回收用户端
        - admin 用react.js+antd+vite+typescript+dva17 开发的后台管理系统
        - server 用node.js+adonis.js+mysql开发的API服务
代码书写规范
    - 使用中文注释清晰
    - 代码越简洁越好
    - 可复用性强，易维护

调试:
    - 使用firecrawl-mcp调试
    - 自动解决调试过程中遇到的错误
    - 自动解决调试过程中遇到的bug
    - 自动解决调试过程中遇到的性能问题
    - 自动解决调试过程中遇到的优化问题
    - 自动解决调试过程中遇到的代码规范问题
    - 自动解决调试过程中遇到的代码安全问题
    - 自动解决调试过程中遇到的代码性能问题
    
Tech Stack:
    - TypeScript
    - Node.js
    - Web APIs
    - ant-design/pro-components
    - Vite
    - Backend: Node.js with adonis.js
    - Frontend: taro.js and React.js
    - Authentication: JSON Web Tokens (JWT)  
    - Version Control: Git  
    - CSS
    - Mysql

Precision in User Requirements:
    - Strictly adhere to specified user flow and game rules.  

You are an export AI programming assistant that primarily focuses on producing clean and readable code.
###参考
请参考Taro官方文档，antd官方文档，adonis.js官方文档，vite官方文档，dva17官方文档，mysql官方文档，CSS官方文档，TypeScript官方文档，Node.js官方文档，Web APIs官方文档，JSON Web Tokens (JWT)官方文档，Git官方文档，CSS官方文档，Mysql官方文档，Drizzle ORM官方文档，Vite官方文档，adonis.js官方文档，taro.js官方文档，taro-ui官方文档，React.js官方文档，ant-design/pro-components官方文档，

You are an expert in TypeScript, Node.js, Web APIs,antd,  Vite, adonis.js, taro, taro-ui,CSS, Mysql,Drizzle ORM, with a deep understanding of best practices and performance optimization techniques in these technologies. Code Style and Structure - Write concise, maintainable, and technically accurate code with relevant examples. 
- Use functional, declarative programming patterns.
- Prefer iteration and modularization over code duplication. 
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
- Organize files systematically: each file should contain only related content, such as exported components, subcomponents, helpers, static content, and types.
- Use lowercase with dashes for directories (e.g., components/auth-wizard).
- Favor named exports for functions.
- Use the "function" keyword for pure functions to benefit from hoisting and clarity.
- Prefer the Receive an Object, Return an Object (RORO) pattern for function parameters. 
- Prefer one-line syntax for simple conditional statements (e.g., if (condition) doSomething()). 
- Use TypeScript for all code. Prefer interfaces over types. Avoid enums; use maps instead for better type safety and flexibility. 
- Error Handling and Validation - Handle errors and edge cases at the beginning of functions. 
- Use early returns for error conditions to avoid deeply nested if statements. 
- Use guard clauses to handle preconditions and invalid states early.
- Avoid unnecessary else statements; use if-return pattern instead.
- Implement proper error logging and user-friendly error messages.
- Consider using custom error types or error factories for consistent error handling.
- Use functional components with TypeScript interfaces.
- Prefer the "function" keyword for methods but use arrow functions with const for computed properties.
- Prefer the `defineModel` macro for creating two-way bindings.
- Use the succint syntax for defineEmits (e.g. `change: [id: number]`)
- UI and Styling
- Use React, ant-design/pro-components and Tailwind CSS for components and styling.
- Implement responsive design with Tailwind CSS; use a mobile-first approach.
- Performance Optimization
- Wrap asynchronous components in Suspense with a fallback UI.
- Use dynamic loading for non-critical components.
- Optimize images: use WebP format, include size data, implement lazy loading.
- Implement an optimized chunking strategy during the Vite build process, such as code splitting, to generate smaller bundle sizes.
- Key Conventions
- Optimize Web Vitals (LCP, CLS, FID) using tools like Lighthouse or WebPageTest.