'use strict'

const Model = use('Model')

/**
 * 嗨回收师傅模型
 * 专门处理嗨回收平台的师傅管理
 */
class HiWorker extends Model {
  static get table() {
    return 'hi_worker'
  }
  
  static get primaryKey() {
    return 'id'
  }
  
  static get createdAtColumn() {
    return 'createdAt'
  }
  
  static get updatedAtColumn() {
    return 'updatedAt'
  }

  /**
   * 关联嗨回收订单
   */
  orders() {
    return this.hasMany('App/Models/HiOrder', 'id', 'workerID')
  }

  /**
   * 关联子师傅（小工）
   */
  assistants() {
    return this.hasMany('App/Models/HiCOWorker', 'id', 'managerID')
  }

  /**
   * 关联主师傅
   */
  manager() {
    return this.belongsTo('App/Models/HiWorker', 'managerID', 'id')
  }

  /**
   * 关联公司信息
   */
  company() {
    return this.belongsTo('App/Models/Company', 'companyID', 'id')
  }

  /**
   * 关联钱包记录
   */
  walletLogs() {
    return this.hasMany('App/Models/HiWorkerWalletLog', 'id', 'workerID')
  }

  /**
   * 关联工作区域
   */
  areas() {
    return this.belongsToMany('App/Models/Area').pivotTable('hi_worker_area')
  }

  /**
   * 获取师傅当前状态
   */
  static getStatusText(status) {
    const statusMap = {
      '0': '待审核',
      '1': '正常',
      '2': '禁用',
      '3': '拒绝'
    }
    return statusMap[status] || '未知状态'
  }

  /**
   * 获取师傅等级
   */
  static getLevelText(level) {
    const levelMap = {
      '1': '普通师傅',
      '2': '高级师傅',
      '3': '金牌师傅',
      '4': '钻石师傅'
    }
    return levelMap[level] || '普通师傅'
  }
}

module.exports = HiWorker 