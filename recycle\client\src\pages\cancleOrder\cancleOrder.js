import Taro, { requirePlugin } from '@tarojs/taro'
import React, { Component } from 'react'
import { connect } from 'react-redux'
import { View, Image, Button, Text, Checkbox } from '@tarojs/components'
import { AtModal, AtTextarea } from 'taro-ui'
import './cancleOrder.less'
import E from '../../config/E'
import dayjs from 'dayjs'
import T from '../../config/T'
import 'taro-ui/dist/style/components/textarea.scss'
class CancleOrder extends Component {
  constructor() {
    super(...arguments)
    this.state = {
      locale: 'zh_CN',
      whoCancel: false,
      selectReason: '',
      reason: '',
      orderID: null,
      manage: null,
      selectIndex: null,
    }
  }

  config = {}

  //-----------------------生命周期-------------------------//
  componentWillMount() {
    let orderID = Taro.getCurrentInstance().router.params.orderID
    let whoCancel = Taro.getCurrentInstance().router.params.cancle
    let manage = Taro.getCurrentInstance().router.params.manage
    this.setState({
      whoCancel,
      orderID,
      manage,
    })
  }

  componentDidMount() {
    const locale = Taro.getStorageSync('locale')
    this.setState({
      locale,
    })
    Taro.setNavigationBarTitle({ title: locale == 'en' ? 'Cancel Order' : '取消订单' })
  }

  componentWillUnmount() {}

  componentDidShow() {}

  componentDidHide() {}

  //-----------------------事件-------------------------//
  selectReasonFun(selectReason, index) {
    this.setState({
      selectReason,
      selectIndex: index,
    })
  }

  submit() {
    let { whoCancel, orderID, reason, manage, selectReason } = this.state
    if (whoCancel) {
      this.props.dispatch({
        type: 'NOrder/EUpdateOrderStatus',
        payload: {
          id: orderID,
          status: E.OrderStatus.Cancelled,
          cancelReason: selectReason ? selectReason + ' ' + reason : reason,
          manage,
        },
      })
    } else {
      this.props.dispatch({
        type: 'NOrder/EOrderComplaint',
        payload: {
          orderID,
          content: selectReason ? selectReason + ' ' + reason : reason,
        },
      })
    }
  }

  //-----------------------渲染-------------------------//
  render() {
    let { whoCancel, reason, selectIndex, selectReason, locale } = this.state
    return (
      <View className="cancleOrder">
        {whoCancel ? (
          <Image src={('https://oss.evergreenrecycle.cn/donggua/client/images/cancel.png')} className="img" />
        ) : (
          <Image src={('https://oss.evergreenrecycle.cn/donggua/client/images/complaint.png')} className="img" />
        )}
        <Text className="title">{whoCancel ? T.cancelPage.cancelTitle : T.cancelPage.complaintTitle}</Text>
        <Text className="title2">{whoCancel ? T.cancelPage.cancelReason : T.cancelPage.complaintReason}</Text>
        <View className="content_wrapper">
          <View className="top_select">
            {(whoCancel ? E.OrderCancel : E.OrderComplaint).map((vo, index) => (
              <Text
                onClick={() => {
                  this.selectReasonFun(vo.zh_CN, index)
                }}
                key={index + vo}
                style={index === selectIndex ? null: {
                  background: '#F3F3F3',
                  color: '#999999'
                }}
              >
                {locale == 'en' ? vo.en : vo.zh_CN}
              </Text>
            ))}
          </View>
          <Text className="input_title">{T.cancelPage.others}：</Text>
          <AtTextarea
            className="detail_input"
            count={false}
            value={reason}
            style={{ width: '100%' }}
            maxLength={100}
            onChange={e => {
              this.setState({
                reason: e,
              })
            }}
            placeholder={T.cancelPage.remark}
            placeholderStyle="font-size: 12px;color: #7C8696;"
          />
          <View
            className={`submit_button ${
              (reason || selectReason)?"isClick":""
            }`}
          
            onClick={() => {
              if (reason || selectReason) {
                this.submit()
              }
            }}
          >
            {whoCancel ? T.cancelPage.cancelSubmit : T.cancelPage.complaintSubmit}
          </View>
        </View>
      </View>
    )
  }
}
export default connect(({ NOrder }) => ({}))(CancleOrder)
