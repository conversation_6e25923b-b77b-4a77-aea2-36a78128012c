.address {
  width: 100vw;
  min-height: 100vh;
  background-color: #F5F6F8;

  .wechat_get_address {
    border-bottom: 10px solid #F5F6F8;
    width: 100%;
    height: 90px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 48px 0 36px;
    background: #ffffff;

    .left_wrapper {
      display: flex;
      align-items: center;

      > Image {
        width: 34px;
        height: 27.5px;
        margin-right: 16px;
      }

      > Text {
        font-size: 24px;
        color: #444444;
      }
    }

    .right_img {
      width: 4vw;
      height: 4vw;
    }
  }

  .address_list {
    padding: 0 48px 0 36px;
    background: #ffffff;
    height: 15.5vw;
    width: 100%;
    margin: 0 auto;
    border-bottom: 1px solid rgba(247, 247, 247, 1);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .address_list_title {
      color: rgba(51, 51, 51, 1);
      font-size: 4vw;
      text-align: left;
      font-family: PingFangSC-regular;
    }

    .address_list_des {
      width: 100%;
      font-size: 28px;
      color: #333333;
      font-weight: 500;
    }

    .address_list_icon {
      font-size: 28px;
      color: #333333;
      font-weight: 500;
    }
    .address_Arrow_icon {
      display: block;
      width: 4vw;
      height: 4vw;
    }
  }

  .address_save {
    position: fixed;
    left: 50px;
    bottom: 80px;
    width: 650px;
    height: 90px;
    line-height: 90px;
    border-radius: 45px;
    background: #E5E5E5;
    color: #999;
    .text_wrapper{
      display: flex;
   
      width: 100%;
      justify-content: center;
      align-items: center;
    }
    .add_item{
      height: 32px;
      width: 100%;
    }
  }

  .save_change {
    background: #15b381;
    color: #ffffff;
  }
}

.gender_wrapper {
  width: 300px;
  display: flex;
  justify-content: space-between;

  .gender_item {
    background: #F5F6F8;
    border-radius: 26px;
    width: 138px;
    height: 52px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 26px;
    font-weight: 300;
    color: #7C8696;
    letter-spacing: 2px;

    .icon_img {
      width: 32px;
      height: 32px;
    }
  }

  .this_gender {
    background: #15b381;
    color: #ffffff;
  }
}

.address_list_bottom {
  padding: 28px 48px 1px 36px;
  background: #ffffff;
  width: 100%;
  border-bottom: 1px solid #f7f7f7;
  .detail_input {
    width: 100%;
    padding-left: 0;
    border: none;
  }
}


.get_phone_button {
  border: 1px solid green;
  font-size: 28px;
  font-weight: 300;
  color: green;
  width: 200px;
  height: 50px;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
}

.at-textarea__textarea{
  font-size: 28px;
  color: #333333;
}
.address_button{
  background: #15b381;
  color: #ffffff;
  height: 52px;
  font-size: 26px;
  line-height: 52px;
  padding: 0 30px;
  border-radius: 26px;
}
.address_input{
  width: 440px;
  font-size: 26px;
}