'use strict'

const _ = require('lodash')
const moment = require('moment')
const {
  User,
  Worker,
  OrderWaste,
  WorkerWalletLog,
  ReqLog,
  ClientOrderLog,
  ClientOrder,
  ClientOrderCancel
} = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const Config = require('../../../Util/Config')
const { _remindMaster, _record } = require('../../../Services/OrderService')
const { OrderStatus } = require('../../../../../../constants/E')
const Database = use('Database')

//订单
class ClientOrderController {
  async index({ request, response }) {
    let { page = 1, perPage = 20, workDate, status, orderNo, sort = 'desc', whoCancel, date, keyword } = request.all()
    let workerID = request.worker && request.worker.id
    workDate = parseInt(workDate)
    if (!workerID) {
      return ERR.USER_NOT_EXISTS
    }
    let query = ClientOrder.query().where('workerID', workerID)
    if (status) {
      if (status === E.OrderStatus.Pending) {
        query.where('status', E.OrderStatus.Reservation)
      } else if (status === E.OrderStatus.Dispatched) {
        throw ERR.INVALID_PARAMS
      } else if (status === E.OrderStatus.TransferOrder) {
        throw ERR.INVALID_PARAMS
      } else {
        query.where('status', status).with('doneInfo')
      }
    } else if (orderNo) {
      query.where('orderNo', orderNo)
    } else if (whoCancel) {
      let cancelList = await ClientOrderCancel.query().where('workerID', workerID).where('whoCancel', whoCancel).fetch()
      let orderList = []
      cancelList.rows.forEach((vlaue) => {
        orderList.push(vlaue.orderID)
      })
      query = ClientOrder.query().whereIn('id', orderList)
    } else {
      query.orderBy('id', 'desc')
    }
    if (keyword) {
      query.whereRaw('keywords like ?', [`%${keyword}%`])
    }
    if (date) {
      query.whereBetween('workTime', [moment(date).format('YYYY-MM-DD'), moment(date).add(1, 'days').format('YYYY-MM-DD')])
    }
    if (status === E.OrderStatus.InProgress) {
      switch (workDate) {
        case 0:
          query.where('workTime ', '<=', moment().add(1, 'days').format('YYYY-MM-DD'))
          break;
        case 1:
          query.whereBetween('workTime', [moment().add(1, 'days').format('YYYY-MM-DD'), moment().add(2, 'days').format('YYYY-MM-DD')])
          break;
        case 2:
          query.whereBetween('workTime', [moment().add(2, 'days').format('YYYY-MM-DD'), moment().add(60, 'days').format('YYYY-MM-DD')])
          break;
        default:
          break;
      }
      query.orderBy('workTime', 'asc')
    } else if (status === E.OrderStatus.Completed) {
      query.orderBy('finishedAt', 'desc')
    } else {
      query.orderBy('id', sort)
    }
    let vo = await query.with('worker').with('cancel')
      .with('userAddress')
      .with('orderWasteVs').paginate(page, perPage)
    vo.rows = vo.rows.map(item => {
      item.orderNo = item.orderNO
      item.type = item.$relations.orderWasteVs && item.$relations.orderWasteVs.rows[0] && item.$relations.orderWasteVs.rows[0].name
      item.infoFee = 0
      item.address = item.$relations.userAddress && (item.$relations.userAddress.province +
        item.$relations.userAddress.city + item.$relations.userAddress.district + item.$relations.userAddress.subDistrct +
        item.$relations.userAddress.address)
      return item
    })
    response.json(vo)
  }
  async orderRemark({ request, response }) {
    let workerID = request.worker.id
    let { id, remark } = request.all()
    let vo = await ClientOrderLog.create({
      status: E.OrderLogStatus.Remark,
      orderID: id,
      workerID: workerID,
      content: remark,
    })
    response.json(vo)
  }
  async orderCall({ request, response }) {
    let workerID = request.worker.id
    let { id } = request.all()
    let vo = await ClientOrderLog.create({
      status: E.OrderLogStatus.Call,
      orderID: id,
      workerID: workerID,
      content: '拨打客户电话',
    })
    response.json(vo)
  }

  //扇形图数据 某个区间完成订单
  async chart({ request, response }) {
    let { startDate, endDate, page = 1, perPage = 10 } = request.all()
    let workerID = request.worker.id
    if (!workerID) {
      throw ERR.RESTFUL_GET_ID
    }
    let finish
    let finishList
    if (startDate && endDate) {
      finish = await ClientOrder.query()
        .where('workerID', workerID)
        .where('status', E.OrderStatus.Completed)
        .where('finishedAt', '>=', moment(startDate).toDate())
        .where('finishedAt', '<=', moment(endDate).add(1, 'd').toDate())
        .paginate(page, perPage)
      finishList = await ClientOrder.query().select('id', 'type', 'status', 'commission', 'infoFee', 'createdAt', 'finishedAt', 'workTime', 'workerID')
        .where('workerID', workerID)
        .where('status', E.OrderStatus.Completed)
        .where('finishedAt', '>=', moment(startDate).toDate())
        .where('finishedAt', '<=', moment(endDate).add(1, 'd').toDate())
        .fetch()
    } else {
      finish = await ClientOrder.query().where('workerID', workerID).where('status', E.OrderStatus.Completed).paginate(page, perPage)
      finishList = await ClientOrder.query().select('id', 'type', 'status', 'commission', 'infoFee', 'createdAt', 'finishedAt', 'workTime', 'workerID')
        .where('workerID', workerID).where('status', E.OrderStatus.Completed).fetch()
    }
    response.json({ count: finish.pages.total, finishList })
  }
  //订单详情
  async show({ request, params, response }) {
    let { worker } = request
    let { companyID, type } = worker
    let query = ClientOrder.query().where('id', params.id)
    if (type !== '超级管理员') {
      query.where('companyID', companyID)
    }
    if (!query) {
      throw ERR.RESTFUL_GET_ID
    }
    let vo = await query.with('cancel')
      .with('doneInfo')
      .with('worker')
      .with('userAddress')
      .with('orderWasteVs')
      .first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    vo = vo.toJSON()
    vo.orderNo = vo.orderNO
    vo.type = vo.orderWasteVs && vo.orderWasteVs[0] && vo.orderWasteVs[0].name
    vo.infoFee = 0
    vo.address = vo.userAddress && (vo.userAddress.province +
      vo.userAddress.city + vo.userAddress.district + vo.userAddress.subDistrct +
      vo.userAddress.address)
    response.json(vo)
  }
  //订单撤回
  async orderBack({ request, response }) {
    let { worker } = request
    let { id: workerID } = worker
    let { whoCancel, cancelReason, orderID } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '小程序 管理撤回订单' })
    let order = await ClientOrder.find(orderID)
    if (!order) {
      throw ERR.RESTFUL_GET_ID
    }
    if (order.toJSON().status === E.OrderStatus.Cancelled || order.toJSON().status === E.OrderStatus.Completed) {
      return ({ error: 4003, message: '订单已取消或已完成，请刷新页面' })
    }
    order.workerID = null
    order.status = E.OrderStatus.MasterReturn
    let cancel = await ClientOrderCancel.create({ orderID, workerID, cancelReason, whoCancel, companyID: order.companyID, userID: order.userID })
    await order.save()
    response.json(cancel)
    order = order.toJSON()
    await ReqLog.create({ res: JSON.stringify(order), source: '小程序 管理撤回订单' })
    let workerInfo = await Worker.find(workerID)
    await _remindMaster(workerInfo, order, E.OrderLogStatus.Recall)
    await ClientOrderLog.create({
      status: E.OrderLogStatus.Recall,
      orderID,
      workerID: worker.id,
      content: cancelReason,
    })
  }
  async orderPrice({ request, response, params }) {
    let { worker } = request
    let { status = OrderStatus.InProgress } = request.all()
    let data = await ClientOrder.query().where('status', status).where('workerID', worker.id).getSum('infoFee')
    return data
  }
  async comfirmOrder({ request, response }) {
    let { worker } = request
    let { companyID } = worker
    let { id, remuneration, imageUrl, payMoney, remark, sign, unit } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '小程序 管理订单确认' })
    let data = await ClientOrder.find(id)
    if (data.toJSON().status === OrderStatus.Cancelled) {
      throw ERR.UNLOCK
    }
    let workerVO = await Worker.find(worker.id)
    let uniqOrder = await WorkerWalletLog.query().where("orderID", id).where("isClient", 1).first()
    if (uniqOrder) {
      throw ERR.INVALID_PARAMS;
    }
    await WorkerWalletLog.create({
      workerID: worker.id,
      money: 0,
      remuneration,
      commission: 0,
      imageUrl,
      payMoney,
      isClient: 1,
      companyID,
      remark: "c端订单-" + data.toJSON().orderNO,
      sign,
      unit,
      orderID: id,
    })
    await workerVO.save()
    data.status = '完成'
    data.infoFee = 0
    data.commission = 0
    data.actualMoney = payMoney
    data.finishedAt = moment().format('YYYY-MM-DD HH:mm:ss')
    await data.save()
    await ClientOrderLog.create({
      status: E.OrderLogStatus.Completed,
      orderID: id,
      workerID: worker.id,
      content: remark,
    })
    response.json(workerVO)
    data = data.toJSON()
    await ReqLog.create({ res: JSON.stringify(workerVO), source: '小程序 管理订单确认' })
    await _record(data, E.OrderStatus.Completed)
  }
  async getOrderLog({ request, response, params }) {
    let { worker } = request
    if (!params.id) {
      throw ERR.API_ERROR
    }
    let logList = await ClientOrderLog.query().with('user').with('worker').with('creater').with('order')
      .where('orderID', params.id)
      .where('workerID', worker.id)
      .orderBy('createdAt', 'asc')
      .fetch()
    response.json(logList)
  }
  //订单创建
  async store({ request, response }) {
    let {
      userID,
      estimatedMoney,
      waste_1st_ID,
      orderWastes,
      userName,
      from,
      sentinelAddress,
      unit,
      wasteType,
      commission,
      remuneration,
    } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '小程序 管理订单创建' })
    let user = await User.findBy({ id: userID })
    if (!user) {
      throw ERR.RESTFUL_GET_ID
    }
    let worker = request.worker
    if (!userID || !waste_1st_ID || !orderWastes || !userName || !from || !sentinelAddress || !wasteType) {
      throw ERR.INVALID_PARAMS
    }
    let workTime = moment(new Date()).format('YYYY-MM-DD') + ' ' + '13:00'
    const createData = {
      userID,
      workerID: worker.id,
      companyID: worker.companyID,
      waste_1st_ID,
      wasteType,
      estimatedMoney,
      userName,
      from,
      sentinelAddress,
      unit,
      workTime,
      remuneration,
      commission,
    }
    if (userData.salesman_id) {
      createData.salesman_id = userData.salesman_id
    }

    let vo = await ClientOrder.create(createData)
    vo.status = E.OrderStatus.InProgress
    vo.orderNo = moment().format('YYYYMMDD') + vo.id
    if (orderWastes && orderWastes.length > 0) {
      orderWastes.forEach((waste, index) => {
        waste.orderID = vo.id
      })
      await vo.orderWastes().delete()
      orderWastes.forEach(async function (itm) {
        let orderWaste = new OrderWaste()
        _.assign(orderWaste, itm)
        await orderWaste.save()
      })
    }
    await vo.save()
    response.json(vo)
    await ReqLog.create({ res: JSON.stringify(vo), source: '小程序 管理订单创建' })
  }
  //订单数据更新
  async update({ request, params, response }) {
    let { status, whoCancel, cancelReason, list, workTime } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '小程序 管理订单数据更新' })
    let workerID = request.worker.id
    let { worker } = request
    let companyID = worker.companyID
    let vo = await ClientOrder.query().where('id', params.id).first()
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    if (vo.toJSON().status === E.OrderStatus.Cancelled || vo.toJSON().status === E.OrderStatus.Completed) {
      return ({ error: 4003, message: '订单已取消或已完成，请刷新页面' })
    }
    if (status === E.OrderStatus.InProgress || status === E.OrderStatus.Completed || status === E.OrderStatus.Cancelled) {
      if (status === E.OrderStatus.Cancelled) {
        let cancel = await ClientOrderCancel.create({ orderID: vo.id, workerID, cancelReason, whoCancel, companyID: vo.companyID })
      }
    }
    if (vo.status === E.OrderStatus.InProgress && list) {
      _.forEach(list, async function (value) {
        let vo = await OrderWaste.find(value.id)
        _.assign(vo, value)
        await vo.save()
      })
    }
    let obj = request.all()
    delete obj.list
    delete obj.cancelReason
    delete obj.whoCancel
    if (status === E.OrderStatus.InProgress || status === E.OrderStatus.Completed) {
      if (!vo.companyID || vo.companyID !== companyID) {
        throw ERR.RESTFUL_GET_ID
      }
    }
    if (status === E.OrderStatus.InProgress) {
      obj.workerID = workerID
    }
    if (whoCancel === E.CancleOrderStatus.MasterRevoke) {
      let cancel = await ClientOrderCancel.create({ orderID: vo.id, workerID, cancelReason, whoCancel, companyID: vo.companyID })
    }
    if (workTime) {
      await ClientOrderLog.create({
        status: E.OrderLogStatus.RESCHEDULE,
        orderID: vo.id,
        workerID: workerID,
        content: "改约到" + workTime,
      })
    }
    _.assign(vo, obj)
    //  师傅接单
    if (status === E.OrderStatus.InProgress) {
      await ClientOrderLog.create({
        status: E.OrderLogStatus.AcceptOrder,
        orderID: vo.id,
        workerID: worker.id,
        content: '师傅接单',
      })
    }
    await vo.save()
    response.json(vo)
    await ReqLog.create({ res: JSON.stringify(vo), source: '小程序 管理订单数据更新' })
  }

  async getRevenue({ request, response }) {
    let { worker } = request
    let { companyID, type } = worker
    let { startDate, endDate, platform } = request.all()
    if (worker.isUse !== "同意" || worker.type !== "管理") {
      throw ERR.AUTH_FAILED
    }
    let query = ClientOrder.query()
      .select(Database.raw('id,commission,createdAt '))
      .whereNot('status', '取消')
      .whereBetween('createdAt', [moment(startDate).add(1, 'days').format('YYYY-MM-DD'), moment(endDate).format('YYYY-MM-DD')])
    switch (type) {
      case '超级管理员':
        break
      case '管理':
        query.where('companyID', companyID)
        break
      case '师傅':
        return false
      default:
        break
    }
    if (platform) {
      if (platform === '全部' || platform === 'null' || platform === '0' || platform === 'undefined') {
        console.log('ALL')
      } else {
        query.where('from', platform)
      }
    }
    let revenue = await query.fetch()
    let count = await query.getCount()
    let countAll = await query.getSum('commission')
    return { revenues: revenue, len: count, countAll }
  }
  async managerOrder({ request, response }) {
    let { startDate, endDate, platform, delay, page = 1, perPage = 10, cancel, status } = request.all()
    let query = ClientOrder.query().with('worker').with('cancel')
    let { worker } = request
    delay = parseInt(delay)
    cancel = parseInt(cancel)
    if (worker.isUse !== "同意" || worker.type !== "管理") {
      throw ERR.AUTH_FAILED
    }
    if (cancel) {
      // 已取消+退单订单
      query.where('workerID', null)
    }
    if (delay) {
      query.where('workTime', '<', moment().format('YYYY-MM-DD'))
    }
    if (status) {
      query.where('status', status)
    }
    if (startDate && endDate) {
      query.whereBetween('createdAt', [moment(startDate).add(1, 'days').format('YYYY-MM-DD'), moment(endDate).format('YYYY-MM-DD')])
    }
    if (platform) {
      if (platform === '全部' || platform === 'null' || platform === '0' || platform === 'undefined') {
        console.log('ALL')
      } else {
        query.where('from', platform)
      }
    }
    let revenue = await query.paginate(page, perPage)
    response.json(revenue)
  }
  async orderChange({ request, response }) {
    let { worker } = request
    if (worker.isUse !== "同意" || worker.type !== "管理") {
      throw ERR.AUTH_FAILED
    }
    let { orderID, companyID, workerID, workerName, remark, status = E.OrderStatus.Reservation } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '小程序 管理订单变更' })
    let query = ClientOrder.query().where('id', orderID)
    let orderVo = await query.first()
    if (orderVo.toJSON().status === E.OrderStatus.Cancelled || orderVo.toJSON().status === E.OrderStatus.Completed) {
      return ({ error: 4003, message: '订单已取消或已完成，请刷新页面' })
    }
    await query.update({ companyID, workerID, status, isTransferID: workerID })
    let vo = await Worker.find(workerID)
    response.json(vo)
    let data = await ClientOrderLog.create({
      status: E.OrderLogStatus.TransferOrder,
      orderID,
      workerID: worker.id,
      content: `管理员:${worker.workerName}转派给师傅${vo.workerName} 备注:` + remark,
    })
    response.json(data)
    await ReqLog.create({ res: JSON.stringify(data), source: '小程序 管理订单变更' })
  }
}

module.exports = ClientOrderController
