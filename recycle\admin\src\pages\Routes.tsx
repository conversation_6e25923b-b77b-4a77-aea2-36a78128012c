import React, { Children } from 'react'
import {
  SmileOutlined,
  CrownOutlined,
  TabletOutlined,
  AntDesignOutlined,
  TransactionOutlined,
  SolutionOutlined,
  ShopOutlined,
  Bar<PERSON><PERSON>Outlined,
  TeamOutlined,
  AccountBookOutlined,
  MoneyCollectOutlined,
  SkinOutlined,
  ToolOutlined,
  UnorderedListOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  RobotOutlined,
  DeleteOutlined,
} from '@ant-design/icons'

import { PageName } from '../common/enum'
import BigObject from './BigObject'
import Company from './Company'
import Commission from './Commission'
import CendInfo from './CendInfo'
import OverView from './OverView'
import Permission from './Permission'
import WorkerManage from './WorkerManage'
import PayManage from './PayManage'
import CompanyEdit from './CompanyEditor'
import AreaEdit from './AreaEdit'
import AgentDistribution from './AgentDistribution'
import PublicWelfare from './PublicWelfare'
import CompanyArea from './CompanyArea'
import WorkerArea from './WorkerArea'
import StoreOrder from './StoreOrder'
import SmsManager from './SmsManager'
import DashBorad from './DashBorad'
import PriceSet from './PriceSet'
import ClientOrder from './ClientOrder'
import PriceManage from './PriceManage'

import OtherOrder from './OtherOrder'
import JDWorkerManage from './JDWorkerManage'
import Infofee from './Infofee'
import HiInfofee from './HiInfofee'
import HIWorkerManage from './HiWorkerManage'
import HiOrder from './HiOrder'
import JoinTeam from './JoinTeam'
import CBanner from './CBanner'
import YCInfofee from './YCInfofee'
import YCWorker from './YCWorker'
import YCOrder from './YCOrder'
import JDRecyceInfofee from './JDRecyceInfofee'
import JDRecycleOrder from './JDRecycleOrder'

// 普通回收平台路由
const normalRoutes = [
  {
    path: '/',
    component: <BigObject />,
  },
  {
    path: PageName.DashBorad,
    name: '数据看板',
    icon: <PieChartOutlined />,
    component: <DashBorad />,
    authority: ['服务商新建与编辑']
  },
  {
    path: PageName.BigObject,
    name: '订单管理',
    icon: <ToolOutlined />,
    component: <BigObject />,
    authority: ['订单查看', '订单操作'],
  },

  {
    path: PageName.StoreOrder,
    name: '服务商订单',
    icon: <ToolOutlined />,
    component: <StoreOrder />,
    authority: ['订单查看', '订单操作'],
  },

  {
    path: PageName.WorkerManage,
    name: '师傅管理',
    icon: <SolutionOutlined />,
    component: <WorkerManage />,
    authority: ['回收人员查看', '回收人员编辑', '回收人员审核'],
  },
  {
    path: PageName.WorkerArea,
    name: '师傅-工作区域管理',
    icon: <CrownOutlined />,
    component: <WorkerArea />,
    authority: ['回收人员编辑'],
    hideInMenu: true
  },
  {
    path: PageName.AreaEdit,
    name: '师傅-工作区域新增',
    icon: <CrownOutlined />,
    component: <AreaEdit />,
    authority: ['回收人员编辑'],
    hideInMenu: true
  },
  {
    path: PageName.Commission,
    name: '信息费管理',
    icon: <AccountBookOutlined />,
    component: <Commission />,
    authority: ['佣金查看', '佣金编辑'],
  },
  {
    path: PageName.OverView,
    name: '业务数据总览',
    icon: <UnorderedListOutlined />,
    component: <OverView />,
    authority: ['数据查询', '数据导出']
  },

  {
    path: PageName.JDManager,
    authority: ['服务商新建与编辑'],
    name: '京东结算',
    icon: <SolutionOutlined />,
    routes: [
      {
        path: PageName.JDRecycleOrderManage,
        name: 'JD以旧换新订单管理',
        icon: <ToolOutlined />,
        component: <OtherOrder />,
        authority: ['订单查看', '订单操作'],
      },
      {
        path: PageName.JDRecycleInfofee,
        name: 'JD以旧换新信息费管理',
        icon: <AccountBookOutlined />,
        component: <Infofee />,
        authority: ['佣金查看', '佣金编辑'],
      },
      {
        path: PageName.JDOrderManage,
        name: 'JD纯回收订单管理',
        icon: <ToolOutlined />,
        component: <JDRecycleOrder />,
        authority: ['订单查看', '订单操作'],
      },
      {
        path: PageName.JDinfofee,
        name: 'JD纯回收信息费管理',
        icon: <AccountBookOutlined />,
        component: <JDRecyceInfofee />,
        authority: ['佣金查看', '佣金编辑'],
      },


      {
        path: PageName.JDWorkersManage,
        name: 'JD师傅管理',
        icon: <TeamOutlined />,
        component: <JDWorkerManage />,
        authority: ['服务商新建与编辑']
      },
    ]
  },
  {
    path: PageName.YCManager,
    authority: ['服务商新建与编辑'],
    name: 'YC回收结算',
    icon: <SolutionOutlined />,
    routes: [
      {
        path: PageName.YCOrderManage,
        name: 'YC回收订单管理',
        icon: <ToolOutlined />,
        component: <YCOrder />,
        authority: ['订单查看', '订单操作'],
      },
      {
        path: PageName.YCWorkersManage,
        name: 'YC回收师傅管理',
        icon: <TeamOutlined />,
        component: <YCWorker />,
        authority: ['服务商新建与编辑']
      },
      {
        path: PageName.YCinfofee,
        name: 'YC回收信息费管理',
        icon: <AccountBookOutlined />,
        component: <YCInfofee />,
        authority: ['佣金查看', '佣金编辑'],
      }
    ]
  },
  {
    path: PageName.HaiRecycleManager,
    authority: ['服务商新建与编辑'],
    name: '嗨回收结算',
    icon: <SolutionOutlined />,
    routes: [
      {
        path: PageName.HaiRecycleOrderManage,
        name: '嗨回收订单管理',
        icon: <ToolOutlined />,
        component: <HiOrder />,
        authority: ['订单查看', '订单操作'],
      },
      {
        path: PageName.HaiRecycleWorkersManage,
        name: '嗨回收师傅管理',
        icon: <TeamOutlined />,
        component: <HIWorkerManage />,
        authority: ['服务商新建与编辑']
      },
      {
        path: PageName.HaiRecycleInfofee,
        name: '嗨回收信息费管理',
        icon: <AccountBookOutlined />,
        component: <HiInfofee />,
        authority: ['佣金查看', '佣金编辑'],
      }
    ]
  },
  {
    name: 'C端管理',
    icon: <TabletOutlined />,
    path: PageName.ClientManage,
    authority: ['服务商查看', '服务商新建与编辑'],
    routes: [
      {
        path: PageName.ClientOrder,
        name: 'C端订单管理',
        icon: <RobotOutlined />,
        component: <ClientOrder />,
        authority: ['订单查看', '订单操作'],
      },
      {
        path: PageName.ClientPrice,
        name: 'C端估价管理',
        icon: <TransactionOutlined />,
        component: <PriceManage />,
        authority: ['订单查看', '订单操作'],
      },
      // news模块
        {
          path: PageName.PublicWelfare,
          name: '文章管理',
          icon: <SolutionOutlined />,
          component: <PublicWelfare />,
        },
        {
          path: PageName.CBanner,
          name: '首页管理',
          icon: <SolutionOutlined />,
          component: <CBanner />,
        },
      {
        path: PageName.AgentDistribution,
        name: '渠道商管理',
        icon: <SolutionOutlined />,
        component: <AgentDistribution />,
      },
      {
        path: PageName.CendInfo,
        name: 'C端信息费',
        icon: <MoneyCollectOutlined />,
        component: <CendInfo />,
      },
    ]
  },
  {
    path: PageName.Other,
    name: '其他',
    icon: <SolutionOutlined />,
    routes: [
      {
        path: PageName.Company,
        name: '服务商管理',
        icon: <ShopOutlined />,
        component: <Company />,
        authority: ['服务商查看', '服务商新建与编辑'],
      },
      {
        path: PageName.CompanyArea,
        name: '服务商-工作区域管理',
        icon: <SolutionOutlined />,
        component: <CompanyArea />,
        hideInMenu: true,
        authority: ['服务商新建与编辑']
      },
      {
        path: PageName.CompanyEdit,
        name: '服务商-工作区域新增',
        icon: <SolutionOutlined />,
        component: <CompanyEdit />,
        hideInMenu: true,
        authority: ['服务商新建与编辑']
      },
      {
        path: PageName.SmsManager,
        name: '短信管理',
        icon: <UnorderedListOutlined />,
        component: <SmsManager />,
        authority: ['服务商新建与编辑']
      },
      {
        path: PageName.PriceManage,
        name: '最低充值管理',
        icon: <TransactionOutlined />,
        component: <PriceSet />,
        authority: ['服务商新建与编辑']
      },
      {
        path: PageName.PayManage,
        name: '押金管理',
        icon: <TransactionOutlined />,
        component: <PayManage />,
        authority: ['押金查看', '押金编辑'],
      },
      {
        path: PageName.JoinTeam,
        name: '招募管理',
        icon: <TeamOutlined />,
        component: <JoinTeam />,
        authority: ['招募查看', '招募新建与编辑'],
      },
      {
        path: PageName.Permission,
        name: '账户及权限管理',
        icon: <TeamOutlined />,
        component: <Permission />,
        authority: ['账户的查看', '账户新建与编辑', '权限编辑']
      },
    ]
  },
]

export default {
  route: {
    path: '/',
    routes: [...normalRoutes]
  },
  location: {
    pathname: '/',
  },
}
