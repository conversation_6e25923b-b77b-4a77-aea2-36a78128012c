import { requestGet, requestPost, requestPut, requestDelete } from 'dva17'

// 获取加入团队列表
export async function getJoinTeamList(params?: any) {
  return requestGet('/joinUs', params)
}

// 获取加入团队详情
export async function getJoinTeamDetail(id: number) {
  return requestGet(`/joinUs/${id}`)
}

// 创建加入团队记录
export async function createJoinTeam(data: any) {
  return requestPost('/joinUs', data)
}

// 更新加入团队记录
export async function updateJoinTeam(id: number, data: any) {
  return requestPut(`/joinUs/${id}`, data)
}

// 删除加入团队记录
export async function deleteJoinTeam(id: number) {
  return requestDelete(`/joinUs/${id}`)
} 