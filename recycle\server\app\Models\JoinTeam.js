'use strict'

const Model = use('Model')

class JoinTeam extends Model {
  static get table() {
    return 'join_team'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return null
  }
  getType(value) {
    return value && JSON.parse(value)
  }
  setType(value) {
    return value && JSON.stringify(value)
  }
  getCarType(value) {
    return value && JSON.parse(value)
  }
  setCarType(value) {
    return value && JSON.stringify(value)
  }
  getCert(value) {
    return value && JSON.parse(value)
  }
  setCert(value) {
    return value && JSON.stringify(value)
  }
}

module.exports = JoinTeam
