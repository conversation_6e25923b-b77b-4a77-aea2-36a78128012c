.ChooseAddress{
   position: fixed;
    bottom: 0;
    left: 0;
    height:100vh;
    width: 100vw;
    z-index: 100;
    // background-color: #fff;
    .mask{
        position: absolute;
        height:100vh;
        width: 100vw;
        z-index: 1;
        background-color: #000000;
        opacity: 0.5;
    }
    .ChooseAddress_day_time{
        width: 100vw; 
        position: absolute;
        bottom: 0;
        left: 0;
        height:65vw;
        width: 100vw;
        z-index: 2;
        background-color: #fff;
        overflow: hidden;
        .<PERSON>oseAddress_day_time_title{
            position: absolute;
            top: 0;
            left: 0;
            height: 15vw;
            width: 100vw;
            display: flex;
            justify-content:space-between;
            align-items: center;
            z-index: 2;
            background-color: #fff;
            .ChooseAddress_day_time_title_button{
                width:20vw;
                height:8vw;
                line-height:8vw;
                text-align: center;
                background-color: #1296db;
                // border:1px solid red;
                font-size: 4vw;
                color: #fff;
                border-radius: 1vw;
            }
            .ChooseAddressTitle{
                display: flex;
                flex-direction: column;
                width:40vw;
                // border:1px solid red;
                .ChooseAddressTitle_text{
                    display: block;
                    width: 100%;
                    text-align: center;
                    font-size:3.5vw;
                }
            }
        }
       .PickerView_box{
           position: absolute;
           top:-20vw;
           left:0;
           width: 100vw;
           height:80vw;
       }
       .PickerView_box_item{
            height:10vw;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 4vw;
            
        }
    }
}