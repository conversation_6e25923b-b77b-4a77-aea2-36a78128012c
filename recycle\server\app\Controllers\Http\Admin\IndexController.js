'use strict'

const _ = require('lodash')
const moment = require('moment')

const { CLaba, CPath, CBanner, ClientEstimate } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const Env = use('Env')

class IndexController {
  //获取首页数据
  async index({ request, response }) {
    let { type } = request.all()
    if (type == 'ad') {
      let Banners = await CBanner.query().where('status', 1).fetch()
      let vo = {
        data: Banners
      }
      response.json(vo)
    } else if (type == 'marquee') {
      let Labas = await CLaba.query().where('status', 1).fetch()
      let vo = {
        data: Labas
      }
      response.json(vo)
    } else if (type == 'category') {
      let Paths = await CPath.query().where('status', 1).fetch()
      let vo = {
        data: Paths
      }
      response.json(vo)
    }
  }
  async update({ request, response, params }) {
    let { id } = params
    let { type, text, img, name, status } = request.all()
    if (type == 'ad') {
      let vo = await CBanner.query().where('id', id).first()
      vo.img = img
      vo.status = status
      await vo.save()
    } else if (type == 'marquee') {
      let vo = await CLaba.query().where('id', id).first()
      vo.text = text
      vo.status = status
      await vo.save()
    } else if (type == 'category') {
      let vo = await CPath.query().where('id', id).first()
      vo.name = name
      vo.img = img
      vo.status = status
      await vo.save()
    }
    response.json({ success: true })
  }
  async store({ request, response }) {
    let { type, text, img, name } = request.all()
    try {
      if (type == 'ad') {
        await CBanner.create({ img, status: 1 })
      } else if (type == 'marquee') {
        await CLaba.create({ text, status: 1 })
      } else if (type == 'category') {
        await CPath.create({ name, img, status: 1 })
      }
      response.json({ success: true })
    } catch (error) {
      response.json({ success: false, message: error.message })
    }

  }
  async destroy({ request, response, params }) {
    let { id } = params
    let { type } = request.all()
    if (type == 'ad') {
      let vo = await CBanner.query().where('id', id).first()
      vo.status = 0
      await vo.save()
    } else if (type == 'marquee') {
      let vo = await CLaba.query().where('id', id).first()
      vo.status = 0
      await vo.save()
    } else if (type == 'category') {
      let vo = await CPath.query().where('id', id).first()
      vo.status = 0
      await vo.save()
    }
    response.json(vo)
  }
}

module.exports = IndexController
