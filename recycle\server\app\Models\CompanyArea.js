'use strict'

const Model = use('Model')

//公司信息
class CompanyArea extends Model {
  static get table() {
    return 'company_area'
  }
  static get createdAtColumn() {
    return null
  }
  static get updatedAtColumn() {
    return null
  }
  company() {
    return this.belongsTo('App/Models/Company', 'companyID', 'id')
  }
  address() {
    return this.belongsTo('App/Models/SysArea', 'areaCode', 'areaCode')
      .select('id', 'areaCode', 'name', 'provinceCode', 'cityCode')
      .where('level', '3')
      .with('province')
      .with('city')
  }
  typeInfo() {
    return this.hasMany('App/Models/CompanyArea', 'code', 'code')
      .select('type', 'typeName', 'code', 'name', 'area', 'areaCode')
  }
}

module.exports = CompanyArea
