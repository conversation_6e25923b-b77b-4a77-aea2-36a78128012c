import { PlusOutlined } from '@ant-design/icons'
import { Button, Tabs, Input, Switch, Modal, notification, Upload, Image, Select } from 'antd'
import type { ActionType, ProColumns } from '@ant-design/pro-table'
import ProTable, { } from '@ant-design/pro-table'
import ProCard from '@ant-design/pro-card'
import { effect } from 'dva17'
import { useEffect, useRef, useState } from 'react'
import { EGetCIndexList, EPostCIndexList, EPutCIndexList, NHiWorker } from '../../common/action'
import { CBannerType, } from '../../common/enum'
import { deleteCIndex } from "../../services/discovery"
import styles from './index.module.less'
import { SERVER_HOME } from '../../common/config'
const { TabPane } = Tabs
const { Option } = Select
const { TextArea } = Input

type Item = {
  id: number
  text: string
  name: string
  createdAt: string
  img: string
  updatedAt: string
  type: string
  status: number
}

export default () => {
  const actionRef = useRef<ActionType>()
  const [type, setType] = useState<any>(CBannerType.AD)
  const [visible, setVisible] = useState<any>(false)
  const [vo, setVo] = useState<any>(null)
  const [content, setContent] = useState<any>(null)
  const [imgs, setImgs] = useState<any>([])
  let [files, setFiles] = useState<any>([])
  let [previewVisible, setPreviewVisible] = useState<any>(false)
  let [previewImage, setPreviewImage] = useState<any>(null)

  const columns: ProColumns<Item>[] = [
    {
      title: '内容',
      dataIndex: 'text',
      width: '30%',
      copyable: false,
      search: false,
      render: (_, row) => (row.text || row.name) && <div dangerouslySetInnerHTML={{ __html: row.text || row.name }} />
    },
    {
      title: '图片',
      dataIndex: 'img',
      width: '30%',
      copyable: false,
      search: false,
      render: (_, row) => row.img && <Image preview src={row.img} alt="" style={{ width: '100px', height: '100px' }} />
    },

    {
      title: '启用状态',
      dataIndex: 'status',
      copyable: false,
      ellipsis: true,
      search: false,
      render: (_, row) => <Switch
        checkedChildren="启动"
        unCheckedChildren="停用"
        defaultChecked={Boolean(row.status)}
        onChange={
          e => {
            changeForbidden(e, row)
          }}
      />,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      copyable: false,
      search: false,
    },
    {
      title: '修改时间',
      dataIndex: 'updatedAt',
      copyable: false,
      search: false,
    },
    {
      title: '操作',
      copyable: false,
      ellipsis: true,
      search: false,
      render: (_, row: any) => (
        <>
          <Button
            style={{ marginRight: 10 }} onClick={() => {
              setVisible(true)
              setVo(row)
            }}>编辑</Button>
          <Button
            type='primary'
            onClick={() => {
              deleteItem(row)
            }}>删除</Button>
        </>
      ),
    },
  ]
  const deleteItem = (e: any) => {
    Modal.confirm({
      title: '确认删除该条数据',
      content: <div>删除后数据无法恢复！</div>,
      okText: '确认删除',
      cancelText: '退出',
      onOk: async () => {
        await deleteCIndex({ id: e.id })
        refreshPage()
        notification.success({
          message: '成功！',
          description: '删除成功',
          duration: 2
        })
      },
      width: 700
    })
  }
  const changeForbidden = async (e: any, record: any) => {
    console.log(e, record);
    await effect(NHiWorker, EPutCIndexList, { id: record.id, isReport: e }).then(() => {
      notification.success({
        message: '成功！',
        description: '修改成功',
        duration: 2
      })
    })
    refreshPage()
  }
  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    if (vo) {
      setContent(vo.text)
      setFiles(JSON.parse(vo.img))
      setImgs(vo.img && JSON.parse(vo.img).map((e: string) => {
        return { uid: Math.random(), url: e, name: e.split('/').pop() }
      }))
    }
  }, [vo])

  /*--------------------- 响应 ---------------------*/
  const refreshPage = () => {
    if (actionRef.current) {
      actionRef.current.reload()
    }
  }
  function getBase64(file: any) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  }
  const onPreview = async (file: any) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setPreviewImage(file.url || file.preview)
    setPreviewVisible(true)
  }
  //上传Banner图
  const beforeUploadPic = (file: any) => {
    let isImage;
    if (['image/png', 'image/jpg', 'image/jpeg', 'image/gif', 'image/svg'].includes(file.type)) {
      isImage = true
    } else {
      isImage = false;
      alert('请上传正确的格式')
    }
    return isImage
  };
  //上传详情图片
  const handleChangePicture = async ({ fileList }: any) => {
    setImgs(fileList)
    if (fileList.length > 0) {
      fileList.map((vo: any, index: number) => {
        if (vo.status === 'done') {
          files.push(vo.response.url)
        }
      })
      setFiles([...new Set(files)])
    }
  }
  /*--------------------- 渲染 ---------------------*/
  return (
    <ProCard>
      <Tabs defaultActiveKey={CBannerType.AD} onChange={(e) => {
        setType(e)
        refreshPage()
      }}>
        <TabPane tab="广告位" key={CBannerType.AD} />
        <TabPane tab="跑马灯" key={CBannerType.MARQUEE} />
        <TabPane tab="回收分类" key={CBannerType.CATEGORY} />
      </Tabs>
      <ProTable<Item>
        actionRef={actionRef}
        columns={columns}
        request={async (params = {}) => (await effect(NHiWorker, EGetCIndexList, { ...params, type: type })) as any}
        pagination={{
        }}
        rowKey="id"
        dateFormatter="string"
        headerTitle="首页banner管理"
        toolBarRender={() => [
          <Button
            onClick={() => {
              setVisible(true)
              setVo(null)
            }} key="3" type="primary">
            <PlusOutlined />
            新建
          </Button>,
        ]}
      />


      <Modal open={visible} title={vo ? '编辑内容' : '新建内容'}
        onOk={async () => {
          if (!vo) {
            setVisible(false)
            await effect(NHiWorker, EPostCIndexList, { type, text: content, name: content, img: JSON.stringify(files) }).then(() => {
              refreshPage()
              setContent(null)
              setFiles([])
              setImgs([])
            })
          } else {
            setVisible(false)
            await effect(NHiWorker, EPutCIndexList, { id: vo.id, type, text: content, name: content, img: JSON.stringify(files) }).then(() => {
              refreshPage()
              setContent(null)
              setFiles([])
              setImgs([])
            })
          }
        }}
        onCancel={() => {
          setVisible(false)
        }} width={800}>
        <div className={styles.item}>
          <span className={styles.item_title}>类型：</span>
          <div className={styles.item_content}>
            <Select
              value={type}
              onChange={(e: any) => {
                setType(e)
              }}
            >
              <Option value={CBannerType.AD}>广告位</Option>
              <Option value={CBannerType.MARQUEE}>跑马灯</Option>
              <Option value={CBannerType.CATEGORY}>回收分类</Option>
            </Select>
          </div>
        </div>
        <div className={styles.item}>
          <span className={styles.item_title}>文字内容：</span>
          <div className={styles.item_content}>
            <TextArea
              value={content}
              onChange={(e: any) => {
                setContent(e.target.value)
              }}
            />
          </div>
        </div>
        <div className={styles.item} style={{ marginTop: '30px' }}>
          <span className={styles.item_title}>相关图片：</span>
          <div className={styles.item_content}>
            <div className={styles.imag_wrapper}>
              <div>
                {/* 只上传一张图片，使用单文件对象而非数组 */}
                <Upload
                  listType="picture-card"
                  accept="image/*"
                  maxCount={1}
                  action={`${SERVER_HOME}file`}
                  fileList={imgs ? [imgs] : []}
                  beforeUpload={beforeUploadPic}
                  onChange={info => {
                    // 只保留最新一张图片
                    const file = info.file;
                    setImgs(file);
                    // 如果需要files同步，也只存一张
                    setFiles(file.response ? file.response.url : '');
                  }}
                  onPreview={onPreview}
                >
                  {imgs ? null : (
                    <div>
                      <div className="ant-upload-text">点击上传图片</div>
                    </div>
                  )}
                </Upload>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </ProCard>
  )
}
