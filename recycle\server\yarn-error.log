Arguments: 
  D:\Program Files\nodejs\node.exe D:\Program Files\nodejs\node_modules\yarn\bin\yarn.js install

PATH: 
  C:\Program Files\ShadowBot;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.2\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.2\libnvvp;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;D:\software\nvm;D:\Program Files\nodejs;C:\ProgramData\chocolatey\bin;D:\Program Files\python;D:\software\Java\jdk-*********\bin;D:\software\Java\jdk-*********\jre\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\software\Git LFS;C:\Program Files\NVIDIA Corporation\Nsight Compute 2023.2.0\;D:\sof;ware\go\bin;D:\download\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;D:\software\微信web开发者工具\dll;c:\Use;s\Jeff\AppData\Local\Programs\cursor\resources\app\bin;D:\Anaconda3;D:\Anaconda3\Scripts;D:\Anaconda3\Library\bin;C:\Program Files (x86)\ZeroTier\One\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\.local\bin\;C:\Users\<USER>\.bun\bin\;D:\software\微信web开发者工具\bin\adb-win;;;;D:\software\Git\cmd;C:\Program Files\dotnet\;C:\Program Files\ShadowBot;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\pnpm;D:\Program Files\python\Scripts\;D:\Program Files\python\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\software\Microsoft VS Code\bin;D:\software\nvm;D:\Program Files\nodejs;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\.local\bin\;;C:\Users\<USER>\.bun\bin

Yarn version: 
  1.22.19

Node version: 
  16.17.0

Platform: 
  win32 x64

Trace: 
  Error: getaddrinfo ENOTFOUND registry.nlark.com
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:109:26)

npm manifest: 
  {
    "name": "adonis-api-app",
    "version": "4.1.0",
    "adonis-version": "4.1.0",
    "description": "Adonisjs boilerplate for API server with pre-configured JWT",
    "main": "index.js",
    "scripts": {
      "start": "node server.js",
      "dev": "adonis serve --dev",
      "test": "node ace test",
      "example-test": "node ace test --glob /**/unit/example.spec.js"
    },
    "keywords": [
      "adonisjs",
      "adonis-app"
    ],
    "author": "",
    "license": "UNLICENSED",
    "private": true,
    "dependencies": {
      "@adonisjs/ace": "^5.0.8",
      "@adonisjs/auth": "^3.0.7",
      "@adonisjs/bodyparser": "^2.0.5",
      "@adonisjs/cors": "^1.0.7",
      "@adonisjs/fold": "^4.0.9",
      "@adonisjs/framework": "^5.0.9",
      "@adonisjs/ignitor": "^2.0.8",
      "@adonisjs/lucid": "^6.1.3",
      "@adonisjs/vow": "^1.0.17",
      "@alicloud/sms-sdk": "^1.1.6",
      "adonis": "^0.9.0",
      "ali-oss": "^6.1.0",
      "axios": "^0.18.0",
      "co-wechat-api": "^3.9.1",
      "co-wechat-oauth": "^2.0.1",
      "exceljs": "^4.1.1",
      "fs-extra": "^7.0.1",
      "hasha": "^3.0.0",
      "jsonwebtoken": "^8.5.0",
      "lodash": "^4.17.15",
      "md5": "^2.2.1",
      "moment": "^2.24.0",
      "mysql": "^2.16.0",
      "node-fecth": "^0.0.1-security",
      "node-schedule": "^1.3.2",
      "node-xlsx": "^0.15.0",
      "qrcode": "^1.3.3",
      "random-int": "^1.0.0",
      "shortid": "^2.2.14",
      "tenpay": "^2.1.18",
      "wechatpay-node-v3": "^2.0.0",
      "weixin-pay": "^1.1.7",
      "xlsx": "^0.18.5"
    },
    "autoload": {
      "App": "./app"
    }
  }

yarn manifest: 
  No manifest

Lockfile: 
  # THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
  # yarn lockfile v1
  
  
  "@adonisjs/ace@^5.0.8":
    "integrity" "sha1-mP9uVWh85Ne5vYPWHnfdTdQ0IOo="
    "resolved" "https://registry.npm.taobao.org/@adonisjs/ace/download/@adonisjs/ace-5.0.8.tgz"
    "version" "5.0.8"
    dependencies:
      "cli-table" "^0.3.1"
      "commander" "^2.18.0"
      "debug" "^4.0.1"
      "enquirer" "^1.0.3"
      "fast-levenshtein" "^2.0.6"
      "fs-extra" "^7.0.0"
      "is-arrow-function" "^2.0.3"
      "kleur" "^2.0.2"
      "lodash" "^4.17.11"
      "mustache" "^3.0.0"
      "prompt-checkbox" "^2.2.0"
      "prompt-confirm" "^2.0.4"
      "prompt-expand" "^1.0.1"
      "prompt-list" "^3.2.0"
      "prompt-password" "^1.2.0"
  
  "@adonisjs/auth@^3.0.7":
    "integrity" "sha1-A5wr1g2VnTu15uSewpglrVSxZdg="
    "resolved" "https://registry.npm.taobao.org/@adonisjs/auth/download/@adonisjs/auth-3.1.0.tgz"
    "version" "3.1.0"
    dependencies:
      "@adonisjs/generic-exceptions" "^2.0.1"
      "basic-auth" "^2.0.1"
      "debug" "^4.0.1"
      "jsonwebtoken" "^8.3.0"
      "lodash" "^4.17.11"
      "ms" "^2.1.1"
      "resetable" "^1.0.3"
      "uuid" "^3.3.2"
  
  "@adonisjs/bodyparser@^2.0.5":
    "integrity" "sha1-ZneY57Dyz5dej6eoFLeTLSCXthw="
    "resolved" "https://registry.npm.taobao.org/@adonisjs/bodyparser/download/@adonisjs/bodyparser-2.0.9.tgz"
    "version" "2.0.9"
    dependencies:
      "@adonisjs/generic-exceptions" "^2.0.1"
      "bytes" "^3.0.0"
      "co-body" "^6.0.0"
      "debug" "^4.1.0"
      "end-of-stream" "^1.4.1"
      "fs-extra" "^7.0.0"
      "get-stream" "^4.1.0"
      "lodash" "^4.17.11"
      "media-typer" "^0.3.0"
      "multiparty" "^4.2.1"
  
  "@adonisjs/cors@^1.0.7":
    "integrity" "sha1-m3VlrQuAIVxiDohktK+GUSIKUnw="
    "resolved" "https://registry.npm.taobao.org/@adonisjs/cors/download/@adonisjs/cors-1.0.7.tgz"
    "version" "1.0.7"
  
  "@adonisjs/fold@^4.0.9":
    "integrity" "sha1-Gphgry2+KeQRHX8MwjS+FcLii+g="
    "resolved" "https://registry.npm.taobao.org/@adonisjs/fold/download/@adonisjs/fold-4.0.9.tgz"
    "version" "4.0.9"
    dependencies:
      "@adonisjs/generic-exceptions" "^2.0.1"
      "caller" "^1.0.1"
      "debug" "^3.1.0"
      "lodash" "^4.17.10"
      "require-stack" "^1.0.2"
  
  "@adonisjs/framework@^5.0.9":
    "integrity" "sha1-ehT1fTeQZdDujEnhGmgoAxWaq04="
    "resolved" "https://registry.npm.taobao.org/@adonisjs/framework/download/@adonisjs/framework-5.0.13.tgz"
    "version" "5.0.13"
    dependencies:
      "@adonisjs/generic-exceptions" "^2.0.1"
      "@adonisjs/middleware-base" "^1.0.0"
      "bcryptjs" "^2.4.3"
      "co-compose" "^4.0.0"
      "debug" "^4.1.1"
      "dotenv" "^6.2.0"
      "edge.js" "^1.1.4"
      "eventemitter2" "^5.0.1"
      "haye" "^2.0.2"
      "lodash" "^4.17.11"
      "macroable" "^1.0.0"
      "node-cookie" "^2.1.1"
      "node-exceptions" "^3.0.0"
      "node-req" "^2.1.1"
      "node-res" "4.1.4"
      "parseurl" "^1.3.2"
      "path-to-regexp" "^2.4.0"
      "require-all" "^3.0.0"
      "resetable" "^1.0.3"
      "serve-static" "^1.13.2"
      "simple-encryptor" "^2.0.0"
      "useragent" "^2.3.0"
      "winston" "^3.2.1"
      "youch" "^2.0.10"
  
  "@adonisjs/generic-exceptions@^2.0.0", "@adonisjs/generic-exceptions@^2.0.1":
    "integrity" "sha1-eT4zxKzwH16Y5oU18qeYeiO9oOM="
    "resolved" "https://registry.npm.taobao.org/@adonisjs/generic-exceptions/download/@adonisjs/generic-exceptions-2.0.1.tgz"
    "version" "2.0.1"
    dependencies:
      "node-exceptions" "^3.0.0"
      "upcast" "^2.1.1"
  
  "@adonisjs/ignitor@^2.0.8":
    "integrity" "sha1-1NtPCdjw7g5/m1Aw3hQTWiWov5E="
    "resolved" "https://registry.npm.taobao.org/@adonisjs/ignitor/download/@adonisjs/ignitor-2.0.8.tgz"
    "version" "2.0.8"
    dependencies:
      "debug" "^4.0.1"
      "pify" "^4.0.0"
      "youch" "^2.0.10"
      "youch-terminal" "^1.0.0"
  
  "@adonisjs/lucid@^6.1.3":
    "integrity" "sha1-x1mtp7OvLDUwg5xj8lSjjA8d780="
    "resolved" "https://registry.npm.taobao.org/@adonisjs/lucid/download/@adonisjs/lucid-6.1.3.tgz"
    "version" "6.1.3"
    dependencies:
      "@adonisjs/generic-exceptions" "^2.0.1"
      "chance" "^1.0.16"
      "debug" "^4.0.1"
      "knex" "^0.15.2"
      "lodash" "^4.17.11"
      "moment" "^2.22.2"
      "pluralize" "^7.0.0"
      "pretty-hrtime" "^1.0.3"
      "require-all" "^3.0.0"
  
  "@adonisjs/middleware-base@^1.0.0":
    "integrity" "sha1-xHT5TKPIQaZFQf2BEnnuNudwMcg="
    "resolved" "https://registry.npm.taobao.org/@adonisjs/middleware-base/download/@adonisjs/middleware-base-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "@adonisjs/generic-exceptions" "^2.0.0"
      "co-compose" "^4.0.0"
      "debug" "^3.1.0"
      "haye" "^2.0.1"
      "lodash" "^4.17.5"
  
  "@adonisjs/vow@^1.0.17":
    "integrity" "sha1-eyBsLhR0QiN1UqFeSGYLXhHdsbg="
    "resolved" "https://registry.npm.taobao.org/@adonisjs/vow/download/@adonisjs/vow-1.0.17.tgz"
    "version" "1.0.17"
    dependencies:
      "chai-subset" "^1.6.0"
      "debug" "^4.0.1"
      "globby" "^8.0.1"
      "japa" "1.0.6"
      "lodash" "^4.17.11"
      "macroable" "^1.0.0"
      "node-cookie" "^2.1.1"
      "p-series" "^1.1.0"
      "superagent" "^4.0.0-beta.5"
  
  "@alicloud/dybaseapi@^1.0.0":
    "integrity" "sha1-VS7dCZD5Db1p1AI3zf7/w2bQofU="
    "resolved" "https://registry.npm.taobao.org/@alicloud/dybaseapi/download/@alicloud/dybaseapi-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "@alicloud/pop-core" "^1.3.3"
  
  "@alicloud/dysmsapi-2017-05-25@^1.0.1":
    "integrity" "sha1-qKtpTb4QXrnWfL0eNzV/ag+Eh9U="
    "resolved" "https://registry.npm.taobao.org/@alicloud/dysmsapi-2017-05-25/download/@alicloud/dysmsapi-2017-05-25-1.0.1.tgz"
    "version" "1.0.1"
    dependencies:
      "@alicloud/pop-core" "^1.5.1"
  
  "@alicloud/mns@^1.0.0-beta6":
    "integrity" "sha1-AzqBmJmNXtk9onw72f8LnusJDpY="
    "resolved" "https://registry.npm.taobao.org/@alicloud/mns/download/@alicloud/mns-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "debug" "^2.6.3"
      "httpx" "^2.1.1"
      "kitx" "^1.2.0"
      "xml2js" "^0.4.17"
  
  "@alicloud/pop-core@^1.3.3", "@alicloud/pop-core@^1.5.1":
    "integrity" "sha1-l8DFu1kYlP7ah+ioLUXBF7673hM="
    "resolved" "https://registry.npm.taobao.org/@alicloud/pop-core/download/@alicloud/pop-core-1.7.9.tgz"
    "version" "1.7.9"
    dependencies:
      "debug" "^3.1.0"
      "httpx" "^2.1.2"
      "json-bigint" "^0.2.3"
      "kitx" "^1.2.1"
      "xml2js" "^0.4.17"
  
  "@alicloud/sms-sdk@^1.1.6":
    "integrity" "sha1-dwHq4eCDLINo1nebQtWDaPlEy/A="
    "resolved" "https://registry.npm.taobao.org/@alicloud/sms-sdk/download/@alicloud/sms-sdk-1.1.6.tgz"
    "version" "1.1.6"
    dependencies:
      "@alicloud/dybaseapi" "^1.0.0"
      "@alicloud/dysmsapi-2017-05-25" "^1.0.1"
      "@alicloud/mns" "^1.0.0-beta6"
      "babel-runtime" "^6.26.0"
  
  "@fast-csv/format@4.3.5":
    "integrity" "sha1-kNg9G0e2qvZ75w1hGPhPPhLuH/M="
    "resolved" "https://registry.npm.taobao.org/@fast-csv/format/download/@fast-csv/format-4.3.5.tgz"
    "version" "4.3.5"
    dependencies:
      "@types/node" "^14.0.1"
      "lodash.escaperegexp" "^4.1.2"
      "lodash.isboolean" "^3.0.3"
      "lodash.isequal" "^4.5.0"
      "lodash.isfunction" "^3.0.9"
      "lodash.isnil" "^4.0.0"
  
  "@fast-csv/parse@4.3.6":
    "integrity" "sha1-7kfQZAygKRA0x6qUA5p0TPsBkmQ="
    "resolved" "https://registry.npm.taobao.org/@fast-csv/parse/download/@fast-csv/parse-4.3.6.tgz"
    "version" "4.3.6"
    dependencies:
      "@types/node" "^14.0.1"
      "lodash.escaperegexp" "^4.1.2"
      "lodash.groupby" "^4.6.0"
      "lodash.isfunction" "^3.0.9"
      "lodash.isnil" "^4.0.0"
      "lodash.isundefined" "^3.0.1"
      "lodash.uniq" "^4.5.0"
  
  "@fidm/asn1@^1.0.4":
    "integrity" "sha512-esd1jyNvRb2HVaQGq2Gg8Z0kbQPXzV9Tq5Z14KNIov6KfFD6PTaRIO8UpcsYiTNzOqJpmyzWgVTrUwFV3UF4TQ=="
    "resolved" "https://registry.npmmirror.com/@fidm/asn1/-/asn1-1.0.4.tgz"
    "version" "1.0.4"
  
  "@fidm/x509@^1.2.1":
    "integrity" "sha512-nwc2iesjyc9hkuzcrMCBXQRn653XuAUKorfWM8PZyJawiy1QzLj4vahwzaI25+pfpwOLvMzbJ0uKpWLDNmo16w=="
    "resolved" "https://registry.npmmirror.com/@fidm/x509/-/x509-1.2.1.tgz"
    "version" "1.2.1"
    dependencies:
      "@fidm/asn1" "^1.0.4"
      "tweetnacl" "^1.0.1"
  
  "@mrmlnc/readdir-enhanced@^2.2.1":
    "integrity" "sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4="
    "resolved" "https://registry.npm.taobao.org/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz"
    "version" "2.2.1"
    dependencies:
      "call-me-maybe" "^1.0.1"
      "glob-to-regexp" "^0.3.0"
  
  "@nodelib/fs.stat@^1.1.2":
    "integrity" "sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs="
    "resolved" "https://registry.npm.taobao.org/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz"
    "version" "1.1.3"
  
  "@sindresorhus/is@^0.7.0":
    "integrity" "sha1-mgb08TfuhNffBGDB/bETX/psUP0="
    "resolved" "https://registry.npm.taobao.org/@sindresorhus/is/download/@sindresorhus/is-0.7.0.tgz?cache=0&sync_timestamp=1580320301895&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40sindresorhus%2Fis%2Fdownload%2F%40sindresorhus%2Fis-0.7.0.tgz"
    "version" "0.7.0"
  
  "@tootallnate/once@1":
    "integrity" "sha1-zLkURTYBeaBOf+av94wA/8Hur4I="
    "resolved" "https://registry.npm.taobao.org/@tootallnate/once/download/@tootallnate/once-1.1.2.tgz"
    "version" "1.1.2"
  
  "@types/node@^12.0.2":
    "integrity" "sha1-IT4VO6usDtFp1EptkZUB5o9Z3qk="
    "resolved" "https://registry.npm.taobao.org/@types/node/download/@types/node-12.12.26.tgz"
    "version" "12.12.26"
  
  "@types/node@^14.0.1":
    "integrity" "sha1-HpRHbbV+yTo3LH99KapXB8+5Izk="
    "resolved" "https://registry.nlark.com/@types/node/download/@types/node-14.17.2.tgz"
    "version" "14.17.2"
  
  "accepts@^1.3.5":
    "integrity" "sha1-UxvHJlF6OytB+FACHGzBXqq1B80="
    "resolved" "https://registry.npm.taobao.org/accepts/download/accepts-1.3.7.tgz"
    "version" "1.3.7"
    dependencies:
      "mime-types" "~2.1.24"
      "negotiator" "0.6.2"
  
  "acorn-node@^1.2.0":
    "integrity" "sha1-EUyV1kU55T3t4j3oudlt98euKvg="
    "resolved" "https://registry.npm.taobao.org/acorn-node/download/acorn-node-1.8.2.tgz"
    "version" "1.8.2"
    dependencies:
      "acorn" "^7.0.0"
      "acorn-walk" "^7.0.0"
      "xtend" "^4.0.2"
  
  "acorn-walk@^7.0.0":
    "integrity" "sha1-yLpvDxqsSwqeMtHwrxK+dpUo82s="
    "resolved" "https://registry.npm.taobao.org/acorn-walk/download/acorn-walk-7.0.0.tgz"
    "version" "7.0.0"
  
  "acorn@^7.0.0":
    "integrity" "sha1-lJ028sKSU12mAig1hsJHfFfrLWw="
    "resolved" "https://registry.npm.taobao.org/acorn/download/acorn-7.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn%2Fdownload%2Facorn-7.1.0.tgz"
    "version" "7.1.0"
  
  "address-parse-cn@^2.3.2":
    "integrity" "sha512-dIo7z5xxi05EabMkj9weuUis4KkM9qCqrQVjXpzE1BS8wHbjDnb+ifRdjhbvxA7VcbWegmtIciHAz1UBpVEbGw=="
    "resolved" "https://registry.npmmirror.com/address-parse-cn/-/address-parse-cn-2.3.2.tgz"
    "version" "2.3.2"
  
  "address-parse@^1.2.19":
    "integrity" "sha512-LjTHC6dGDO/Ymh0WEZwvEb/XxGpm7IJEJVzNsy+aBQPGIQXs0T+SlytZIPnGNE1VYJ3fwwME2pSJDczmQaogvg=="
    "resolved" "https://registry.npmmirror.com/address-parse/-/address-parse-1.2.19.tgz"
    "version" "1.2.19"
    dependencies:
      "address-parse" "^1.2.19"
  
  "address-smart-parse@^3.0.3":
    "integrity" "sha512-MMhe9hFSJJWK6h2MTLVbsspmPDErMIX+ZtLoQlhpsVjqfiqzFWwfxVySt1xHds1nUIBOfGEJiIeF2lTy77AFVA=="
    "resolved" "https://registry.npmmirror.com/address-smart-parse/-/address-smart-parse-3.0.3.tgz"
    "version" "3.0.3"
  
  "address@^1.0.0", "address@>=0.0.1":
    "integrity" "sha1-vxEWycdYxRt6kz0pa3LCIe2UKLY="
    "resolved" "https://registry.npm.taobao.org/address/download/address-1.1.2.tgz"
    "version" "1.1.2"
  
  "adler-32@~1.2.0":
    "integrity" "sha1-aj5r8KY5ALoVZSgIyxXGgT0aXyU="
    "resolved" "https://registry.npm.taobao.org/adler-32/download/adler-32-1.2.0.tgz"
    "version" "1.2.0"
    dependencies:
      "exit-on-epipe" "~1.0.1"
      "printj" "~1.1.0"
  
  "adler-32@~1.3.0":
    "version" "1.3.1"
  
  "adonis@^0.9.0":
    "integrity" "sha1-XEyOnK6wMY2eqYwKVncbgwMbYQw="
    "resolved" "https://registry.npm.taobao.org/adonis/download/adonis-0.9.0.tgz"
    "version" "0.9.0"
    dependencies:
      "aphrodite" "^1.1.0"
  
  "agent-base@^4.2.0":
    "integrity" "sha1-gWXwHENgCbzK0LHRIvBe13Dvxu4="
    "resolved" "https://registry.npm.taobao.org/agent-base/download/agent-base-4.3.0.tgz"
    "version" "4.3.0"
    dependencies:
      "es6-promisify" "^5.0.0"
  
  "agent-base@^4.3.0":
    "integrity" "sha1-gWXwHENgCbzK0LHRIvBe13Dvxu4="
    "resolved" "https://registry.npm.taobao.org/agent-base/download/agent-base-4.3.0.tgz"
    "version" "4.3.0"
    dependencies:
      "es6-promisify" "^5.0.0"
  
  "agent-base@^6.0.0", "agent-base@6":
    "integrity" "sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c="
    "resolved" "https://registry.npm.taobao.org/agent-base/download/agent-base-6.0.2.tgz?cache=0&sync_timestamp=1603479872755&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fagent-base%2Fdownload%2Fagent-base-6.0.2.tgz"
    "version" "6.0.2"
    dependencies:
      "debug" "4"
  
  "agent-base@~4.2.1":
    "integrity" "sha1-2J5ZmfeXh1Z0wH2H8mD8Qeg+jKk="
    "resolved" "https://registry.npm.taobao.org/agent-base/download/agent-base-4.2.1.tgz"
    "version" "4.2.1"
    dependencies:
      "es6-promisify" "^5.0.0"
  
  "agent-base@4":
    "integrity" "sha1-gWXwHENgCbzK0LHRIvBe13Dvxu4="
    "resolved" "https://registry.npm.taobao.org/agent-base/download/agent-base-4.3.0.tgz"
    "version" "4.3.0"
    dependencies:
      "es6-promisify" "^5.0.0"
  
  "agentkeepalive@^3.4.1":
    "integrity" "sha1-oROSTdP6JKC8O3gQjEUMKr7gD2c="
    "resolved" "https://registry.npm.taobao.org/agentkeepalive/download/agentkeepalive-3.5.2.tgz"
    "version" "3.5.2"
    dependencies:
      "humanize-ms" "^1.2.1"
  
  "ajv@^6.5.5":
    "integrity" "sha1-w2B8vIrjktilpTbyWyH45fP4f+k="
    "resolved" "https://registry.npm.taobao.org/ajv/download/ajv-6.11.0.tgz"
    "version" "6.11.0"
    dependencies:
      "fast-deep-equal" "^3.1.1"
      "fast-json-stable-stringify" "^2.0.0"
      "json-schema-traverse" "^0.4.1"
      "uri-js" "^4.2.2"
  
  "ali-oss@^6.1.0":
    "integrity" "sha1-4UYenk1PagFobNlORhc1BwJXbys="
    "resolved" "https://registry.npm.taobao.org/ali-oss/download/ali-oss-6.5.1.tgz?cache=0&sync_timestamp=1579255324519&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fali-oss%2Fdownload%2Fali-oss-6.5.1.tgz"
    "version" "6.5.1"
    dependencies:
      "address" "^1.0.0"
      "agentkeepalive" "^3.4.1"
      "any-promise" "^1.3.0"
      "bowser" "^1.6.0"
      "co-defer" "^1.0.0"
      "copy-to" "^2.0.1"
      "dateformat" "^2.0.0"
      "debug" "^2.2.0"
      "destroy" "^1.0.4"
      "end-or-error" "^1.0.1"
      "get-ready" "^1.0.0"
      "humanize-ms" "^1.2.0"
      "is-type-of" "^1.0.0"
      "jstoxml" "^0.2.3"
      "merge-descriptors" "^1.0.1"
      "mime" "^1.3.4"
      "mz-modules" "^2.1.0"
      "platform" "^1.3.1"
      "sdk-base" "^2.0.1"
      "stream-http" "2.8.2"
      "stream-wormhole" "^1.0.4"
      "urllib" "^2.33.1"
      "utility" "^1.8.0"
      "xml2js" "^0.4.16"
  
  "ansi-bgblack@^0.1.1":
    "integrity" "sha1-poulAHiHcBtqr74/oNrf36juPKI="
    "resolved" "https://registry.npm.taobao.org/ansi-bgblack/download/ansi-bgblack-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-bgblue@^0.1.1":
    "integrity" "sha1-Z73ATtybm1J4lp2hlt6j11yMNhM="
    "resolved" "https://registry.npm.taobao.org/ansi-bgblue/download/ansi-bgblue-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-bgcyan@^0.1.1":
    "integrity" "sha1-WEiUJWAL3p9VBwaN2Wnr/bUP52g="
    "resolved" "https://registry.npm.taobao.org/ansi-bgcyan/download/ansi-bgcyan-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-bggreen@^0.1.1":
    "integrity" "sha1-TjGRJIUplD9DIelr8THRwTgWr0k="
    "resolved" "https://registry.npm.taobao.org/ansi-bggreen/download/ansi-bggreen-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-bgmagenta@^0.1.1":
    "integrity" "sha1-myhDLAduqpmUGGcqPvvhk5HCx6E="
    "resolved" "https://registry.npm.taobao.org/ansi-bgmagenta/download/ansi-bgmagenta-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-bgred@^0.1.1":
    "integrity" "sha1-p2+Sg4OCukMpCmwXeEJPmE1vEEE="
    "resolved" "https://registry.npm.taobao.org/ansi-bgred/download/ansi-bgred-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-bgwhite@^0.1.1":
    "integrity" "sha1-ZQRlE3elim7OzQMxmU5IAljhG6g="
    "resolved" "https://registry.npm.taobao.org/ansi-bgwhite/download/ansi-bgwhite-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-bgyellow@^0.1.1":
    "integrity" "sha1-w/4usIzUdmSAKeaHTRWgs49h1E8="
    "resolved" "https://registry.npm.taobao.org/ansi-bgyellow/download/ansi-bgyellow-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-black@^0.1.1":
    "integrity" "sha1-9hheiJNgslRaHsUMC/Bj/EMDJFM="
    "resolved" "https://registry.npm.taobao.org/ansi-black/download/ansi-black-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-blue@^0.1.1":
    "integrity" "sha1-FbgEmQ6S/JyoxUds6PaZd3wh7b8="
    "resolved" "https://registry.npm.taobao.org/ansi-blue/download/ansi-blue-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-bold@^0.1.1":
    "integrity" "sha1-PmOVCvWswq4uZw5vZ96xFdGl9QU="
    "resolved" "https://registry.npm.taobao.org/ansi-bold/download/ansi-bold-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-colors@^0.2.0":
    "integrity" "sha1-csMd4qDZoszQysMMyYI+6y9kNLU="
    "resolved" "https://registry.npm.taobao.org/ansi-colors/download/ansi-colors-0.2.0.tgz"
    "version" "0.2.0"
    dependencies:
      "ansi-bgblack" "^0.1.1"
      "ansi-bgblue" "^0.1.1"
      "ansi-bgcyan" "^0.1.1"
      "ansi-bggreen" "^0.1.1"
      "ansi-bgmagenta" "^0.1.1"
      "ansi-bgred" "^0.1.1"
      "ansi-bgwhite" "^0.1.1"
      "ansi-bgyellow" "^0.1.1"
      "ansi-black" "^0.1.1"
      "ansi-blue" "^0.1.1"
      "ansi-bold" "^0.1.1"
      "ansi-cyan" "^0.1.1"
      "ansi-dim" "^0.1.1"
      "ansi-gray" "^0.1.1"
      "ansi-green" "^0.1.1"
      "ansi-grey" "^0.1.1"
      "ansi-hidden" "^0.1.1"
      "ansi-inverse" "^0.1.1"
      "ansi-italic" "^0.1.1"
      "ansi-magenta" "^0.1.1"
      "ansi-red" "^0.1.1"
      "ansi-reset" "^0.1.1"
      "ansi-strikethrough" "^0.1.1"
      "ansi-underline" "^0.1.1"
      "ansi-white" "^0.1.1"
      "ansi-yellow" "^0.1.1"
      "lazy-cache" "^2.0.1"
  
  "ansi-colors@^1.1.0":
    "integrity" "sha1-Y3S03V1HGP884npnGjscrQdxMqk="
    "resolved" "https://registry.npm.taobao.org/ansi-colors/download/ansi-colors-1.1.0.tgz"
    "version" "1.1.0"
    dependencies:
      "ansi-wrap" "^0.1.0"
  
  "ansi-cyan@^0.1.1":
    "integrity" "sha1-U4rlKK+JgvKK4w2G8vF0VtJgmHM="
    "resolved" "https://registry.npm.taobao.org/ansi-cyan/download/ansi-cyan-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-dim@^0.1.1":
    "integrity" "sha1-QN5MYDqoCG2Oeoa4/5mNXDbu/Ww="
    "resolved" "https://registry.npm.taobao.org/ansi-dim/download/ansi-dim-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-gray@^0.1.1":
    "integrity" "sha1-KWLPVOyXksSFEKPetSRDaGHvclE="
    "resolved" "https://registry.npm.taobao.org/ansi-gray/download/ansi-gray-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-green@^0.1.1":
    "integrity" "sha1-il2al55FjVfEDjNYCzc5C44Q0Pc="
    "resolved" "https://registry.npm.taobao.org/ansi-green/download/ansi-green-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-grey@^0.1.1":
    "integrity" "sha1-WdmLasK6GfilF5jphT+6eDOaM8E="
    "resolved" "https://registry.npm.taobao.org/ansi-grey/download/ansi-grey-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-hidden@^0.1.1":
    "integrity" "sha1-7WpMSY0rt8uyidvyqNHcyFZ/rg8="
    "resolved" "https://registry.npm.taobao.org/ansi-hidden/download/ansi-hidden-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-inverse@^0.1.1":
    "integrity" "sha1-tq9Fgm/oJr+1KKbHmIV5Q1XM0mk="
    "resolved" "https://registry.npm.taobao.org/ansi-inverse/download/ansi-inverse-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-italic@^0.1.1":
    "integrity" "sha1-EEdDRj9iXBQqA2c5z4XtpoiYbyM="
    "resolved" "https://registry.npm.taobao.org/ansi-italic/download/ansi-italic-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-magenta@^0.1.1":
    "integrity" "sha1-BjtboW+z8j4c/aKwfAqJ3hHkMK4="
    "resolved" "https://registry.npm.taobao.org/ansi-magenta/download/ansi-magenta-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-red@^0.1.1":
    "integrity" "sha1-jGOPnRCAgAo1PJwoyKgcpHBdlGw="
    "resolved" "https://registry.npm.taobao.org/ansi-red/download/ansi-red-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-regex@^2.0.0":
    "integrity" "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="
    "resolved" "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-2.1.1.tgz"
    "version" "2.1.1"
  
  "ansi-regex@^3.0.0":
    "integrity" "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg="
    "resolved" "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-3.0.0.tgz"
    "version" "3.0.0"
  
  "ansi-regex@^4.1.0":
    "integrity" "sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc="
    "resolved" "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-4.1.0.tgz"
    "version" "4.1.0"
  
  "ansi-reset@^0.1.1":
    "integrity" "sha1-5+cSksPH3c1NYu9KbHwFmAkRw7c="
    "resolved" "https://registry.npm.taobao.org/ansi-reset/download/ansi-reset-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-strikethrough@^0.1.1":
    "integrity" "sha1-2Eh3FAss/wfRyT685pkE9oiF5Wg="
    "resolved" "https://registry.npm.taobao.org/ansi-strikethrough/download/ansi-strikethrough-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-styles@^2.2.1":
    "integrity" "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4="
    "resolved" "https://registry.npm.taobao.org/ansi-styles/download/ansi-styles-2.2.1.tgz"
    "version" "2.2.1"
  
  "ansi-styles@^3.2.0", "ansi-styles@^3.2.1":
    "integrity" "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0="
    "resolved" "https://registry.npm.taobao.org/ansi-styles/download/ansi-styles-3.2.1.tgz"
    "version" "3.2.1"
    dependencies:
      "color-convert" "^1.9.0"
  
  "ansi-underline@^0.1.1":
    "integrity" "sha1-38kg9Ml7WXfqFi34/7mIMIqqcaQ="
    "resolved" "https://registry.npm.taobao.org/ansi-underline/download/ansi-underline-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-white@^0.1.1":
    "integrity" "sha1-nHe3wZPF7pkuYBHTbsTJIbRXiUQ="
    "resolved" "https://registry.npm.taobao.org/ansi-white/download/ansi-white-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "ansi-wrap@^0.1.0", "ansi-wrap@0.1.0":
    "integrity" "sha1-qCJQ3bABXponyoLoLqYDu/pF768="
    "resolved" "https://registry.npm.taobao.org/ansi-wrap/download/ansi-wrap-0.1.0.tgz"
    "version" "0.1.0"
  
  "ansi-yellow@^0.1.1":
    "integrity" "sha1-y5NW8vRscy8OMZnmEClVp32oPB0="
    "resolved" "https://registry.npm.taobao.org/ansi-yellow/download/ansi-yellow-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-wrap" "0.1.0"
  
  "any-promise@^0.1.0":
    "integrity" "sha1-gwtoCqflbzNFHUsEnzvYBESY7ic="
    "resolved" "https://registry.npm.taobao.org/any-promise/download/any-promise-0.1.0.tgz"
    "version" "0.1.0"
  
  "any-promise@^1.0.0", "any-promise@^1.3.0":
    "integrity" "sha1-q8av7tzqUugJzcA3au0845Y10X8="
    "resolved" "https://registry.npm.taobao.org/any-promise/download/any-promise-1.3.0.tgz"
    "version" "1.3.0"
  
  "aphrodite@^1.1.0":
    "integrity" "sha1-g1jDbIC7A67puXFlqqcBhiJbSYM="
    "resolved" "https://registry.npm.taobao.org/aphrodite/download/aphrodite-1.2.5.tgz"
    "version" "1.2.5"
    dependencies:
      "asap" "^2.0.3"
      "inline-style-prefixer" "^3.0.1"
      "string-hash" "^1.1.3"
  
  "archiver-utils@^2.1.0":
    "integrity" "sha1-6KRg6UtpPD49oYKgmMpihbqSSeI="
    "resolved" "https://registry.npm.taobao.org/archiver-utils/download/archiver-utils-2.1.0.tgz"
    "version" "2.1.0"
    dependencies:
      "glob" "^7.1.4"
      "graceful-fs" "^4.2.0"
      "lazystream" "^1.0.0"
      "lodash.defaults" "^4.2.0"
      "lodash.difference" "^4.5.0"
      "lodash.flatten" "^4.4.0"
      "lodash.isplainobject" "^4.0.6"
      "lodash.union" "^4.6.0"
      "normalize-path" "^3.0.0"
      "readable-stream" "^2.0.0"
  
  "archiver@^5.0.0":
    "integrity" "sha1-3T4JdiRIF0HfYmJnVk992GQKRbo="
    "resolved" "https://registry.npm.taobao.org/archiver/download/archiver-5.3.0.tgz"
    "version" "5.3.0"
    dependencies:
      "archiver-utils" "^2.1.0"
      "async" "^3.2.0"
      "buffer-crc32" "^0.2.1"
      "readable-stream" "^3.6.0"
      "readdir-glob" "^1.0.0"
      "tar-stream" "^2.2.0"
      "zip-stream" "^4.1.0"
  
  "arr-diff@^4.0.0":
    "integrity" "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA="
    "resolved" "https://registry.npm.taobao.org/arr-diff/download/arr-diff-4.0.0.tgz"
    "version" "4.0.0"
  
  "arr-flatten@^1.0.3", "arr-flatten@^1.1.0":
    "integrity" "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE="
    "resolved" "https://registry.npm.taobao.org/arr-flatten/download/arr-flatten-1.1.0.tgz"
    "version" "1.1.0"
  
  "arr-swap@^1.0.1":
    "integrity" "sha1-FHWQ7WX8gVvAf+8Jl8Llgj1kNTQ="
    "resolved" "https://registry.npm.taobao.org/arr-swap/download/arr-swap-1.0.1.tgz"
    "version" "1.0.1"
    dependencies:
      "is-number" "^3.0.0"
  
  "arr-union@^3.1.0":
    "integrity" "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ="
    "resolved" "https://registry.npm.taobao.org/arr-union/download/arr-union-3.1.0.tgz"
    "version" "3.1.0"
  
  "array-each@^1.0.1":
    "integrity" "sha1-p5SvDAWrF1KEbudTofIRoFugxE8="
    "resolved" "https://registry.npm.taobao.org/array-each/download/array-each-1.0.1.tgz"
    "version" "1.0.1"
  
  "array-slice@^1.0.0":
    "integrity" "sha1-42jqFfibxwaff/uJrsOmx9SsItQ="
    "resolved" "https://registry.npm.taobao.org/array-slice/download/array-slice-1.1.0.tgz"
    "version" "1.1.0"
  
  "array-union@^1.0.1":
    "integrity" "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk="
    "resolved" "https://registry.npm.taobao.org/array-union/download/array-union-1.0.2.tgz"
    "version" "1.0.2"
    dependencies:
      "array-uniq" "^1.0.1"
  
  "array-uniq@^1.0.1":
    "integrity" "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY="
    "resolved" "https://registry.npm.taobao.org/array-uniq/download/array-uniq-1.0.3.tgz"
    "version" "1.0.3"
  
  "array-unique@^0.3.2":
    "integrity" "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg="
    "resolved" "https://registry.npm.taobao.org/array-unique/download/array-unique-0.3.2.tgz"
    "version" "0.3.2"
  
  "arrify@^1.0.1":
    "integrity" "sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0="
    "resolved" "https://registry.npm.taobao.org/arrify/download/arrify-1.0.1.tgz"
    "version" "1.0.1"
  
  "asap@^2.0.3":
    "integrity" "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY="
    "resolved" "https://registry.npm.taobao.org/asap/download/asap-2.0.6.tgz"
    "version" "2.0.6"
  
  "asn1@~0.2.3":
    "integrity" "sha1-jSR136tVO7M+d7VOWeiAu4ziMTY="
    "resolved" "https://registry.npm.taobao.org/asn1/download/asn1-0.2.4.tgz"
    "version" "0.2.4"
    dependencies:
      "safer-buffer" "~2.1.0"
  
  "assert-plus@^1.0.0", "assert-plus@1.0.0":
    "integrity" "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU="
    "resolved" "https://registry.npm.taobao.org/assert-plus/download/assert-plus-1.0.0.tgz"
    "version" "1.0.0"
  
  "assertion-error@^1.1.0":
    "integrity" "sha1-5gtrDo8wG9l+U3UhW9pAbIURjAs="
    "resolved" "https://registry.npm.taobao.org/assertion-error/download/assertion-error-1.1.0.tgz"
    "version" "1.1.0"
  
  "assign-symbols@^1.0.0":
    "integrity" "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c="
    "resolved" "https://registry.npm.taobao.org/assign-symbols/download/assign-symbols-1.0.0.tgz"
    "version" "1.0.0"
  
  "ast-types@^0.13.2":
    "integrity" "sha1-7g13s0MmOWXsw/ti2hbnIisrZ4I="
    "resolved" "https://registry.npm.taobao.org/ast-types/download/ast-types-0.13.4.tgz"
    "version" "0.13.4"
    dependencies:
      "tslib" "^2.0.1"
  
  "ast-types@0.x.x":
    "integrity" "sha1-3zm2d6kRqD86BJZE+3T93tI86kg="
    "resolved" "https://registry.npm.taobao.org/ast-types/download/ast-types-0.13.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fast-types%2Fdownload%2Fast-types-0.13.2.tgz"
    "version" "0.13.2"
  
  "async@^2.6.1":
    "integrity" "sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8="
    "resolved" "https://registry.npm.taobao.org/async/download/async-2.6.3.tgz"
    "version" "2.6.3"
    dependencies:
      "lodash" "^4.17.14"
  
  "async@^3.2.0":
    "integrity" "sha1-s6JoXF67ZB094C0WEALGD8n4VyA="
    "resolved" "https://registry.npm.taobao.org/async/download/async-3.2.0.tgz?cache=0&sync_timestamp=1582513244496&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fasync%2Fdownload%2Fasync-3.2.0.tgz"
    "version" "3.2.0"
  
  "asynckit@^0.4.0":
    "integrity" "sha1-x57Zf380y48robyXkLzDZkdLS3k="
    "resolved" "https://registry.npm.taobao.org/asynckit/download/asynckit-0.4.0.tgz"
    "version" "0.4.0"
  
  "atob@^2.1.2":
    "integrity" "sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k="
    "resolved" "https://registry.npm.taobao.org/atob/download/atob-2.1.2.tgz"
    "version" "2.1.2"
  
  "aws-sign2@~0.7.0":
    "integrity" "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg="
    "resolved" "https://registry.npm.taobao.org/aws-sign2/download/aws-sign2-0.7.0.tgz"
    "version" "0.7.0"
  
  "aws4@^1.8.0":
    "integrity" "sha1-fjPY99RJs/ZzzXLeuavcVS2+Uo4="
    "resolved" "https://registry.npm.taobao.org/aws4/download/aws4-1.9.1.tgz?cache=0&sync_timestamp=1578959055063&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Faws4%2Fdownload%2Faws4-1.9.1.tgz"
    "version" "1.9.1"
  
  "axios@^0.18.0":
    "integrity" "sha1-/z8N4ue10YDnV62YAA8Qgbh7zqM="
    "resolved" "https://registry.npm.taobao.org/axios/download/axios-0.18.1.tgz"
    "version" "0.18.1"
    dependencies:
      "follow-redirects" "1.5.10"
      "is-buffer" "^2.0.2"
  
  "babel-runtime@^6.26.0":
    "integrity" "sha1-llxwWGaOgrVde/4E/yM3vItWR/4="
    "resolved" "https://registry.npm.taobao.org/babel-runtime/download/babel-runtime-6.26.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-runtime%2Fdownload%2Fbabel-runtime-6.26.0.tgz"
    "version" "6.26.0"
    dependencies:
      "core-js" "^2.4.0"
      "regenerator-runtime" "^0.11.0"
  
  "balanced-match@^1.0.0":
    "integrity" "sha1-ibTRmasr7kneFk6gK4nORi1xt2c="
    "resolved" "https://registry.npm.taobao.org/balanced-match/download/balanced-match-1.0.0.tgz"
    "version" "1.0.0"
  
  "base@^0.11.1":
    "integrity" "sha1-e95c7RRbbVUakNuH+DxVi060io8="
    "resolved" "https://registry.npm.taobao.org/base/download/base-0.11.2.tgz"
    "version" "0.11.2"
    dependencies:
      "cache-base" "^1.0.1"
      "class-utils" "^0.3.5"
      "component-emitter" "^1.2.1"
      "define-property" "^1.0.0"
      "isobject" "^3.0.1"
      "mixin-deep" "^1.2.0"
      "pascalcase" "^0.1.1"
  
  "base64-js@^1.0.2":
    "integrity" "sha1-WOzoy3XdB+ce0IxzarxfrE2/jfE="
    "resolved" "https://registry.npm.taobao.org/base64-js/download/base64-js-1.3.1.tgz"
    "version" "1.3.1"
  
  "base64-js@^1.3.1":
    "integrity" "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo="
    "resolved" "https://registry.npm.taobao.org/base64-js/download/base64-js-1.5.1.tgz"
    "version" "1.5.1"
  
  "basic-auth@^2.0.1":
    "integrity" "sha1-uZgnm/R844NEtPPPkW1Gebv1Hjo="
    "resolved" "https://registry.npm.taobao.org/basic-auth/download/basic-auth-2.0.1.tgz"
    "version" "2.0.1"
    dependencies:
      "safe-buffer" "5.1.2"
  
  "bcrypt-pbkdf@^1.0.0":
    "integrity" "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4="
    "resolved" "https://registry.npm.taobao.org/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz"
    "version" "1.0.2"
    dependencies:
      "tweetnacl" "^0.14.3"
  
  "bcryptjs@^2.4.3":
    "integrity" "sha1-mrVie5PmBiH/fNrF2pczAn3x0Ms="
    "resolved" "https://registry.npm.taobao.org/bcryptjs/download/bcryptjs-2.4.3.tgz"
    "version" "2.4.3"
  
  "big-integer@^1.6.17":
    "integrity" "sha1-j9iL0WMsukocjD49cVnwi7lbS54="
    "resolved" "https://registry.npm.taobao.org/big-integer/download/big-integer-1.6.48.tgz"
    "version" "1.6.48"
  
  "bignumber.js@^4.0.0":
    "integrity" "sha1-228UBnwUC9RmJIFaeRbJLZtsJLE="
    "resolved" "https://registry.npm.taobao.org/bignumber.js/download/bignumber.js-4.1.0.tgz"
    "version" "4.1.0"
  
  "bignumber.js@^7.0.0":
    "integrity" "sha1-gMBIdZ2CaACAfEv9Uh5Q7bulel8="
    "resolved" "https://registry.npm.taobao.org/bignumber.js/download/bignumber.js-7.2.1.tgz"
    "version" "7.2.1"
  
  "bignumber.js@9.0.0":
    "integrity" "sha1-gFiA+Eoym16sbny2+CdLbYK98HU="
    "resolved" "https://registry.npm.taobao.org/bignumber.js/download/bignumber.js-9.0.0.tgz"
    "version" "9.0.0"
  
  "binary@~0.3.0":
    "integrity" "sha1-n2BVO8XOjDOG87VTz/R0Yq3sqnk="
    "resolved" "https://registry.nlark.com/binary/download/binary-0.3.0.tgz"
    "version" "0.3.0"
    dependencies:
      "buffers" "~0.1.1"
      "chainsaw" "~0.1.0"
  
  "bl@^4.0.3":
    "integrity" "sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo="
    "resolved" "https://registry.npm.taobao.org/bl/download/bl-4.1.0.tgz?cache=0&sync_timestamp=1617381897308&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbl%2Fdownload%2Fbl-4.1.0.tgz"
    "version" "4.1.0"
    dependencies:
      "buffer" "^5.5.0"
      "inherits" "^2.0.4"
      "readable-stream" "^3.4.0"
  
  "bluebird@^3.5.1":
    "integrity" "sha1-nyKcFb4nJFT/qXOs4NvueaGww28="
    "resolved" "https://registry.npm.taobao.org/bluebird/download/bluebird-3.7.2.tgz"
    "version" "3.7.2"
  
  "bluebird@~3.4.1":
    "integrity" "sha1-9y12C+Cbf3bQjtj66Ysomo0F+rM="
    "resolved" "https://registry.npm.taobao.org/bluebird/download/bluebird-3.4.7.tgz"
    "version" "3.4.7"
  
  "bowser@^1.6.0", "bowser@^1.7.3":
    "integrity" "sha1-iQxYooE6nTJDcEM0+oG5alwVDJo="
    "resolved" "https://registry.npm.taobao.org/bowser/download/bowser-1.9.4.tgz"
    "version" "1.9.4"
  
  "brace-expansion@^1.1.7":
    "integrity" "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0="
    "resolved" "https://registry.npm.taobao.org/brace-expansion/download/brace-expansion-1.1.11.tgz"
    "version" "1.1.11"
    dependencies:
      "balanced-match" "^1.0.0"
      "concat-map" "0.0.1"
  
  "braces@^2.3.1":
    "integrity" "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk="
    "resolved" "https://registry.npm.taobao.org/braces/download/braces-2.3.2.tgz"
    "version" "2.3.2"
    dependencies:
      "arr-flatten" "^1.1.0"
      "array-unique" "^0.3.2"
      "extend-shallow" "^2.0.1"
      "fill-range" "^4.0.0"
      "isobject" "^3.0.1"
      "repeat-element" "^1.1.2"
      "snapdragon" "^0.8.1"
      "snapdragon-node" "^2.0.1"
      "split-string" "^3.0.2"
      "to-regex" "^3.0.1"
  
  "buffer-alloc-unsafe@^1.1.0":
    "integrity" "sha1-vX3CauKXLQ7aJTvgYdupkjScGfA="
    "resolved" "https://registry.npm.taobao.org/buffer-alloc-unsafe/download/buffer-alloc-unsafe-1.1.0.tgz"
    "version" "1.1.0"
  
  "buffer-alloc@^1.2.0":
    "integrity" "sha1-iQ3ZDZI6hz4I4Q5f1RpX5bfM4Ow="
    "resolved" "https://registry.npm.taobao.org/buffer-alloc/download/buffer-alloc-1.2.0.tgz"
    "version" "1.2.0"
    dependencies:
      "buffer-alloc-unsafe" "^1.1.0"
      "buffer-fill" "^1.0.0"
  
  "buffer-crc32@^0.2.1", "buffer-crc32@^0.2.13":
    "integrity" "sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI="
    "resolved" "https://registry.npm.taobao.org/buffer-crc32/download/buffer-crc32-0.2.13.tgz"
    "version" "0.2.13"
  
  "buffer-equal-constant-time@1.0.1":
    "integrity" "sha1-+OcRMvf/5uAaXJaXpMbz5I1cyBk="
    "resolved" "https://registry.npm.taobao.org/buffer-equal-constant-time/download/buffer-equal-constant-time-1.0.1.tgz"
    "version" "1.0.1"
  
  "buffer-fill@^1.0.0":
    "integrity" "sha1-+PeLdniYiO858gXNY39o5wISKyw="
    "resolved" "https://registry.npm.taobao.org/buffer-fill/download/buffer-fill-1.0.0.tgz"
    "version" "1.0.0"
  
  "buffer-from@^1.1.0", "buffer-from@^1.1.1":
    "integrity" "sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8="
    "resolved" "https://registry.npm.taobao.org/buffer-from/download/buffer-from-1.1.1.tgz"
    "version" "1.1.1"
  
  "buffer-indexof-polyfill@~1.0.0":
    "integrity" "sha1-0nMhNcWZnGSyd/z5savjSYJUcpw="
    "resolved" "https://registry.npm.taobao.org/buffer-indexof-polyfill/download/buffer-indexof-polyfill-1.0.2.tgz?cache=0&sync_timestamp=1599616507344&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbuffer-indexof-polyfill%2Fdownload%2Fbuffer-indexof-polyfill-1.0.2.tgz"
    "version" "1.0.2"
  
  "buffer@^5.4.3":
    "integrity" "sha1-P7ycaetxPTI+P8Gole7gcQwHIRU="
    "resolved" "https://registry.npm.taobao.org/buffer/download/buffer-5.4.3.tgz"
    "version" "5.4.3"
    dependencies:
      "base64-js" "^1.0.2"
      "ieee754" "^1.1.4"
  
  "buffer@^5.5.0":
    "integrity" "sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA="
    "resolved" "https://registry.npm.taobao.org/buffer/download/buffer-5.7.1.tgz?cache=0&sync_timestamp=1606098073225&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbuffer%2Fdownload%2Fbuffer-5.7.1.tgz"
    "version" "5.7.1"
    dependencies:
      "base64-js" "^1.3.1"
      "ieee754" "^1.1.13"
  
  "buffers@~0.1.1":
    "integrity" "sha1-skV5w77U1tOWru5tmorn9Ugqt7s="
    "resolved" "https://registry.npm.taobao.org/buffers/download/buffers-0.1.1.tgz"
    "version" "0.1.1"
  
  "builtin-status-codes@^3.0.0":
    "integrity" "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug="
    "resolved" "https://registry.npm.taobao.org/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz"
    "version" "3.0.0"
  
  "bytes@^3.0.0", "bytes@3.1.0":
    "integrity" "sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY="
    "resolved" "https://registry.npm.taobao.org/bytes/download/bytes-3.1.0.tgz"
    "version" "3.1.0"
  
  "cache-base@^1.0.1":
    "integrity" "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI="
    "resolved" "https://registry.npm.taobao.org/cache-base/download/cache-base-1.0.1.tgz"
    "version" "1.0.1"
    dependencies:
      "collection-visit" "^1.0.0"
      "component-emitter" "^1.2.1"
      "get-value" "^2.0.6"
      "has-value" "^1.0.0"
      "isobject" "^3.0.1"
      "set-value" "^2.0.0"
      "to-object-path" "^0.3.0"
      "union-value" "^1.0.0"
      "unset-value" "^1.0.0"
  
  "call-bind@^1.0.0":
    "integrity" "sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA=="
    "resolved" "https://registry.npmmirror.com/call-bind/-/call-bind-1.0.2.tgz"
    "version" "1.0.2"
    dependencies:
      "function-bind" "^1.1.1"
      "get-intrinsic" "^1.0.2"
  
  "call-me-maybe@^1.0.1":
    "integrity" "sha1-JtII6onje1y95gJQoV8DHBak1ms="
    "resolved" "https://registry.npm.taobao.org/call-me-maybe/download/call-me-maybe-1.0.1.tgz"
    "version" "1.0.1"
  
  "caller-path@^0.1.0":
    "integrity" "sha1-lAhe9jWB7NPaqSREqP6U6CV3dR8="
    "resolved" "https://registry.npm.taobao.org/caller-path/download/caller-path-0.1.0.tgz"
    "version" "0.1.0"
    dependencies:
      "callsites" "^0.2.0"
  
  "caller@^1.0.1":
    "integrity" "sha1-uFGGD3Dhlds9J3OVqhp+I+ow7PU="
    "resolved" "https://registry.npm.taobao.org/caller/download/caller-1.0.1.tgz"
    "version" "1.0.1"
  
  "callsites@^0.2.0":
    "integrity" "sha1-r6uWJikQp/M8GaV3WCXGnzTjUMo="
    "resolved" "https://registry.npm.taobao.org/callsites/download/callsites-0.2.0.tgz"
    "version" "0.2.0"
  
  "camelcase@^5.0.0":
    "integrity" "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA="
    "resolved" "https://registry.npm.taobao.org/camelcase/download/camelcase-5.3.1.tgz"
    "version" "5.3.1"
  
  "caseless@~0.12.0":
    "integrity" "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw="
    "resolved" "https://registry.npm.taobao.org/caseless/download/caseless-0.12.0.tgz"
    "version" "0.12.0"
  
  "cfb@^1.1.2", "cfb@~1.2.1":
    "version" "1.2.2"
    dependencies:
      "adler-32" "~1.3.0"
      "crc-32" "~1.2.0"
  
  "chai-subset@^1.6.0":
    "integrity" "sha1-pdDKFOMpp5WW7XAFi2ZGvWmIz+k="
    "resolved" "https://registry.npm.taobao.org/chai-subset/download/chai-subset-1.6.0.tgz"
    "version" "1.6.0"
  
  "chai@^4.1.2":
    "integrity" "sha1-dgqnLPION5XoSxKHfODoNzeqKeU="
    "resolved" "https://registry.npm.taobao.org/chai/download/chai-4.2.0.tgz"
    "version" "4.2.0"
    dependencies:
      "assertion-error" "^1.1.0"
      "check-error" "^1.0.2"
      "deep-eql" "^3.0.1"
      "get-func-name" "^2.0.0"
      "pathval" "^1.1.0"
      "type-detect" "^4.0.5"
  
  "chainsaw@~0.1.0":
    "integrity" "sha1-XqtQsor+WAdNDVgpE4iCi15fvJg="
    "resolved" "https://registry.nlark.com/chainsaw/download/chainsaw-0.1.0.tgz"
    "version" "0.1.0"
    dependencies:
      "traverse" ">=0.3.0 <0.4"
  
  "chalk@^1.1.1":
    "integrity" "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg="
    "resolved" "https://registry.npm.taobao.org/chalk/download/chalk-1.1.3.tgz?cache=0&sync_timestamp=1573282918610&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchalk%2Fdownload%2Fchalk-1.1.3.tgz"
    "version" "1.1.3"
    dependencies:
      "ansi-styles" "^2.2.1"
      "escape-string-regexp" "^1.0.2"
      "has-ansi" "^2.0.0"
      "strip-ansi" "^3.0.0"
      "supports-color" "^2.0.0"
  
  "chalk@^2.3.0":
    "integrity" "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ="
    "resolved" "https://registry.npm.taobao.org/chalk/download/chalk-2.4.2.tgz?cache=0&sync_timestamp=1573282918610&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchalk%2Fdownload%2Fchalk-2.4.2.tgz"
    "version" "2.4.2"
    dependencies:
      "ansi-styles" "^3.2.1"
      "escape-string-regexp" "^1.0.5"
      "supports-color" "^5.3.0"
  
  "chalk@2.3.2":
    "integrity" "sha512-ZM4j2/ld/YZDc3Ma8PgN7gyAk+kHMMMyzLNryCPGhWrsfAuDVeuid5bpRFTDgMH9JBK2lA4dyyAkkZYF/WcqDQ=="
    "resolved" "https://registry.npmmirror.com/chalk/-/chalk-2.3.2.tgz"
    "version" "2.3.2"
    dependencies:
      "ansi-styles" "^3.2.1"
      "escape-string-regexp" "^1.0.5"
      "supports-color" "^5.3.0"
  
  "chance@^1.0.16":
    "integrity" "sha1-2HQ7+OQLsF4CTDBcof9EEZXrI9s="
    "resolved" "https://registry.npm.taobao.org/chance/download/chance-1.1.4.tgz"
    "version" "1.1.4"
  
  "charenc@>= 0.0.1", "charenc@0.0.2":
    "integrity" "sha1-wKHS86cJLgN3S/qD8UwPxXkKhmc="
    "resolved" "https://registry.npm.taobao.org/charenc/download/charenc-0.0.2.tgz"
    "version" "0.0.2"
  
  "check-error@^1.0.2":
    "integrity" "sha1-V00xLt2Iu13YkS6Sht1sCu1KrII="
    "resolved" "https://registry.npm.taobao.org/check-error/download/check-error-1.0.2.tgz"
    "version" "1.0.2"
  
  "choices-separator@^2.0.0":
    "integrity" "sha1-kv0XYxgteQM/XFxR0Lo1LlVnxpY="
    "resolved" "https://registry.npm.taobao.org/choices-separator/download/choices-separator-2.0.0.tgz"
    "version" "2.0.0"
    dependencies:
      "ansi-dim" "^0.1.1"
      "debug" "^2.6.6"
      "strip-color" "^0.1.0"
  
  "class-utils@^0.3.5":
    "integrity" "sha1-+TNprouafOAv1B+q0MqDAzGQxGM="
    "resolved" "https://registry.npm.taobao.org/class-utils/download/class-utils-0.3.6.tgz"
    "version" "0.3.6"
    dependencies:
      "arr-union" "^3.1.0"
      "define-property" "^0.2.5"
      "isobject" "^3.0.0"
      "static-extend" "^0.1.1"
  
  "cli-table@^0.3.1":
    "integrity" "sha1-9TsFJmqLGguTSz0IIebi3FkUriM="
    "resolved" "https://registry.npm.taobao.org/cli-table/download/cli-table-0.3.1.tgz"
    "version" "0.3.1"
    dependencies:
      "colors" "1.0.3"
  
  "cliui@^5.0.0":
    "integrity" "sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U="
    "resolved" "https://registry.npm.taobao.org/cliui/download/cliui-5.0.0.tgz?cache=0&sync_timestamp=1573942320052&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcliui%2Fdownload%2Fcliui-5.0.0.tgz"
    "version" "5.0.0"
    dependencies:
      "string-width" "^3.1.0"
      "strip-ansi" "^5.2.0"
      "wrap-ansi" "^5.1.0"
  
  "clone-deep@^0.3.0":
    "integrity" "sha1-NIxhrpzb4O3+BT2R/0zFIdeQ7eg="
    "resolved" "https://registry.npm.taobao.org/clone-deep/download/clone-deep-0.3.0.tgz"
    "version" "0.3.0"
    dependencies:
      "for-own" "^1.0.0"
      "is-plain-object" "^2.0.1"
      "kind-of" "^3.2.2"
      "shallow-clone" "^0.1.2"
  
  "clone-deep@^1.0.0":
    "integrity" "sha1-svNUREtdSgzlj6yjN+802isUpsc="
    "resolved" "https://registry.npm.taobao.org/clone-deep/download/clone-deep-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "for-own" "^1.0.0"
      "is-plain-object" "^2.0.4"
      "kind-of" "^5.0.0"
      "shallow-clone" "^1.0.0"
  
  "clone-deep@^4.0.0":
    "integrity" "sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c="
    "resolved" "https://registry.npm.taobao.org/clone-deep/download/clone-deep-4.0.1.tgz"
    "version" "4.0.1"
    dependencies:
      "is-plain-object" "^2.0.4"
      "kind-of" "^6.0.2"
      "shallow-clone" "^3.0.0"
  
  "clone@^2.1.1":
    "integrity" "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18="
    "resolved" "https://registry.npm.taobao.org/clone/download/clone-2.1.2.tgz"
    "version" "2.1.2"
  
  "co-body@^6.0.0":
    "integrity" "sha1-lluTN9f1ZVSAeHRx9CN2ZIIIJ+M="
    "resolved" "https://registry.npm.taobao.org/co-body/download/co-body-6.0.0.tgz"
    "version" "6.0.0"
    dependencies:
      "inflation" "^2.0.0"
      "qs" "^6.5.2"
      "raw-body" "^2.3.3"
      "type-is" "^1.6.16"
  
  "co-compose@^4.0.0":
    "integrity" "sha1-5pmocgUvO85QkJNwoQMwtVIxtAs="
    "resolved" "https://registry.npm.taobao.org/co-compose/download/co-compose-4.0.0.tgz"
    "version" "4.0.0"
    dependencies:
      "once" "^1.4.0"
  
  "co-defer@^1.0.0":
    "integrity" "sha1-Pkp4eo7tawoh7ih8CU9+jeDTyBg="
    "resolved" "https://registry.npm.taobao.org/co-defer/download/co-defer-1.0.0.tgz"
    "version" "1.0.0"
  
  "co-wechat-api@^3.9.1":
    "integrity" "sha1-jMNobzDPb4W6g/zMv1Wtp40kPj8="
    "resolved" "https://registry.npm.taobao.org/co-wechat-api/download/co-wechat-api-3.10.0.tgz"
    "version" "3.10.0"
    dependencies:
      "formstream" ">=0.0.8"
      "httpx" "^2.1.1"
      "json-bigint" "^0.3.0"
  
  "co-wechat-oauth@^2.0.1":
    "integrity" "sha1-n/pS3fuTWdAwAYiBw/jWIQSKKVQ="
    "resolved" "https://registry.npm.taobao.org/co-wechat-oauth/download/co-wechat-oauth-2.0.1.tgz"
    "version" "2.0.1"
    dependencies:
      "httpx" "^2.1.1"
  
  "co@^4.6.0", "co@4":
    "integrity" "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ="
    "resolved" "https://registry.npm.taobao.org/co/download/co-4.6.0.tgz"
    "version" "4.6.0"
  
  "codepage@~1.14.0":
    "integrity" "sha1-jL4lSBMjVZ19MHVxsP/5HnodL5k="
    "resolved" "https://registry.npm.taobao.org/codepage/download/codepage-1.14.0.tgz"
    "version" "1.14.0"
    dependencies:
      "commander" "~2.14.1"
      "exit-on-epipe" "~1.0.1"
  
  "codepage@~1.15.0":
    "version" "1.15.0"
  
  "collection-visit@^1.0.0":
    "integrity" "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA="
    "resolved" "https://registry.npm.taobao.org/collection-visit/download/collection-visit-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "map-visit" "^1.0.0"
      "object-visit" "^1.0.0"
  
  "color-convert@^1.9.0", "color-convert@^1.9.1":
    "integrity" "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg="
    "resolved" "https://registry.npm.taobao.org/color-convert/download/color-convert-1.9.3.tgz"
    "version" "1.9.3"
    dependencies:
      "color-name" "1.1.3"
  
  "color-name@^1.0.0":
    "integrity" "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI="
    "resolved" "https://registry.npm.taobao.org/color-name/download/color-name-1.1.4.tgz"
    "version" "1.1.4"
  
  "color-name@1.1.3":
    "integrity" "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="
    "resolved" "https://registry.npm.taobao.org/color-name/download/color-name-1.1.3.tgz"
    "version" "1.1.3"
  
  "color-string@^1.5.2":
    "integrity" "sha1-ybvF8BtYtUkvPWhXRZy2WQziBMw="
    "resolved" "https://registry.npm.taobao.org/color-string/download/color-string-1.5.3.tgz"
    "version" "1.5.3"
    dependencies:
      "color-name" "^1.0.0"
      "simple-swizzle" "^0.2.2"
  
  "color@3.0.x":
    "integrity" "sha1-2SC0Mo1TSjrIKV1o971LpsQnvpo="
    "resolved" "https://registry.npm.taobao.org/color/download/color-3.0.0.tgz"
    "version" "3.0.0"
    dependencies:
      "color-convert" "^1.9.1"
      "color-string" "^1.5.2"
  
  "colornames@^1.1.1":
    "integrity" "sha1-+IiQMGhcfE/54qVZ9Qd+t2qBb5Y="
    "resolved" "https://registry.npm.taobao.org/colornames/download/colornames-1.1.1.tgz"
    "version" "1.1.1"
  
  "colors@^1.2.1":
    "integrity" "sha1-xQSRR51MG9rtLJztMs98fcI2D3g="
    "resolved" "https://registry.npm.taobao.org/colors/download/colors-1.4.0.tgz"
    "version" "1.4.0"
  
  "colors@1.0.3":
    "integrity" "sha1-BDP0TYCWgP3rYO0mDxsMJi6CpAs="
    "resolved" "https://registry.npm.taobao.org/colors/download/colors-1.0.3.tgz"
    "version" "1.0.3"
  
  "colorspace@1.1.x":
    "integrity" "sha1-4BKJUNCCuGohaFgHlqCqXWxo2MU="
    "resolved" "https://registry.npm.taobao.org/colorspace/download/colorspace-1.1.2.tgz"
    "version" "1.1.2"
    dependencies:
      "color" "3.0.x"
      "text-hex" "1.0.x"
  
  "combined-stream@^1.0.6", "combined-stream@^1.0.8", "combined-stream@~1.0.6":
    "integrity" "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8="
    "resolved" "https://registry.npm.taobao.org/combined-stream/download/combined-stream-1.0.8.tgz"
    "version" "1.0.8"
    dependencies:
      "delayed-stream" "~1.0.0"
  
  "commander@^2.16.0", "commander@^2.18.0":
    "integrity" "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM="
    "resolved" "https://registry.npm.taobao.org/commander/download/commander-2.20.3.tgz"
    "version" "2.20.3"
  
  "commander@~2.14.1":
    "integrity" "sha1-IjUSPjevjKPGXfRbAm29NXsBuao="
    "resolved" "https://registry.npm.taobao.org/commander/download/commander-2.14.1.tgz"
    "version" "2.14.1"
  
  "commander@~2.17.1":
    "integrity" "sha1-vXerfebelCBc6sxy8XFtKfIKd78="
    "resolved" "https://registry.npm.taobao.org/commander/download/commander-2.17.1.tgz"
    "version" "2.17.1"
  
  "component-emitter@^1.2.0", "component-emitter@^1.2.1", "component-emitter@^1.3.0":
    "integrity" "sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A="
    "resolved" "https://registry.npm.taobao.org/component-emitter/download/component-emitter-1.3.0.tgz"
    "version" "1.3.0"
  
  "compress-commons@^4.1.0":
    "integrity" "sha1-3yoJp+0XRHZCutEKhcyaGeXEKn0="
    "resolved" "https://registry.nlark.com/compress-commons/download/compress-commons-4.1.1.tgz?cache=0&sync_timestamp=1622399262423&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcompress-commons%2Fdownload%2Fcompress-commons-4.1.1.tgz"
    "version" "4.1.1"
    dependencies:
      "buffer-crc32" "^0.2.13"
      "crc32-stream" "^4.0.2"
      "normalize-path" "^3.0.0"
      "readable-stream" "^3.6.0"
  
  "concat-map@0.0.1":
    "integrity" "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="
    "resolved" "https://registry.npm.taobao.org/concat-map/download/concat-map-0.0.1.tgz"
    "version" "0.0.1"
  
  "content-disposition@^0.5.2":
    "integrity" "sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70="
    "resolved" "https://registry.npm.taobao.org/content-disposition/download/content-disposition-0.5.3.tgz"
    "version" "0.5.3"
    dependencies:
      "safe-buffer" "5.1.2"
  
  "content-type@^1.0.2":
    "integrity" "sha1-4TjMdeBAxyexlm/l5fjJruJW/js="
    "resolved" "https://registry.npm.taobao.org/content-type/download/content-type-1.0.4.tgz"
    "version" "1.0.4"
  
  "cookie-signature@^1.1.0":
    "integrity" "sha1-zJSXT5H7mpwbtIXpX8K39LEgr/I="
    "resolved" "https://registry.npm.taobao.org/cookie-signature/download/cookie-signature-1.1.0.tgz"
    "version" "1.1.0"
  
  "cookie@^0.3.1":
    "integrity" "sha1-5+Ch+e9DtMi6klxcWpboBtFoc7s="
    "resolved" "https://registry.npm.taobao.org/cookie/download/cookie-0.3.1.tgz"
    "version" "0.3.1"
  
  "cookiejar@^2.1.2":
    "integrity" "sha1-3YojVTB1L5iPmghE8/xYnjERElw="
    "resolved" "https://registry.npm.taobao.org/cookiejar/download/cookiejar-2.1.2.tgz"
    "version" "2.1.2"
  
  "copy-descriptor@^0.1.0":
    "integrity" "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40="
    "resolved" "https://registry.npm.taobao.org/copy-descriptor/download/copy-descriptor-0.1.1.tgz"
    "version" "0.1.1"
  
  "copy-to@^2.0.1":
    "integrity" "sha1-JoD7uAaKSNCGVrYJgJK9r8kG9KU="
    "resolved" "https://registry.npm.taobao.org/copy-to/download/copy-to-2.0.1.tgz"
    "version" "2.0.1"
  
  "core-js@^2.4.0":
    "integrity" "sha1-OIMUafmSK97Y7iHJ3EaYXgOZMIw="
    "resolved" "https://registry.npm.taobao.org/core-js/download/core-js-2.6.11.tgz"
    "version" "2.6.11"
  
  "core-util-is@^1.0.2", "core-util-is@~1.0.0", "core-util-is@1.0.2":
    "integrity" "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="
    "resolved" "https://registry.npm.taobao.org/core-util-is/download/core-util-is-1.0.2.tgz"
    "version" "1.0.2"
  
  "crc-32@^1.2.0", "crc-32@~1.2.0", "crc-32@~1.2.1":
    "version" "1.2.2"
  
  "crc32-stream@^4.0.2":
    "integrity" "sha1-ySKtIrODlavp04cPAvqBNO1wkAc="
    "resolved" "https://registry.npm.taobao.org/crc32-stream/download/crc32-stream-4.0.2.tgz"
    "version" "4.0.2"
    dependencies:
      "crc-32" "^1.2.0"
      "readable-stream" "^3.4.0"
  
  "cron-parser@^2.7.3":
    "integrity" "sha1-b5MLtvKTF5DSqe7IOz7CduJ6ZyU="
    "resolved" "https://registry.npm.taobao.org/cron-parser/download/cron-parser-2.13.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcron-parser%2Fdownload%2Fcron-parser-2.13.0.tgz"
    "version" "2.13.0"
    dependencies:
      "is-nan" "^1.2.1"
      "moment-timezone" "^0.5.25"
  
  "cross-env@^5.1.0":
    "integrity" "sha1-ssdsHKet1m3IdNEXmEZglPVRs00="
    "resolved" "https://registry.npm.taobao.org/cross-env/download/cross-env-5.2.1.tgz?cache=0&sync_timestamp=1579979058174&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcross-env%2Fdownload%2Fcross-env-5.2.1.tgz"
    "version" "5.2.1"
    dependencies:
      "cross-spawn" "^6.0.5"
  
  "cross-spawn@^6.0.5":
    "integrity" "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q="
    "resolved" "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-6.0.5.tgz"
    "version" "6.0.5"
    dependencies:
      "nice-try" "^1.0.4"
      "path-key" "^2.0.1"
      "semver" "^5.5.0"
      "shebang-command" "^1.2.0"
      "which" "^1.2.9"
  
  "crypt@>= 0.0.1", "crypt@0.0.2":
    "integrity" "sha1-iNf/fsDfuG9xPch7u0LQRNPmxBs="
    "resolved" "https://registry.npm.taobao.org/crypt/download/crypt-0.0.2.tgz"
    "version" "0.0.2"
  
  "css-in-js-utils@^2.0.0":
    "integrity" "sha1-O0crOYeHKRtHz+PkT+z92ekUupk="
    "resolved" "https://registry.npm.taobao.org/css-in-js-utils/download/css-in-js-utils-2.0.1.tgz"
    "version" "2.0.1"
    dependencies:
      "hyphenate-style-name" "^1.0.2"
      "isobject" "^3.0.1"
  
  "dashdash@^1.12.0":
    "integrity" "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA="
    "resolved" "https://registry.npm.taobao.org/dashdash/download/dashdash-1.14.1.tgz"
    "version" "1.14.1"
    dependencies:
      "assert-plus" "^1.0.0"
  
  "data-uri-to-buffer@1":
    "integrity" "sha1-dxY+qcINhkG0cH6PGKvfmnjzSDU="
    "resolved" "https://registry.npm.taobao.org/data-uri-to-buffer/download/data-uri-to-buffer-1.2.0.tgz"
    "version" "1.2.0"
  
  "data-uri-to-buffer@3":
    "integrity" "sha1-WUuJc5OMW8LDMEZTV4U0GrxPNjY="
    "resolved" "https://registry.npm.taobao.org/data-uri-to-buffer/download/data-uri-to-buffer-3.0.1.tgz"
    "version" "3.0.1"
  
  "dateformat@^2.0.0":
    "integrity" "sha1-QGXiATz5+5Ft39gu+1Bq1MZ2kGI="
    "resolved" "https://registry.npm.taobao.org/dateformat/download/dateformat-2.2.0.tgz"
    "version" "2.2.0"
  
  "dayjs@^1.8.34":
    "integrity" "sha1-VgDfRUj8JFOz8WPrsqu+llzPuYY="
    "resolved" "https://registry.nlark.com/dayjs/download/dayjs-1.10.5.tgz?cache=0&sync_timestamp=1622012243793&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdayjs%2Fdownload%2Fdayjs-1.10.5.tgz"
    "version" "1.10.5"
  
  "debug@^2.2.0", "debug@^2.3.3", "debug@^2.6.3", "debug@^2.6.6", "debug@^2.6.8", "debug@^2.6.9", "debug@2.6.9":
    "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
    "resolved" "https://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz"
    "version" "2.6.9"
    dependencies:
      "ms" "2.0.0"
  
  "debug@^3.0.1":
    "integrity" "sha1-6D0X3hbYp++3cX7b5fsQE17uYps="
    "resolved" "https://registry.npm.taobao.org/debug/download/debug-3.2.6.tgz"
    "version" "3.2.6"
    dependencies:
      "ms" "^2.1.1"
  
  "debug@^3.1.0":
    "integrity" "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ=="
    "resolved" "https://registry.npmmirror.com/debug/-/debug-3.2.7.tgz"
    "version" "3.2.7"
    dependencies:
      "ms" "^2.1.1"
  
  "debug@^4.0.1":
    "integrity" "sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E="
    "resolved" "https://registry.npm.taobao.org/debug/download/debug-4.1.1.tgz"
    "version" "4.1.1"
    dependencies:
      "ms" "^2.1.1"
  
  "debug@^4.1.0":
    "integrity" "sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E="
    "resolved" "https://registry.npm.taobao.org/debug/download/debug-4.1.1.tgz"
    "version" "4.1.1"
    dependencies:
      "ms" "^2.1.1"
  
  "debug@^4.1.1":
    "integrity" "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ=="
    "resolved" "https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz"
    "version" "4.4.1"
    dependencies:
      "ms" "^2.1.3"
  
  "debug@=3.1.0":
    "integrity" "sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE="
    "resolved" "https://registry.npm.taobao.org/debug/download/debug-3.1.0.tgz"
    "version" "3.1.0"
    dependencies:
      "ms" "2.0.0"
  
  "debug@2":
    "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
    "resolved" "https://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz"
    "version" "2.6.9"
    dependencies:
      "ms" "2.0.0"
  
  "debug@3.1.0":
    "integrity" "sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE="
    "resolved" "https://registry.npm.taobao.org/debug/download/debug-3.1.0.tgz"
    "version" "3.1.0"
    dependencies:
      "ms" "2.0.0"
  
  "debug@4":
    "integrity" "sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E="
    "resolved" "https://registry.npm.taobao.org/debug/download/debug-4.1.1.tgz"
    "version" "4.1.1"
    dependencies:
      "ms" "^2.1.1"
  
  "decamelize@^1.2.0":
    "integrity" "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA="
    "resolved" "https://registry.npm.taobao.org/decamelize/download/decamelize-1.2.0.tgz?cache=0&sync_timestamp=1580010393599&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdecamelize%2Fdownload%2Fdecamelize-1.2.0.tgz"
    "version" "1.2.0"
  
  "decode-uri-component@^0.2.0":
    "integrity" "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU="
    "resolved" "https://registry.npm.taobao.org/decode-uri-component/download/decode-uri-component-0.2.0.tgz"
    "version" "0.2.0"
  
  "deep-eql@^3.0.1":
    "integrity" "sha1-38lARACtHI/gI+faHfHBR8S0RN8="
    "resolved" "https://registry.npm.taobao.org/deep-eql/download/deep-eql-3.0.1.tgz"
    "version" "3.0.1"
    dependencies:
      "type-detect" "^4.0.0"
  
  "deep-is@~0.1.3":
    "integrity" "sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ="
    "resolved" "https://registry.npm.taobao.org/deep-is/download/deep-is-0.1.3.tgz"
    "version" "0.1.3"
  
  "default-user-agent@^1.0.0":
    "integrity" "sha1-FsRu/cq6PtxF8k8r1IaLAbfCrcY="
    "resolved" "https://registry.npm.taobao.org/default-user-agent/download/default-user-agent-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "os-name" "~1.0.3"
  
  "define-properties@^1.1.3":
    "integrity" "sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE="
    "resolved" "https://registry.npm.taobao.org/define-properties/download/define-properties-1.1.3.tgz"
    "version" "1.1.3"
    dependencies:
      "object-keys" "^1.0.12"
  
  "define-property@^0.2.5":
    "integrity" "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY="
    "resolved" "https://registry.npm.taobao.org/define-property/download/define-property-0.2.5.tgz"
    "version" "0.2.5"
    dependencies:
      "is-descriptor" "^0.1.0"
  
  "define-property@^1.0.0":
    "integrity" "sha1-dp66rz9KY6rTr56NMEybvnm/sOY="
    "resolved" "https://registry.npm.taobao.org/define-property/download/define-property-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "is-descriptor" "^1.0.0"
  
  "define-property@^2.0.2":
    "integrity" "sha1-1Flono1lS6d+AqgX+HENcCyxbp0="
    "resolved" "https://registry.npm.taobao.org/define-property/download/define-property-2.0.2.tgz"
    "version" "2.0.2"
    dependencies:
      "is-descriptor" "^1.0.2"
      "isobject" "^3.0.1"
  
  "degenerator@^1.0.4":
    "integrity" "sha1-/PSQo37OJmRk2cxDGrmMWBnO0JU="
    "resolved" "https://registry.npm.taobao.org/degenerator/download/degenerator-1.0.4.tgz"
    "version" "1.0.4"
    dependencies:
      "ast-types" "0.x.x"
      "escodegen" "1.x.x"
      "esprima" "3.x.x"
  
  "degenerator@^2.2.0":
    "integrity" "sha1-SemMEfoCk8Wybt+7UvFXKa/NslQ="
    "resolved" "https://registry.npm.taobao.org/degenerator/download/degenerator-2.2.0.tgz"
    "version" "2.2.0"
    dependencies:
      "ast-types" "^0.13.2"
      "escodegen" "^1.8.1"
      "esprima" "^4.0.0"
  
  "delayed-stream@~1.0.0":
    "integrity" "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="
    "resolved" "https://registry.npm.taobao.org/delayed-stream/download/delayed-stream-1.0.0.tgz"
    "version" "1.0.0"
  
  "depd@~1.1.2":
    "integrity" "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="
    "resolved" "https://registry.npm.taobao.org/depd/download/depd-1.1.2.tgz"
    "version" "1.1.2"
  
  "destroy@^1.0.4", "destroy@~1.0.4":
    "integrity" "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="
    "resolved" "https://registry.npm.taobao.org/destroy/download/destroy-1.0.4.tgz"
    "version" "1.0.4"
  
  "detect-file@^1.0.0":
    "integrity" "sha1-8NZtA2cqglyxtzvbP+YjEMjlUrc="
    "resolved" "https://registry.npm.taobao.org/detect-file/download/detect-file-1.0.0.tgz"
    "version" "1.0.0"
  
  "diagnostics@^1.1.1":
    "integrity" "sha1-yrasM99wydmnJ0kK5DrJladpsio="
    "resolved" "https://registry.npm.taobao.org/diagnostics/download/diagnostics-1.1.1.tgz"
    "version" "1.1.1"
    dependencies:
      "colorspace" "1.1.x"
      "enabled" "1.0.x"
      "kuler" "1.0.x"
  
  "digest-header@^0.0.1":
    "integrity" "sha1-Ecz23uxXZqw3l0TZAcEsuklRS+Y="
    "resolved" "https://registry.npm.taobao.org/digest-header/download/digest-header-0.0.1.tgz"
    "version" "0.0.1"
    dependencies:
      "utility" "0.1.11"
  
  "dijkstrajs@^1.0.1":
    "integrity" "sha1-082BIh4+pAdCz83lVtTpnpjdxxs="
    "resolved" "https://registry.npm.taobao.org/dijkstrajs/download/dijkstrajs-1.0.1.tgz"
    "version" "1.0.1"
  
  "dir-glob@2.0.0":
    "integrity" "sha1-CyBdK2rvmCOMooZZioIE0p0KADQ="
    "resolved" "https://registry.npm.taobao.org/dir-glob/download/dir-glob-2.0.0.tgz"
    "version" "2.0.0"
    dependencies:
      "arrify" "^1.0.1"
      "path-type" "^3.0.0"
  
  "dotenv@^6.2.0":
    "integrity" "sha1-lBwEEFNdlCyL7PKNPzV9vZ1HYGQ="
    "resolved" "https://registry.npm.taobao.org/dotenv/download/dotenv-6.2.0.tgz?cache=0&sync_timestamp=1571190782798&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdotenv%2Fdownload%2Fdotenv-6.2.0.tgz"
    "version" "6.2.0"
  
  "duplexer2@~0.1.4":
    "integrity" "sha1-ixLauHjA1p4+eJEFFmKjL8a93ME="
    "resolved" "https://registry.npm.taobao.org/duplexer2/download/duplexer2-0.1.4.tgz"
    "version" "0.1.4"
    dependencies:
      "readable-stream" "^2.0.2"
  
  "ecc-jsbn@~0.1.1":
    "integrity" "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk="
    "resolved" "https://registry.npm.taobao.org/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz"
    "version" "0.1.2"
    dependencies:
      "jsbn" "~0.1.0"
      "safer-buffer" "^2.1.0"
  
  "ecdsa-sig-formatter@1.0.11":
    "integrity" "sha1-rg8PothQRe8UqBfao86azQSJ5b8="
    "resolved" "https://registry.npm.taobao.org/ecdsa-sig-formatter/download/ecdsa-sig-formatter-1.0.11.tgz"
    "version" "1.0.11"
    dependencies:
      "safe-buffer" "^5.0.1"
  
  "edge.js@^1.1.4":
    "integrity" "sha1-b6wCuHp0dgAdpubxTR3x2vSHzg0="
    "resolved" "https://registry.npm.taobao.org/edge.js/download/edge.js-1.1.4.tgz"
    "version" "1.1.4"
    dependencies:
      "debug" "^3.1.0"
      "encodeurl" "^1.0.2"
      "escape-html" "^1.0.3"
      "esprima" "^4.0.0"
      "indent-string" "^3.2.0"
      "lodash" "^4.17.4"
      "node-exceptions" "^3.0.0"
      "require-uncached" "^1.0.3"
      "upcast" "^2.1.1"
  
  "ee-first@~1.1.1", "ee-first@1.1.1":
    "integrity" "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="
    "resolved" "https://registry.npm.taobao.org/ee-first/download/ee-first-1.1.1.tgz"
    "version" "1.1.1"
  
  "emoji-regex@^7.0.1":
    "integrity" "sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY="
    "resolved" "https://registry.npm.taobao.org/emoji-regex/download/emoji-regex-7.0.3.tgz"
    "version" "7.0.3"
  
  "enabled@1.0.x":
    "integrity" "sha1-ll9lE9LC0cX0ZStkouM5ZGf8L5M="
    "resolved" "https://registry.npm.taobao.org/enabled/download/enabled-1.0.2.tgz"
    "version" "1.0.2"
    dependencies:
      "env-variable" "0.0.x"
  
  "encodeurl@^1.0.2", "encodeurl@~1.0.2":
    "integrity" "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="
    "resolved" "https://registry.npm.taobao.org/encodeurl/download/encodeurl-1.0.2.tgz"
    "version" "1.0.2"
  
  "end-of-stream@^1.1.0", "end-of-stream@^1.4.1":
    "integrity" "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA="
    "resolved" "https://registry.npm.taobao.org/end-of-stream/download/end-of-stream-1.4.4.tgz"
    "version" "1.4.4"
    dependencies:
      "once" "^1.4.0"
  
  "end-or-error@^1.0.1":
    "integrity" "sha1-3HpiEP5403L+4kqLSJnb0VVBTcs="
    "resolved" "https://registry.npm.taobao.org/end-or-error/download/end-or-error-1.0.1.tgz"
    "version" "1.0.1"
  
  "enquirer@^1.0.3":
    "integrity" "sha1-A+pmeq8kGyHksduTjfo+Cxi9iSQ="
    "resolved" "https://registry.npm.taobao.org/enquirer/download/enquirer-1.0.3.tgz"
    "version" "1.0.3"
    dependencies:
      "choices-separator" "^2.0.0"
      "collection-visit" "^1.0.0"
      "component-emitter" "^1.2.1"
      "debug" "^2.6.8"
      "extend-shallow" "^2.0.1"
      "get-value" "^2.0.6"
      "isobject" "^3.0.0"
      "promise-reduce" "^2.1.0"
      "prompt-input" "^3.0.0"
      "prompt-question" "^3.0.3"
      "readline-ui" "^2.2.2"
      "set-value" "^1.0.0"
  
  "env-variable@0.0.x":
    "integrity" "sha1-dKsgs3hsVFtitKSBOrjPInJsmAg="
    "resolved" "https://registry.npm.taobao.org/env-variable/download/env-variable-0.0.6.tgz"
    "version" "0.0.6"
  
  "error-symbol@^0.1.0":
    "integrity" "sha1-Ck2uN9YA0VopukU9jvkg8YRDM/Y="
    "resolved" "https://registry.npm.taobao.org/error-symbol/download/error-symbol-0.1.0.tgz"
    "version" "0.1.0"
  
  "es6-promise@^4.0.3":
    "integrity" "sha1-TrIVlMlyvEBVPSduUQU5FD21Pgo="
    "resolved" "https://registry.npm.taobao.org/es6-promise/download/es6-promise-4.2.8.tgz"
    "version" "4.2.8"
  
  "es6-promisify@^5.0.0":
    "integrity" "sha1-UQnWLz5W6pZ8S2NQWu8IKRyKUgM="
    "resolved" "https://registry.npm.taobao.org/es6-promisify/download/es6-promisify-5.0.0.tgz"
    "version" "5.0.0"
    dependencies:
      "es6-promise" "^4.0.3"
  
  "escape-html@^1.0.3", "escape-html@~1.0.3":
    "integrity" "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="
    "resolved" "https://registry.npm.taobao.org/escape-html/download/escape-html-1.0.3.tgz"
    "version" "1.0.3"
  
  "escape-string-regexp@^1.0.2", "escape-string-regexp@^1.0.5":
    "integrity" "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="
    "resolved" "https://registry.npm.taobao.org/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz"
    "version" "1.0.5"
  
  "escodegen@^1.8.1":
    "integrity" "sha512-qFcX0XJkdg+PB3xjZZG/wKSuT1PnQWx57+TVSjIMmILd2yC/6ByYElPwJnslDsuWuSAp4AwJGumarAAmJch5Kw=="
    "resolved" "https://registry.npmmirror.com/escodegen/-/escodegen-1.14.3.tgz"
    "version" "1.14.3"
    dependencies:
      "esprima" "^4.0.1"
      "estraverse" "^4.2.0"
      "esutils" "^2.0.2"
      "optionator" "^0.8.1"
    optionalDependencies:
      "source-map" "~0.6.1"
  
  "escodegen@1.x.x":
    "integrity" "sha1-x635vT88xnW7dS8gL3mnIBicqyk="
    "resolved" "https://registry.npm.taobao.org/escodegen/download/escodegen-1.13.0.tgz?cache=0&sync_timestamp=1579321594611&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fescodegen%2Fdownload%2Fescodegen-1.13.0.tgz"
    "version" "1.13.0"
    dependencies:
      "esprima" "^4.0.1"
      "estraverse" "^4.2.0"
      "esutils" "^2.0.2"
      "optionator" "^0.8.1"
    optionalDependencies:
      "source-map" "~0.6.1"
  
  "esprima@^4.0.0", "esprima@^4.0.1":
    "integrity" "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE="
    "resolved" "https://registry.npm.taobao.org/esprima/download/esprima-4.0.1.tgz"
    "version" "4.0.1"
  
  "esprima@3.x.x":
    "integrity" "sha1-/cpRzuYTOJXjyI1TXOSdv/YqRjM="
    "resolved" "https://registry.npm.taobao.org/esprima/download/esprima-3.1.3.tgz"
    "version" "3.1.3"
  
  "estraverse@^4.2.0":
    "integrity" "sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0="
    "resolved" "https://registry.npm.taobao.org/estraverse/download/estraverse-4.3.0.tgz"
    "version" "4.3.0"
  
  "esutils@^2.0.2":
    "integrity" "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q="
    "resolved" "https://registry.npm.taobao.org/esutils/download/esutils-2.0.3.tgz"
    "version" "2.0.3"
  
  "etag@^1.8.1", "etag@~1.8.1":
    "integrity" "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="
    "resolved" "https://registry.npm.taobao.org/etag/download/etag-1.8.1.tgz"
    "version" "1.8.1"
  
  "eventemitter2@^5.0.1":
    "integrity" "sha1-YZegldX7a1folC9v1+qtY6CclFI="
    "resolved" "https://registry.npm.taobao.org/eventemitter2/download/eventemitter2-5.0.1.tgz"
    "version" "5.0.1"
  
  "exceljs@^4.1.1":
    "integrity" "sha1-SddLq/yudPYbz8jplk8P7KCE2Rs="
    "resolved" "https://registry.npm.taobao.org/exceljs/download/exceljs-4.2.1.tgz"
    "version" "4.2.1"
    dependencies:
      "archiver" "^5.0.0"
      "dayjs" "^1.8.34"
      "fast-csv" "^4.3.1"
      "jszip" "^3.5.0"
      "readable-stream" "^3.6.0"
      "saxes" "^5.0.1"
      "tmp" "^0.2.0"
      "unzipper" "^0.10.11"
      "uuid" "^8.3.0"
  
  "exit-on-epipe@~1.0.1":
    "integrity" "sha1-C92S6H1ShdJn2qgXHQ6wYVlolpI="
    "resolved" "https://registry.npm.taobao.org/exit-on-epipe/download/exit-on-epipe-1.0.1.tgz"
    "version" "1.0.1"
  
  "expand-brackets@^2.1.4":
    "integrity" "sha1-t3c14xXOMPa27/D4OwQVGiJEliI="
    "resolved" "https://registry.npm.taobao.org/expand-brackets/download/expand-brackets-2.1.4.tgz"
    "version" "2.1.4"
    dependencies:
      "debug" "^2.3.3"
      "define-property" "^0.2.5"
      "extend-shallow" "^2.0.1"
      "posix-character-classes" "^0.1.0"
      "regex-not" "^1.0.0"
      "snapdragon" "^0.8.1"
      "to-regex" "^3.0.1"
  
  "expand-tilde@^2.0.0", "expand-tilde@^2.0.2":
    "integrity" "sha1-l+gBqgUt8CRU3kawK/YhZCzchQI="
    "resolved" "https://registry.npm.taobao.org/expand-tilde/download/expand-tilde-2.0.2.tgz"
    "version" "2.0.2"
    dependencies:
      "homedir-polyfill" "^1.0.1"
  
  "extend-shallow@^2.0.1":
    "integrity" "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8="
    "resolved" "https://registry.npm.taobao.org/extend-shallow/download/extend-shallow-2.0.1.tgz"
    "version" "2.0.1"
    dependencies:
      "is-extendable" "^0.1.0"
  
  "extend-shallow@^3.0.0":
    "integrity" "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg="
    "resolved" "https://registry.npm.taobao.org/extend-shallow/download/extend-shallow-3.0.2.tgz"
    "version" "3.0.2"
    dependencies:
      "assign-symbols" "^1.0.0"
      "is-extendable" "^1.0.1"
  
  "extend-shallow@^3.0.2":
    "integrity" "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg="
    "resolved" "https://registry.npm.taobao.org/extend-shallow/download/extend-shallow-3.0.2.tgz"
    "version" "3.0.2"
    dependencies:
      "assign-symbols" "^1.0.0"
      "is-extendable" "^1.0.1"
  
  "extend@^3.0.0", "extend@~3.0.2":
    "integrity" "sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo="
    "resolved" "https://registry.npm.taobao.org/extend/download/extend-3.0.2.tgz"
    "version" "3.0.2"
  
  "extglob@^2.0.4":
    "integrity" "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM="
    "resolved" "https://registry.npm.taobao.org/extglob/download/extglob-2.0.4.tgz"
    "version" "2.0.4"
    dependencies:
      "array-unique" "^0.3.2"
      "define-property" "^1.0.0"
      "expand-brackets" "^2.1.4"
      "extend-shallow" "^2.0.1"
      "fragment-cache" "^0.2.1"
      "regex-not" "^1.0.0"
      "snapdragon" "^0.8.1"
      "to-regex" "^3.0.1"
  
  "extsprintf@^1.2.0":
    "integrity" "sha1-4mifjzVvrWLMplo6kcXfX5VRaS8="
    "resolved" "https://registry.npm.taobao.org/extsprintf/download/extsprintf-1.4.0.tgz"
    "version" "1.4.0"
  
  "extsprintf@1.3.0":
    "integrity" "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU="
    "resolved" "https://registry.npm.taobao.org/extsprintf/download/extsprintf-1.3.0.tgz"
    "version" "1.3.0"
  
  "fast-csv@^4.3.1":
    "integrity" "sha1-cDSb3Y/k1msRMNjJGCC2SiG8SmM="
    "resolved" "https://registry.npm.taobao.org/fast-csv/download/fast-csv-4.3.6.tgz"
    "version" "4.3.6"
    dependencies:
      "@fast-csv/format" "4.3.5"
      "@fast-csv/parse" "4.3.6"
  
  "fast-deep-equal@^3.1.1":
    "integrity" "sha1-VFFFB3xQFJHjOxXsQIwpQ3bpSuQ="
    "resolved" "https://registry.npm.taobao.org/fast-deep-equal/download/fast-deep-equal-3.1.1.tgz?cache=0&sync_timestamp=1575383522243&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffast-deep-equal%2Fdownload%2Ffast-deep-equal-3.1.1.tgz"
    "version" "3.1.1"
  
  "fast-glob@^2.0.2":
    "integrity" "sha1-aVOFfDr6R1//ku5gFdUtpwpM050="
    "resolved" "https://registry.npm.taobao.org/fast-glob/download/fast-glob-2.2.7.tgz?cache=0&sync_timestamp=1575197566634&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffast-glob%2Fdownload%2Ffast-glob-2.2.7.tgz"
    "version" "2.2.7"
    dependencies:
      "@mrmlnc/readdir-enhanced" "^2.2.1"
      "@nodelib/fs.stat" "^1.1.2"
      "glob-parent" "^3.1.0"
      "is-glob" "^4.0.0"
      "merge2" "^1.2.3"
      "micromatch" "^3.1.10"
  
  "fast-json-stable-stringify@^2.0.0":
    "integrity" "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM="
    "resolved" "https://registry.npm.taobao.org/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz?cache=0&sync_timestamp=1576340291001&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffast-json-stable-stringify%2Fdownload%2Ffast-json-stable-stringify-2.1.0.tgz"
    "version" "2.1.0"
  
  "fast-levenshtein@^2.0.6", "fast-levenshtein@~2.0.6":
    "integrity" "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="
    "resolved" "https://registry.npm.taobao.org/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz"
    "version" "2.0.6"
  
  "fast-safe-stringify@^2.0.4":
    "integrity" "sha1-EkqohYmSYfaK7bQqfAgN6dpgh0M="
    "resolved" "https://registry.npm.taobao.org/fast-safe-stringify/download/fast-safe-stringify-2.0.7.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffast-safe-stringify%2Fdownload%2Ffast-safe-stringify-2.0.7.tgz"
    "version" "2.0.7"
  
  "fast-safe-stringify@^2.0.7":
    "integrity" "sha512-W+KJc2dmILlPplD/H4K9l9LcAHAfPtP6BY84uVLXQ6Evcz9Lcg33Y2z1IVblT6xdY54PXYVHEv+0Wpq8Io6zkA=="
    "resolved" "https://registry.npmmirror.com/fast-safe-stringify/-/fast-safe-stringify-2.1.1.tgz"
    "version" "2.1.1"
  
  "fd-slicer@1.1.0":
    "integrity" "sha1-JcfInLH5B3+IkbvmHY85Dq4lbx4="
    "resolved" "https://registry.npm.taobao.org/fd-slicer/download/fd-slicer-1.1.0.tgz"
    "version" "1.1.0"
    dependencies:
      "pend" "~1.2.0"
  
  "fecha@^2.3.3":
    "integrity" "sha1-lI50FX3xoy/RsSw6PDzctuydls0="
    "resolved" "https://registry.npm.taobao.org/fecha/download/fecha-2.3.3.tgz"
    "version" "2.3.3"
  
  "file-uri-to-path@1":
    "integrity" "sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90="
    "resolved" "https://registry.npm.taobao.org/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz"
    "version" "1.0.0"
  
  "file-uri-to-path@2":
    "integrity" "sha1-e0Fa66In1XWFHgpbDGQNdlZAP7o="
    "resolved" "https://registry.npm.taobao.org/file-uri-to-path/download/file-uri-to-path-2.0.0.tgz"
    "version" "2.0.0"
  
  "fill-range@^4.0.0":
    "integrity" "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc="
    "resolved" "https://registry.npm.taobao.org/fill-range/download/fill-range-4.0.0.tgz"
    "version" "4.0.0"
    dependencies:
      "extend-shallow" "^2.0.1"
      "is-number" "^3.0.0"
      "repeat-string" "^1.6.1"
      "to-regex-range" "^2.1.0"
  
  "find-up@^3.0.0":
    "integrity" "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M="
    "resolved" "https://registry.npm.taobao.org/find-up/download/find-up-3.0.0.tgz"
    "version" "3.0.0"
    dependencies:
      "locate-path" "^3.0.0"
  
  "findup-sync@^2.0.0":
    "integrity" "sha1-kyaxSIwi0aYIhlCoaQGy2akKLLw="
    "resolved" "https://registry.npm.taobao.org/findup-sync/download/findup-sync-2.0.0.tgz"
    "version" "2.0.0"
    dependencies:
      "detect-file" "^1.0.0"
      "is-glob" "^3.1.0"
      "micromatch" "^3.0.4"
      "resolve-dir" "^1.0.1"
  
  "fined@^1.0.1":
    "integrity" "sha1-0AvszxqitHXRbUI7Aji3E6LEo3s="
    "resolved" "https://registry.npm.taobao.org/fined/download/fined-1.2.0.tgz"
    "version" "1.2.0"
    dependencies:
      "expand-tilde" "^2.0.2"
      "is-plain-object" "^2.0.3"
      "object.defaults" "^1.1.0"
      "object.pick" "^1.2.0"
      "parse-filepath" "^1.0.1"
  
  "flagged-respawn@^1.0.0":
    "integrity" "sha1-595vEnnd2cqarIpZcdYYYGs6q0E="
    "resolved" "https://registry.npm.taobao.org/flagged-respawn/download/flagged-respawn-1.0.1.tgz"
    "version" "1.0.1"
  
  "follow-redirects@1.5.10":
    "integrity" "sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio="
    "resolved" "https://registry.npm.taobao.org/follow-redirects/download/follow-redirects-1.5.10.tgz"
    "version" "1.5.10"
    dependencies:
      "debug" "=3.1.0"
  
  "for-in@^0.1.3":
    "integrity" "sha1-2Hc5COMSVhCZUrH9ubP6hn0ndeE="
    "resolved" "https://registry.npm.taobao.org/for-in/download/for-in-0.1.8.tgz"
    "version" "0.1.8"
  
  "for-in@^1.0.1", "for-in@^1.0.2":
    "integrity" "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA="
    "resolved" "https://registry.npm.taobao.org/for-in/download/for-in-1.0.2.tgz"
    "version" "1.0.2"
  
  "for-own@^1.0.0":
    "integrity" "sha1-xjMy9BXO3EsE2/5wz4NklMU8tEs="
    "resolved" "https://registry.npm.taobao.org/for-own/download/for-own-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "for-in" "^1.0.1"
  
  "forever-agent@~0.6.1":
    "integrity" "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE="
    "resolved" "https://registry.npm.taobao.org/forever-agent/download/forever-agent-0.6.1.tgz"
    "version" "0.6.1"
  
  "form-data@^2.3.3":
    "integrity" "sha1-8svsV7XlniNxbhKP5E1OXdI4lfQ="
    "resolved" "https://registry.npm.taobao.org/form-data/download/form-data-2.5.1.tgz?cache=0&sync_timestamp=1573027118125&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fform-data%2Fdownload%2Fform-data-2.5.1.tgz"
    "version" "2.5.1"
    dependencies:
      "asynckit" "^0.4.0"
      "combined-stream" "^1.0.6"
      "mime-types" "^2.1.12"
  
  "form-data@^3.0.0":
    "integrity" "sha512-RHkBKtLWUVwd7SqRIvCZMEvAMoGUp0XU+seQiZejj0COz3RI3hWP4sCv3gZWWLjJTd7rGwcsF5eKZGii0r/hbg=="
    "resolved" "https://registry.npmmirror.com/form-data/-/form-data-3.0.1.tgz"
    "version" "3.0.1"
    dependencies:
      "asynckit" "^0.4.0"
      "combined-stream" "^1.0.8"
      "mime-types" "^2.1.12"
  
  "form-data@~2.3.2":
    "integrity" "sha1-3M5SwF9kTymManq5Nr1yTO/786Y="
    "resolved" "https://registry.npm.taobao.org/form-data/download/form-data-2.3.3.tgz?cache=0&sync_timestamp=1573027118125&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fform-data%2Fdownload%2Fform-data-2.3.3.tgz"
    "version" "2.3.3"
    dependencies:
      "asynckit" "^0.4.0"
      "combined-stream" "^1.0.6"
      "mime-types" "^2.1.12"
  
  "formidable@^1.2.0":
    "integrity" "sha1-cPt8oCkO5v+WEJBBX0s989IIJlk="
    "resolved" "https://registry.npm.taobao.org/formidable/download/formidable-1.2.1.tgz"
    "version" "1.2.1"
  
  "formidable@^1.2.2":
    "integrity" "sha512-KcpbcpuLNOwrEjnbpMC0gS+X8ciDoZE1kkqzat4a8vrprf+s9pKNQ/QIwWfbfs4ltgmFl3MD177SNTkve3BwGQ=="
    "resolved" "https://registry.npmmirror.com/formidable/-/formidable-1.2.6.tgz"
    "version" "1.2.6"
  
  "formstream@^1.1.0", "formstream@>=0.0.8":
    "integrity" "sha1-UfOXDyYTbrCtRDBN5M67UCB7RHk="
    "resolved" "https://registry.npm.taobao.org/formstream/download/formstream-1.1.0.tgz"
    "version" "1.1.0"
    dependencies:
      "destroy" "^1.0.4"
      "mime" "^1.3.4"
      "pause-stream" "~0.0.11"
  
  "forwarded@~0.1.2":
    "integrity" "sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ="
    "resolved" "https://registry.npm.taobao.org/forwarded/download/forwarded-0.1.2.tgz"
    "version" "0.1.2"
  
  "frac@~1.1.2":
    "integrity" "sha1-PXT39keMiKG1AgMG10fcYxPHTQs="
    "resolved" "https://registry.npm.taobao.org/frac/download/frac-1.1.2.tgz"
    "version" "1.1.2"
  
  "fragment-cache@^0.2.1":
    "integrity" "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk="
    "resolved" "https://registry.npm.taobao.org/fragment-cache/download/fragment-cache-0.2.1.tgz"
    "version" "0.2.1"
    dependencies:
      "map-cache" "^0.2.2"
  
  "fresh@^0.5.2", "fresh@0.5.2":
    "integrity" "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="
    "resolved" "https://registry.npm.taobao.org/fresh/download/fresh-0.5.2.tgz"
    "version" "0.5.2"
  
  "fs-constants@^1.0.0":
    "integrity" "sha1-a+Dem+mYzhavivwkSXue6bfM2a0="
    "resolved" "https://registry.npm.taobao.org/fs-constants/download/fs-constants-1.0.0.tgz"
    "version" "1.0.0"
  
  "fs-extra@^7.0.0", "fs-extra@^7.0.1":
    "integrity" "sha1-TxicRKoSO4lfcigE9V6iPq3DSOk="
    "resolved" "https://registry.npm.taobao.org/fs-extra/download/fs-extra-7.0.1.tgz"
    "version" "7.0.1"
    dependencies:
      "graceful-fs" "^4.1.2"
      "jsonfile" "^4.0.0"
      "universalify" "^0.1.0"
  
  "fs-extra@^8.1.0":
    "integrity" "sha1-SdQ8RaiM2Wd2aMt74bRu/bjS4cA="
    "resolved" "https://registry.nlark.com/fs-extra/download/fs-extra-8.1.0.tgz"
    "version" "8.1.0"
    dependencies:
      "graceful-fs" "^4.2.0"
      "jsonfile" "^4.0.0"
      "universalify" "^0.1.0"
  
  "fs.realpath@^1.0.0":
    "integrity" "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="
    "resolved" "https://registry.npm.taobao.org/fs.realpath/download/fs.realpath-1.0.0.tgz"
    "version" "1.0.0"
  
  "fstream@^1.0.12":
    "integrity" "sha1-Touo7i1Ivk99DeUFRVVI6uWTIEU="
    "resolved" "https://registry.npm.taobao.org/fstream/download/fstream-1.0.12.tgz"
    "version" "1.0.12"
    dependencies:
      "graceful-fs" "^4.1.2"
      "inherits" "~2.0.0"
      "mkdirp" ">=0.5 0"
      "rimraf" "2"
  
  "ftp@^0.3.10", "ftp@~0.3.10":
    "integrity" "sha1-kZfYYa2BQvPmPVqDv+TFn3MwiF0="
    "resolved" "https://registry.npm.taobao.org/ftp/download/ftp-0.3.10.tgz"
    "version" "0.3.10"
    dependencies:
      "readable-stream" "1.1.x"
      "xregexp" "2.0.0"
  
  "function-bind@^1.1.1":
    "integrity" "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="
    "resolved" "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.1.tgz"
    "version" "1.1.1"
  
  "get-caller-file@^2.0.1":
    "integrity" "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34="
    "resolved" "https://registry.npm.taobao.org/get-caller-file/download/get-caller-file-2.0.5.tgz"
    "version" "2.0.5"
  
  "get-func-name@^2.0.0":
    "integrity" "sha1-6td0q+5y4gQJQzoGY2YCPdaIekE="
    "resolved" "https://registry.npm.taobao.org/get-func-name/download/get-func-name-2.0.0.tgz"
    "version" "2.0.0"
  
  "get-intrinsic@^1.0.2":
    "integrity" "sha512-QJVz1Tj7MS099PevUG5jvnt9tSkXN8K14dxQlikJuPt4uD9hHAHjLyLBiLR5zELelBdD9QNRAXZzsJx0WaDL9A=="
    "resolved" "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.1.3.tgz"
    "version" "1.1.3"
    dependencies:
      "function-bind" "^1.1.1"
      "has" "^1.0.3"
      "has-symbols" "^1.0.3"
  
  "get-ready@^1.0.0", "get-ready@~1.0.0":
    "integrity" "sha1-+RgX8emt7P6hOlYq38jeiDqzR4I="
    "resolved" "https://registry.npm.taobao.org/get-ready/download/get-ready-1.0.0.tgz"
    "version" "1.0.0"
  
  "get-stream@^4.1.0":
    "integrity" "sha1-wbJVV189wh1Zv8ec09K0axw6VLU="
    "resolved" "https://registry.npm.taobao.org/get-stream/download/get-stream-4.1.0.tgz"
    "version" "4.1.0"
    dependencies:
      "pump" "^3.0.0"
  
  "get-uri@^2.0.0":
    "integrity" "sha1-1JN6uBniGNTLWuGOT1livvFpzGo="
    "resolved" "https://registry.npm.taobao.org/get-uri/download/get-uri-2.0.4.tgz"
    "version" "2.0.4"
    dependencies:
      "data-uri-to-buffer" "1"
      "debug" "2"
      "extend" "~3.0.2"
      "file-uri-to-path" "1"
      "ftp" "~0.3.10"
      "readable-stream" "2"
  
  "get-uri@3":
    "integrity" "sha1-8O8TVvqrxw4flAT6O2ayupv8clw="
    "resolved" "https://registry.npm.taobao.org/get-uri/download/get-uri-3.0.2.tgz"
    "version" "3.0.2"
    dependencies:
      "@tootallnate/once" "1"
      "data-uri-to-buffer" "3"
      "debug" "4"
      "file-uri-to-path" "2"
      "fs-extra" "^8.1.0"
      "ftp" "^0.3.10"
  
  "get-value@^2.0.3", "get-value@^2.0.6":
    "integrity" "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg="
    "resolved" "https://registry.npm.taobao.org/get-value/download/get-value-2.0.6.tgz"
    "version" "2.0.6"
  
  "getpass@^0.1.1":
    "integrity" "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo="
    "resolved" "https://registry.npm.taobao.org/getpass/download/getpass-0.1.7.tgz"
    "version" "0.1.7"
    dependencies:
      "assert-plus" "^1.0.0"
  
  "glob-parent@^3.1.0":
    "integrity" "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4="
    "resolved" "https://registry.npm.taobao.org/glob-parent/download/glob-parent-3.1.0.tgz"
    "version" "3.1.0"
    dependencies:
      "is-glob" "^3.1.0"
      "path-dirname" "^1.0.0"
  
  "glob-to-regexp@^0.3.0":
    "integrity" "sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs="
    "resolved" "https://registry.npm.taobao.org/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz"
    "version" "0.3.0"
  
  "glob@^7.1.2", "glob@^7.1.3":
    "integrity" "sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY="
    "resolved" "https://registry.npm.taobao.org/glob/download/glob-7.1.6.tgz?cache=0&sync_timestamp=1573078079496&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglob%2Fdownload%2Fglob-7.1.6.tgz"
    "version" "7.1.6"
    dependencies:
      "fs.realpath" "^1.0.0"
      "inflight" "^1.0.4"
      "inherits" "2"
      "minimatch" "^3.0.4"
      "once" "^1.3.0"
      "path-is-absolute" "^1.0.0"
  
  "glob@^7.1.4":
    "integrity" "sha1-Oxk+kjPwHULQs/eClLvutBj5SpA="
    "resolved" "https://registry.nlark.com/glob/download/glob-7.1.7.tgz?cache=0&sync_timestamp=1620337382269&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglob%2Fdownload%2Fglob-7.1.7.tgz"
    "version" "7.1.7"
    dependencies:
      "fs.realpath" "^1.0.0"
      "inflight" "^1.0.4"
      "inherits" "2"
      "minimatch" "^3.0.4"
      "once" "^1.3.0"
      "path-is-absolute" "^1.0.0"
  
  "global-modules@^1.0.0":
    "integrity" "sha1-bXcPDrUjrHgWTXK15xqIdyZcw+o="
    "resolved" "https://registry.npm.taobao.org/global-modules/download/global-modules-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "global-prefix" "^1.0.1"
      "is-windows" "^1.0.1"
      "resolve-dir" "^1.0.0"
  
  "global-prefix@^1.0.1":
    "integrity" "sha1-2/dDxsFJklk8ZVVoy2btMsASLr4="
    "resolved" "https://registry.npm.taobao.org/global-prefix/download/global-prefix-1.0.2.tgz"
    "version" "1.0.2"
    dependencies:
      "expand-tilde" "^2.0.2"
      "homedir-polyfill" "^1.0.1"
      "ini" "^1.3.4"
      "is-windows" "^1.0.1"
      "which" "^1.2.14"
  
  "globby@^8.0.1":
    "integrity" "sha1-VpdhnM2VxSdduy1vqkIIfBqUHY0="
    "resolved" "https://registry.npm.taobao.org/globby/download/globby-8.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglobby%2Fdownload%2Fglobby-8.0.2.tgz"
    "version" "8.0.2"
    dependencies:
      "array-union" "^1.0.1"
      "dir-glob" "2.0.0"
      "fast-glob" "^2.0.2"
      "glob" "^7.1.2"
      "ignore" "^3.3.5"
      "pify" "^3.0.0"
      "slash" "^1.0.0"
  
  "graceful-fs@^4.1.2", "graceful-fs@^4.1.6":
    "integrity" "sha1-ShL/G2A3bvCYYsIJPt2Qgyi+hCM="
    "resolved" "https://registry.npm.taobao.org/graceful-fs/download/graceful-fs-4.2.3.tgz"
    "version" "4.2.3"
  
  "graceful-fs@^4.2.0":
    "integrity" "sha1-/wQLKwhTsjw9MQJ1I3BvGIXXa+4="
    "resolved" "https://registry.npm.taobao.org/graceful-fs/download/graceful-fs-4.2.6.tgz"
    "version" "4.2.6"
  
  "graceful-fs@^4.2.2":
    "integrity" "sha1-/wQLKwhTsjw9MQJ1I3BvGIXXa+4="
    "resolved" "https://registry.npm.taobao.org/graceful-fs/download/graceful-fs-4.2.6.tgz"
    "version" "4.2.6"
  
  "har-schema@^2.0.0":
    "integrity" "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI="
    "resolved" "https://registry.npm.taobao.org/har-schema/download/har-schema-2.0.0.tgz"
    "version" "2.0.0"
  
  "har-validator@~5.1.0":
    "integrity" "sha1-HvievT5JllV2de7ZiTEQ3DUPoIA="
    "resolved" "https://registry.npm.taobao.org/har-validator/download/har-validator-5.1.3.tgz"
    "version" "5.1.3"
    dependencies:
      "ajv" "^6.5.5"
      "har-schema" "^2.0.0"
  
  "has-ansi@^2.0.0":
    "integrity" "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE="
    "resolved" "https://registry.npm.taobao.org/has-ansi/download/has-ansi-2.0.0.tgz"
    "version" "2.0.0"
    dependencies:
      "ansi-regex" "^2.0.0"
  
  "has-flag@^3.0.0":
    "integrity" "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="
    "resolved" "https://registry.npm.taobao.org/has-flag/download/has-flag-3.0.0.tgz"
    "version" "3.0.0"
  
  "has-symbols@^1.0.3":
    "integrity" "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A=="
    "resolved" "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz"
    "version" "1.0.3"
  
  "has-value@^0.3.1":
    "integrity" "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8="
    "resolved" "https://registry.npm.taobao.org/has-value/download/has-value-0.3.1.tgz"
    "version" "0.3.1"
    dependencies:
      "get-value" "^2.0.3"
      "has-values" "^0.1.4"
      "isobject" "^2.0.0"
  
  "has-value@^1.0.0":
    "integrity" "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc="
    "resolved" "https://registry.npm.taobao.org/has-value/download/has-value-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "get-value" "^2.0.6"
      "has-values" "^1.0.0"
      "isobject" "^3.0.0"
  
  "has-values@^0.1.4":
    "integrity" "sha1-bWHeldkd/Km5oCCJrThL/49it3E="
    "resolved" "https://registry.npm.taobao.org/has-values/download/has-values-0.1.4.tgz"
    "version" "0.1.4"
  
  "has-values@^1.0.0":
    "integrity" "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8="
    "resolved" "https://registry.npm.taobao.org/has-values/download/has-values-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "is-number" "^3.0.0"
      "kind-of" "^4.0.0"
  
  "has@^1.0.3":
    "integrity" "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw=="
    "resolved" "https://registry.npmmirror.com/has/-/has-1.0.3.tgz"
    "version" "1.0.3"
    dependencies:
      "function-bind" "^1.1.1"
  
  "hasha@^3.0.0":
    "integrity" "sha1-UqMvq4Vp1BymmmH/GiFPjrfIvTk="
    "resolved" "https://registry.npm.taobao.org/hasha/download/hasha-3.0.0.tgz"
    "version" "3.0.0"
    dependencies:
      "is-stream" "^1.0.1"
  
  "haye@^2.0.1", "haye@^2.0.2":
    "integrity" "sha1-dYBDrxfj1QFM7OgqP8I1JdRwT/M="
    "resolved" "https://registry.npm.taobao.org/haye/download/haye-2.0.2.tgz"
    "version" "2.0.2"
  
  "homedir-polyfill@^1.0.1":
    "integrity" "sha1-dDKYzvTlrz4ZQWH7rcwhUdOgWOg="
    "resolved" "https://registry.npm.taobao.org/homedir-polyfill/download/homedir-polyfill-1.0.3.tgz"
    "version" "1.0.3"
    dependencies:
      "parse-passwd" "^1.0.0"
  
  "http-errors@~1.6.2":
    "integrity" "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0="
    "resolved" "https://registry.npm.taobao.org/http-errors/download/http-errors-1.6.3.tgz"
    "version" "1.6.3"
    dependencies:
      "depd" "~1.1.2"
      "inherits" "2.0.3"
      "setprototypeof" "1.1.0"
      "statuses" ">= 1.4.0 < 2"
  
  "http-errors@~1.7.0", "http-errors@~1.7.2", "http-errors@1.7.3":
    "integrity" "sha1-bGGeT5xgMIw4UZSYwU+7EKrOuwY="
    "resolved" "https://registry.npm.taobao.org/http-errors/download/http-errors-1.7.3.tgz"
    "version" "1.7.3"
    dependencies:
      "depd" "~1.1.2"
      "inherits" "2.0.4"
      "setprototypeof" "1.1.1"
      "statuses" ">= 1.5.0 < 2"
      "toidentifier" "1.0.0"
  
  "http-proxy-agent@^2.1.0":
    "integrity" "sha1-5IIb7vWyFCogJr1zkm/lN2McVAU="
    "resolved" "https://registry.npm.taobao.org/http-proxy-agent/download/http-proxy-agent-2.1.0.tgz"
    "version" "2.1.0"
    dependencies:
      "agent-base" "4"
      "debug" "3.1.0"
  
  "http-proxy-agent@^4.0.0", "http-proxy-agent@^4.0.1":
    "integrity" "sha1-ioyO9/WTLM+VPClsqCkblap0qjo="
    "resolved" "https://registry.npm.taobao.org/http-proxy-agent/download/http-proxy-agent-4.0.1.tgz"
    "version" "4.0.1"
    dependencies:
      "@tootallnate/once" "1"
      "agent-base" "6"
      "debug" "4"
  
  "http-signature@~1.2.0":
    "integrity" "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE="
    "resolved" "https://registry.npm.taobao.org/http-signature/download/http-signature-1.2.0.tgz?cache=0&sync_timestamp=1572997318670&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-signature%2Fdownload%2Fhttp-signature-1.2.0.tgz"
    "version" "1.2.0"
    dependencies:
      "assert-plus" "^1.0.0"
      "jsprim" "^1.2.2"
      "sshpk" "^1.7.0"
  
  "https-proxy-agent@^3.0.0":
    "integrity" "sha1-uMKGQz6HYCMRsByOo0QT2Fakr4E="
    "resolved" "https://registry.npm.taobao.org/https-proxy-agent/download/https-proxy-agent-3.0.1.tgz"
    "version" "3.0.1"
    dependencies:
      "agent-base" "^4.3.0"
      "debug" "^3.1.0"
  
  "https-proxy-agent@^5.0.0", "https-proxy-agent@5":
    "integrity" "sha1-4qkFQqu2inYuCghQ9sntrf2FBrI="
    "resolved" "https://registry.npm.taobao.org/https-proxy-agent/download/https-proxy-agent-5.0.0.tgz"
    "version" "5.0.0"
    dependencies:
      "agent-base" "6"
      "debug" "4"
  
  "httpx@^2.1.1", "httpx@^2.1.2":
    "integrity" "sha1-fiYjNDtcuUJOmt2TRw7Z1gtLSPk="
    "resolved" "https://registry.npm.taobao.org/httpx/download/httpx-2.2.2.tgz"
    "version" "2.2.2"
    dependencies:
      "@types/node" "^12.0.2"
      "debug" "^4.1.1"
  
  "humanize-ms@^1.2.0", "humanize-ms@^1.2.1":
    "integrity" "sha1-xG4xWaKT9riW2ikxbYtv6Lt5u+0="
    "resolved" "https://registry.npm.taobao.org/humanize-ms/download/humanize-ms-1.2.1.tgz"
    "version" "1.2.1"
    dependencies:
      "ms" "^2.0.0"
  
  "hyphenate-style-name@^1.0.2":
    "integrity" "sha1-CXu3+guPGpzwvVxzTPlYmZgam0g="
    "resolved" "https://registry.npm.taobao.org/hyphenate-style-name/download/hyphenate-style-name-1.0.3.tgz"
    "version" "1.0.3"
  
  "iconv-lite@^0.4.15", "iconv-lite@0.4.24":
    "integrity" "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs="
    "resolved" "https://registry.npm.taobao.org/iconv-lite/download/iconv-lite-0.4.24.tgz?cache=0&sync_timestamp=1579333981154&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ficonv-lite%2Fdownload%2Ficonv-lite-0.4.24.tgz"
    "version" "0.4.24"
    dependencies:
      "safer-buffer" ">= 2.1.2 < 3"
  
  "ieee754@^1.1.13":
    "integrity" "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I="
    "resolved" "https://registry.npm.taobao.org/ieee754/download/ieee754-1.2.1.tgz?cache=0&sync_timestamp=1603841495413&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fieee754%2Fdownload%2Fieee754-1.2.1.tgz"
    "version" "1.2.1"
  
  "ieee754@^1.1.4":
    "integrity" "sha1-7BaFWOlaoYH9h9N/VcMrvLZwi4Q="
    "resolved" "https://registry.npm.taobao.org/ieee754/download/ieee754-1.1.13.tgz"
    "version" "1.1.13"
  
  "ignore@^3.3.5":
    "integrity" "sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM="
    "resolved" "https://registry.npm.taobao.org/ignore/download/ignore-3.3.10.tgz"
    "version" "3.3.10"
  
  "immediate@~3.0.5":
    "integrity" "sha1-nbHb0Pr43m++D13V5Wu2BigN5ps="
    "resolved" "https://registry.npm.taobao.org/immediate/download/immediate-3.0.6.tgz?cache=0&sync_timestamp=1591712549735&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fimmediate%2Fdownload%2Fimmediate-3.0.6.tgz"
    "version" "3.0.6"
  
  "indent-string@^3.2.0":
    "integrity" "sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok="
    "resolved" "https://registry.npm.taobao.org/indent-string/download/indent-string-3.2.0.tgz"
    "version" "3.2.0"
  
  "inflation@^2.0.0":
    "integrity" "sha1-i0F+R8KPklpFEz2RTKH9OJEH8w8="
    "resolved" "https://registry.npm.taobao.org/inflation/download/inflation-2.0.0.tgz"
    "version" "2.0.0"
  
  "inflight@^1.0.4":
    "integrity" "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk="
    "resolved" "https://registry.npm.taobao.org/inflight/download/inflight-1.0.6.tgz"
    "version" "1.0.6"
    dependencies:
      "once" "^1.3.0"
      "wrappy" "1"
  
  "info-symbol@^0.1.0":
    "integrity" "sha1-J4QdcoZ920JCzWEtecEGM4gcang="
    "resolved" "https://registry.npm.taobao.org/info-symbol/download/info-symbol-0.1.0.tgz"
    "version" "0.1.0"
  
  "inherits@^2.0.1", "inherits@^2.0.3", "inherits@^2.0.4", "inherits@~2.0.0", "inherits@~2.0.1", "inherits@~2.0.3", "inherits@2", "inherits@2.0.4":
    "integrity" "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="
    "resolved" "https://registry.npm.taobao.org/inherits/download/inherits-2.0.4.tgz"
    "version" "2.0.4"
  
  "inherits@2.0.3":
    "integrity" "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="
    "resolved" "https://registry.npm.taobao.org/inherits/download/inherits-2.0.3.tgz"
    "version" "2.0.3"
  
  "ini@^1.3.4":
    "integrity" "sha1-7uJfVtscnsYIXgwid4CD9Zar+Sc="
    "resolved" "https://registry.npm.taobao.org/ini/download/ini-1.3.5.tgz"
    "version" "1.3.5"
  
  "inline-style-prefixer@^3.0.1":
    "integrity" "sha1-hVG45bTVcyROZqNLBPfTIHaitTQ="
    "resolved" "https://registry.npm.taobao.org/inline-style-prefixer/download/inline-style-prefixer-3.0.8.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Finline-style-prefixer%2Fdownload%2Finline-style-prefixer-3.0.8.tgz"
    "version" "3.0.8"
    dependencies:
      "bowser" "^1.7.3"
      "css-in-js-utils" "^2.0.0"
  
  "interpret@^1.1.0":
    "integrity" "sha1-1QYaYiS+WOgIOYX1AU2EQ1lXYpY="
    "resolved" "https://registry.npm.taobao.org/interpret/download/interpret-1.2.0.tgz"
    "version" "1.2.0"
  
  "ip@^1.1.5", "ip@1.1.5":
    "integrity" "sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo="
    "resolved" "https://registry.npm.taobao.org/ip/download/ip-1.1.5.tgz"
    "version" "1.1.5"
  
  "ipaddr.js@1.9.0":
    "integrity" "sha1-N9905DCg5HVQ/lSi3v4w2KzZX2U="
    "resolved" "https://registry.npm.taobao.org/ipaddr.js/download/ipaddr.js-1.9.0.tgz"
    "version" "1.9.0"
  
  "is-absolute@^1.0.0":
    "integrity" "sha1-OV4a6EsR8mrReV5zwXN45IowFXY="
    "resolved" "https://registry.npm.taobao.org/is-absolute/download/is-absolute-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "is-relative" "^1.0.0"
      "is-windows" "^1.0.1"
  
  "is-accessor-descriptor@^0.1.6":
    "integrity" "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY="
    "resolved" "https://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz"
    "version" "0.1.6"
    dependencies:
      "kind-of" "^3.0.2"
  
  "is-accessor-descriptor@^1.0.0":
    "integrity" "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY="
    "resolved" "https://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "kind-of" "^6.0.0"
  
  "is-arrayish@^0.3.1":
    "integrity" "sha1-RXSirlb3qyBolvtDHq7tBm/fjwM="
    "resolved" "https://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.3.2.tgz"
    "version" "0.3.2"
  
  "is-arrow-function@^2.0.3":
    "integrity" "sha1-Kb4sLY2UUIUri7r7Y1unuNjofsI="
    "resolved" "https://registry.npm.taobao.org/is-arrow-function/download/is-arrow-function-2.0.3.tgz"
    "version" "2.0.3"
    dependencies:
      "is-callable" "^1.0.4"
  
  "is-buffer@^1.0.2", "is-buffer@^1.1.5", "is-buffer@~1.1.6":
    "integrity" "sha1-76ouqdqg16suoTqXsritUf776L4="
    "resolved" "https://registry.npm.taobao.org/is-buffer/download/is-buffer-1.1.6.tgz"
    "version" "1.1.6"
  
  "is-buffer@^2.0.2":
    "integrity" "sha1-PlcvI8hBGlz9lVfISeNmXgspBiM="
    "resolved" "https://registry.npm.taobao.org/is-buffer/download/is-buffer-2.0.4.tgz"
    "version" "2.0.4"
  
  "is-callable@^1.0.4":
    "integrity" "sha1-9+RrWWiQRW23Tn9ul2yzJz0G+qs="
    "resolved" "https://registry.npm.taobao.org/is-callable/download/is-callable-1.1.5.tgz"
    "version" "1.1.5"
  
  "is-class-hotfix@~0.0.6":
    "integrity" "sha1-pSfTH7IyeSgd3l84XHe13nCnJDU="
    "resolved" "https://registry.npm.taobao.org/is-class-hotfix/download/is-class-hotfix-0.0.6.tgz"
    "version" "0.0.6"
  
  "is-data-descriptor@^0.1.4":
    "integrity" "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y="
    "resolved" "https://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz"
    "version" "0.1.4"
    dependencies:
      "kind-of" "^3.0.2"
  
  "is-data-descriptor@^1.0.0":
    "integrity" "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc="
    "resolved" "https://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "kind-of" "^6.0.0"
  
  "is-descriptor@^0.1.0":
    "integrity" "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco="
    "resolved" "https://registry.npm.taobao.org/is-descriptor/download/is-descriptor-0.1.6.tgz"
    "version" "0.1.6"
    dependencies:
      "is-accessor-descriptor" "^0.1.6"
      "is-data-descriptor" "^0.1.4"
      "kind-of" "^5.0.0"
  
  "is-descriptor@^1.0.0", "is-descriptor@^1.0.2":
    "integrity" "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw="
    "resolved" "https://registry.npm.taobao.org/is-descriptor/download/is-descriptor-1.0.2.tgz"
    "version" "1.0.2"
    dependencies:
      "is-accessor-descriptor" "^1.0.0"
      "is-data-descriptor" "^1.0.0"
      "kind-of" "^6.0.2"
  
  "is-extendable@^0.1.0", "is-extendable@^0.1.1":
    "integrity" "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="
    "resolved" "https://registry.npm.taobao.org/is-extendable/download/is-extendable-0.1.1.tgz"
    "version" "0.1.1"
  
  "is-extendable@^1.0.1":
    "integrity" "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ="
    "resolved" "https://registry.npm.taobao.org/is-extendable/download/is-extendable-1.0.1.tgz"
    "version" "1.0.1"
    dependencies:
      "is-plain-object" "^2.0.4"
  
  "is-extglob@^2.1.0", "is-extglob@^2.1.1":
    "integrity" "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="
    "resolved" "https://registry.npm.taobao.org/is-extglob/download/is-extglob-2.1.1.tgz"
    "version" "2.1.1"
  
  "is-fullwidth-code-point@^2.0.0":
    "integrity" "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="
    "resolved" "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz"
    "version" "2.0.0"
  
  "is-glob@^3.1.0":
    "integrity" "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo="
    "resolved" "https://registry.npm.taobao.org/is-glob/download/is-glob-3.1.0.tgz"
    "version" "3.1.0"
    dependencies:
      "is-extglob" "^2.1.0"
  
  "is-glob@^4.0.0":
    "integrity" "sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw="
    "resolved" "https://registry.npm.taobao.org/is-glob/download/is-glob-4.0.1.tgz"
    "version" "4.0.1"
    dependencies:
      "is-extglob" "^2.1.1"
  
  "is-nan@^1.2.1":
    "integrity" "sha1-hdH1SC9wUcIBn1ZzzOvbBvOw2wM="
    "resolved" "https://registry.npm.taobao.org/is-nan/download/is-nan-1.3.0.tgz"
    "version" "1.3.0"
    dependencies:
      "define-properties" "^1.1.3"
  
  "is-number@^3.0.0":
    "integrity" "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU="
    "resolved" "https://registry.npm.taobao.org/is-number/download/is-number-3.0.0.tgz"
    "version" "3.0.0"
    dependencies:
      "kind-of" "^3.0.2"
  
  "is-number@^6.0.0":
    "integrity" "sha1-5tFa0x/CYoh8zPIXrl+TFvgbGZU="
    "resolved" "https://registry.npm.taobao.org/is-number/download/is-number-6.0.0.tgz"
    "version" "6.0.0"
  
  "is-plain-object@^2.0.1", "is-plain-object@^2.0.3", "is-plain-object@^2.0.4":
    "integrity" "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc="
    "resolved" "https://registry.npm.taobao.org/is-plain-object/download/is-plain-object-2.0.4.tgz"
    "version" "2.0.4"
    dependencies:
      "isobject" "^3.0.1"
  
  "is-relative@^1.0.0":
    "integrity" "sha1-obtpNc6MXboei5dUubLcwCDiJg0="
    "resolved" "https://registry.npm.taobao.org/is-relative/download/is-relative-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "is-unc-path" "^1.0.0"
  
  "is-stream@^1.0.1", "is-stream@^1.1.0":
    "integrity" "sha1-EtSj3U5o4Lec6428hBc66A2RykQ="
    "resolved" "https://registry.npm.taobao.org/is-stream/download/is-stream-1.1.0.tgz"
    "version" "1.1.0"
  
  "is-type-of@^1.0.0":
    "integrity" "sha1-4mPsOFes608oxHEw7HjbCakg+MU="
    "resolved" "https://registry.npm.taobao.org/is-type-of/download/is-type-of-1.2.1.tgz"
    "version" "1.2.1"
    dependencies:
      "core-util-is" "^1.0.2"
      "is-class-hotfix" "~0.0.6"
      "isstream" "~0.1.2"
  
  "is-typedarray@~1.0.0":
    "integrity" "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="
    "resolved" "https://registry.npm.taobao.org/is-typedarray/download/is-typedarray-1.0.0.tgz"
    "version" "1.0.0"
  
  "is-unc-path@^1.0.0":
    "integrity" "sha1-1zHoiY7QkKEsNSrS6u1Qla0yLJ0="
    "resolved" "https://registry.npm.taobao.org/is-unc-path/download/is-unc-path-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "unc-path-regex" "^0.1.2"
  
  "is-windows@^1.0.1", "is-windows@^1.0.2":
    "integrity" "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0="
    "resolved" "https://registry.npm.taobao.org/is-windows/download/is-windows-1.0.2.tgz"
    "version" "1.0.2"
  
  "isarray@^2.0.1":
    "integrity" "sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw=="
    "resolved" "https://registry.npmmirror.com/isarray/-/isarray-2.0.5.tgz"
    "version" "2.0.5"
  
  "isarray@~1.0.0", "isarray@1.0.0":
    "integrity" "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="
    "resolved" "https://registry.npm.taobao.org/isarray/download/isarray-1.0.0.tgz"
    "version" "1.0.0"
  
  "isarray@0.0.1":
    "integrity" "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="
    "resolved" "https://registry.npm.taobao.org/isarray/download/isarray-0.0.1.tgz"
    "version" "0.0.1"
  
  "isexe@^2.0.0":
    "integrity" "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="
    "resolved" "https://registry.npm.taobao.org/isexe/download/isexe-2.0.0.tgz"
    "version" "2.0.0"
  
  "isobject@^2.0.0":
    "integrity" "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk="
    "resolved" "https://registry.npm.taobao.org/isobject/download/isobject-2.1.0.tgz"
    "version" "2.1.0"
    dependencies:
      "isarray" "1.0.0"
  
  "isobject@^3.0.0", "isobject@^3.0.1":
    "integrity" "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="
    "resolved" "https://registry.npm.taobao.org/isobject/download/isobject-3.0.1.tgz"
    "version" "3.0.1"
  
  "isstream@~0.1.2":
    "integrity" "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo="
    "resolved" "https://registry.npm.taobao.org/isstream/download/isstream-0.1.2.tgz"
    "version" "0.1.2"
  
  "japa@1.0.6":
    "integrity" "sha1-u0pNCtj9suJPOZBPsznkA8zSqHw="
    "resolved" "https://registry.npm.taobao.org/japa/download/japa-1.0.6.tgz"
    "version" "1.0.6"
    dependencies:
      "chai" "^4.1.2"
      "chalk" "^2.3.0"
      "ms" "^2.1.1"
      "retry" "^0.10.1"
      "right-pad" "^1.0.1"
      "variable-diff" "^1.1.0"
  
  "jsbn@~0.1.0":
    "integrity" "sha1-peZUwuWi3rXyAdls77yoDA7y9RM="
    "resolved" "https://registry.npm.taobao.org/jsbn/download/jsbn-0.1.1.tgz"
    "version" "0.1.1"
  
  "json-bigint@^0.2.3":
    "integrity" "sha1-EY1/b/HThlnxn5TPc+ZKdaP5iKg="
    "resolved" "https://registry.npm.taobao.org/json-bigint/download/json-bigint-0.2.3.tgz"
    "version" "0.2.3"
    dependencies:
      "bignumber.js" "^4.0.0"
  
  "json-bigint@^0.3.0":
    "integrity" "sha1-DM2RLEuCcNBfBW+9E4FLU9OCWx4="
    "resolved" "https://registry.npm.taobao.org/json-bigint/download/json-bigint-0.3.0.tgz"
    "version" "0.3.0"
    dependencies:
      "bignumber.js" "^7.0.0"
  
  "json-schema-traverse@^0.4.1":
    "integrity" "sha1-afaofZUTq4u4/mO9sJecRI5oRmA="
    "resolved" "https://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz"
    "version" "0.4.1"
  
  "json-schema@0.2.3":
    "integrity" "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM="
    "resolved" "https://registry.npm.taobao.org/json-schema/download/json-schema-0.2.3.tgz"
    "version" "0.2.3"
  
  "json-stringify-safe@~5.0.1":
    "integrity" "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus="
    "resolved" "https://registry.npm.taobao.org/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz"
    "version" "5.0.1"
  
  "jsonfile@^4.0.0":
    "integrity" "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss="
    "resolved" "https://registry.npm.taobao.org/jsonfile/download/jsonfile-4.0.0.tgz"
    "version" "4.0.0"
    optionalDependencies:
      "graceful-fs" "^4.1.6"
  
  "jsonwebtoken@^8.3.0", "jsonwebtoken@^8.5.0":
    "integrity" "sha1-AOceC431TCEhofJhN98igGc7zA0="
    "resolved" "https://registry.npm.taobao.org/jsonwebtoken/download/jsonwebtoken-8.5.1.tgz"
    "version" "8.5.1"
    dependencies:
      "jws" "^3.2.2"
      "lodash.includes" "^4.3.0"
      "lodash.isboolean" "^3.0.3"
      "lodash.isinteger" "^4.0.4"
      "lodash.isnumber" "^3.0.3"
      "lodash.isplainobject" "^4.0.6"
      "lodash.isstring" "^4.0.1"
      "lodash.once" "^4.0.0"
      "ms" "^2.1.1"
      "semver" "^5.6.0"
  
  "jsprim@^1.2.2":
    "integrity" "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI="
    "resolved" "https://registry.npm.taobao.org/jsprim/download/jsprim-1.4.1.tgz"
    "version" "1.4.1"
    dependencies:
      "assert-plus" "1.0.0"
      "extsprintf" "1.3.0"
      "json-schema" "0.2.3"
      "verror" "1.10.0"
  
  "jstoxml@^0.2.3":
    "integrity" "sha1-/z+2eFaIOgMpU8fOjOdIYhD0hEc="
    "resolved" "https://registry.npm.taobao.org/jstoxml/download/jstoxml-0.2.4.tgz"
    "version" "0.2.4"
  
  "jszip@^3.5.0":
    "integrity" "sha1-g5tygS4/l4GcwTrEE0/87ZXdavk="
    "resolved" "https://registry.npm.taobao.org/jszip/download/jszip-3.6.0.tgz"
    "version" "3.6.0"
    dependencies:
      "lie" "~3.3.0"
      "pako" "~1.0.2"
      "readable-stream" "~2.3.6"
      "set-immediate-shim" "~1.0.1"
  
  "jwa@^1.4.1":
    "integrity" "sha1-dDwymFy56YZVUw1TZBtmyGRbA5o="
    "resolved" "https://registry.npm.taobao.org/jwa/download/jwa-1.4.1.tgz"
    "version" "1.4.1"
    dependencies:
      "buffer-equal-constant-time" "1.0.1"
      "ecdsa-sig-formatter" "1.0.11"
      "safe-buffer" "^5.0.1"
  
  "jws@^3.2.2":
    "integrity" "sha1-ABCZ82OUaMlBQADpmZX6UvtHgwQ="
    "resolved" "https://registry.npm.taobao.org/jws/download/jws-3.2.2.tgz"
    "version" "3.2.2"
    dependencies:
      "jwa" "^1.4.1"
      "safe-buffer" "^5.0.1"
  
  "kind-of@^2.0.1":
    "integrity" "sha1-AY7HpM5+OobLkUG+UZ0kyPqpgbU="
    "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-2.0.1.tgz"
    "version" "2.0.1"
    dependencies:
      "is-buffer" "^1.0.2"
  
  "kind-of@^3.0.2", "kind-of@^3.0.3":
    "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
    "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz"
    "version" "3.2.2"
    dependencies:
      "is-buffer" "^1.1.5"
  
  "kind-of@^3.2.0":
    "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
    "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz"
    "version" "3.2.2"
    dependencies:
      "is-buffer" "^1.1.5"
  
  "kind-of@^3.2.2":
    "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
    "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz"
    "version" "3.2.2"
    dependencies:
      "is-buffer" "^1.1.5"
  
  "kind-of@^4.0.0":
    "integrity" "sha1-IIE989cSkosgc3hpGkUGb65y3Vc="
    "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-4.0.0.tgz"
    "version" "4.0.0"
    dependencies:
      "is-buffer" "^1.1.5"
  
  "kind-of@^5.0.0", "kind-of@^5.0.2":
    "integrity" "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0="
    "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-5.1.0.tgz"
    "version" "5.1.0"
  
  "kind-of@^6.0.0", "kind-of@^6.0.2":
    "integrity" "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0="
    "resolved" "https://registry.npm.taobao.org/kind-of/download/kind-of-6.0.3.tgz"
    "version" "6.0.3"
  
  "kitx@^1.2.0", "kitx@^1.2.1":
    "integrity" "sha1-qz7nxZjSsdYp/VVWj4aMREDCAOo="
    "resolved" "https://registry.npm.taobao.org/kitx/download/kitx-1.3.0.tgz"
    "version" "1.3.0"
  
  "kleur@^2.0.2":
    "integrity" "sha1-twT0lE2V4lXQOPDLBfuKYCxVowA="
    "resolved" "https://registry.npm.taobao.org/kleur/download/kleur-2.0.2.tgz"
    "version" "2.0.2"
  
  "knex@^0.15.2":
    "integrity" "sha1-YFm4dIlgX0zIdZmm0qnSZXCek0A="
    "resolved" "https://registry.npm.taobao.org/knex/download/knex-0.15.2.tgz"
    "version" "0.15.2"
    dependencies:
      "babel-runtime" "^6.26.0"
      "bluebird" "^3.5.1"
      "chalk" "2.3.2"
      "commander" "^2.16.0"
      "debug" "3.1.0"
      "inherits" "~2.0.3"
      "interpret" "^1.1.0"
      "liftoff" "2.5.0"
      "lodash" "^4.17.10"
      "minimist" "1.2.0"
      "mkdirp" "^0.5.1"
      "pg-connection-string" "2.0.0"
      "tarn" "^1.1.4"
      "tildify" "1.2.0"
      "uuid" "^3.3.2"
      "v8flags" "^3.1.1"
  
  "ko-sleep@^1.0.3":
    "integrity" "sha1-KKKgoUhei39BX/SI3uF9JHiKsII="
    "resolved" "https://registry.npm.taobao.org/ko-sleep/download/ko-sleep-1.0.3.tgz"
    "version" "1.0.3"
    dependencies:
      "ms" "^2.0.0"
  
  "koalas@^1.0.2":
    "integrity" "sha1-MYQz8HQjXbePrlZhoCqMpT7ilc0="
    "resolved" "https://registry.npm.taobao.org/koalas/download/koalas-1.0.2.tgz"
    "version" "1.0.2"
  
  "kuler@1.0.x":
    "integrity" "sha1-73x4TzbJ+24W3TFQ0VJneysCKKY="
    "resolved" "https://registry.npm.taobao.org/kuler/download/kuler-1.0.1.tgz"
    "version" "1.0.1"
    dependencies:
      "colornames" "^1.1.1"
  
  "lazy-cache@^0.2.3":
    "integrity" "sha1-f+3fLctu23fRHvHRF6tf/fCrG2U="
    "resolved" "https://registry.npm.taobao.org/lazy-cache/download/lazy-cache-0.2.7.tgz"
    "version" "0.2.7"
  
  "lazy-cache@^2.0.1", "lazy-cache@^2.0.2":
    "integrity" "sha1-uRkKT5EzVGlIQIWfio9whNiCImQ="
    "resolved" "https://registry.npm.taobao.org/lazy-cache/download/lazy-cache-2.0.2.tgz"
    "version" "2.0.2"
    dependencies:
      "set-getter" "^0.1.0"
  
  "lazystream@^1.0.0":
    "integrity" "sha1-9plf4PggOS9hOWvolGJAe7dxaOQ="
    "resolved" "https://registry.npm.taobao.org/lazystream/download/lazystream-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "readable-stream" "^2.0.5"
  
  "levn@~0.3.0":
    "integrity" "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4="
    "resolved" "https://registry.npm.taobao.org/levn/download/levn-0.3.0.tgz"
    "version" "0.3.0"
    dependencies:
      "prelude-ls" "~1.1.2"
      "type-check" "~0.3.2"
  
  "lie@~3.3.0":
    "integrity" "sha1-3Pgt7lRfRgdNryAMfBxaCOD0D2o="
    "resolved" "https://registry.npm.taobao.org/lie/download/lie-3.3.0.tgz"
    "version" "3.3.0"
    dependencies:
      "immediate" "~3.0.5"
  
  "liftoff@2.5.0":
    "integrity" "sha1-IAkpG7Mc6oYbvxCnwVooyvdcMew="
    "resolved" "https://registry.npm.taobao.org/liftoff/download/liftoff-2.5.0.tgz"
    "version" "2.5.0"
    dependencies:
      "extend" "^3.0.0"
      "findup-sync" "^2.0.0"
      "fined" "^1.0.1"
      "flagged-respawn" "^1.0.0"
      "is-plain-object" "^2.0.4"
      "object.map" "^1.0.0"
      "rechoir" "^0.6.2"
      "resolve" "^1.1.7"
  
  "listenercount@~1.0.1":
    "integrity" "sha1-hMinKrWcRyUyFIDJdeZQg0LnCTc="
    "resolved" "https://registry.npm.taobao.org/listenercount/download/listenercount-1.0.1.tgz"
    "version" "1.0.1"
  
  "locate-path@^3.0.0":
    "integrity" "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4="
    "resolved" "https://registry.npm.taobao.org/locate-path/download/locate-path-3.0.0.tgz"
    "version" "3.0.0"
    dependencies:
      "p-locate" "^3.0.0"
      "path-exists" "^3.0.0"
  
  "lodash.defaults@^4.2.0":
    "integrity" "sha1-0JF4cW/+pN3p5ft7N/bwgCJ0WAw="
    "resolved" "https://registry.nlark.com/lodash.defaults/download/lodash.defaults-4.2.0.tgz"
    "version" "4.2.0"
  
  "lodash.difference@^4.5.0":
    "integrity" "sha1-nMtOUF1Ia5FlE0V3KIWi3yf9AXw="
    "resolved" "https://registry.npm.taobao.org/lodash.difference/download/lodash.difference-4.5.0.tgz?cache=0&sync_timestamp=1587902338777&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flodash.difference%2Fdownload%2Flodash.difference-4.5.0.tgz"
    "version" "4.5.0"
  
  "lodash.escaperegexp@^4.1.2":
    "integrity" "sha1-ZHYsSGGAglGKw99Mz11YhtriA0c="
    "resolved" "https://registry.npm.taobao.org/lodash.escaperegexp/download/lodash.escaperegexp-4.1.2.tgz"
    "version" "4.1.2"
  
  "lodash.flatten@^4.4.0":
    "integrity" "sha1-8xwiIlqWMtK7+OSt2+8kCqdlph8="
    "resolved" "https://registry.npm.taobao.org/lodash.flatten/download/lodash.flatten-4.4.0.tgz"
    "version" "4.4.0"
  
  "lodash.groupby@^4.6.0":
    "integrity" "sha1-Cwih3PaDl8OXhVwyOXg4Mt90A9E="
    "resolved" "https://registry.npm.taobao.org/lodash.groupby/download/lodash.groupby-4.6.0.tgz"
    "version" "4.6.0"
  
  "lodash.includes@^4.3.0":
    "integrity" "sha1-YLuYqHy5I8aMoeUTJUgzFISfVT8="
    "resolved" "https://registry.npm.taobao.org/lodash.includes/download/lodash.includes-4.3.0.tgz"
    "version" "4.3.0"
  
  "lodash.isboolean@^3.0.3":
    "integrity" "sha1-bC4XHbKiV82WgC/UOwGyDV9YcPY="
    "resolved" "https://registry.npm.taobao.org/lodash.isboolean/download/lodash.isboolean-3.0.3.tgz"
    "version" "3.0.3"
  
  "lodash.isequal@^4.5.0":
    "integrity" "sha1-QVxEePK8wwEgwizhDtMib30+GOA="
    "resolved" "https://registry.npm.taobao.org/lodash.isequal/download/lodash.isequal-4.5.0.tgz"
    "version" "4.5.0"
  
  "lodash.isfunction@^3.0.9":
    "integrity" "sha1-Bt4l302zJ6yTGYHRvbBn5a9o0FE="
    "resolved" "https://registry.npm.taobao.org/lodash.isfunction/download/lodash.isfunction-3.0.9.tgz"
    "version" "3.0.9"
  
  "lodash.isinteger@^4.0.4":
    "integrity" "sha1-YZwK89A/iwTDH1iChAt3sRzWg0M="
    "resolved" "https://registry.npm.taobao.org/lodash.isinteger/download/lodash.isinteger-4.0.4.tgz"
    "version" "4.0.4"
  
  "lodash.isnil@^4.0.0":
    "integrity" "sha1-SeKM1VkBNFjIFMVHnTxmOiG/qmw="
    "resolved" "https://registry.npm.taobao.org/lodash.isnil/download/lodash.isnil-4.0.0.tgz"
    "version" "4.0.0"
  
  "lodash.isnumber@^3.0.3":
    "integrity" "sha1-POdoEMWSjQM1IwGsKHMX8RwLH/w="
    "resolved" "https://registry.npm.taobao.org/lodash.isnumber/download/lodash.isnumber-3.0.3.tgz"
    "version" "3.0.3"
  
  "lodash.isplainobject@^4.0.6":
    "integrity" "sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs="
    "resolved" "https://registry.npm.taobao.org/lodash.isplainobject/download/lodash.isplainobject-4.0.6.tgz"
    "version" "4.0.6"
  
  "lodash.isstring@^4.0.1":
    "integrity" "sha1-1SfftUVuynzJu5XV2ur4i6VKVFE="
    "resolved" "https://registry.npm.taobao.org/lodash.isstring/download/lodash.isstring-4.0.1.tgz"
    "version" "4.0.1"
  
  "lodash.isundefined@^3.0.1":
    "integrity" "sha1-I+89lTVWUgOmbO/VuDD4SJEa+0g="
    "resolved" "https://registry.npm.taobao.org/lodash.isundefined/download/lodash.isundefined-3.0.1.tgz"
    "version" "3.0.1"
  
  "lodash.once@^4.0.0":
    "integrity" "sha1-DdOXEhPHxW34gJd9UEyI+0cal6w="
    "resolved" "https://registry.npm.taobao.org/lodash.once/download/lodash.once-4.1.1.tgz"
    "version" "4.1.1"
  
  "lodash.union@^4.6.0":
    "integrity" "sha1-SLtQiECfFvGCFmZkHETdGqrjzYg="
    "resolved" "https://registry.npm.taobao.org/lodash.union/download/lodash.union-4.6.0.tgz"
    "version" "4.6.0"
  
  "lodash.uniq@^4.5.0":
    "integrity" "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M="
    "resolved" "https://registry.nlark.com/lodash.uniq/download/lodash.uniq-4.5.0.tgz"
    "version" "4.5.0"
  
  "lodash@^4.17.10", "lodash@^4.17.11", "lodash@^4.17.14", "lodash@^4.17.15", "lodash@^4.17.4", "lodash@^4.17.5":
    "integrity" "sha1-tEf2ZwoEVbv+7dETku/zMOoJdUg="
    "resolved" "https://registry.npm.taobao.org/lodash/download/lodash-4.17.15.tgz"
    "version" "4.17.15"
  
  "log-ok@^0.1.1":
    "integrity" "sha1-vqPdNqzQuKckDXhza1uXxlREozQ="
    "resolved" "https://registry.npm.taobao.org/log-ok/download/log-ok-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "ansi-green" "^0.1.1"
      "success-symbol" "^0.1.0"
  
  "log-utils@^0.2.1":
    "integrity" "sha1-pMIXoN2aUFFdm5ICBgkas9TgMc8="
    "resolved" "https://registry.npm.taobao.org/log-utils/download/log-utils-0.2.1.tgz"
    "version" "0.2.1"
    dependencies:
      "ansi-colors" "^0.2.0"
      "error-symbol" "^0.1.0"
      "info-symbol" "^0.1.0"
      "log-ok" "^0.1.1"
      "success-symbol" "^0.1.0"
      "time-stamp" "^1.0.1"
      "warning-symbol" "^0.1.0"
  
  "logform@^2.1.1":
    "integrity" "sha1-lXFV6+tnoTFkBpglzmfdtbst02A="
    "resolved" "https://registry.npm.taobao.org/logform/download/logform-2.1.2.tgz"
    "version" "2.1.2"
    dependencies:
      "colors" "^1.2.1"
      "fast-safe-stringify" "^2.0.4"
      "fecha" "^2.3.3"
      "ms" "^2.1.1"
      "triple-beam" "^1.3.0"
  
  "long-timeout@0.1.1":
    "integrity" "sha1-lyHXiLR+C8taJMLivuGg2lXatRQ="
    "resolved" "https://registry.npm.taobao.org/long-timeout/download/long-timeout-0.1.1.tgz"
    "version" "0.1.1"
  
  "lru-cache@^5.1.1":
    "integrity" "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA="
    "resolved" "https://registry.npm.taobao.org/lru-cache/download/lru-cache-5.1.1.tgz"
    "version" "5.1.1"
    dependencies:
      "yallist" "^3.0.2"
  
  "lru-cache@^6.0.0":
    "integrity" "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA=="
    "resolved" "https://registry.npmmirror.com/lru-cache/-/lru-cache-6.0.0.tgz"
    "version" "6.0.0"
    dependencies:
      "yallist" "^4.0.0"
  
  "lru-cache@4.1.x":
    "integrity" "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80="
    "resolved" "https://registry.npm.taobao.org/lru-cache/download/lru-cache-4.1.5.tgz"
    "version" "4.1.5"
    dependencies:
      "pseudomap" "^1.0.2"
      "yallist" "^2.1.2"
  
  "macroable@^1.0.0":
    "integrity" "sha1-I+Y0gyXG0GgqZQIfiL70CIve0WA="
    "resolved" "https://registry.npm.taobao.org/macroable/download/macroable-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "node-exceptions" "^2.0.1"
  
  "make-iterator@^1.0.0":
    "integrity" "sha1-KbM/MSqo9UfEpeSQ9Wr87JkTOtY="
    "resolved" "https://registry.npm.taobao.org/make-iterator/download/make-iterator-1.0.1.tgz"
    "version" "1.0.1"
    dependencies:
      "kind-of" "^6.0.2"
  
  "map-cache@^0.2.0", "map-cache@^0.2.2":
    "integrity" "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8="
    "resolved" "https://registry.npm.taobao.org/map-cache/download/map-cache-0.2.2.tgz"
    "version" "0.2.2"
  
  "map-visit@^1.0.0":
    "integrity" "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48="
    "resolved" "https://registry.npm.taobao.org/map-visit/download/map-visit-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "object-visit" "^1.0.0"
  
  "MD5@^1.2.1":
    "integrity" "sha1-PMJm8Oiau2tDpQ85pFnfW/3gskA="
    "resolved" "https://registry.npm.taobao.org/MD5/download/MD5-1.3.0.tgz"
    "version" "1.3.0"
    dependencies:
      "charenc" ">= 0.0.1"
      "crypt" ">= 0.0.1"
  
  "md5@^2.2.1":
    "version" "2.3.0"
    dependencies:
      "charenc" "0.0.2"
      "crypt" "0.0.2"
      "is-buffer" "~1.1.6"
  
  "media-typer@^0.3.0", "media-typer@0.3.0":
    "integrity" "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="
    "resolved" "https://registry.npm.taobao.org/media-typer/download/media-typer-0.3.0.tgz"
    "version" "0.3.0"
  
  "merge-descriptors@^1.0.1":
    "integrity" "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="
    "resolved" "https://registry.npm.taobao.org/merge-descriptors/download/merge-descriptors-1.0.1.tgz"
    "version" "1.0.1"
  
  "merge2@^1.2.3":
    "integrity" "sha1-WzZu6DsvFYLEj4fkfPGpNSEDyoE="
    "resolved" "https://registry.npm.taobao.org/merge2/download/merge2-1.3.0.tgz"
    "version" "1.3.0"
  
  "methods@^1.1.1", "methods@^1.1.2":
    "integrity" "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="
    "resolved" "https://registry.npm.taobao.org/methods/download/methods-1.1.2.tgz"
    "version" "1.1.2"
  
  "micromatch@^3.0.4", "micromatch@^3.1.10":
    "integrity" "sha1-cIWbyVyYQJUvNZoGij/En57PrCM="
    "resolved" "https://registry.npm.taobao.org/micromatch/download/micromatch-3.1.10.tgz"
    "version" "3.1.10"
    dependencies:
      "arr-diff" "^4.0.0"
      "array-unique" "^0.3.2"
      "braces" "^2.3.1"
      "define-property" "^2.0.2"
      "extend-shallow" "^3.0.2"
      "extglob" "^2.0.4"
      "fragment-cache" "^0.2.1"
      "kind-of" "^6.0.2"
      "nanomatch" "^1.2.9"
      "object.pick" "^1.3.0"
      "regex-not" "^1.0.0"
      "snapdragon" "^0.8.1"
      "to-regex" "^3.0.2"
  
  "mime-db@1.43.0":
    "integrity" "sha1-ChLgUCZQ5HPXNVNQUOfI9OtPrlg="
    "resolved" "https://registry.npm.taobao.org/mime-db/download/mime-db-1.43.0.tgz?cache=0&sync_timestamp=1578281173535&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-db%2Fdownload%2Fmime-db-1.43.0.tgz"
    "version" "1.43.0"
  
  "mime-types@^2.1.12", "mime-types@^2.1.17", "mime-types@~2.1.19", "mime-types@~2.1.24":
    "integrity" "sha1-nJIfwJt+FJpl39wNpNIJlyALCgY="
    "resolved" "https://registry.npm.taobao.org/mime-types/download/mime-types-2.1.26.tgz?cache=0&sync_timestamp=1578282559977&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-types%2Fdownload%2Fmime-types-2.1.26.tgz"
    "version" "2.1.26"
    dependencies:
      "mime-db" "1.43.0"
  
  "mime@^1.3.4", "mime@1.6.0":
    "integrity" "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE="
    "resolved" "https://registry.npm.taobao.org/mime/download/mime-1.6.0.tgz"
    "version" "1.6.0"
  
  "mime@^2.4.0":
    "integrity" "sha1-vXuRE1/GsBzePpuuM9ZZtj2IV+U="
    "resolved" "https://registry.npm.taobao.org/mime/download/mime-2.4.4.tgz"
    "version" "2.4.4"
  
  "mime@^2.4.6":
    "integrity" "sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg=="
    "resolved" "https://registry.npmmirror.com/mime/-/mime-2.6.0.tgz"
    "version" "2.6.0"
  
  "mime@1.4.1":
    "integrity" "sha1-Eh+evEnjdm8xGnbh+hyAA8SwOqY="
    "resolved" "https://registry.npm.taobao.org/mime/download/mime-1.4.1.tgz"
    "version" "1.4.1"
  
  "minimatch@^3.0.4":
    "integrity" "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM="
    "resolved" "https://registry.npm.taobao.org/minimatch/download/minimatch-3.0.4.tgz"
    "version" "3.0.4"
    dependencies:
      "brace-expansion" "^1.1.7"
  
  "minimist@^1.1.0", "minimist@1.2.0":
    "integrity" "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ="
    "resolved" "https://registry.npm.taobao.org/minimist/download/minimist-1.2.0.tgz"
    "version" "1.2.0"
  
  "minimist@^1.2.5":
    "integrity" "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI="
    "resolved" "https://registry.npm.taobao.org/minimist/download/minimist-1.2.5.tgz"
    "version" "1.2.5"
  
  "minimist@0.0.8":
    "integrity" "sha512-miQKw5Hv4NS1Psg2517mV4e4dYNaO3++hjAvLOAzKqZ61rH8NS1SK+vbfBWZ5PY/Me/bEWhUwqMghEW5Fb9T7Q=="
    "resolved" "https://registry.npmmirror.com/minimist/-/minimist-0.0.8.tgz"
    "version" "0.0.8"
  
  "mixin-deep@^1.2.0":
    "integrity" "sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY="
    "resolved" "https://registry.npm.taobao.org/mixin-deep/download/mixin-deep-1.3.2.tgz"
    "version" "1.3.2"
    dependencies:
      "for-in" "^1.0.2"
      "is-extendable" "^1.0.1"
  
  "mixin-object@^2.0.1":
    "integrity" "sha1-T7lJRB2rGCVA8f4DW6YOGUel5X4="
    "resolved" "https://registry.npm.taobao.org/mixin-object/download/mixin-object-2.0.1.tgz"
    "version" "2.0.1"
    dependencies:
      "for-in" "^0.1.3"
      "is-extendable" "^0.1.1"
  
  "mkdirp@^0.5.1":
    "integrity" "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM="
    "resolved" "https://registry.npm.taobao.org/mkdirp/download/mkdirp-0.5.1.tgz"
    "version" "0.5.1"
    dependencies:
      "minimist" "0.0.8"
  
  "mkdirp@>=0.5 0":
    "integrity" "sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8="
    "resolved" "https://registry.npm.taobao.org/mkdirp/download/mkdirp-0.5.5.tgz"
    "version" "0.5.5"
    dependencies:
      "minimist" "^1.2.5"
  
  "moment-timezone@^0.5.25":
    "integrity" "sha1-c63sgTm2/jBFLnjyEPJ7HzRriHc="
    "resolved" "https://registry.npm.taobao.org/moment-timezone/download/moment-timezone-0.5.27.tgz?cache=0&sync_timestamp=1571207551387&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmoment-timezone%2Fdownload%2Fmoment-timezone-0.5.27.tgz"
    "version" "0.5.27"
    dependencies:
      "moment" ">= 2.9.0"
  
  "moment@^2.22.2", "moment@^2.24.0", "moment@>= 2.9.0":
    "integrity" "sha1-DQVdU/UFKqZTyfbraLtdEr9cK1s="
    "resolved" "https://registry.npm.taobao.org/moment/download/moment-2.24.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmoment%2Fdownload%2Fmoment-2.24.0.tgz"
    "version" "2.24.0"
  
  "ms@^2.0.0", "ms@^2.1.1":
    "integrity" "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="
    "resolved" "https://registry.npm.taobao.org/ms/download/ms-2.1.2.tgz"
    "version" "2.1.2"
  
  "ms@^2.1.3":
    "integrity" "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="
    "resolved" "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz"
    "version" "2.1.3"
  
  "ms@2.0.0":
    "integrity" "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="
    "resolved" "https://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz"
    "version" "2.0.0"
  
  "ms@2.1.1":
    "integrity" "sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo="
    "resolved" "https://registry.npm.taobao.org/ms/download/ms-2.1.1.tgz"
    "version" "2.1.1"
  
  "multiparty@^4.2.1":
    "integrity" "sha1-2bbEbYuN6rHucMc0sK93HdRuCxM="
    "resolved" "https://registry.npm.taobao.org/multiparty/download/multiparty-4.2.1.tgz"
    "version" "4.2.1"
    dependencies:
      "fd-slicer" "1.1.0"
      "http-errors" "~1.7.0"
      "safe-buffer" "5.1.2"
      "uid-safe" "2.1.5"
  
  "mustache@^3.0.0":
    "integrity" "sha1-ieeKnSB9ePJ5mx6Vdkolv3GigyI="
    "resolved" "https://registry.npm.taobao.org/mustache/download/mustache-3.2.1.tgz"
    "version" "3.2.1"
  
  "mute-stream@0.0.7":
    "integrity" "sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s="
    "resolved" "https://registry.npm.taobao.org/mute-stream/download/mute-stream-0.0.7.tgz"
    "version" "0.0.7"
  
  "mysql@^2.16.0":
    "integrity" "sha1-IlQUOFXFqMc4JeRSK68uoCF2Zxc="
    "resolved" "https://registry.npm.taobao.org/mysql/download/mysql-2.18.1.tgz"
    "version" "2.18.1"
    dependencies:
      "bignumber.js" "9.0.0"
      "readable-stream" "2.3.7"
      "safe-buffer" "5.1.2"
      "sqlstring" "2.3.1"
  
  "mz-modules@^2.1.0":
    "integrity" "sha1-f1KYd6/Q1C9AmnRjuWmG1hz7z5Y="
    "resolved" "https://registry.npm.taobao.org/mz-modules/download/mz-modules-2.1.0.tgz"
    "version" "2.1.0"
    dependencies:
      "glob" "^7.1.2"
      "ko-sleep" "^1.0.3"
      "mkdirp" "^0.5.1"
      "pump" "^3.0.0"
      "rimraf" "^2.6.1"
  
  "mz@^2.7.0":
    "integrity" "sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI="
    "resolved" "https://registry.npm.taobao.org/mz/download/mz-2.7.0.tgz"
    "version" "2.7.0"
    dependencies:
      "any-promise" "^1.0.0"
      "object-assign" "^4.0.1"
      "thenify-all" "^1.0.0"
  
  "nanoid@^2.1.0":
    "integrity" "sha1-7CS4p1jVkVYVMbQXagHjq08PAoA="
    "resolved" "https://registry.npm.taobao.org/nanoid/download/nanoid-2.1.11.tgz"
    "version" "2.1.11"
  
  "nanomatch@^1.2.9":
    "integrity" "sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk="
    "resolved" "https://registry.npm.taobao.org/nanomatch/download/nanomatch-1.2.13.tgz"
    "version" "1.2.13"
    dependencies:
      "arr-diff" "^4.0.0"
      "array-unique" "^0.3.2"
      "define-property" "^2.0.2"
      "extend-shallow" "^3.0.2"
      "fragment-cache" "^0.2.1"
      "is-windows" "^1.0.2"
      "kind-of" "^6.0.2"
      "object.pick" "^1.3.0"
      "regex-not" "^1.0.0"
      "snapdragon" "^0.8.1"
      "to-regex" "^3.0.1"
  
  "negotiator@0.6.2":
    "integrity" "sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs="
    "resolved" "https://registry.npm.taobao.org/negotiator/download/negotiator-0.6.2.tgz"
    "version" "0.6.2"
  
  "netmask@^1.0.6":
    "integrity" "sha1-ICl+idhvb2QA8lDZ9Pa0wZRfzTU="
    "resolved" "https://registry.npm.taobao.org/netmask/download/netmask-1.0.6.tgz"
    "version" "1.0.6"
  
  "netmask@^2.0.1":
    "integrity" "sha1-iwGgdkQGXVNjg4NYI7xSAE66xec="
    "resolved" "https://registry.npm.taobao.org/netmask/download/netmask-2.0.2.tgz?cache=0&sync_timestamp=1617387623646&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnetmask%2Fdownload%2Fnetmask-2.0.2.tgz"
    "version" "2.0.2"
  
  "nice-try@^1.0.4":
    "integrity" "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y="
    "resolved" "https://registry.npm.taobao.org/nice-try/download/nice-try-1.0.5.tgz"
    "version" "1.0.5"
  
  "node-cookie@^2.1.1":
    "integrity" "sha1-ZQFC15+HFZt9AV4ZFoH1pqQd3T4="
    "resolved" "https://registry.npm.taobao.org/node-cookie/download/node-cookie-2.1.1.tgz"
    "version" "2.1.1"
    dependencies:
      "cookie" "^0.3.1"
      "cookie-signature" "^1.1.0"
      "simple-encryptor" "^1.4.0"
  
  "node-exceptions@^2.0.1":
    "integrity" "sha1-JhK0WSm3dONmlVf4UrR1B3Zaz+c="
    "resolved" "https://registry.npm.taobao.org/node-exceptions/download/node-exceptions-2.0.2.tgz"
    "version" "2.0.2"
  
  "node-exceptions@^3.0.0":
    "integrity" "sha1-SLjhDacTcIRe8a9YN3gTlScwjRE="
    "resolved" "https://registry.npm.taobao.org/node-exceptions/download/node-exceptions-3.0.0.tgz"
    "version" "3.0.0"
  
  "node-fecth@^0.0.1-security":
    "integrity" "sha512-7YRxL4a1aV2Up7f7pxaQU70wNXpA2X5IRyiMRB0OreG2jhLH3C4olv6t5sEExEX4kOeq4181wwFegDmNAlfQPg=="
    "resolved" "https://registry.npmmirror.com/node-fecth/-/node-fecth-0.0.1-security.tgz"
    "version" "0.0.1-security"
  
  "node-req@^2.1.1":
    "integrity" "sha1-MttmCFXbA7RW3jcO4NU+zWIgKH4="
    "resolved" "https://registry.npm.taobao.org/node-req/download/node-req-2.1.2.tgz"
    "version" "2.1.2"
    dependencies:
      "accepts" "^1.3.5"
      "fresh" "^0.5.2"
      "parseurl" "^1.3.2"
      "proxy-addr" "^2.0.4"
      "qs" "^6.5.2"
      "type-is" "^1.6.16"
  
  "node-res@4.1.4":
    "integrity" "sha1-gPGMtsscDHMNUtjylZSzH9A7ErA="
    "resolved" "https://registry.npm.taobao.org/node-res/download/node-res-4.1.4.tgz"
    "version" "4.1.4"
    dependencies:
      "content-disposition" "^0.5.2"
      "etag" "^1.8.1"
      "mime-types" "^2.1.17"
      "send" "^0.16.1"
      "vary" "^1.1.2"
  
  "node-schedule@^1.3.2":
    "integrity" "sha1-13Szg+Km9q3lnuzGIlSuoHzXWMs="
    "resolved" "https://registry.npm.taobao.org/node-schedule/download/node-schedule-1.3.2.tgz"
    "version" "1.3.2"
    dependencies:
      "cron-parser" "^2.7.3"
      "long-timeout" "0.1.1"
      "sorted-array-functions" "^1.0.0"
  
  "node-xlsx@^0.15.0":
    "integrity" "sha1-HxsNetzlxwboa/2WpaoABb+KncM="
    "resolved" "https://registry.npm.taobao.org/node-xlsx/download/node-xlsx-0.15.0.tgz"
    "version" "0.15.0"
    dependencies:
      "buffer-from" "^1.1.0"
      "xlsx" "^0.14.1"
  
  "normalize-path@^3.0.0":
    "integrity" "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU="
    "resolved" "https://registry.nlark.com/normalize-path/download/normalize-path-3.0.0.tgz"
    "version" "3.0.0"
  
  "oauth-sign@~0.9.0":
    "integrity" "sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU="
    "resolved" "https://registry.npm.taobao.org/oauth-sign/download/oauth-sign-0.9.0.tgz"
    "version" "0.9.0"
  
  "object-assign@^4.0.1":
    "integrity" "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="
    "resolved" "https://registry.npm.taobao.org/object-assign/download/object-assign-4.1.1.tgz"
    "version" "4.1.1"
  
  "object-copy@^0.1.0":
    "integrity" "sha1-fn2Fi3gb18mRpBupde04EnVOmYw="
    "resolved" "https://registry.npm.taobao.org/object-copy/download/object-copy-0.1.0.tgz"
    "version" "0.1.0"
    dependencies:
      "copy-descriptor" "^0.1.0"
      "define-property" "^0.2.5"
      "kind-of" "^3.0.3"
  
  "object-inspect@^1.9.0":
    "integrity" "sha512-z+cPxW0QGUp0mcqcsgQyLVRDoXFQbXOwBaqyF7VIgI4TWNQsDHrBpUQslRmIfAoYWdYzs6UlKJtB2XJpTaNSpQ=="
    "resolved" "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.12.2.tgz"
    "version" "1.12.2"
  
  "object-keys@^1.0.12":
    "integrity" "sha1-HEfyct8nfzsdrwYWd9nILiMixg4="
    "resolved" "https://registry.npm.taobao.org/object-keys/download/object-keys-1.1.1.tgz"
    "version" "1.1.1"
  
  "object-visit@^1.0.0":
    "integrity" "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs="
    "resolved" "https://registry.npm.taobao.org/object-visit/download/object-visit-1.0.1.tgz"
    "version" "1.0.1"
    dependencies:
      "isobject" "^3.0.0"
  
  "object.defaults@^1.1.0":
    "integrity" "sha1-On+GgzS0B96gbaFtiNXNKeQ1/s8="
    "resolved" "https://registry.npm.taobao.org/object.defaults/download/object.defaults-1.1.0.tgz"
    "version" "1.1.0"
    dependencies:
      "array-each" "^1.0.1"
      "array-slice" "^1.0.0"
      "for-own" "^1.0.0"
      "isobject" "^3.0.0"
  
  "object.map@^1.0.0":
    "integrity" "sha1-z4Plncj8wK1fQlDh94s7gb2AHTc="
    "resolved" "https://registry.npm.taobao.org/object.map/download/object.map-1.0.1.tgz"
    "version" "1.0.1"
    dependencies:
      "for-own" "^1.0.0"
      "make-iterator" "^1.0.0"
  
  "object.pick@^1.2.0", "object.pick@^1.3.0":
    "integrity" "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c="
    "resolved" "https://registry.npm.taobao.org/object.pick/download/object.pick-1.3.0.tgz"
    "version" "1.3.0"
    dependencies:
      "isobject" "^3.0.1"
  
  "on-finished@~2.3.0":
    "integrity" "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc="
    "resolved" "https://registry.npm.taobao.org/on-finished/download/on-finished-2.3.0.tgz"
    "version" "2.3.0"
    dependencies:
      "ee-first" "1.1.1"
  
  "once@^1.3.0", "once@^1.3.1", "once@^1.4.0":
    "integrity" "sha1-WDsap3WWHUsROsF9nFC6753Xa9E="
    "resolved" "https://registry.npm.taobao.org/once/download/once-1.4.0.tgz"
    "version" "1.4.0"
    dependencies:
      "wrappy" "1"
  
  "one-time@0.0.4":
    "integrity" "sha1-+M33eISCb+Tf+T46nMN7HkSAdC4="
    "resolved" "https://registry.npm.taobao.org/one-time/download/one-time-0.0.4.tgz"
    "version" "0.0.4"
  
  "optionator@^0.8.1":
    "integrity" "sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU="
    "resolved" "https://registry.npm.taobao.org/optionator/download/optionator-0.8.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Foptionator%2Fdownload%2Foptionator-0.8.3.tgz"
    "version" "0.8.3"
    dependencies:
      "deep-is" "~0.1.3"
      "fast-levenshtein" "~2.0.6"
      "levn" "~0.3.0"
      "prelude-ls" "~1.1.2"
      "type-check" "~0.3.2"
      "word-wrap" "~1.2.3"
  
  "os-homedir@^1.0.0":
    "integrity" "sha1-/7xJiDNuDoM94MFox+8VISGqf7M="
    "resolved" "https://registry.npm.taobao.org/os-homedir/download/os-homedir-1.0.2.tgz"
    "version" "1.0.2"
  
  "os-name@~1.0.3":
    "integrity" "sha1-GzefZINa98Wn9JizV8uVIVwVnt8="
    "resolved" "https://registry.npm.taobao.org/os-name/download/os-name-1.0.3.tgz"
    "version" "1.0.3"
    dependencies:
      "osx-release" "^1.0.0"
      "win-release" "^1.0.0"
  
  "os-tmpdir@~1.0.2":
    "integrity" "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="
    "resolved" "https://registry.npm.taobao.org/os-tmpdir/download/os-tmpdir-1.0.2.tgz"
    "version" "1.0.2"
  
  "osx-release@^1.0.0":
    "integrity" "sha1-8heRGigTaUmvG/kwiyQeJzfTzWw="
    "resolved" "https://registry.npm.taobao.org/osx-release/download/osx-release-1.1.0.tgz"
    "version" "1.1.0"
    dependencies:
      "minimist" "^1.1.0"
  
  "p-limit@^2.0.0":
    "integrity" "sha1-YSebZ3IfUoeqHBOpp/u8SMkpGx4="
    "resolved" "https://registry.npm.taobao.org/p-limit/download/p-limit-2.2.2.tgz?cache=0&sync_timestamp=1577904218145&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-limit%2Fdownload%2Fp-limit-2.2.2.tgz"
    "version" "2.2.2"
    dependencies:
      "p-try" "^2.0.0"
  
  "p-locate@^3.0.0":
    "integrity" "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ="
    "resolved" "https://registry.npm.taobao.org/p-locate/download/p-locate-3.0.0.tgz"
    "version" "3.0.0"
    dependencies:
      "p-limit" "^2.0.0"
  
  "p-reduce@^1.0.0":
    "integrity" "sha1-GMKw3ZNqRpClKfgjH1ig/bakffo="
    "resolved" "https://registry.npm.taobao.org/p-reduce/download/p-reduce-1.0.0.tgz"
    "version" "1.0.0"
  
  "p-series@^1.1.0":
    "integrity" "sha1-8thSLN/Vi0ZOuWhWUdRlA37jyVc="
    "resolved" "https://registry.npm.taobao.org/p-series/download/p-series-1.1.0.tgz"
    "version" "1.1.0"
    dependencies:
      "@sindresorhus/is" "^0.7.0"
      "p-reduce" "^1.0.0"
  
  "p-try@^2.0.0":
    "integrity" "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY="
    "resolved" "https://registry.npm.taobao.org/p-try/download/p-try-2.2.0.tgz"
    "version" "2.2.0"
  
  "pac-proxy-agent@^3.0.1":
    "integrity" "sha1-EVseWPkldsrC66cYWTynsON94q0="
    "resolved" "https://registry.npm.taobao.org/pac-proxy-agent/download/pac-proxy-agent-3.0.1.tgz"
    "version" "3.0.1"
    dependencies:
      "agent-base" "^4.2.0"
      "debug" "^4.1.1"
      "get-uri" "^2.0.0"
      "http-proxy-agent" "^2.1.0"
      "https-proxy-agent" "^3.0.0"
      "pac-resolver" "^3.0.0"
      "raw-body" "^2.2.0"
      "socks-proxy-agent" "^4.0.1"
  
  "pac-proxy-agent@^4.1.0":
    "integrity" "sha1-Zog+6rrckV/F6VRXMkyw8Kx43vs="
    "resolved" "https://registry.npm.taobao.org/pac-proxy-agent/download/pac-proxy-agent-4.1.0.tgz?cache=0&sync_timestamp=1581983404701&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpac-proxy-agent%2Fdownload%2Fpac-proxy-agent-4.1.0.tgz"
    "version" "4.1.0"
    dependencies:
      "@tootallnate/once" "1"
      "agent-base" "6"
      "debug" "4"
      "get-uri" "3"
      "http-proxy-agent" "^4.0.1"
      "https-proxy-agent" "5"
      "pac-resolver" "^4.1.0"
      "raw-body" "^2.2.0"
      "socks-proxy-agent" "5"
  
  "pac-resolver@^3.0.0":
    "integrity" "sha1-auoweH2wqJFwTet4AKcip2FabyY="
    "resolved" "https://registry.npm.taobao.org/pac-resolver/download/pac-resolver-3.0.0.tgz"
    "version" "3.0.0"
    dependencies:
      "co" "^4.6.0"
      "degenerator" "^1.0.4"
      "ip" "^1.1.5"
      "netmask" "^1.0.6"
      "thunkify" "^2.1.2"
  
  "pac-resolver@^4.1.0":
    "integrity" "sha1-uCvLmZLUgWaSC8g8dUKrtFS9m90="
    "resolved" "https://registry.npm.taobao.org/pac-resolver/download/pac-resolver-4.2.0.tgz"
    "version" "4.2.0"
    dependencies:
      "degenerator" "^2.2.0"
      "ip" "^1.1.5"
      "netmask" "^2.0.1"
  
  "pako@~1.0.2":
    "integrity" "sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8="
    "resolved" "https://registry.npm.taobao.org/pako/download/pako-1.0.11.tgz?cache=0&sync_timestamp=1610208910632&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpako%2Fdownload%2Fpako-1.0.11.tgz"
    "version" "1.0.11"
  
  "parse-filepath@^1.0.1":
    "integrity" "sha1-pjISf1Oq89FYdvWHLz/6x2PWyJE="
    "resolved" "https://registry.npm.taobao.org/parse-filepath/download/parse-filepath-1.0.2.tgz"
    "version" "1.0.2"
    dependencies:
      "is-absolute" "^1.0.0"
      "map-cache" "^0.2.0"
      "path-root" "^0.1.1"
  
  "parse-passwd@^1.0.0":
    "integrity" "sha1-bVuTSkVpk7I9N/QKOC1vFmao5cY="
    "resolved" "https://registry.npm.taobao.org/parse-passwd/download/parse-passwd-1.0.0.tgz"
    "version" "1.0.0"
  
  "parseurl@^1.3.2", "parseurl@~1.3.3":
    "integrity" "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="
    "resolved" "https://registry.npm.taobao.org/parseurl/download/parseurl-1.3.3.tgz"
    "version" "1.3.3"
  
  "pascalcase@^0.1.1":
    "integrity" "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ="
    "resolved" "https://registry.npm.taobao.org/pascalcase/download/pascalcase-0.1.1.tgz"
    "version" "0.1.1"
  
  "path-dirname@^1.0.0":
    "integrity" "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA="
    "resolved" "https://registry.npm.taobao.org/path-dirname/download/path-dirname-1.0.2.tgz"
    "version" "1.0.2"
  
  "path-exists@^3.0.0":
    "integrity" "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU="
    "resolved" "https://registry.npm.taobao.org/path-exists/download/path-exists-3.0.0.tgz"
    "version" "3.0.0"
  
  "path-is-absolute@^1.0.0":
    "integrity" "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="
    "resolved" "https://registry.npm.taobao.org/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
    "version" "1.0.1"
  
  "path-key@^2.0.1":
    "integrity" "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="
    "resolved" "https://registry.npm.taobao.org/path-key/download/path-key-2.0.1.tgz?cache=0&sync_timestamp=1574441404712&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-key%2Fdownload%2Fpath-key-2.0.1.tgz"
    "version" "2.0.1"
  
  "path-parse@^1.0.6":
    "integrity" "sha1-1i27VnlAXXLEc37FhgDp3c8G0kw="
    "resolved" "https://registry.npm.taobao.org/path-parse/download/path-parse-1.0.6.tgz"
    "version" "1.0.6"
  
  "path-root-regex@^0.1.0":
    "integrity" "sha1-v8zcjfWxLcUsi0PsONGNcsBLqW0="
    "resolved" "https://registry.npm.taobao.org/path-root-regex/download/path-root-regex-0.1.2.tgz"
    "version" "0.1.2"
  
  "path-root@^0.1.1":
    "integrity" "sha1-mkpoFMrBwM1zNgqV8yCDyOpHRbc="
    "resolved" "https://registry.npm.taobao.org/path-root/download/path-root-0.1.1.tgz"
    "version" "0.1.1"
    dependencies:
      "path-root-regex" "^0.1.0"
  
  "path-to-regexp@^2.4.0":
    "integrity" "sha1-Nc5/Mz1WFvHB4b/iZsOrouWy5wQ="
    "resolved" "https://registry.npm.taobao.org/path-to-regexp/download/path-to-regexp-2.4.0.tgz"
    "version" "2.4.0"
  
  "path-type@^3.0.0":
    "integrity" "sha1-zvMdyOCho7sNEFwM2Xzzv0f0428="
    "resolved" "https://registry.npm.taobao.org/path-type/download/path-type-3.0.0.tgz"
    "version" "3.0.0"
    dependencies:
      "pify" "^3.0.0"
  
  "pathval@^1.1.0":
    "integrity" "sha1-uULm1L3mUwBe9rcTYd74cn0GReA="
    "resolved" "https://registry.npm.taobao.org/pathval/download/pathval-1.1.0.tgz"
    "version" "1.1.0"
  
  "pause-stream@~0.0.11":
    "integrity" "sha1-/lo0sMvOErWqaitAPuLnO2AvFEU="
    "resolved" "https://registry.npm.taobao.org/pause-stream/download/pause-stream-0.0.11.tgz"
    "version" "0.0.11"
    dependencies:
      "through" "~2.3"
  
  "pend@~1.2.0":
    "integrity" "sha1-elfrVQpng/kRUzH89GY9XI4AelA="
    "resolved" "https://registry.npm.taobao.org/pend/download/pend-1.2.0.tgz"
    "version" "1.2.0"
  
  "performance-now@^2.1.0":
    "integrity" "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns="
    "resolved" "https://registry.npm.taobao.org/performance-now/download/performance-now-2.1.0.tgz"
    "version" "2.1.0"
  
  "pg-connection-string@2.0.0":
    "integrity" "sha1-Pu/lmX4G2Ugh5NUC5CtqHHP434I="
    "resolved" "https://registry.npm.taobao.org/pg-connection-string/download/pg-connection-string-2.0.0.tgz"
    "version" "2.0.0"
  
  "pify@^3.0.0":
    "integrity" "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="
    "resolved" "https://registry.npm.taobao.org/pify/download/pify-3.0.0.tgz"
    "version" "3.0.0"
  
  "pify@^4.0.0":
    "integrity" "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g=="
    "resolved" "https://registry.npmmirror.com/pify/-/pify-4.0.1.tgz"
    "version" "4.0.1"
  
  "platform@^1.3.1":
    "integrity" "sha1-+2lYxpbgfikY0u7aDwvJRI1zNEQ="
    "resolved" "https://registry.npm.taobao.org/platform/download/platform-1.3.5.tgz"
    "version" "1.3.5"
  
  "pluralize@^7.0.0":
    "integrity" "sha1-KYuJ34uTsCIdv0Ia0rGx6iP8Z3c="
    "resolved" "https://registry.npm.taobao.org/pluralize/download/pluralize-7.0.0.tgz"
    "version" "7.0.0"
  
  "pngjs@^3.3.0":
    "integrity" "sha1-mcp9clll+2VYFOr2XzjxK72/VV8="
    "resolved" "https://registry.npm.taobao.org/pngjs/download/pngjs-3.4.0.tgz"
    "version" "3.4.0"
  
  "pointer-symbol@^1.0.0":
    "integrity" "sha1-YPkRAgTqepKbYmRKITFVQ8uz1Ec="
    "resolved" "https://registry.npm.taobao.org/pointer-symbol/download/pointer-symbol-1.0.0.tgz"
    "version" "1.0.0"
  
  "posix-character-classes@^0.1.0":
    "integrity" "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs="
    "resolved" "https://registry.npm.taobao.org/posix-character-classes/download/posix-character-classes-0.1.1.tgz"
    "version" "0.1.1"
  
  "prelude-ls@~1.1.2":
    "integrity" "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ="
    "resolved" "https://registry.npm.taobao.org/prelude-ls/download/prelude-ls-1.1.2.tgz"
    "version" "1.1.2"
  
  "pretty-hrtime@^1.0.3":
    "integrity" "sha1-t+PqQkNaTJsnWdmeDyAesZWALuE="
    "resolved" "https://registry.npm.taobao.org/pretty-hrtime/download/pretty-hrtime-1.0.3.tgz"
    "version" "1.0.3"
  
  "printj@~1.1.0":
    "integrity" "sha1-2Q3rKXWoufYA+zoclOP0xTx4oiI="
    "resolved" "https://registry.npm.taobao.org/printj/download/printj-1.1.2.tgz"
    "version" "1.1.2"
  
  "process-nextick-args@~2.0.0":
    "integrity" "sha1-eCDZsWEgzFXKmud5JoCufbptf+I="
    "resolved" "https://registry.npm.taobao.org/process-nextick-args/download/process-nextick-args-2.0.1.tgz"
    "version" "2.0.1"
  
  "promise-reduce@^2.1.0":
    "integrity" "sha1-dxmHMbXGLL1fjhhFUREx0A47bEc="
    "resolved" "https://registry.npm.taobao.org/promise-reduce/download/promise-reduce-2.1.0.tgz"
    "version" "2.1.0"
    dependencies:
      "any-promise" "^0.1.0"
  
  "prompt-actions@^3.0.2":
    "integrity" "sha1-U37uUiQclAN581Sgbq6FKORM7ro="
    "resolved" "https://registry.npm.taobao.org/prompt-actions/download/prompt-actions-3.0.2.tgz"
    "version" "3.0.2"
    dependencies:
      "debug" "^2.6.8"
  
  "prompt-base@^4.0.1", "prompt-base@^4.0.2":
    "integrity" "sha1-e4jkwBsJbIPS9OUBp+hfDTaezR8="
    "resolved" "https://registry.npm.taobao.org/prompt-base/download/prompt-base-4.1.0.tgz"
    "version" "4.1.0"
    dependencies:
      "component-emitter" "^1.2.1"
      "debug" "^3.0.1"
      "koalas" "^1.0.2"
      "log-utils" "^0.2.1"
      "prompt-actions" "^3.0.2"
      "prompt-question" "^5.0.1"
      "readline-ui" "^2.2.3"
      "readline-utils" "^2.2.3"
      "static-extend" "^0.1.2"
  
  "prompt-checkbox@^2.2.0":
    "integrity" "sha1-obCGusfjQio/HDT2bW2lJIZHJpA="
    "resolved" "https://registry.npm.taobao.org/prompt-checkbox/download/prompt-checkbox-2.2.0.tgz"
    "version" "2.2.0"
    dependencies:
      "ansi-cyan" "^0.1.1"
      "debug" "^2.6.8"
      "prompt-base" "^4.0.2"
  
  "prompt-choices@^3.0.3":
    "integrity" "sha1-XDXD2Z7hft/ZVjwfuBBGsISe+P8="
    "resolved" "https://registry.npm.taobao.org/prompt-choices/download/prompt-choices-3.0.6.tgz"
    "version" "3.0.6"
    dependencies:
      "arr-flatten" "^1.0.3"
      "choices-separator" "^2.0.0"
      "clone-deep" "^0.3.0"
      "collection-visit" "^1.0.0"
      "debug" "^2.6.8"
      "define-property" "^1.0.0"
      "extend-shallow" "^2.0.1"
      "is-number" "^3.0.0"
      "kind-of" "^4.0.0"
      "lazy-cache" "^2.0.2"
      "log-utils" "^0.2.1"
      "pointer-symbol" "^1.0.0"
      "radio-symbol" "^2.0.0"
      "set-value" "^1.0.0"
      "strip-color" "^0.1.0"
      "terminal-paginator" "^2.0.0"
      "toggle-array" "^1.0.1"
  
  "prompt-choices@^4.0.5":
    "integrity" "sha1-YJQgLE5V0HYuScHlNzVyflP9SE8="
    "resolved" "https://registry.npm.taobao.org/prompt-choices/download/prompt-choices-4.1.0.tgz"
    "version" "4.1.0"
    dependencies:
      "arr-flatten" "^1.1.0"
      "arr-swap" "^1.0.1"
      "choices-separator" "^2.0.0"
      "clone-deep" "^4.0.0"
      "collection-visit" "^1.0.0"
      "define-property" "^2.0.2"
      "is-number" "^6.0.0"
      "kind-of" "^6.0.2"
      "koalas" "^1.0.2"
      "log-utils" "^0.2.1"
      "pointer-symbol" "^1.0.0"
      "radio-symbol" "^2.0.0"
      "set-value" "^3.0.0"
      "strip-color" "^0.1.0"
      "terminal-paginator" "^2.0.2"
      "toggle-array" "^1.0.1"
  
  "prompt-confirm@^2.0.4":
    "integrity" "sha1-QsBpBzM+h28q6IZygeC5UhpHlso="
    "resolved" "https://registry.npm.taobao.org/prompt-confirm/download/prompt-confirm-2.0.4.tgz"
    "version" "2.0.4"
    dependencies:
      "ansi-cyan" "^0.1.1"
      "prompt-base" "^4.0.1"
  
  "prompt-expand@^1.0.1":
    "integrity" "sha1-9pFiVRR+6iib0F5VZcYdQTP/ei4="
    "resolved" "https://registry.npm.taobao.org/prompt-expand/download/prompt-expand-1.0.1.tgz"
    "version" "1.0.1"
    dependencies:
      "ansi-colors" "^1.1.0"
      "prompt-rawlist" "^2.0.1"
  
  "prompt-input@^3.0.0":
    "integrity" "sha1-9QnkRxMiP4UmjnBYXdrl32jXkCc="
    "resolved" "https://registry.npm.taobao.org/prompt-input/download/prompt-input-3.0.0.tgz"
    "version" "3.0.0"
    dependencies:
      "debug" "^2.6.8"
      "prompt-base" "^4.0.2"
  
  "prompt-list@^2.0.1":
    "integrity" "sha1-LUOKuWGT96Nh+/WoQtHUoVK/ZdI="
    "resolved" "https://registry.npm.taobao.org/prompt-list/download/prompt-list-2.2.0.tgz"
    "version" "2.2.0"
    dependencies:
      "ansi-cyan" "^0.1.1"
      "ansi-dim" "^0.1.1"
      "debug" "^3.0.1"
      "prompt-radio" "^1.2.1"
  
  "prompt-list@^3.2.0":
    "integrity" "sha1-szRy4BZ3pXUfWQlPp/4gsJ2p25Q="
    "resolved" "https://registry.npm.taobao.org/prompt-list/download/prompt-list-3.2.0.tgz"
    "version" "3.2.0"
    dependencies:
      "ansi-cyan" "^0.1.1"
      "ansi-dim" "^0.1.1"
      "prompt-radio" "^1.2.1"
  
  "prompt-password@^1.2.0":
    "integrity" "sha1-h9ukr9XDSVTlEcLAafuSLatgtxg="
    "resolved" "https://registry.npm.taobao.org/prompt-password/download/prompt-password-1.2.0.tgz"
    "version" "1.2.0"
    dependencies:
      "debug" "^2.6.8"
      "prompt-base" "^4.0.2"
  
  "prompt-question@^3.0.3":
    "integrity" "sha1-xVhYvXaxh4wequy5F6koqcE8ijc="
    "resolved" "https://registry.npm.taobao.org/prompt-question/download/prompt-question-3.0.3.tgz"
    "version" "3.0.3"
    dependencies:
      "clone-deep" "^0.3.0"
      "debug" "^2.6.8"
      "define-property" "^1.0.0"
      "extend-shallow" "^2.0.1"
      "kind-of" "^4.0.0"
      "koalas" "^1.0.2"
      "prompt-choices" "^3.0.3"
  
  "prompt-question@^5.0.1":
    "integrity" "sha1-gaR5848Lr+zHWOXW97xYblmWELM="
    "resolved" "https://registry.npm.taobao.org/prompt-question/download/prompt-question-5.0.2.tgz"
    "version" "5.0.2"
    dependencies:
      "clone-deep" "^1.0.0"
      "debug" "^3.0.1"
      "define-property" "^1.0.0"
      "isobject" "^3.0.1"
      "kind-of" "^5.0.2"
      "koalas" "^1.0.2"
      "prompt-choices" "^4.0.5"
  
  "prompt-radio@^1.2.1":
    "integrity" "sha1-ZqTLKxidM3Pn+d27wQcAGGyJWf8="
    "resolved" "https://registry.npm.taobao.org/prompt-radio/download/prompt-radio-1.2.1.tgz"
    "version" "1.2.1"
    dependencies:
      "debug" "^2.6.8"
      "prompt-checkbox" "^2.2.0"
  
  "prompt-rawlist@^2.0.1":
    "integrity" "sha1-3TxgYq42P8hQHiacAzz3y9bVa9c="
    "resolved" "https://registry.npm.taobao.org/prompt-rawlist/download/prompt-rawlist-2.0.1.tgz"
    "version" "2.0.1"
    dependencies:
      "ansi-cyan" "^0.1.1"
      "ansi-dim" "^0.1.1"
      "ansi-red" "^0.1.1"
      "is-number" "^3.0.0"
      "prompt-list" "^2.0.1"
  
  "proxy-addr@^2.0.4":
    "integrity" "sha1-NMvWSi2B9LH9IedvnwbIpFKZ7jQ="
    "resolved" "https://registry.npm.taobao.org/proxy-addr/download/proxy-addr-2.0.5.tgz"
    "version" "2.0.5"
    dependencies:
      "forwarded" "~0.1.2"
      "ipaddr.js" "1.9.0"
  
  "proxy-agent@^3.1.0":
    "integrity" "sha1-fgTga/Nq+mJKFUC+JHtHyXC9MBQ="
    "resolved" "https://registry.npm.taobao.org/proxy-agent/download/proxy-agent-3.1.1.tgz"
    "version" "3.1.1"
    dependencies:
      "agent-base" "^4.2.0"
      "debug" "4"
      "http-proxy-agent" "^2.1.0"
      "https-proxy-agent" "^3.0.0"
      "lru-cache" "^5.1.1"
      "pac-proxy-agent" "^3.0.1"
      "proxy-from-env" "^1.0.0"
      "socks-proxy-agent" "^4.0.1"
  
  "proxy-agent@^4.0.1":
    "integrity" "sha1-MmwyUHdscETNGWVcy/rfLgZaBFw="
    "resolved" "https://registry.npm.taobao.org/proxy-agent/download/proxy-agent-4.0.1.tgz"
    "version" "4.0.1"
    dependencies:
      "agent-base" "^6.0.0"
      "debug" "4"
      "http-proxy-agent" "^4.0.0"
      "https-proxy-agent" "^5.0.0"
      "lru-cache" "^5.1.1"
      "pac-proxy-agent" "^4.1.0"
      "proxy-from-env" "^1.0.0"
      "socks-proxy-agent" "^5.0.0"
  
  "proxy-from-env@^1.0.0":
    "integrity" "sha1-M8UDmPcOp+uW0h97gXYwpVeRx+4="
    "resolved" "https://registry.npm.taobao.org/proxy-from-env/download/proxy-from-env-1.0.0.tgz"
    "version" "1.0.0"
  
  "pseudomap@^1.0.2":
    "integrity" "sha1-8FKijacOYYkX7wqKw0wa5aaChrM="
    "resolved" "https://registry.npm.taobao.org/pseudomap/download/pseudomap-1.0.2.tgz"
    "version" "1.0.2"
  
  "psl@^1.1.24":
    "integrity" "sha1-8cTEeo75cWfepda79IFtc26ISjw="
    "resolved" "https://registry.npm.taobao.org/psl/download/psl-1.7.0.tgz?cache=0&sync_timestamp=1577538558975&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpsl%2Fdownload%2Fpsl-1.7.0.tgz"
    "version" "1.7.0"
  
  "pump@^3.0.0":
    "integrity" "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ="
    "resolved" "https://registry.npm.taobao.org/pump/download/pump-3.0.0.tgz"
    "version" "3.0.0"
    dependencies:
      "end-of-stream" "^1.1.0"
      "once" "^1.3.1"
  
  "punycode@^1.4.1":
    "integrity" "sha1-wNWmOycYgArY4esPpSachN1BhF4="
    "resolved" "https://registry.npm.taobao.org/punycode/download/punycode-1.4.1.tgz"
    "version" "1.4.1"
  
  "punycode@^2.1.0":
    "integrity" "sha1-tYsBCsQMIsVldhbI0sLALHv0eew="
    "resolved" "https://registry.npm.taobao.org/punycode/download/punycode-2.1.1.tgz"
    "version" "2.1.1"
  
  "qrcode@^1.3.3":
    "integrity" "sha1-8MQ1aKfnUQpV78O4jZYC9xlj6oM="
    "resolved" "https://registry.npm.taobao.org/qrcode/download/qrcode-1.4.4.tgz"
    "version" "1.4.4"
    dependencies:
      "buffer" "^5.4.3"
      "buffer-alloc" "^1.2.0"
      "buffer-from" "^1.1.1"
      "dijkstrajs" "^1.0.1"
      "isarray" "^2.0.1"
      "pngjs" "^3.3.0"
      "yargs" "^13.2.4"
  
  "qs@^6.4.0", "qs@^6.5.2", "qs@^6.6.0":
    "integrity" "sha1-IAgsZct4IjY1qxqerKiHWim/jsk="
    "resolved" "https://registry.npm.taobao.org/qs/download/qs-6.9.1.tgz?cache=0&sync_timestamp=1573195631718&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fqs%2Fdownload%2Fqs-6.9.1.tgz"
    "version" "6.9.1"
  
  "qs@^6.9.4":
    "integrity" "sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q=="
    "resolved" "https://registry.npmmirror.com/qs/-/qs-6.11.0.tgz"
    "version" "6.11.0"
    dependencies:
      "side-channel" "^1.0.4"
  
  "qs@~6.5.2":
    "integrity" "sha1-yzroBuh0BERYTvFUzo7pjUA/PjY="
    "resolved" "https://registry.npm.taobao.org/qs/download/qs-6.5.2.tgz?cache=0&sync_timestamp=1573195631718&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fqs%2Fdownload%2Fqs-6.5.2.tgz"
    "version" "6.5.2"
  
  "radio-symbol@^2.0.0":
    "integrity" "sha1-eqm/xQSFY21S3XbWqOYxspB5muE="
    "resolved" "https://registry.npm.taobao.org/radio-symbol/download/radio-symbol-2.0.0.tgz"
    "version" "2.0.0"
    dependencies:
      "ansi-gray" "^0.1.1"
      "ansi-green" "^0.1.1"
      "is-windows" "^1.0.1"
  
  "random-bytes@~1.0.0":
    "integrity" "sha1-T2ih3Arli9P7lYSMMDJNt11kNgs="
    "resolved" "https://registry.npm.taobao.org/random-bytes/download/random-bytes-1.0.0.tgz"
    "version" "1.0.0"
  
  "random-int@^1.0.0":
    "integrity" "sha1-5qLtNEisnGZGoGV0Q7HBUhWS7Qg="
    "resolved" "https://registry.npm.taobao.org/random-int/download/random-int-1.0.0.tgz"
    "version" "1.0.0"
  
  "range-parser@~1.2.0", "range-parser@~1.2.1":
    "integrity" "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE="
    "resolved" "https://registry.npm.taobao.org/range-parser/download/range-parser-1.2.1.tgz"
    "version" "1.2.1"
  
  "raw-body@^2.2.0", "raw-body@^2.3.3":
    "integrity" "sha1-MKyC+Yu1rowVLmcUnayNVRU7Fow="
    "resolved" "https://registry.npm.taobao.org/raw-body/download/raw-body-2.4.1.tgz"
    "version" "2.4.1"
    dependencies:
      "bytes" "3.1.0"
      "http-errors" "1.7.3"
      "iconv-lite" "0.4.24"
      "unpipe" "1.0.0"
  
  "readable-stream@^2.0.0", "readable-stream@^2.0.2", "readable-stream@^2.0.5", "readable-stream@^2.3.6", "readable-stream@~2.3.6", "readable-stream@2", "readable-stream@2.3.7":
    "integrity" "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c="
    "resolved" "https://registry.npm.taobao.org/readable-stream/download/readable-stream-2.3.7.tgz"
    "version" "2.3.7"
    dependencies:
      "core-util-is" "~1.0.0"
      "inherits" "~2.0.3"
      "isarray" "~1.0.0"
      "process-nextick-args" "~2.0.0"
      "safe-buffer" "~5.1.1"
      "string_decoder" "~1.1.1"
      "util-deprecate" "~1.0.1"
  
  "readable-stream@^3.0.6":
    "integrity" "sha1-Rl1w5tEIf2Fi0HnNC123++v9FgY="
    "resolved" "https://registry.npm.taobao.org/readable-stream/download/readable-stream-3.5.0.tgz"
    "version" "3.5.0"
    dependencies:
      "inherits" "^2.0.3"
      "string_decoder" "^1.1.1"
      "util-deprecate" "^1.0.1"
  
  "readable-stream@^3.1.1":
    "integrity" "sha1-Rl1w5tEIf2Fi0HnNC123++v9FgY="
    "resolved" "https://registry.npm.taobao.org/readable-stream/download/readable-stream-3.5.0.tgz"
    "version" "3.5.0"
    dependencies:
      "inherits" "^2.0.3"
      "string_decoder" "^1.1.1"
      "util-deprecate" "^1.0.1"
  
  "readable-stream@^3.4.0":
    "integrity" "sha1-M3u9o63AcGvT4CRCaihtS0sskZg="
    "resolved" "https://registry.npm.taobao.org/readable-stream/download/readable-stream-3.6.0.tgz"
    "version" "3.6.0"
    dependencies:
      "inherits" "^2.0.3"
      "string_decoder" "^1.1.1"
      "util-deprecate" "^1.0.1"
  
  "readable-stream@^3.6.0":
    "integrity" "sha1-M3u9o63AcGvT4CRCaihtS0sskZg="
    "resolved" "https://registry.npm.taobao.org/readable-stream/download/readable-stream-3.6.0.tgz"
    "version" "3.6.0"
    dependencies:
      "inherits" "^2.0.3"
      "string_decoder" "^1.1.1"
      "util-deprecate" "^1.0.1"
  
  "readable-stream@1.1.x":
    "integrity" "sha1-fPTFTvZI44EwhMY23SB54WbAgdk="
    "resolved" "https://registry.npm.taobao.org/readable-stream/download/readable-stream-1.1.14.tgz"
    "version" "1.1.14"
    dependencies:
      "core-util-is" "~1.0.0"
      "inherits" "~2.0.1"
      "isarray" "0.0.1"
      "string_decoder" "~0.10.x"
  
  "readdir-glob@^1.0.0":
    "integrity" "sha1-8OELt797+n4K3Yuv/cVMP32+5sQ="
    "resolved" "https://registry.npm.taobao.org/readdir-glob/download/readdir-glob-1.1.1.tgz"
    "version" "1.1.1"
    dependencies:
      "minimatch" "^3.0.4"
  
  "readline-ui@^2.2.2", "readline-ui@^2.2.3":
    "integrity" "sha1-noc6dmi72MqKVXPOgQprr7cKUIk="
    "resolved" "https://registry.npm.taobao.org/readline-ui/download/readline-ui-2.2.3.tgz"
    "version" "2.2.3"
    dependencies:
      "component-emitter" "^1.2.1"
      "debug" "^2.6.8"
      "readline-utils" "^2.2.1"
      "string-width" "^2.0.0"
  
  "readline-utils@^2.2.1", "readline-utils@^2.2.3":
    "integrity" "sha1-b4R9a48ZFcORtYHDZ81HhzhiNRo="
    "resolved" "https://registry.npm.taobao.org/readline-utils/download/readline-utils-2.2.3.tgz"
    "version" "2.2.3"
    dependencies:
      "arr-flatten" "^1.1.0"
      "extend-shallow" "^2.0.1"
      "is-buffer" "^1.1.5"
      "is-number" "^3.0.0"
      "is-windows" "^1.0.1"
      "koalas" "^1.0.2"
      "mute-stream" "0.0.7"
      "strip-color" "^0.1.0"
      "window-size" "^1.1.0"
  
  "rechoir@^0.6.2":
    "integrity" "sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q="
    "resolved" "https://registry.npm.taobao.org/rechoir/download/rechoir-0.6.2.tgz"
    "version" "0.6.2"
    dependencies:
      "resolve" "^1.1.6"
  
  "regenerator-runtime@^0.11.0":
    "integrity" "sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk="
    "resolved" "https://registry.npm.taobao.org/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz"
    "version" "0.11.1"
  
  "regex-not@^1.0.0", "regex-not@^1.0.2":
    "integrity" "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw="
    "resolved" "https://registry.npm.taobao.org/regex-not/download/regex-not-1.0.2.tgz"
    "version" "1.0.2"
    dependencies:
      "extend-shallow" "^3.0.2"
      "safe-regex" "^1.1.0"
  
  "repeat-element@^1.1.2":
    "integrity" "sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4="
    "resolved" "https://registry.npm.taobao.org/repeat-element/download/repeat-element-1.1.3.tgz"
    "version" "1.1.3"
  
  "repeat-string@^1.6.1":
    "integrity" "sha1-jcrkcOHIirwtYA//Sndihtp15jc="
    "resolved" "https://registry.npm.taobao.org/repeat-string/download/repeat-string-1.6.1.tgz"
    "version" "1.6.1"
  
  "request@^2.54.0":
    "integrity" "sha1-nC/KT301tZLv5Xx/ClXoEFIST+8="
    "resolved" "https://registry.npm.taobao.org/request/download/request-2.88.0.tgz"
    "version" "2.88.0"
    dependencies:
      "aws-sign2" "~0.7.0"
      "aws4" "^1.8.0"
      "caseless" "~0.12.0"
      "combined-stream" "~1.0.6"
      "extend" "~3.0.2"
      "forever-agent" "~0.6.1"
      "form-data" "~2.3.2"
      "har-validator" "~5.1.0"
      "http-signature" "~1.2.0"
      "is-typedarray" "~1.0.0"
      "isstream" "~0.1.2"
      "json-stringify-safe" "~5.0.1"
      "mime-types" "~2.1.19"
      "oauth-sign" "~0.9.0"
      "performance-now" "^2.1.0"
      "qs" "~6.5.2"
      "safe-buffer" "^5.1.2"
      "tough-cookie" "~2.4.3"
      "tunnel-agent" "^0.6.0"
      "uuid" "^3.3.2"
  
  "require-all@^3.0.0":
    "integrity" "sha1-Rz1JcEvjEBFc4ST3c4Ox69hnExI="
    "resolved" "https://registry.npm.taobao.org/require-all/download/require-all-3.0.0.tgz"
    "version" "3.0.0"
  
  "require-directory@^2.1.1":
    "integrity" "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="
    "resolved" "https://registry.npm.taobao.org/require-directory/download/require-directory-2.1.1.tgz"
    "version" "2.1.1"
  
  "require-main-filename@^2.0.0":
    "integrity" "sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs="
    "resolved" "https://registry.npm.taobao.org/require-main-filename/download/require-main-filename-2.0.0.tgz"
    "version" "2.0.0"
  
  "require-stack@^1.0.2":
    "integrity" "sha1-4A7jSL+Wy1w+LUwntJ5BR24Ill0="
    "resolved" "https://registry.npm.taobao.org/require-stack/download/require-stack-1.0.2.tgz"
    "version" "1.0.2"
    dependencies:
      "syntax-error" "^1.1.4"
  
  "require-uncached@^1.0.3":
    "integrity" "sha1-Tg1W1slmL9MeQwEcS5WqSZVUIdM="
    "resolved" "https://registry.npm.taobao.org/require-uncached/download/require-uncached-1.0.3.tgz"
    "version" "1.0.3"
    dependencies:
      "caller-path" "^0.1.0"
      "resolve-from" "^1.0.0"
  
  "resetable@^1.0.3":
    "integrity" "sha1-C4NwvsK5IdoE2wsi34yWQP45lCA="
    "resolved" "https://registry.npm.taobao.org/resetable/download/resetable-1.0.3.tgz"
    "version" "1.0.3"
    dependencies:
      "clone" "^2.1.1"
  
  "resolve-dir@^1.0.0", "resolve-dir@^1.0.1":
    "integrity" "sha1-eaQGRMNivoLybv/nOcm7U4IEb0M="
    "resolved" "https://registry.npm.taobao.org/resolve-dir/download/resolve-dir-1.0.1.tgz"
    "version" "1.0.1"
    dependencies:
      "expand-tilde" "^2.0.0"
      "global-modules" "^1.0.0"
  
  "resolve-from@^1.0.0":
    "integrity" "sha1-Jsv+k10a7uq7Kbw/5a6wHpPUQiY="
    "resolved" "https://registry.npm.taobao.org/resolve-from/download/resolve-from-1.0.1.tgz"
    "version" "1.0.1"
  
  "resolve-url@^0.2.1":
    "integrity" "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo="
    "resolved" "https://registry.npm.taobao.org/resolve-url/download/resolve-url-0.2.1.tgz"
    "version" "0.2.1"
  
  "resolve@^1.1.6", "resolve@^1.1.7":
    "integrity" "sha1-G3ypYHPrtS50H/15n2s56kYsZ/U="
    "resolved" "https://registry.npm.taobao.org/resolve/download/resolve-1.15.0.tgz"
    "version" "1.15.0"
    dependencies:
      "path-parse" "^1.0.6"
  
  "ret@~0.1.10":
    "integrity" "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w="
    "resolved" "https://registry.npm.taobao.org/ret/download/ret-0.1.15.tgz"
    "version" "0.1.15"
  
  "retry@^0.10.1":
    "integrity" "sha1-52OI0heZLCUnUCQdPTlW/tmNj/Q="
    "resolved" "https://registry.npm.taobao.org/retry/download/retry-0.10.1.tgz"
    "version" "0.10.1"
  
  "right-pad@^1.0.1":
    "integrity" "sha1-jKCMLLtbVedNr6lr9/0aJ9VoyNA="
    "resolved" "https://registry.npm.taobao.org/right-pad/download/right-pad-1.0.1.tgz"
    "version" "1.0.1"
  
  "rimraf@^2.6.1", "rimraf@2":
    "integrity" "sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w="
    "resolved" "https://registry.npm.taobao.org/rimraf/download/rimraf-2.7.1.tgz"
    "version" "2.7.1"
    dependencies:
      "glob" "^7.1.3"
  
  "rimraf@^3.0.0":
    "integrity" "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho="
    "resolved" "https://registry.npm.taobao.org/rimraf/download/rimraf-3.0.2.tgz"
    "version" "3.0.2"
    dependencies:
      "glob" "^7.1.3"
  
  "safe-buffer@^5.0.1":
    "integrity" "sha1-t02uxJsRSPiMZLaNSbHoFcHy9Rk="
    "resolved" "https://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.2.0.tgz"
    "version" "5.2.0"
  
  "safe-buffer@^5.1.2":
    "integrity" "sha1-t02uxJsRSPiMZLaNSbHoFcHy9Rk="
    "resolved" "https://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.2.0.tgz"
    "version" "5.2.0"
  
  "safe-buffer@~5.1.0", "safe-buffer@~5.1.1", "safe-buffer@5.1.2":
    "integrity" "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="
    "resolved" "https://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.1.2.tgz"
    "version" "5.1.2"
  
  "safe-buffer@~5.2.0":
    "integrity" "sha1-t02uxJsRSPiMZLaNSbHoFcHy9Rk="
    "resolved" "https://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.2.0.tgz"
    "version" "5.2.0"
  
  "safe-regex@^1.1.0":
    "integrity" "sha1-QKNmnzsHfR6UPURinhV91IAjvy4="
    "resolved" "https://registry.npm.taobao.org/safe-regex/download/safe-regex-1.1.0.tgz?cache=0&sync_timestamp=1571687713993&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsafe-regex%2Fdownload%2Fsafe-regex-1.1.0.tgz"
    "version" "1.1.0"
    dependencies:
      "ret" "~0.1.10"
  
  "safer-buffer@^2.0.2", "safer-buffer@^2.1.0", "safer-buffer@>= 2.1.2 < 3", "safer-buffer@~2.1.0":
    "integrity" "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="
    "resolved" "https://registry.npm.taobao.org/safer-buffer/download/safer-buffer-2.1.2.tgz"
    "version" "2.1.2"
  
  "sax@>=0.6.0":
    "integrity" "sha1-KBYjTiN4vdxOU1T6tcqold9xANk="
    "resolved" "https://registry.npm.taobao.org/sax/download/sax-1.2.4.tgz"
    "version" "1.2.4"
  
  "saxes@^5.0.1":
    "integrity" "sha1-7rq5U/o7dgjb6U5drbFciI+maW0="
    "resolved" "https://registry.nlark.com/saxes/download/saxes-5.0.1.tgz"
    "version" "5.0.1"
    dependencies:
      "xmlchars" "^2.2.0"
  
  "scmp@2.0.0":
    "integrity" "sha1-JHEQ7yLM+JexOj8KvdtSeCOTzWo="
    "resolved" "https://registry.npm.taobao.org/scmp/download/scmp-2.0.0.tgz"
    "version" "2.0.0"
  
  "sdk-base@^2.0.1":
    "integrity" "sha1-ukAonovfJy7RHdnql+r5jgNtJMY="
    "resolved" "https://registry.npm.taobao.org/sdk-base/download/sdk-base-2.0.1.tgz"
    "version" "2.0.1"
    dependencies:
      "get-ready" "~1.0.0"
  
  "semver@^5.0.1", "semver@^5.5.0", "semver@^5.6.0":
    "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
    "resolved" "https://registry.npm.taobao.org/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1580434301781&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz"
    "version" "5.7.1"
  
  "semver@^7.3.2":
    "integrity" "sha512-QlYTucUYOews+WeEujDoEGziz4K6c47V/Bd+LjSSYcA94p+DmINdf7ncaUinThfvZyu13lN9OY1XDxt8C0Tw0g=="
    "resolved" "https://registry.npmmirror.com/semver/-/semver-7.3.7.tgz"
    "version" "7.3.7"
    dependencies:
      "lru-cache" "^6.0.0"
  
  "send@^0.16.1":
    "integrity" "sha1-bsyh4PjBVtFBWXVZhI32RzCmu8E="
    "resolved" "https://registry.npm.taobao.org/send/download/send-0.16.2.tgz"
    "version" "0.16.2"
    dependencies:
      "debug" "2.6.9"
      "depd" "~1.1.2"
      "destroy" "~1.0.4"
      "encodeurl" "~1.0.2"
      "escape-html" "~1.0.3"
      "etag" "~1.8.1"
      "fresh" "0.5.2"
      "http-errors" "~1.6.2"
      "mime" "1.4.1"
      "ms" "2.0.0"
      "on-finished" "~2.3.0"
      "range-parser" "~1.2.0"
      "statuses" "~1.4.0"
  
  "send@0.17.1":
    "integrity" "sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg="
    "resolved" "https://registry.npm.taobao.org/send/download/send-0.17.1.tgz"
    "version" "0.17.1"
    dependencies:
      "debug" "2.6.9"
      "depd" "~1.1.2"
      "destroy" "~1.0.4"
      "encodeurl" "~1.0.2"
      "escape-html" "~1.0.3"
      "etag" "~1.8.1"
      "fresh" "0.5.2"
      "http-errors" "~1.7.2"
      "mime" "1.6.0"
      "ms" "2.1.1"
      "on-finished" "~2.3.0"
      "range-parser" "~1.2.1"
      "statuses" "~1.5.0"
  
  "serve-static@^1.13.2":
    "integrity" "sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk="
    "resolved" "https://registry.npm.taobao.org/serve-static/download/serve-static-1.14.1.tgz"
    "version" "1.14.1"
    dependencies:
      "encodeurl" "~1.0.2"
      "escape-html" "~1.0.3"
      "parseurl" "~1.3.3"
      "send" "0.17.1"
  
  "set-blocking@^2.0.0":
    "integrity" "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="
    "resolved" "https://registry.npm.taobao.org/set-blocking/download/set-blocking-2.0.0.tgz"
    "version" "2.0.0"
  
  "set-getter@^0.1.0":
    "integrity" "sha1-12nBgsnVpR9AkUXy+6guXoboA3Y="
    "resolved" "https://registry.npm.taobao.org/set-getter/download/set-getter-0.1.0.tgz"
    "version" "0.1.0"
    dependencies:
      "to-object-path" "^0.3.0"
  
  "set-immediate-shim@~1.0.1":
    "integrity" "sha1-SysbJ+uAip+NzEgaWOXlb1mfP2E="
    "resolved" "https://registry.nlark.com/set-immediate-shim/download/set-immediate-shim-1.0.1.tgz"
    "version" "1.0.1"
  
  "set-value@^1.0.0":
    "integrity" "sha1-vMdvcaDx4HokuYfQoCr+yfZlME8="
    "resolved" "https://registry.npm.taobao.org/set-value/download/set-value-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "extend-shallow" "^2.0.1"
      "is-extendable" "^0.1.1"
      "is-plain-object" "^2.0.1"
      "to-object-path" "^0.3.0"
  
  "set-value@^2.0.0":
    "integrity" "sha1-oY1AUw5vB95CKMfe/kInr4ytAFs="
    "resolved" "https://registry.npm.taobao.org/set-value/download/set-value-2.0.1.tgz"
    "version" "2.0.1"
    dependencies:
      "extend-shallow" "^2.0.1"
      "is-extendable" "^0.1.1"
      "is-plain-object" "^2.0.3"
      "split-string" "^3.0.1"
  
  "set-value@^2.0.1":
    "integrity" "sha1-oY1AUw5vB95CKMfe/kInr4ytAFs="
    "resolved" "https://registry.npm.taobao.org/set-value/download/set-value-2.0.1.tgz"
    "version" "2.0.1"
    dependencies:
      "extend-shallow" "^2.0.1"
      "is-extendable" "^0.1.1"
      "is-plain-object" "^2.0.3"
      "split-string" "^3.0.1"
  
  "set-value@^3.0.0":
    "integrity" "sha1-Usgq92U7pp6x25LoH1zbMnObnpU="
    "resolved" "https://registry.npm.taobao.org/set-value/download/set-value-3.0.1.tgz"
    "version" "3.0.1"
    dependencies:
      "is-plain-object" "^2.0.4"
  
  "setimmediate@~1.0.4":
    "integrity" "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU="
    "resolved" "https://registry.nlark.com/setimmediate/download/setimmediate-1.0.5.tgz"
    "version" "1.0.5"
  
  "setprototypeof@1.1.0":
    "integrity" "sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY="
    "resolved" "https://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.1.0.tgz"
    "version" "1.1.0"
  
  "setprototypeof@1.1.1":
    "integrity" "sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM="
    "resolved" "https://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.1.1.tgz"
    "version" "1.1.1"
  
  "shallow-clone@^0.1.2":
    "integrity" "sha1-WQnodLp3EG1zrEFM/sH/yofZcGA="
    "resolved" "https://registry.npm.taobao.org/shallow-clone/download/shallow-clone-0.1.2.tgz"
    "version" "0.1.2"
    dependencies:
      "is-extendable" "^0.1.1"
      "kind-of" "^2.0.1"
      "lazy-cache" "^0.2.3"
      "mixin-object" "^2.0.1"
  
  "shallow-clone@^1.0.0":
    "integrity" "sha1-RIDNBuiC72iyrYij6lSDLixItXE="
    "resolved" "https://registry.npm.taobao.org/shallow-clone/download/shallow-clone-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "is-extendable" "^0.1.1"
      "kind-of" "^5.0.0"
      "mixin-object" "^2.0.1"
  
  "shallow-clone@^3.0.0":
    "integrity" "sha1-jymBrZJTH1UDWwH7IwdppA4C76M="
    "resolved" "https://registry.npm.taobao.org/shallow-clone/download/shallow-clone-3.0.1.tgz"
    "version" "3.0.1"
    dependencies:
      "kind-of" "^6.0.2"
  
  "shebang-command@^1.2.0":
    "integrity" "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo="
    "resolved" "https://registry.npm.taobao.org/shebang-command/download/shebang-command-1.2.0.tgz"
    "version" "1.2.0"
    dependencies:
      "shebang-regex" "^1.0.0"
  
  "shebang-regex@^1.0.0":
    "integrity" "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="
    "resolved" "https://registry.npm.taobao.org/shebang-regex/download/shebang-regex-1.0.0.tgz"
    "version" "1.0.0"
  
  "shortid@^2.2.14":
    "integrity" "sha1-K5AuqpOmmxESA3PNQqHx/kQ3wSI="
    "resolved" "https://registry.npm.taobao.org/shortid/download/shortid-2.2.15.tgz?cache=0&sync_timestamp=1567753623963&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fshortid%2Fdownload%2Fshortid-2.2.15.tgz"
    "version" "2.2.15"
    dependencies:
      "nanoid" "^2.1.0"
  
  "side-channel@^1.0.4":
    "integrity" "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw=="
    "resolved" "https://registry.npmmirror.com/side-channel/-/side-channel-1.0.4.tgz"
    "version" "1.0.4"
    dependencies:
      "call-bind" "^1.0.0"
      "get-intrinsic" "^1.0.2"
      "object-inspect" "^1.9.0"
  
  "simple-encryptor@^1.4.0":
    "integrity" "sha1-YL/RZlARp8D1D+Eqi1G1e5TdG8Q="
    "resolved" "https://registry.npm.taobao.org/simple-encryptor/download/simple-encryptor-1.4.0.tgz"
    "version" "1.4.0"
    dependencies:
      "scmp" "2.0.0"
  
  "simple-encryptor@^2.0.0":
    "integrity" "sha1-y8FxKZihLqsLJw3E1C0M/WZ9Uso="
    "resolved" "https://registry.npm.taobao.org/simple-encryptor/download/simple-encryptor-2.0.0.tgz"
    "version" "2.0.0"
    dependencies:
      "scmp" "2.0.0"
  
  "simple-swizzle@^0.2.2":
    "integrity" "sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo="
    "resolved" "https://registry.npm.taobao.org/simple-swizzle/download/simple-swizzle-0.2.2.tgz"
    "version" "0.2.2"
    dependencies:
      "is-arrayish" "^0.3.1"
  
  "slash@^1.0.0":
    "integrity" "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU="
    "resolved" "https://registry.npm.taobao.org/slash/download/slash-1.0.0.tgz"
    "version" "1.0.0"
  
  "smart-buffer@^4.1.0":
    "integrity" "sha1-kWBcJdkWUvRmHqacz0XxszHKIbo="
    "resolved" "https://registry.npm.taobao.org/smart-buffer/download/smart-buffer-4.1.0.tgz"
    "version" "4.1.0"
  
  "snapdragon-node@^2.0.1":
    "integrity" "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs="
    "resolved" "https://registry.npm.taobao.org/snapdragon-node/download/snapdragon-node-2.1.1.tgz"
    "version" "2.1.1"
    dependencies:
      "define-property" "^1.0.0"
      "isobject" "^3.0.0"
      "snapdragon-util" "^3.0.1"
  
  "snapdragon-util@^3.0.1":
    "integrity" "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI="
    "resolved" "https://registry.npm.taobao.org/snapdragon-util/download/snapdragon-util-3.0.1.tgz"
    "version" "3.0.1"
    dependencies:
      "kind-of" "^3.2.0"
  
  "snapdragon@^0.8.1":
    "integrity" "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0="
    "resolved" "https://registry.npm.taobao.org/snapdragon/download/snapdragon-0.8.2.tgz"
    "version" "0.8.2"
    dependencies:
      "base" "^0.11.1"
      "debug" "^2.2.0"
      "define-property" "^0.2.5"
      "extend-shallow" "^2.0.1"
      "map-cache" "^0.2.2"
      "source-map" "^0.5.6"
      "source-map-resolve" "^0.5.0"
      "use" "^3.1.0"
  
  "socks-proxy-agent@^4.0.1":
    "integrity" "sha1-PImR8xRbJ5nnDhG9X7yLGWMRY4Y="
    "resolved" "https://registry.npm.taobao.org/socks-proxy-agent/download/socks-proxy-agent-4.0.2.tgz"
    "version" "4.0.2"
    dependencies:
      "agent-base" "~4.2.1"
      "socks" "~2.3.2"
  
  "socks-proxy-agent@^5.0.0", "socks-proxy-agent@5":
    "integrity" "sha1-fA82Tnsc9KekN+cSU77XLpAEvmA="
    "resolved" "https://registry.npm.taobao.org/socks-proxy-agent/download/socks-proxy-agent-5.0.0.tgz?cache=0&sync_timestamp=1580846328285&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsocks-proxy-agent%2Fdownload%2Fsocks-proxy-agent-5.0.0.tgz"
    "version" "5.0.0"
    dependencies:
      "agent-base" "6"
      "debug" "4"
      "socks" "^2.3.3"
  
  "socks@^2.3.3":
    "integrity" "sha1-mJ5lNKB88zfesbHJSqpEKWUg0w4="
    "resolved" "https://registry.npm.taobao.org/socks/download/socks-2.6.1.tgz?cache=0&sync_timestamp=1618719120412&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsocks%2Fdownload%2Fsocks-2.6.1.tgz"
    "version" "2.6.1"
    dependencies:
      "ip" "^1.1.5"
      "smart-buffer" "^4.1.0"
  
  "socks@~2.3.2":
    "integrity" "sha1-ARKfCl1TTSuJdxLtis6rfuZdeOM="
    "resolved" "https://registry.npm.taobao.org/socks/download/socks-2.3.3.tgz?cache=0&sync_timestamp=1573063369351&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsocks%2Fdownload%2Fsocks-2.3.3.tgz"
    "version" "2.3.3"
    dependencies:
      "ip" "1.1.5"
      "smart-buffer" "^4.1.0"
  
  "sorted-array-functions@^1.0.0":
    "integrity" "sha1-QyZbIdbphbffMWIbHBHMaNjvx8M="
    "resolved" "https://registry.npm.taobao.org/sorted-array-functions/download/sorted-array-functions-1.2.0.tgz"
    "version" "1.2.0"
  
  "source-map-resolve@^0.5.0":
    "integrity" "sha1-GQhmvs51U+H48mei7oLGBrVQmho="
    "resolved" "https://registry.npm.taobao.org/source-map-resolve/download/source-map-resolve-0.5.3.tgz?cache=0&sync_timestamp=1577562200059&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsource-map-resolve%2Fdownload%2Fsource-map-resolve-0.5.3.tgz"
    "version" "0.5.3"
    dependencies:
      "atob" "^2.1.2"
      "decode-uri-component" "^0.2.0"
      "resolve-url" "^0.2.1"
      "source-map-url" "^0.4.0"
      "urix" "^0.1.0"
  
  "source-map-url@^0.4.0":
    "integrity" "sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM="
    "resolved" "https://registry.npm.taobao.org/source-map-url/download/source-map-url-0.4.0.tgz"
    "version" "0.4.0"
  
  "source-map@^0.5.6":
    "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
    "resolved" "https://registry.npm.taobao.org/source-map/download/source-map-0.5.7.tgz"
    "version" "0.5.7"
  
  "source-map@~0.6.1":
    "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
    "resolved" "https://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz"
    "version" "0.6.1"
  
  "split-string@^3.0.1", "split-string@^3.0.2":
    "integrity" "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I="
    "resolved" "https://registry.npm.taobao.org/split-string/download/split-string-3.1.0.tgz"
    "version" "3.1.0"
    dependencies:
      "extend-shallow" "^3.0.0"
  
  "sqlstring@2.3.1":
    "integrity" "sha1-R1OT/56RR5rqYtyvDKPRSYOn+0A="
    "resolved" "https://registry.npm.taobao.org/sqlstring/download/sqlstring-2.3.1.tgz"
    "version" "2.3.1"
  
  "ssf@~0.10.2":
    "version" "0.10.3"
    dependencies:
      "frac" "~1.1.2"
  
  "ssf@~0.11.2":
    "version" "0.11.2"
    dependencies:
      "frac" "~1.1.2"
  
  "sshpk@^1.7.0":
    "integrity" "sha1-+2YcC+8ps520B2nuOfpwCT1vaHc="
    "resolved" "https://registry.npm.taobao.org/sshpk/download/sshpk-1.16.1.tgz"
    "version" "1.16.1"
    dependencies:
      "asn1" "~0.2.3"
      "assert-plus" "^1.0.0"
      "bcrypt-pbkdf" "^1.0.0"
      "dashdash" "^1.12.0"
      "ecc-jsbn" "~0.1.1"
      "getpass" "^0.1.1"
      "jsbn" "~0.1.0"
      "safer-buffer" "^2.0.2"
      "tweetnacl" "~0.14.0"
  
  "stack-trace@0.0.10", "stack-trace@0.0.x":
    "integrity" "sha1-VHxws0fo0ytOEI6hoqFZ5f3eGcA="
    "resolved" "https://registry.npm.taobao.org/stack-trace/download/stack-trace-0.0.10.tgz"
    "version" "0.0.10"
  
  "static-extend@^0.1.1", "static-extend@^0.1.2":
    "integrity" "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY="
    "resolved" "https://registry.npm.taobao.org/static-extend/download/static-extend-0.1.2.tgz"
    "version" "0.1.2"
    dependencies:
      "define-property" "^0.2.5"
      "object-copy" "^0.1.0"
  
  "statuses@^1.3.1", "statuses@>= 1.5.0 < 2", "statuses@~1.5.0":
    "integrity" "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="
    "resolved" "https://registry.npm.taobao.org/statuses/download/statuses-1.5.0.tgz"
    "version" "1.5.0"
  
  "statuses@>= 1.4.0 < 2":
    "integrity" "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="
    "resolved" "https://registry.npm.taobao.org/statuses/download/statuses-1.5.0.tgz"
    "version" "1.5.0"
  
  "statuses@~1.4.0":
    "integrity" "sha1-u3PURtonlhBu/MG2AaJT1sRr0Ic="
    "resolved" "https://registry.npm.taobao.org/statuses/download/statuses-1.4.0.tgz"
    "version" "1.4.0"
  
  "stream-http@2.8.2":
    "integrity" "sha1-QSboxrEHAERlkYqi/DVUnndALIc="
    "resolved" "https://registry.npm.taobao.org/stream-http/download/stream-http-2.8.2.tgz"
    "version" "2.8.2"
    dependencies:
      "builtin-status-codes" "^3.0.0"
      "inherits" "^2.0.1"
      "readable-stream" "^2.3.6"
      "to-arraybuffer" "^1.0.0"
      "xtend" "^4.0.0"
  
  "stream-wormhole@^1.0.4":
    "integrity" "sha1-MAr/Rs7VU8/sZCoFJRiFQXaTwz0="
    "resolved" "https://registry.npm.taobao.org/stream-wormhole/download/stream-wormhole-1.1.0.tgz"
    "version" "1.1.0"
  
  "string_decoder@^1.1.1":
    "integrity" "sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4="
    "resolved" "https://registry.npm.taobao.org/string_decoder/download/string_decoder-1.3.0.tgz"
    "version" "1.3.0"
    dependencies:
      "safe-buffer" "~5.2.0"
  
  "string_decoder@~0.10.x":
    "integrity" "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ="
    "resolved" "https://registry.npm.taobao.org/string_decoder/download/string_decoder-0.10.31.tgz"
    "version" "0.10.31"
  
  "string_decoder@~1.1.1":
    "integrity" "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g="
    "resolved" "https://registry.npm.taobao.org/string_decoder/download/string_decoder-1.1.1.tgz"
    "version" "1.1.1"
    dependencies:
      "safe-buffer" "~5.1.0"
  
  "string-hash@^1.1.3":
    "integrity" "sha1-6Kr8CsGFW0Zmkp7X3RJ1311sgRs="
    "resolved" "https://registry.npm.taobao.org/string-hash/download/string-hash-1.1.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring-hash%2Fdownload%2Fstring-hash-1.1.3.tgz"
    "version" "1.1.3"
  
  "string-width@^2.0.0":
    "integrity" "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4="
    "resolved" "https://registry.npm.taobao.org/string-width/download/string-width-2.1.1.tgz?cache=0&sync_timestamp=1573488535785&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring-width%2Fdownload%2Fstring-width-2.1.1.tgz"
    "version" "2.1.1"
    dependencies:
      "is-fullwidth-code-point" "^2.0.0"
      "strip-ansi" "^4.0.0"
  
  "string-width@^3.0.0", "string-width@^3.1.0":
    "integrity" "sha1-InZ74htirxCBV0MG9prFG2IgOWE="
    "resolved" "https://registry.npm.taobao.org/string-width/download/string-width-3.1.0.tgz?cache=0&sync_timestamp=1573488535785&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring-width%2Fdownload%2Fstring-width-3.1.0.tgz"
    "version" "3.1.0"
    dependencies:
      "emoji-regex" "^7.0.1"
      "is-fullwidth-code-point" "^2.0.0"
      "strip-ansi" "^5.1.0"
  
  "strip-ansi@^3.0.0":
    "integrity" "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8="
    "resolved" "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-3.0.1.tgz"
    "version" "3.0.1"
    dependencies:
      "ansi-regex" "^2.0.0"
  
  "strip-ansi@^4.0.0":
    "integrity" "sha1-qEeQIusaw2iocTibY1JixQXuNo8="
    "resolved" "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-4.0.0.tgz"
    "version" "4.0.0"
    dependencies:
      "ansi-regex" "^3.0.0"
  
  "strip-ansi@^5.0.0", "strip-ansi@^5.1.0", "strip-ansi@^5.2.0":
    "integrity" "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4="
    "resolved" "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-5.2.0.tgz"
    "version" "5.2.0"
    dependencies:
      "ansi-regex" "^4.1.0"
  
  "strip-color@^0.1.0":
    "integrity" "sha1-EG9l09PmotlAHKwOsM6LinArT3s="
    "resolved" "https://registry.npm.taobao.org/strip-color/download/strip-color-0.1.0.tgz"
    "version" "0.1.0"
  
  "success-symbol@^0.1.0":
    "integrity" "sha1-JAIuSG878c3KCUKDt2nEctO3KJc="
    "resolved" "https://registry.npm.taobao.org/success-symbol/download/success-symbol-0.1.0.tgz"
    "version" "0.1.0"
  
  "superagent@^4.0.0-beta.5":
    "integrity" "sha1-xGXC3kHfK40FwWXL5APigHkM39U="
    "resolved" "https://registry.npm.taobao.org/superagent/download/superagent-4.1.0.tgz"
    "version" "4.1.0"
    dependencies:
      "component-emitter" "^1.2.0"
      "cookiejar" "^2.1.2"
      "debug" "^4.1.0"
      "form-data" "^2.3.3"
      "formidable" "^1.2.0"
      "methods" "^1.1.1"
      "mime" "^2.4.0"
      "qs" "^6.6.0"
      "readable-stream" "^3.0.6"
  
  "superagent@^6.1.0":
    "integrity" "sha512-OUDHEssirmplo3F+1HWKUrUjvnQuA+nZI6i/JJBdXb5eq9IyEQwPyPpqND+SSsxf6TygpBEkUjISVRN4/VOpeg=="
    "resolved" "https://registry.npmmirror.com/superagent/-/superagent-6.1.0.tgz"
    "version" "6.1.0"
    dependencies:
      "component-emitter" "^1.3.0"
      "cookiejar" "^2.1.2"
      "debug" "^4.1.1"
      "fast-safe-stringify" "^2.0.7"
      "form-data" "^3.0.0"
      "formidable" "^1.2.2"
      "methods" "^1.1.2"
      "mime" "^2.4.6"
      "qs" "^6.9.4"
      "readable-stream" "^3.6.0"
      "semver" "^7.3.2"
  
  "supports-color@^2.0.0":
    "integrity" "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc="
    "resolved" "https://registry.npm.taobao.org/supports-color/download/supports-color-2.0.0.tgz"
    "version" "2.0.0"
  
  "supports-color@^5.3.0":
    "integrity" "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8="
    "resolved" "https://registry.npm.taobao.org/supports-color/download/supports-color-5.5.0.tgz"
    "version" "5.5.0"
    dependencies:
      "has-flag" "^3.0.0"
  
  "syntax-error@^1.1.4":
    "integrity" "sha1-LZ1P9cBkrLcRWUo+O5UFStUdkHw="
    "resolved" "https://registry.npm.taobao.org/syntax-error/download/syntax-error-1.4.0.tgz"
    "version" "1.4.0"
    dependencies:
      "acorn-node" "^1.2.0"
  
  "tar-stream@^2.2.0":
    "integrity" "sha1-rK2EwoQTawYNw/qmRHSqmuvXcoc="
    "resolved" "https://registry.npm.taobao.org/tar-stream/download/tar-stream-2.2.0.tgz"
    "version" "2.2.0"
    dependencies:
      "bl" "^4.0.3"
      "end-of-stream" "^1.4.1"
      "fs-constants" "^1.0.0"
      "inherits" "^2.0.3"
      "readable-stream" "^3.1.1"
  
  "tarn@^1.1.4":
    "integrity" "sha1-e+iGIulRc4ufo/t3R3MJJCzd3C0="
    "resolved" "https://registry.npm.taobao.org/tarn/download/tarn-1.1.5.tgz"
    "version" "1.1.5"
  
  "tenpay@^2.1.18":
    "integrity" "sha1-yxfEZmCYOCemFCCJIs9PcKiaxyw="
    "resolved" "https://registry.npm.taobao.org/tenpay/download/tenpay-2.1.18.tgz"
    "version" "2.1.18"
    dependencies:
      "urllib" "^2.33.0"
      "xml2js" "^0.4.19"
  
  "terminal-paginator@^2.0.0", "terminal-paginator@^2.0.2":
    "integrity" "sha1-ln5mBW8o/o9Vunwe6/t8PvNxwdM="
    "resolved" "https://registry.npm.taobao.org/terminal-paginator/download/terminal-paginator-2.0.2.tgz"
    "version" "2.0.2"
    dependencies:
      "debug" "^2.6.6"
      "extend-shallow" "^2.0.1"
      "log-utils" "^0.2.1"
  
  "text-hex@1.0.x":
    "integrity" "sha1-adycGxdEbueakr9biEu0uRJ1BvU="
    "resolved" "https://registry.npm.taobao.org/text-hex/download/text-hex-1.0.0.tgz"
    "version" "1.0.0"
  
  "thenify-all@^1.0.0":
    "integrity" "sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY="
    "resolved" "https://registry.npm.taobao.org/thenify-all/download/thenify-all-1.6.0.tgz"
    "version" "1.6.0"
    dependencies:
      "thenify" ">= 3.1.0 < 4"
  
  "thenify@>= 3.1.0 < 4":
    "integrity" "sha1-5p44obq+lpsBCCB5eLn2K4hgSDk="
    "resolved" "https://registry.npm.taobao.org/thenify/download/thenify-3.3.0.tgz"
    "version" "3.3.0"
    dependencies:
      "any-promise" "^1.0.0"
  
  "through@~2.3":
    "integrity" "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU="
    "resolved" "https://registry.npm.taobao.org/through/download/through-2.3.8.tgz"
    "version" "2.3.8"
  
  "thunkify@^2.1.2":
    "integrity" "sha1-+qDp0jDFGsyVyhOjYawFyn4EVT0="
    "resolved" "https://registry.npm.taobao.org/thunkify/download/thunkify-2.1.2.tgz"
    "version" "2.1.2"
  
  "tildify@1.2.0":
    "integrity" "sha1-3OwD9V3Km3qj5bBPIYF+tW5jWIo="
    "resolved" "https://registry.npm.taobao.org/tildify/download/tildify-1.2.0.tgz"
    "version" "1.2.0"
    dependencies:
      "os-homedir" "^1.0.0"
  
  "time-stamp@^1.0.1":
    "integrity" "sha1-dkpaEa9QVhkhsTPztE5hhofg9cM="
    "resolved" "https://registry.npm.taobao.org/time-stamp/download/time-stamp-1.1.0.tgz"
    "version" "1.1.0"
  
  "tmp@^0.2.0":
    "integrity" "sha1-hFf8MDfc9HGcJRNnoa9lAO4czxQ="
    "resolved" "https://registry.npm.taobao.org/tmp/download/tmp-0.2.1.tgz"
    "version" "0.2.1"
    dependencies:
      "rimraf" "^3.0.0"
  
  "tmp@0.0.x":
    "integrity" "sha1-bTQzWIl2jSGyvNoKonfO07G/rfk="
    "resolved" "https://registry.npm.taobao.org/tmp/download/tmp-0.0.33.tgz"
    "version" "0.0.33"
    dependencies:
      "os-tmpdir" "~1.0.2"
  
  "to-arraybuffer@^1.0.0":
    "integrity" "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M="
    "resolved" "https://registry.npm.taobao.org/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz"
    "version" "1.0.1"
  
  "to-object-path@^0.3.0":
    "integrity" "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68="
    "resolved" "https://registry.npm.taobao.org/to-object-path/download/to-object-path-0.3.0.tgz"
    "version" "0.3.0"
    dependencies:
      "kind-of" "^3.0.2"
  
  "to-regex-range@^2.1.0":
    "integrity" "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg="
    "resolved" "https://registry.npm.taobao.org/to-regex-range/download/to-regex-range-2.1.1.tgz"
    "version" "2.1.1"
    dependencies:
      "is-number" "^3.0.0"
      "repeat-string" "^1.6.1"
  
  "to-regex@^3.0.1", "to-regex@^3.0.2":
    "integrity" "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4="
    "resolved" "https://registry.npm.taobao.org/to-regex/download/to-regex-3.0.2.tgz"
    "version" "3.0.2"
    dependencies:
      "define-property" "^2.0.2"
      "extend-shallow" "^3.0.2"
      "regex-not" "^1.0.2"
      "safe-regex" "^1.1.0"
  
  "toggle-array@^1.0.1":
    "integrity" "sha1-y/WEB5K9UJfzMReugkyTKv/ofVg="
    "resolved" "https://registry.npm.taobao.org/toggle-array/download/toggle-array-1.0.1.tgz"
    "version" "1.0.1"
    dependencies:
      "isobject" "^3.0.0"
  
  "toidentifier@1.0.0":
    "integrity" "sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM="
    "resolved" "https://registry.npm.taobao.org/toidentifier/download/toidentifier-1.0.0.tgz"
    "version" "1.0.0"
  
  "tough-cookie@~2.4.3":
    "integrity" "sha1-U/Nto/R3g7CSWvoG/587FlKA94E="
    "resolved" "https://registry.npm.taobao.org/tough-cookie/download/tough-cookie-2.4.3.tgz"
    "version" "2.4.3"
    dependencies:
      "psl" "^1.1.24"
      "punycode" "^1.4.1"
  
  "traverse@>=0.3.0 <0.4":
    "integrity" "sha1-cXuPIgzAu3tE5AUUwisui7xw2Lk="
    "resolved" "https://registry.nlark.com/traverse/download/traverse-0.3.9.tgz"
    "version" "0.3.9"
  
  "triple-beam@^1.2.0", "triple-beam@^1.3.0":
    "integrity" "sha1-pZUhTHKY24M57u7gg+TRC9jLjdk="
    "resolved" "https://registry.npm.taobao.org/triple-beam/download/triple-beam-1.3.0.tgz"
    "version" "1.3.0"
  
  "tslib@^2.0.1":
    "integrity" "sha1-+yxHWXfjXiQTEe3iaTzuHsZpj1w="
    "resolved" "https://registry.npm.taobao.org/tslib/download/tslib-2.2.0.tgz"
    "version" "2.2.0"
  
  "tunnel-agent@^0.6.0":
    "integrity" "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0="
    "resolved" "https://registry.npm.taobao.org/tunnel-agent/download/tunnel-agent-0.6.0.tgz"
    "version" "0.6.0"
    dependencies:
      "safe-buffer" "^5.0.1"
  
  "tweetnacl@^0.14.3", "tweetnacl@~0.14.0":
    "integrity" "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q="
    "resolved" "https://registry.npm.taobao.org/tweetnacl/download/tweetnacl-0.14.5.tgz?cache=0&sync_timestamp=1579202900551&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftweetnacl%2Fdownload%2Ftweetnacl-0.14.5.tgz"
    "version" "0.14.5"
  
  "tweetnacl@^1.0.1":
    "integrity" "sha512-6rt+RN7aOi1nGMyC4Xa5DdYiukl2UWCbcJft7YhxReBGQD7OAM8Pbxw6YMo4r2diNEA8FEmu32YOn9rhaiE5yw=="
    "resolved" "https://registry.npmmirror.com/tweetnacl/-/tweetnacl-1.0.3.tgz"
    "version" "1.0.3"
  
  "type-check@~0.3.2":
    "integrity" "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I="
    "resolved" "https://registry.npm.taobao.org/type-check/download/type-check-0.3.2.tgz"
    "version" "0.3.2"
    dependencies:
      "prelude-ls" "~1.1.2"
  
  "type-detect@^4.0.0", "type-detect@^4.0.5":
    "integrity" "sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw="
    "resolved" "https://registry.npm.taobao.org/type-detect/download/type-detect-4.0.8.tgz"
    "version" "4.0.8"
  
  "type-is@^1.6.16":
    "integrity" "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE="
    "resolved" "https://registry.npm.taobao.org/type-is/download/type-is-1.6.18.tgz"
    "version" "1.6.18"
    dependencies:
      "media-typer" "0.3.0"
      "mime-types" "~2.1.24"
  
  "uid-safe@2.1.5":
    "integrity" "sha1-Kz1cckDo/C5Y+Komnl7knAhXvTo="
    "resolved" "https://registry.npm.taobao.org/uid-safe/download/uid-safe-2.1.5.tgz"
    "version" "2.1.5"
    dependencies:
      "random-bytes" "~1.0.0"
  
  "unc-path-regex@^0.1.2":
    "integrity" "sha1-5z3T17DXxe2G+6xrCufYxqadUPo="
    "resolved" "https://registry.npm.taobao.org/unc-path-regex/download/unc-path-regex-0.1.2.tgz"
    "version" "0.1.2"
  
  "unescape@^1.0.1":
    "integrity" "sha1-lW5DD2HK2KTVfYLFGPXmzF0N2pY="
    "resolved" "https://registry.npm.taobao.org/unescape/download/unescape-1.0.1.tgz"
    "version" "1.0.1"
    dependencies:
      "extend-shallow" "^2.0.1"
  
  "union-value@^1.0.0":
    "integrity" "sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc="
    "resolved" "https://registry.npm.taobao.org/union-value/download/union-value-1.0.1.tgz"
    "version" "1.0.1"
    dependencies:
      "arr-union" "^3.1.0"
      "get-value" "^2.0.6"
      "is-extendable" "^0.1.1"
      "set-value" "^2.0.1"
  
  "universalify@^0.1.0":
    "integrity" "sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY="
    "resolved" "https://registry.npm.taobao.org/universalify/download/universalify-0.1.2.tgz"
    "version" "0.1.2"
  
  "unpipe@1.0.0":
    "integrity" "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="
    "resolved" "https://registry.npm.taobao.org/unpipe/download/unpipe-1.0.0.tgz"
    "version" "1.0.0"
  
  "unset-value@^1.0.0":
    "integrity" "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk="
    "resolved" "https://registry.npm.taobao.org/unset-value/download/unset-value-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "has-value" "^0.3.1"
      "isobject" "^3.0.0"
  
  "unzipper@^0.10.11":
    "integrity" "sha1-C0mRRGRyy9uS7nQDkJ8mwkGceC4="
    "resolved" "https://registry.npm.taobao.org/unzipper/download/unzipper-0.10.11.tgz"
    "version" "0.10.11"
    dependencies:
      "big-integer" "^1.6.17"
      "binary" "~0.3.0"
      "bluebird" "~3.4.1"
      "buffer-indexof-polyfill" "~1.0.0"
      "duplexer2" "~0.1.4"
      "fstream" "^1.0.12"
      "graceful-fs" "^4.2.2"
      "listenercount" "~1.0.1"
      "readable-stream" "~2.3.6"
      "setimmediate" "~1.0.4"
  
  "upcast@^2.1.1":
    "integrity" "sha1-aUaON0s8+3si+2MtVaQgBIueJKE="
    "resolved" "https://registry.npm.taobao.org/upcast/download/upcast-2.1.2.tgz"
    "version" "2.1.2"
    dependencies:
      "cross-env" "^5.1.0"
  
  "uri-js@^4.2.2":
    "integrity" "sha1-lMVA4f93KVbiKZUHwBCupsiDjrA="
    "resolved" "https://registry.npm.taobao.org/uri-js/download/uri-js-4.2.2.tgz"
    "version" "4.2.2"
    dependencies:
      "punycode" "^2.1.0"
  
  "urix@^0.1.0":
    "integrity" "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI="
    "resolved" "https://registry.npm.taobao.org/urix/download/urix-0.1.0.tgz"
    "version" "0.1.0"
  
  "urllib@^2.33.0":
    "integrity" "sha1-EBa4iqsysnBIpNUtNiA3ccY+Ow8="
    "resolved" "https://registry.npm.taobao.org/urllib/download/urllib-2.37.1.tgz"
    "version" "2.37.1"
    dependencies:
      "any-promise" "^1.3.0"
      "content-type" "^1.0.2"
      "debug" "^2.6.9"
      "default-user-agent" "^1.0.0"
      "digest-header" "^0.0.1"
      "ee-first" "~1.1.1"
      "formstream" "^1.1.0"
      "humanize-ms" "^1.2.0"
      "iconv-lite" "^0.4.15"
      "ip" "^1.1.5"
      "proxy-agent" "^4.0.1"
      "pump" "^3.0.0"
      "qs" "^6.4.0"
      "statuses" "^1.3.1"
      "utility" "^1.16.1"
  
  "urllib@^2.33.1":
    "integrity" "sha1-zo3a/esipAJlCUwaqWG8vnyGYrg="
    "resolved" "https://registry.npm.taobao.org/urllib/download/urllib-2.34.2.tgz?cache=0&sync_timestamp=1578380298589&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Furllib%2Fdownload%2Furllib-2.34.2.tgz"
    "version" "2.34.2"
    dependencies:
      "any-promise" "^1.3.0"
      "content-type" "^1.0.2"
      "debug" "^2.6.9"
      "default-user-agent" "^1.0.0"
      "digest-header" "^0.0.1"
      "ee-first" "~1.1.1"
      "formstream" "^1.1.0"
      "humanize-ms" "^1.2.0"
      "iconv-lite" "^0.4.15"
      "ip" "^1.1.5"
      "proxy-agent" "^3.1.0"
      "pump" "^3.0.0"
      "qs" "^6.4.0"
      "statuses" "^1.3.1"
      "utility" "^1.16.1"
  
  "use@^3.1.0":
    "integrity" "sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8="
    "resolved" "https://registry.npm.taobao.org/use/download/use-3.1.1.tgz"
    "version" "3.1.1"
  
  "useragent@^2.3.0":
    "integrity" "sha1-IX+UOtVAyyEoZYqyP8lg9qiMmXI="
    "resolved" "https://registry.npm.taobao.org/useragent/download/useragent-2.3.0.tgz"
    "version" "2.3.0"
    dependencies:
      "lru-cache" "4.1.x"
      "tmp" "0.0.x"
  
  "util-deprecate@^1.0.1", "util-deprecate@~1.0.1":
    "integrity" "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="
    "resolved" "https://registry.npm.taobao.org/util-deprecate/download/util-deprecate-1.0.2.tgz"
    "version" "1.0.2"
  
  "utility@^1.16.1", "utility@^1.8.0":
    "integrity" "sha1-Xf0R3nTmv92CbMShZ+YwHZL0tw0="
    "resolved" "https://registry.npm.taobao.org/utility/download/utility-1.16.3.tgz"
    "version" "1.16.3"
    dependencies:
      "copy-to" "^2.0.1"
      "escape-html" "^1.0.3"
      "mkdirp" "^0.5.1"
      "mz" "^2.7.0"
      "unescape" "^1.0.1"
  
  "utility@0.1.11":
    "integrity" "sha1-/eYM+bTkdRlHoM9dEEzik2ciZxU="
    "resolved" "https://registry.npm.taobao.org/utility/download/utility-0.1.11.tgz"
    "version" "0.1.11"
    dependencies:
      "address" ">=0.0.1"
  
  "uuid@^3.3.2":
    "integrity" "sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4="
    "resolved" "https://registry.npm.taobao.org/uuid/download/uuid-3.4.0.tgz"
    "version" "3.4.0"
  
  "uuid@^8.3.0":
    "integrity" "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg=="
    "resolved" "https://registry.npmmirror.com/uuid/-/uuid-8.3.2.tgz"
    "version" "8.3.2"
  
  "v8flags@^3.1.1":
    "integrity" "sha1-/J3CNSHKIMVDP4HMTrmzAzuxBdg="
    "resolved" "https://registry.npm.taobao.org/v8flags/download/v8flags-3.1.3.tgz"
    "version" "3.1.3"
    dependencies:
      "homedir-polyfill" "^1.0.1"
  
  "variable-diff@^1.1.0":
    "integrity" "sha1-0r1cZtt2wTh52W5qMG7cmJ35eNo="
    "resolved" "https://registry.npm.taobao.org/variable-diff/download/variable-diff-1.1.0.tgz"
    "version" "1.1.0"
    dependencies:
      "chalk" "^1.1.1"
      "object-assign" "^4.0.1"
  
  "vary@^1.1.2":
    "integrity" "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="
    "resolved" "https://registry.npm.taobao.org/vary/download/vary-1.1.2.tgz"
    "version" "1.1.2"
  
  "verror@1.10.0":
    "integrity" "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA="
    "resolved" "https://registry.npm.taobao.org/verror/download/verror-1.10.0.tgz"
    "version" "1.10.0"
    dependencies:
      "assert-plus" "^1.0.0"
      "core-util-is" "1.0.2"
      "extsprintf" "^1.2.0"
  
  "warning-symbol@^0.1.0":
    "integrity" "sha1-uzHdEbeg+dZ6su2V9Fe2WCW7rSE="
    "resolved" "https://registry.npm.taobao.org/warning-symbol/download/warning-symbol-0.1.0.tgz"
    "version" "0.1.0"
  
  "wechatpay-node-v3@^2.0.0":
    "integrity" "sha512-158TmelixaXavNwhooVcg2s4lQB165cnIRqDj984ZX9I3yzJqLwJARCUVpjXCaTzm0gQ5jdtamqyMG38/umncA=="
    "resolved" "https://registry.npmmirror.com/wechatpay-node-v3/-/wechatpay-node-v3-2.0.0.tgz"
    "version" "2.0.0"
    dependencies:
      "@fidm/x509" "^1.2.1"
      "superagent" "^6.1.0"
  
  "weixin-pay@^1.1.7":
    "integrity" "sha1-zpudMxu6iVL175YezckTcFrrJlI="
    "resolved" "https://registry.npm.taobao.org/weixin-pay/download/weixin-pay-1.1.7.tgz"
    "version" "1.1.7"
    dependencies:
      "MD5" "^1.2.1"
      "request" "^2.54.0"
      "xml2js" "^0.4.6"
  
  "which-module@^2.0.0":
    "integrity" "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho="
    "resolved" "https://registry.npm.taobao.org/which-module/download/which-module-2.0.0.tgz"
    "version" "2.0.0"
  
  "which@^1.2.14", "which@^1.2.9":
    "integrity" "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo="
    "resolved" "https://registry.npm.taobao.org/which/download/which-1.3.1.tgz?cache=0&sync_timestamp=1574116262707&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwhich%2Fdownload%2Fwhich-1.3.1.tgz"
    "version" "1.3.1"
    dependencies:
      "isexe" "^2.0.0"
  
  "win-release@^1.0.0":
    "integrity" "sha1-X6VeAr58qTTt/BJmVjLoSbcuUgk="
    "resolved" "https://registry.npm.taobao.org/win-release/download/win-release-1.1.1.tgz"
    "version" "1.1.1"
    dependencies:
      "semver" "^5.0.1"
  
  "window-size@^1.1.0":
    "integrity" "sha1-mFhYZYCtp4qybs1peKbgMRXBryA="
    "resolved" "https://registry.npm.taobao.org/window-size/download/window-size-1.1.1.tgz"
    "version" "1.1.1"
    dependencies:
      "define-property" "^1.0.0"
      "is-number" "^3.0.0"
  
  "winston-transport@^4.3.0":
    "integrity" "sha1-32jAwgJILESNm0cxPAcwTC18LGY="
    "resolved" "https://registry.npm.taobao.org/winston-transport/download/winston-transport-4.3.0.tgz"
    "version" "4.3.0"
    dependencies:
      "readable-stream" "^2.3.6"
      "triple-beam" "^1.2.0"
  
  "winston@^3.2.1":
    "integrity" "sha1-YwYTd5dsc1hAKL4kkKGEYFX3fwc="
    "resolved" "https://registry.npm.taobao.org/winston/download/winston-3.2.1.tgz"
    "version" "3.2.1"
    dependencies:
      "async" "^2.6.1"
      "diagnostics" "^1.1.1"
      "is-stream" "^1.1.0"
      "logform" "^2.1.1"
      "one-time" "0.0.4"
      "readable-stream" "^3.1.1"
      "stack-trace" "0.0.x"
      "triple-beam" "^1.3.0"
      "winston-transport" "^4.3.0"
  
  "wmf@~1.0.1":
    "version" "1.0.2"
  
  "word-wrap@~1.2.3":
    "integrity" "sha1-YQY29rH3A4kb00dxzLF/uTtHB5w="
    "resolved" "https://registry.npm.taobao.org/word-wrap/download/word-wrap-1.2.3.tgz"
    "version" "1.2.3"
  
  "word@~0.3.0":
    "version" "0.3.0"
  
  "wrap-ansi@^5.1.0":
    "integrity" "sha1-H9H2cjXVttD+54EFYAG/tpTAOwk="
    "resolved" "https://registry.npm.taobao.org/wrap-ansi/download/wrap-ansi-5.1.0.tgz?cache=0&sync_timestamp=1573488719878&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-5.1.0.tgz"
    "version" "5.1.0"
    dependencies:
      "ansi-styles" "^3.2.0"
      "string-width" "^3.0.0"
      "strip-ansi" "^5.0.0"
  
  "wrappy@1":
    "integrity" "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="
    "resolved" "https://registry.npm.taobao.org/wrappy/download/wrappy-1.0.2.tgz"
    "version" "1.0.2"
  
  "xlsx@^0.14.1":
    "integrity" "sha1-NjfpFNeRvcpzgoFuFz99cl7Q4NI="
    "resolved" "https://registry.npm.taobao.org/xlsx/download/xlsx-0.14.5.tgz"
    "version" "0.14.5"
    dependencies:
      "adler-32" "~1.2.0"
      "cfb" "^1.1.2"
      "codepage" "~1.14.0"
      "commander" "~2.17.1"
      "crc-32" "~1.2.0"
      "exit-on-epipe" "~1.0.1"
      "ssf" "~0.10.2"
  
  "xlsx@^0.18.5":
    "integrity" "sha512-dmg3LCjBPHZnQp5/F/+nnTa+miPJxUXB6vtk42YjBBKayDNagxGEeIdWApkYPOf3Z3pm3k62Knjzp7lMeTEtFQ=="
    "resolved" "https://registry.npmmirror.com/xlsx/-/xlsx-0.18.5.tgz"
    "version" "0.18.5"
    dependencies:
      "adler-32" "~1.3.0"
      "cfb" "~1.2.1"
      "codepage" "~1.15.0"
      "crc-32" "~1.2.1"
      "ssf" "~0.11.2"
      "wmf" "~1.0.1"
      "word" "~0.3.0"
  
  "xml2js@^0.4.16", "xml2js@^0.4.17", "xml2js@^0.4.19", "xml2js@^0.4.6":
    "integrity" "sha1-oMaVFnUkIesqx1juTUzPWIQ+rGY="
    "resolved" "https://registry.npm.taobao.org/xml2js/download/xml2js-0.4.23.tgz?cache=0&sync_timestamp=1576776179444&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fxml2js%2Fdownload%2Fxml2js-0.4.23.tgz"
    "version" "0.4.23"
    dependencies:
      "sax" ">=0.6.0"
      "xmlbuilder" "~11.0.0"
  
  "xmlbuilder@~11.0.0":
    "integrity" "sha1-vpuuHIoEbnazESdyY0fQrXACvrM="
    "resolved" "https://registry.npm.taobao.org/xmlbuilder/download/xmlbuilder-11.0.1.tgz"
    "version" "11.0.1"
  
  "xmlchars@^2.2.0":
    "integrity" "sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs="
    "resolved" "https://registry.npm.taobao.org/xmlchars/download/xmlchars-2.2.0.tgz"
    "version" "2.2.0"
  
  "xregexp@2.0.0":
    "integrity" "sha1-UqY+VsoLhKfzpfPWGHLxJq16WUM="
    "resolved" "https://registry.npm.taobao.org/xregexp/download/xregexp-2.0.0.tgz"
    "version" "2.0.0"
  
  "xtend@^4.0.0", "xtend@^4.0.2":
    "integrity" "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q="
    "resolved" "https://registry.npm.taobao.org/xtend/download/xtend-4.0.2.tgz"
    "version" "4.0.2"
  
  "y18n@^4.0.0":
    "integrity" "sha1-le+U+F7MgdAHwmThkKEg8KPIVms="
    "resolved" "https://registry.npm.taobao.org/y18n/download/y18n-4.0.0.tgz"
    "version" "4.0.0"
  
  "yallist@^2.1.2":
    "integrity" "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI="
    "resolved" "https://registry.npm.taobao.org/yallist/download/yallist-2.1.2.tgz"
    "version" "2.1.2"
  
  "yallist@^3.0.2":
    "integrity" "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0="
    "resolved" "https://registry.npm.taobao.org/yallist/download/yallist-3.1.1.tgz"
    "version" "3.1.1"
  
  "yallist@^4.0.0":
    "integrity" "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="
    "resolved" "https://registry.npmmirror.com/yallist/-/yallist-4.0.0.tgz"
    "version" "4.0.0"
  
  "yargs-parser@^13.1.1":
    "integrity" "sha1-0mBYUyqgbTZf4JH2ofwGsvfl7KA="
    "resolved" "https://registry.npm.taobao.org/yargs-parser/download/yargs-parser-13.1.1.tgz?cache=0&sync_timestamp=1572648776744&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs-parser%2Fdownload%2Fyargs-parser-13.1.1.tgz"
    "version" "13.1.1"
    dependencies:
      "camelcase" "^5.0.0"
      "decamelize" "^1.2.0"
  
  "yargs@^13.2.4":
    "integrity" "sha1-TGV6VeB+Xyz5R/ijZlZ8BKDe3IM="
    "resolved" "https://registry.npm.taobao.org/yargs/download/yargs-13.3.0.tgz?cache=0&sync_timestamp=1577940861093&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs%2Fdownload%2Fyargs-13.3.0.tgz"
    "version" "13.3.0"
    dependencies:
      "cliui" "^5.0.0"
      "find-up" "^3.0.0"
      "get-caller-file" "^2.0.1"
      "require-directory" "^2.1.1"
      "require-main-filename" "^2.0.0"
      "set-blocking" "^2.0.0"
      "string-width" "^3.0.0"
      "which-module" "^2.0.0"
      "y18n" "^4.0.0"
      "yargs-parser" "^13.1.1"
  
  "youch-terminal@^1.0.0":
    "integrity" "sha1-A+IJbuNg75FYFuYuqeyUpv8JTZ4="
    "resolved" "https://registry.npm.taobao.org/youch-terminal/download/youch-terminal-1.0.0.tgz"
    "version" "1.0.0"
    dependencies:
      "chalk" "^2.3.0"
  
  "youch@^2.0.10":
    "integrity" "sha1-4PYxKxIwT9MwoMSg4JJbASP31JU="
    "resolved" "https://registry.npm.taobao.org/youch/download/youch-2.0.10.tgz"
    "version" "2.0.10"
    dependencies:
      "cookie" "^0.3.1"
      "mustache" "^3.0.0"
      "stack-trace" "0.0.10"
  
  "zip-stream@^4.1.0":
    "integrity" "sha1-Ud0yZXFUTjaqP3VkMLMTV23I/Hk="
    "resolved" "https://registry.npm.taobao.org/zip-stream/download/zip-stream-4.1.0.tgz?cache=0&sync_timestamp=1614749662821&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fzip-stream%2Fdownload%2Fzip-stream-4.1.0.tgz"
    "version" "4.1.0"
    dependencies:
      "archiver-utils" "^2.1.0"
      "compress-commons" "^4.1.0"
      "readable-stream" "^3.6.0"
