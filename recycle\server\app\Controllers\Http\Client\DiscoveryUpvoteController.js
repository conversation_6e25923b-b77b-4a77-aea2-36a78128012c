'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Discovery, DiscoveryUpvote } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { CryptUtil } = require('../../../Util')
const Env = use('Env')

//帖子点赞
class DiscoveryUpvoteController {
  //获取用户自身点赞信息
  async index({ request, response }) {
    let user = request.user
    let vo = await DiscoveryUpvote.query()
      .where('userID', user.id)
      .fetch()
    response.json(vo)
  }
  //给帖子点赞或取消点赞
  async store({ request, response }) {
    let { discoveryID } = request.all()
    let user = request.user
    if (!discoveryID) {
      throw ERR.INVALID_PARAMS
    }
    let discovery = await Discovery.find(discoveryID)
    if (!discovery) {
      throw ERR.RESTFUL_GET_ID
    }
    let vo = await DiscoveryUpvote.query()
      .where('discoveryID', discoveryID)
      .where('userID', user.id)
      .first()
    if (!vo) {
      let vo = await DiscoveryUpvote.create({ discoveryID, userID: user.id })
      discovery.likeCount = discovery.likeCount + 1
    } else {
      await vo.delete()
      discovery.likeCount = discovery.likeCount - 1
    }
    await discovery.save()
    response.json(vo)
  }
}

module.exports = DiscoveryUpvoteController
