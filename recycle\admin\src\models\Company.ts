import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'
import {
  ECityAddress,
  ECompanyArea,
  ECompanyEdit,
  EDelete,
  EDetDetail,
  EGet,
  EGetAddress,
  EGetIssues,
  EPost,
  EProvinceAddress,
  EPut,
  ETownSelected,
  EWorkerAreaEdit,
  EWorkerSelected,
  ECompanyEditArea,
  NCompany,
  RAdd,
  RSetState,
  EPostCompanyArea,
  EGetList,
  EGetPrice,
  EPutPrice,
  EPostPrice,
  EDelPrice,
} from '../common/action'
import { adapterPaginationResult } from '../common/utils'
import {
  getCompanyDetail,
  getAddress,
  CompanyEdit,
  getCompanyList,
  getCompanyArea,
  CompanyEditArea,
  CompanyPostArea,
  getCompanyArealist,
  getPrice,
  delPrice,
  postPrice,
  putPrice,
} from '../services/company'
import { getWorkerSelected, workerAreaEdit } from '../services/worker'

export default {
  namespace: NCompany,
  state: {
    companyDetail: null,
    companyTemp: null,
    tempName: null,
    isGetDetail: false,
    provinceList: [],
    cityList: [],
    isEdit: false,
    companyList: [],
    isGetArea: false,
    companyArea: [],
    townSelected: [],
    getSelected: false,
    getWorkerSelected: false,
    workerSelected: [],
    isEditWorker: false,
  },
  reducers: {
    [RSetState](state: any, payload: any) {
      return { ...state, ...payload }
    },
    [RAdd](state: { count: any }, payload: any) {
      return { ...state, count: state.count + payload }
    },
  },
  effects: {
    //  标准CURD示例
    async [EGet]({ payload }: any, { reducer }: any) {
      let result = await getCompanyList(payload)
      reducer(RSetState, { companyList: result?.data })
      return adapterPaginationResult(result)
    },
    async [EDetDetail]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isGetDetail: false })
      let result = await getCompanyDetail(payload)
      reducer(RSetState, { isGetDetail: true, companyDetail: result })
    },
    //获取省份
    async [EProvinceAddress]({ payload }: any, { reducer }: any) {
      let res = await getAddress(payload)
      reducer(RSetState, { provinceList: res })
    },
    //获取城市
    async [ECityAddress]({ payload }: any, { reducer }: any) {
      let res = await getAddress(payload)
      reducer(RSetState, { cityList: res })
    },
    async [ECompanyEdit]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isGetArea: false, isEdit: false })
      let res = await CompanyEdit(payload)
      reducer(RSetState, { companyArea: res, isGetArea: true, isEdit: true })
    },
    async [ECompanyArea]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isGetArea: false, isEdit: false })
      let res = await getCompanyArea(payload)
      reducer(RSetState, { companyArea: res, isGetArea: true, isEdit: true })
    },
    async [ETownSelected]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { getSelected: false })
      let res = await getCompanyArea(payload)
      reducer(RSetState, { townSelected: res, getSelected: true })
    },
    async [EWorkerAreaEdit]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isEditWorker: false })
      let res = await workerAreaEdit(payload)
      reducer(RSetState, { isEditWorker: true })
    },
    async [EWorkerSelected]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { getWorkerSelected: false })
      let res = await getWorkerSelected(payload)
      reducer(RSetState, { workerSelected: res, getWorkerSelected: true })
    },
    async [ECompanyEditArea]({ payload }: any, { reducer }: any) {
      const response = await CompanyEditArea(payload)
      return response
    },
    async [EPostCompanyArea]({ payload }: any, { reducer }: any) {
      const response = await CompanyPostArea(payload)
      return response
    },
    async [EGetList]({ payload }: any, { reducer }: any) {
      const response = await getCompanyArealist(payload)
      if (response && response.data[0] && response.data[0].company) {
        reducer(RSetState, { companyTemp: response.data[0].company,tempName: response?.data[0]?.company?.companyName})
      }
      return adapterPaginationResult(response)
    },

    async [EGetPrice]({ payload }: any, { reducer }: any) {
      const response = await getPrice(payload)
      return response
    },
    async [EPutPrice]({ payload }: any, { reducer }: any) {
      const response = await putPrice(payload)
      return response
    },
    async [EPostPrice]({ payload }: any, { reducer }: any) {
      const response = await postPrice(payload)
      return response
    },
    async [EDelPrice]({ payload }: any, { reducer }: any) {
      const response = await delPrice(payload)
      return response
    },
  },
}
