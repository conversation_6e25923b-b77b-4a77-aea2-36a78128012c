'use strict'

const Model = use('Model')


class PriceSet extends Model {
  static get table() {
    return 'price_set'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return "createdAt"
  }
  static get updatedAtColumn() {
    return "updatedAt"
  }
  company(){
    return this.hasOne('App/Models/Company', 'companyID', 'id').select('id', 'companyName', 'province', 'city')
  }
}

module.exports = PriceSet
