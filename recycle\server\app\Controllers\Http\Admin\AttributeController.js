'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Attribute } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//废品属性
class AttributeController {
  async index({ request, response }) {
    let { current = 1, pageSize = 10, id } = request.all()
    let query = Attribute.query().where('typeID', id)
    let vo = await query.paginate(current, pageSize)
    response.json(vo)
  }
  async show({ request, params, response }) {
    let vo = await Attribute.query()
      .where('typeID', params.id)
      .fetch()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
  async store({ request, response }) {
    let { list } = request.all()
    if (!list) {
      throw ERR.INVALID_PARAMS
    }
    _.forEach(list, async function(value) {
      let vo = await Attribute.find(value.id)
      _.assign(vo, value)
      await vo.save()
    })
    response.json({ result: 'ok' })
  }
  async update({ request, params, response }) {
    let vo = await Attribute.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
  }
  async destroy({ request, params, response }) {
    let vo = await Attribute.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_DELETE_ID
    }
    await vo.delete()
    response.json(vo)
  }
}

module.exports = AttributeController
