import { useEffect, useState, useRef } from 'react'
import { notification, Select, Button, message, Modal, Space } from 'antd'
import styles from './index.module.less'
import { effect, useConnect } from 'dva17'
import {
    NCompany,
    EGetTownAddress,
    EWorkerSelected,
    NWorker,
    EWorkerAreaEdit,
} from '../../common/action'
import { filter, isEqualWith } from 'lodash'
import { PlusOutlined } from '@ant-design/icons'
import { workerDeleteArea } from '../../services/worker'
import { recycleType, recycleTypeApp } from '../../common/enum'
import type { ActionType } from '@ant-design/pro-table'
import ProTable from '@ant-design/pro-table'
import AreaCreateModal from './components/AreaCreateModal'

const { Option } = Select

interface WorkerAreaEditProps {
    visible: boolean;
    workerId: number;
    workerDetail: any;
    onClose: () => void;
    onSuccess: () => void;
}

const WorkerAreaEdit = ({ visible, workerId, workerDetail, onClose, onSuccess }: WorkerAreaEditProps) => {
    /*--------------------- 常变量 ---------------------*/
    const { addressList, townAddressList, theWorkerList } = useConnect(NWorker)
    const { cityList } = useConnect(NCompany)

    // 区域列表相关状态
    const [visibleAreaCreate, setVisibleAreaCreate] = useState(false);
    const [visibleTypeEdit, setVisibleTypeEdit] = useState(false);
    const [editData, setEditData] = useState<any>(null);
    const [removeList, setRemoveList] = useState<any>([]);
    const [typeList, setTypeList] = useState<any>([]);
    const [addList, setAddList] = useState<any>([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>();
    const [selectedRow, setSelectedRow] = useState<any[]>();
    const actionRef = useRef<ActionType>();
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    
    /*--------------------- 生命周期 ---------------------*/
    useEffect(() => {
        let objSelect = filter(addressList, { value: true })
        if (addressList && objSelect.length > 0) {
            effect(NWorker, EGetTownAddress, {
                codeArr: objSelect.map((vo: any) => vo.code),
            })
        }
    }, [addressList])
    
    useEffect(() => {
        let hide: any = function () { }
        if (visible && workerId) {
            if (currentPage !== 1) {
                hide = message.loading('正在加载')
            }
            // 加载区域列表数据
            effect(NWorker, EWorkerSelected, { 
                id: workerId,
                current: currentPage,
                pageSize: pageSize
            }).then(() => {
                hide()
            })
        }
    }, [visible, workerId, currentPage, pageSize])

    useEffect(() => {
        if (editData) {
            setTypeList(editData.typeInfo)
        }
    }, [editData])

    // 计算回收类型的变化
    const computeType = (e: any) => {
        let listItem: any = []
        e.forEach((item: any, index: number) => {
            let len = (filter(recycleType, { typeName: item })).length
            if (len > 0) {
                let vo = {
                    areaCode: editData.areaCode,
                    code: editData.code,
                    area: editData.area,
                    name: editData.name,
                    type: filter(recycleType, { typeName: item })[0].type,
                    typeName: item
                }
                listItem.push(vo)
            } else {
                let vo = {
                    areaCode: editData.areaCode,
                    code: editData.code,
                    area: editData.area,
                    name: editData.name,
                    detail: filter(recycleTypeApp, { typeName: item })[0].id,
                    detailName: item,
                    type: 3,
                    typeName: "大家电"
                }
                listItem.push(vo)
            }
        })
        let equal = isEqualWith(typeList, listItem)
        if (equal) {
            return
        } else {
            setAddList(listItem)
            setRemoveList(typeList)
        }
    }

    // 刷新区域列表数据
    const refreshPage = () => {
        if (actionRef.current) {
            actionRef.current.reload()
        }
        // 重新加载当前页数据
        effect(NWorker, EWorkerSelected, { 
            id: workerId,
            current: currentPage,
            pageSize: pageSize
        });
        onSuccess()
    }

    // 删除区域
    const deleteCompanyItem = (e: any) => {
        Modal.confirm({
            title: '确认删除该区域',
            content: <div>删除后数据无法恢复！</div>,
            okText: '确认删除',
            cancelText: '退出',
            onOk: async () => {
                if (e && e.length > 0) {
                    await workerDeleteArea({ arrIds: e, workerID: workerId }).then(() => {
                        // 返回第一页
                        setCurrentPage(1);
                        refreshPage()
                        notification.success({
                            message: '成功！',
                            description: '删除成功',
                            duration: 2,
                        })
                    })
                } else {
                    await workerDeleteArea({ code: e.code, workerID: workerId }).then(() => {
                        // 返回第一页
                        setCurrentPage(1);
                        refreshPage()
                        notification.success({
                            message: '成功！',
                            description: '删除成功',
                            duration: 2,
                        })
                    })
                }
            },
            width: 700,
        })
    }

    // 处理类型编辑保存
    const handleTypeEditOK = () => {
        if (addList.length > 0 || removeList.length > 0) {
            effect(NCompany, EWorkerAreaEdit, {
                workerID: workerId,
                companyID: editData.companyID,
                addList: addList,
                removeList: removeList,
            }).then(() => {
                setVisibleTypeEdit(false)
                setEditData(null)
                // 返回第一页
                setCurrentPage(1);
                refreshPage()
            })
        }
    }

    // 表格选择行变化
    const onSelectChange = (keys: any, selectedRows: any) => {
        setSelectedRowKeys(keys);
        setSelectedRow(selectedRows.map((vo: any) => vo.code));
    };

    // 表格选择配置
    const rowSelection = {
        selectedRowKeys: selectedRowKeys,
        onChange: onSelectChange
    };

    // 新增服务区域成功回调
    const handleCreateSuccess = () => {
        // 返回第一页
        setCurrentPage(1);
        refreshPage();
        setVisibleAreaCreate(false);
    }

    // 区域列表表格列配置
    const columns = [
        {
            title: '省份',
            dataIndex: ['address', 'province', 'name'],
            search: false,
        },
        {
            title: '城市',
            dataIndex: ['address', 'city', 'name'],
            search: false,
        },
        {
            title: '区县',
            dataIndex: 'area',
            search: false,
        },
        {
            title: '街道',
            dataIndex: 'name',
            search: false,
        },
        {
            title: '操作',
            width: '20%',
            search: false,
            render: (_: any, row: any) => (
                <>
                    <Button
                        onClick={() => {
                            setEditData(row)
                            setVisibleTypeEdit(true)
                        }}
                        style={{ marginRight: 8 }}>
                        编辑
                    </Button>
                    <Button
                        danger
                        onClick={() => {
                            deleteCompanyItem(row)
                        }}>
                        删除
                    </Button>
                </>
            ),
        },
    ];

    /*--------------------- 渲染 ---------------------*/
    return (
        <Modal
            title={`${workerDetail?.workerName || ''}-服务区域管理`}
            open={visible}
            onCancel={onClose}
            width={1000}
            footer={null}
        >
            <div className={styles.main}>
                <div className={styles.tableWrapper}>
                    <ProTable
                        actionRef={actionRef}
                        columns={columns as any}
                        dataSource={theWorkerList?.data}
                        pagination={{
                            showSizeChanger: true,
                            pageSize: pageSize,
                            current: currentPage,
                            defaultPageSize: 10,
                            showQuickJumper: true,
                            total: theWorkerList?.total || 0,
                            showTotal: (total) => `共 ${total} 条`,
                            onChange: (page, size) => {
                                setCurrentPage(page);
                                if (size) setPageSize(size);
                            }
                        }}
                        rowSelection={{
                            type: 'checkbox',
                            ...rowSelection,
                        }}
                        tableAlertRender={({ selectedRowKeys, selectedRows, onCleanSelected }) => (
                            <Space size={24}>
                                <span>
                                    已选 {selectedRowKeys.length} 项
                                    <a style={{ marginLeft: 8 }} onClick={onCleanSelected}>
                                        取消选择
                                    </a>
                                </span>
                            </Space>
                        )}
                        tableAlertOptionRender={() => {
                            return (
                                <Space size={16}>
                                    <a onClick={() => {
                                        deleteCompanyItem(selectedRow)
                                    }}>批量删除</a>
                                </Space>
                            );
                        }}
                        rowKey="id"
                        search={false}
                        dateFormatter="string"
                        headerTitle={`${workerDetail?.workerName || ''}-服务区域`}
                        toolBarRender={() => [
                            <Button
                                key="add"
                                type="primary"
                                onClick={() => setVisibleAreaCreate(true)}>
                                <PlusOutlined />
                                新建服务区域
                            </Button>
                        ]}
                    />
                </div>

                {/* 服务类型编辑弹窗 */}
                <Modal
                    open={visibleTypeEdit}
                    title="服务类型选择"
                    destroyOnClose
                    onOk={handleTypeEditOK}
                    onCancel={() => {
                        setVisibleTypeEdit(false)
                        setEditData(null)
                    }}>
                    <Select
                        mode="multiple"
                        placeholder="选择回收类型!"
                        style={{ width: '100%' }}
                        defaultValue={editData?.typeInfo?.map((vo: any) => {
                            if (vo.detailName) {
                                return vo.detailName
                            } else {
                                return vo.typeName
                            }
                        })}
                        onChange={(e: any) => {
                            computeType(e)
                        }}
                    >
                        {recycleTypeApp.map((vo: any, index: number) => (
                            <Select.Option
                                label={vo.typeName}
                                key={index}
                                value={vo.typeName}>
                                {vo.typeName}
                            </Select.Option>
                        ))}
                    </Select>
                </Modal>

                {/* 新建服务区域 */}
                <AreaCreateModal 
                    visible={visibleAreaCreate}
                    workerDetail={workerDetail} 
                    onCancel={() => setVisibleAreaCreate(false)}
                    onSuccess={handleCreateSuccess}
                    workerId={workerId}
                />
            </div>
        </Modal>
    )
}

export default WorkerAreaEdit 