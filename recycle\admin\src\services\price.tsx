import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'

export async function getFirstKind(payload: any) {
  return requestGet('/Waste1stCategory')
}
export async function getSecondKind(payload: any) {
  return requestGet(`/Waste1stCategory/${payload.id}`)
}
export async function getThirdKind(payload: any) {
  return requestGet(`/waste2ndCategory/${payload.id}`)
}
export async function getAttributeType(payload: any) {
  return requestGet(`/attributeType/${payload.id}`)
}
export async function getAttribute(payload: any) {
  return requestGet(`/attribute/${payload.id}`)
}
export async function changeThirdPrice(payload: any) {
  return requestPost(`/waste`, payload)
}
export async function changePlusPrice(payload: any) {
  return requestPost(`/attribute`, payload)
}
export async function getCompanySelfWasteList(payload: any) {
  return requestGet('companySelfWaste', payload)
}
export async function getCommission(payload: any) {
  return requestGet(`/priceSet`, payload)
}
export async function postCommission(payload: any) {
  return requestPost(`/priceSet`, payload)
}
export async function postBundlePrice(payload: any) {
  return requestPost(`/bundlePriceSet`, payload)
}
export async function putCommission(payload: any) {
  return requestPut(`priceSet/${payload.id}`, payload)
}
export async function delCommission(payload: any) {
  return requestDelete(`priceSet/${payload.id}`)
}
export async function changeSelfCompany(payload: any) {
  return requestPost('companySelfWaste', payload)
}
export async function getCompanySelfAttributeList(payload: any) {
  return requestGet('companySelfAttribute', payload)
}
export async function changeSelfCompanyAttribute(payload: any) {
  return requestPost('companySelfAttribute', payload)
}
export async function getThirdKindDing(payload: any) {
  return requestGet(`wasteSentinel/${payload.id}`)
}
export async function changeThirdPriceDing(payload: any) {
  return requestPut(`wasteSentinel/${payload.id}`, payload)
}
export async function saveCommmisson(payload: any) {
  return requestPost('orderWaste', payload)
}
export async function getClientCommission(payload: any) {
  return requestGet(`/clientCommission`, payload)
}
export async function postClientCommission(payload: any) {
  return requestPost(`/clientCommission`, payload)
}
export async function putClientCommission(payload: any) {
  return requestPut(`/clientCommission/${payload.id}`, payload)
}
export async function delClientCommission(payload: any) {
  return requestDelete(`/clientCommission/${payload.id}`)
}
export async function postBundleClientPrice(payload: any) {
  return requestPost(`/bundleClientPrice`, payload)
}
export async function getClientEstimate(payload: any) {
  return requestGet('/clientEstimate', payload);
}
export async function postClientEstimate(payload: any) {
  return requestPost('/clientEstimate', payload);
}
export async function putClientEstimate(payload: any) {
  const { id, ...data } = payload;
  return requestPut(`/clientEstimate/${id}`, data);
}
export async function deleteClientEstimate(payload: any) {
  return requestDelete(`/clientEstimate/${payload.id}`);
}


// 批量修改佣金
export async function batchUpdateCommission(payload: any) {
  return requestPost('/priceSetbundleUpdate', payload)
}

// 获取佣金修改记录列表
export async function getPriceSetLogList(payload: any) {
  return requestGet('/priceSetLog', payload)
}

// 获取菜鸟补扣记录列表
export async function getCNChargeList(payload: any) {
  return requestGet('/CNWalletlog', payload)
}