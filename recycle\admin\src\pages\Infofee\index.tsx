import {
  notification, Modal, Button, Input, Form, Select
} from 'antd'
import type { ActionType, ProColumns } from '@ant-design/pro-table'
import ProTable, { } from '@ant-design/pro-table'
import ProCard from '@ant-design/pro-card'
import { useEffect, useRef, useState } from 'react'
import { PriceLevelEnum, PriceTypeEnum, } from '../../common/enum'
import styles from './index.module.less'
import { effect, useConnect, } from 'dva17'
import {
  NJDOrder,
  EGetPrice,
  EPutPrice,
  EPostPrice
} from '../../common/action'
import 'antd/dist/antd.css';
import { SERVER_HOME } from '../../common/config'
import qs from 'qs'
import { ExportOutlined, PlusOutlined } from '@ant-design/icons'

type Item = {
  id: number
  province: string
  city: string
  price: number
  four: string
  thrid: string
  forbidden: number
}

// 定义枚举类型的接口
interface EnumType {
  [key: string]: { text: string; status: string }
}

const { Search } = Input;
export default () => {
  const actionRef = useRef<ActionType>()
  const { searchQuery } = useConnect(NJDOrder)
  const [form] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
  }, [])
  /*--------------------- 响应 ---------------------*/
  const refreshPage = () => {
    if (actionRef.current) {
      actionRef.current.reload()
    }
  }
  
  const seeDetail = (row: any) => {
    if (row.id) {
      // 编辑模式
      setIsEditing(true);
      form.setFieldsValue(row);
    } else {
      // 新增模式
      setIsEditing(false);
      form.resetFields();
    }
    setIsModalVisible(true);
  }

  const handleExport = async () => {
    try {
      notification.info({
        message: '导出中',
        description: '正在准备导出文件，请稍候...',
      })
      let qsQuery = qs.stringify(searchQuery)
      window.open(
        `${SERVER_HOME}exportJDPrice?${qsQuery}`
      )

      notification.success({
        message: '导出成功',
        description: '数据已成功导出为Excel文件',
      })
    } catch (error) {
      notification.error({
        message: '导出失败',
        description: '导出数据时发生错误',
      })
    }
  }

  const handleNew = () => {
    setIsEditing(false);
    form.resetFields();
    setIsModalVisible(true);
  }

  /*--------------------- 渲染 ---------------------*/
  const columns: ProColumns<Item>[] = [
    { title: '同步类型', valueEnum: PriceTypeEnum, dataIndex: 'type', copyable: false, ellipsis: true, },
    { title: '城市等级', dataIndex: 'level', copyable: false, ellipsis: true, valueEnum: PriceLevelEnum, },
    { title: '三级品类', dataIndex: 'thrid', search: false, copyable: false, ellipsis: true, },
    { title: '四级品类', dataIndex: 'four', copyable: false, ellipsis: true, },
    { title: '省份', dataIndex: 'province', copyable: false, ellipsis: true, },
    { title: '城市', dataIndex: 'city', copyable: false, ellipsis: true, },
    { title: '区', search: false, dataIndex: 'town', copyable: false, ellipsis: true, },
    {
      title: '信息费', dataIndex: 'price', copyable: false, search: false,
      sorter: (a, b) => a.price - b.price,
    },
    {
      title: '信息费',
      dataIndex: 'price',
      copyable: false,
      ellipsis: true,
      valueType: 'digitRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            lowPrice: value[0],
            hightPrice: value[1],
          }
        },
      },
    },
    {
      title: '操作', width: '18%', copyable: false, ellipsis: true, search: false,
      render: (_, row: any) => {
        return (
          <div>
            <Button
              onClick={() => {
                seeDetail(row)
              }}>
              編輯
            </Button>
          </div >
        )
      },
    },
  ]

  return (
    <div>
      <ProCard>
        <ProTable<Item>
          actionRef={actionRef}
          columns={columns}
          request={async (params = {}, sorter) => (await effect(NJDOrder, EGetPrice, { ...params, ...sorter })) as any}
          pagination={{
          }}
          rowKey="id"
          dateFormatter="string"
          headerTitle=""
          toolBarRender={() => [
            <Button
              icon={<ExportOutlined />}
              key="export"
              type="primary"
              onClick={handleExport}
            >
              导出数据
            </Button>,
            <Button
              icon={<PlusOutlined />}
              key="new"
              onClick={handleNew}
            >
              新增
            </Button>
          ]}
        />
      </ProCard>

      <Modal
        title={isEditing ? "编辑信息费" : "新增信息费"}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        footer={[
          <Button key="cancel" onClick={() => {
            setIsModalVisible(false);
            form.resetFields();
          }}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={async () => {
            try {
              const values = await form.validateFields();
              
              if (isEditing) {
                // 修改价格
                await effect(NJDOrder, EPutPrice, { id: form.getFieldValue('id'),
                  type: values.type,
                  level: values.level,
                  thrid: values.thrid,
                  four: values.four,
                  province: values.province,
                  city: values.city,
                  town: values.town,
                  price: values.price });
                notification.success({
                  message: "成功",
                  description: '修改成功！',
                  duration: 2
                });
              } else {
                // 新增信息费
                await effect(NJDOrder, EPostPrice, values);
                notification.success({
                  message: "成功",
                  description: '新增成功！',
                  duration: 2
                });
              }
              
              setIsModalVisible(false);
              form.resetFields();
              effect(NJDOrder, EGetPrice, { pageSize: 20 });
              refreshPage();
            } catch (error) {
              console.error('提交表单出错:', error);
            }
          }}>
            确定
          </Button>,
        ]}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          name="infoFeeForm"
          initialValues={{ type: '同步' }}
        >
          <Form.Item name="id" hidden>
            <Input />
          </Form.Item>
          
          <Form.Item
            label="同步类型"
            name="type"
            rules={[{ required: true, message: '请选择同步类型' }]}
          >
            <Select>
              {Object.keys(PriceTypeEnum as EnumType).map(key => (
                <Select.Option key={key} value={key}>
                  {(PriceTypeEnum as EnumType)[key].text}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            label="城市等级"
            name="level"
            rules={[{ required: true, message: '请选择城市等级' }]}
          >
            <Select>
              {Object.keys(PriceLevelEnum as EnumType).map(key => (
                <Select.Option key={key} value={key}>
                  {(PriceLevelEnum as EnumType)[key].text}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            label="三级品类"
            name="thrid"
            rules={[{ required: false, message: '请输入三级品类' }]}
          >
            <Input placeholder="请输入三级品类" />
          </Form.Item>
          
          <Form.Item
            label="四级品类"
            name="four"
            rules={[{ required: true, message: '请输入四级品类' }]}
          >
            <Input placeholder="请输入四级品类" />
          </Form.Item>
          
          <Form.Item
            label="省份"
            name="province"
            rules={[{ required: true, message: '请输入省份' }]}
          >
            <Input placeholder="请输入省份" />
          </Form.Item>
          
          <Form.Item
            label="城市"
            name="city"
            rules={[{ required: false, message: '请输入城市' }]}
          >
            <Input placeholder="请输入城市" />
          </Form.Item>
          
          <Form.Item
            label="区"
            name="town"
          >
            <Input placeholder="请输入区" />
          </Form.Item>
          
          <Form.Item
            label="信息费"
            name="price"
            rules={[{ required: true, message: '请输入信息费' }]}
          >
            <Input type="number" placeholder="请输入信息费" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}
