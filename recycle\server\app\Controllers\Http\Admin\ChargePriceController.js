'use strict'

const _ = require('lodash')
const {  ReqLog, ChargePrice } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

class ChargePriceController {
  async index({ request, response }) {
    let { current = 1, pageSize = 10, area, sort = 'desc' } = request.all()
    let query = ChargePrice.query()
    if (area) {
      query.whereRaw('area like ?', [`%${area}%`])
    }
    let vo = await query.orderBy('id', sort).paginate(current, pageSize)
    response.json(vo)
  }
  async show({ request, params, response }) {
    let vo = await ChargePrice.query()
      .where('id', params.id)
      .first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
  async store({ request, response }) {
    let { adminUser: user } = request
    if (user.level !== E.AdminLevel.总部) {
      throw ERR.USER_ROLE_NO_PRIVILEGE
    }
    let { area, lowCharger } = request.all()
    if (!area || !lowCharger) {
      throw ERR.INVALID_PARAMS
    }
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台最低价格创建' })
    let vo = await ChargePrice.create({
      area,
      lowCharger
    })
    response.json(vo)
  }
  async update({ request, params, response }) {
    let { adminUser: user } = request
    if (user.level !== E.AdminLevel.总部) {
      throw ERR.USER_ROLE_NO_PRIVILEGE
    }
    let { area, lowCharger } = request.all()
    let vo = await ChargePrice.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
  }
  async destroy({ request, params, response }) {
    let { adminUser: user } = request
    if (user.level !== E.AdminLevel.总部) {
      throw ERR.USER_ROLE_NO_PRIVILEGE
    }
    let vo = await ChargePrice.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_DELETE_ID
    }
    await vo.delete()
    response.json(vo)
  }
}

module.exports = ChargePriceController
