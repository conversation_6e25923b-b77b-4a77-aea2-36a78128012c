# 嗨回收数据分布API测试文档

## API端点概览

本文档描述了新增的嗨回收数据分布相关API接口的测试方法。

### 基础信息
- **服务器地址**: `http://your-domain.com`
- **API前缀**: `/admin/v1`
- **认证**: 无需登录认证
- **请求方法**: GET
- **响应格式**: JSON

## 接口详情

### 1. 获取省份分布数据

**端点**: `GET /admin/v1/getHiProvinceCharts`

**参数**:
- `startDate` (可选): 开始日期，格式 YYYY-MM-DD，默认为30天前
- `endDate` (可选): 结束日期，格式 YYYY-MM-DD，默认为明天

**请求示例**:
```bash
# 获取默认时间范围的省份分布
curl -X GET "http://your-domain.com/admin/v1/getHiProvinceCharts"

# 获取指定时间范围的省份分布
curl -X GET "http://your-domain.com/admin/v1/getHiProvinceCharts?startDate=2024-01-01&endDate=2024-01-31"
```

**响应示例**:
```json
[
  {
    "province": "北京",
    "count": 150
  },
  {
    "province": "上海", 
    "count": 120
  },
  {
    "province": "广东",
    "count": 200
  }
]
```

### 2. 获取渠道分布数据

**端点**: `GET /admin/v1/getHiChannelCharts`

**参数**:
- `startDate` (可选): 开始日期，格式 YYYY-MM-DD
- `endDate` (可选): 结束日期，格式 YYYY-MM-DD

**请求示例**:
```bash
# 获取默认时间范围的渠道分布
curl -X GET "http://your-domain.com/admin/v1/getHiChannelCharts"

# 获取指定时间范围的渠道分布  
curl -X GET "http://your-domain.com/admin/v1/getHiChannelCharts?startDate=2024-01-01&endDate=2024-01-31"
```

**响应示例**:
```json
[
  {
    "channel": "嗨回收",
    "count": 300
  },
  {
    "channel": "微信小程序",
    "count": 150
  },
  {
    "channel": "官网",
    "count": 80
  }
]
```

### 3. 获取品类分布数据

**端点**: `GET /admin/v1/getHiCategoryCharts`

**参数**:
- `startDate` (可选): 开始日期，格式 YYYY-MM-DD
- `endDate` (可选): 结束日期，格式 YYYY-MM-DD

**请求示例**:
```bash
# 获取默认时间范围的品类分布
curl -X GET "http://your-domain.com/admin/v1/getHiCategoryCharts"

# 获取指定时间范围的品类分布
curl -X GET "http://your-domain.com/admin/v1/getHiCategoryCharts?startDate=2024-01-01&endDate=2024-01-31"
```

**响应示例**:
```json
[
  {
    "category": "冰箱",
    "count": 180
  },
  {
    "category": "洗衣机",
    "count": 150
  },
  {
    "category": "电视",
    "count": 120
  },
  {
    "category": "空调",
    "count": 100
  }
]
```

## Postman测试配置

### 环境变量设置
1. 创建新环境，添加变量：
   - `baseUrl`: `http://your-domain.com`
   - `adminPrefix`: `/admin/v1`

### 测试集合

#### Collection: 嗨回收数据分布API

**Request 1: 省份分布**
- Method: GET
- URL: `{{baseUrl}}{{adminPrefix}}/getHiProvinceCharts`
- Params: 
  - startDate: 2024-01-01 (可选)
  - endDate: 2024-01-31 (可选)

**Request 2: 渠道分布**
- Method: GET  
- URL: `{{baseUrl}}{{adminPrefix}}/getHiChannelCharts`
- Params:
  - startDate: 2024-01-01 (可选)
  - endDate: 2024-01-31 (可选)

**Request 3: 品类分布**
- Method: GET
- URL: `{{baseUrl}}{{adminPrefix}}/getHiCategoryCharts`
- Params:
  - startDate: 2024-01-01 (可选)
  - endDate: 2024-01-31 (可选)

## 错误处理

### 错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

### 常见错误码
- **500**: 服务器内部错误
- **400**: 参数格式错误
- **404**: 接口不存在

## 数据验证检查点

### 1. 数据格式验证
- 确保返回数据是数组格式
- 确保每个元素包含必要字段
- 确保数值字段为数字类型

### 2. 业务逻辑验证
- 验证统计数据的合理性
- 检查时间范围筛选是否生效
- 确认数据按count降序排列

### 3. 性能测试
- 测试大时间范围查询的响应时间
- 监控数据库查询性能
- 验证并发请求的稳定性

## 前端集成测试

### JavaScript测试代码
```javascript
// 测试省份分布API
async function testHiProvinceCharts() {
  try {
    const response = await fetch('/admin/v1/getHiProvinceCharts?startDate=2024-01-01&endDate=2024-01-31');
    const data = await response.json();
    console.log('省份分布数据:', data);
    
    // 验证数据格式
    if (Array.isArray(data) && data.length > 0) {
      const firstItem = data[0];
      if (firstItem.province && typeof firstItem.count === 'number') {
        console.log('✅ 省份分布API测试通过');
      } else {
        console.log('❌ 数据格式不正确');
      }
    }
  } catch (error) {
    console.error('❌ 省份分布API测试失败:', error);
  }
}

// 依次测试所有接口
testHiProvinceCharts();
```

## 注意事项

1. **数据库连接**: 确保服务器能正常连接到数据库
2. **数据存在性**: 确保 `hi_order` 表中有测试数据
3. **字段完整性**: 确保相关字段（province, from, type）有数据
4. **时区处理**: 注意日期参数的时区转换
5. **缓存策略**: 考虑为频繁查询的数据添加缓存

---
*此文档用于测试和验证嗨回收数据分布API的功能和性能* 