'use strict'

const Model = use('Model')

//订单评价
class ClientOrderComplaint extends Model {
  static get table() {
    return 'client_order_complaint'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return null
  }
  order() {
    return this.belongsTo('App/Models/ClientOrder', 'orderID', 'id')
  }
  user() {
    return this.belongsTo('App/Models/User', 'userID', 'id')
  }
  worker() {
    return this.belongsTo('App/Models/Worker', 'workerID', 'id')
  }
  company() {
    return this.belongsTo('App/Models/Company', 'companyID', 'id')
  }
}

module.exports = ClientOrderComplaint
