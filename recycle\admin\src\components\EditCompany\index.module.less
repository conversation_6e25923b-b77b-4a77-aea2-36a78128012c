
.modalTitle {
  font-size: 18px;
  color: #606266;
  line-height: 40px;
  box-sizing: border-box;
  font-weight: 600;
  margin-right: 10px;
  // border:1px solid red;
}
.itemType {
  width: 100%;
  padding: 10px 20px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // border:1px solid red;
  .itemType_text {
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
  }
}
.modalTitle_ {
  padding: 10px 0px;
  font-size: 14px;
  color: #606266;
  box-sizing: border-box;
}
.imgWrapper {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  img {
    width: 200px;
    height: 200px;
    border-radius: 4px;
    margin-right: 10px;
    margin-bottom: 10px;
  }
}
.newCompany {
  display: flex;
  flex-direction: column;
  width: 100%;
  // border:1px solid red;
  .newCompany_item {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 10px 0;
    .newCompany_item_title {
      display: inline-block;
      text-align: right;
      font-size: 14px;
      color: #606266;
      line-height: 40px;
      text-align: right;
      width: 110px;
    }
  }
}
.item {
  display: flex;
  margin-bottom: 20px;
  letter-spacing: 0.8px;
  // flex-wrap: wrap;
  flex-direction: column;
  // border:1px solid red;
  .item_title {
    display: inline-block;
    width: 90px;
    text-align: right;
    font-weight: 600;
  }
  .item_content {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    .item_content_type {
      display: inline-block;
      width: 110px;
      height: 34px;
      line-height: 34px;
      text-align: center;
      border-radius: 4px;
      border: 1px solid #1890ff;
      color: #1890ff;
      margin: 0 8px 8px 0;
    }
  }
}
.newCompany_item_password {
  margin: 0 auto;
  margin-top: 20px;
  .newCompany_item_title {
    display: inline-block;
    width: 80px;
    height: 32px;
    line-height: 32px;
    text-align: right;
  }
}
.item_wrapper {
  .itemx {
    display: flex;
    margin-bottom: 20px;
    letter-spacing: 0.8px;
    //border-bottom: 1px solid #cccccc;
    &:last-child {
      border-bottom: none;
    }
    .item_title {
      display: inline-block;
      width: 90px;
      text-align: right;
      font-weight: 600;
    }
    .item_content {
      display: flex;
      flex: 1;
    }
  }
}
