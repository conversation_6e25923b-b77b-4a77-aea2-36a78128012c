/**
 * 地图服务配置文件
 */

// 腾讯地图API密钥
export const TENCENT_MAP_KEY = 'RLHBZ-WMPRP-Q3JDS-V2IQA-JNRFH-EJBHL';
// 备用key:H3OBZ-47M6W-COSRA-RZBH2-VWXSJ-ZLBSA
// DJ6BZ-XS462-RXUUG-CQHZK-QGYP3-TMFVV
// SX3BZ-XEYKB-ZJAUQ-JUTUW-XEDIJ-CDBBU
// RAIBZ-4LAKZ-UDDXY-ZVPG3-TGW2T-W6BAG
// CAGBZ-QKNWN-V6IFT-SO6L5-RAVPS-TIBT6
// CFVBZ-7IXLU-2UAVC-2CJMC-A4BHE-MSFZ5
// 地图服务类型
export const MAP_TYPE = {
  TENCENT: 'tencent',
  AMAP: 'amap',
};

// 当前使用的地图服务
export const CURRENT_MAP = MAP_TYPE.TENCENT;

// 地图API接口
export const MAP_API = {
  //  定位转地址
  GEOCODER: 'https://apis.map.qq.com/ws/geocoder/v1/', // 逆地理编码
  // 省市区县
  DISTRICT: 'https://apis.map.qq.com/ws/district/v1/list', // 省市区县
  // 省市区县关键词搜索
  SEARCH: 'https://apis.map.qq.com/ws/district/v1/search', // 省市区县搜索
  //  获取下级行政区划
  CHILDREN: 'https://apis.map.qq.com/ws/district/v1/getchildren', // 获取下级行政区划
};

























