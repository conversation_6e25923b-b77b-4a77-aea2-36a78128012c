import { PlusOutlined } from '@ant-design/icons'
import { Button, message, Input, Drawer } from 'antd'
import React, { useState, useRef, useEffect } from 'react'
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout'
import type { ProColumns, ActionType } from '@ant-design/pro-table'
import ProTable from '@ant-design/pro-table'
import { ModalForm, ProFormText, ProFormTextArea } from '@ant-design/pro-form'
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions'
import ProDescriptions from '@ant-design/pro-descriptions'
import type { FormValueType } from './UpdateForm'
import UpdateForm from './UpdateForm'
import { effect } from 'dva17'
import { EDelete, EGet, EPost, EPut, NCompany } from '../../common/action'

const TableList: React.FC = () => {
  /** 新建窗口的弹窗 */
  const [createModalVisible, handleModalVisible] = useState<boolean>(false)
  /** 分布更新窗口的弹窗 */
  const [showDetail, setShowDetail] = useState<boolean>(false)
  const actionRef = useRef<ActionType>()
  const [currentRow, setCurrentRow] = useState<any>()
  const [selectedRowsState, setSelectedRows] = useState<any>([])
  /** 国际化配置 */

  const columns: ProColumns<any>[] = [
    {
      title: '规则名称',
      dataIndex: 'name',
      tip: '规则名称是唯一的 id',
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity)
              setShowDetail(true)
            }}>
            {dom}
          </a>
        )
      },
    },
    {
      title: '描述',
      dataIndex: 'desc',
      valueType: 'textarea',
    },
    {
      title: '服务调用次数',
      dataIndex: 'callNo',
      sorter: true,
      hideInForm: true,
      renderText: (val: string) => `${val}${'万'}`,
    },
    {
      title: '状态',
      dataIndex: 'status',
      hideInForm: true,
      valueEnum: {
        0: {
          text: '关闭',
          status: 'Default',
        },
        1: {
          text: '运行中',
          status: 'Processing',
        },
        2: {
          text: '已上线',
          status: 'Success',
        },
        3: {
          text: '异常',
          status: 'Error',
        },
      },
    },
    {
      title: '上次调度时间',
      sorter: true,
      dataIndex: 'updatedAt',
      valueType: 'dateTime',
      renderFormItem: (item, { defaultRender, ...rest }, form) => {
        const status = form.getFieldValue('status')

        if (`${status}` === '0') {
          return false
        }

        if (`${status}` === '3') {
          return <Input {...rest} placeholder={'请输入异常原因！'} />
        }

        return defaultRender(item)
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            setCurrentRow(record)
          }}>
          配置
        </a>,
        <a key="subscribeAlert" href="https://procomponents.ant.design/">
          订阅警报
        </a>,
      ],
    },
  ]

  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {}, [])

  /*--------------------- 响应 ---------------------*/
  const handleAdd = async (fields: any) => {
    const hide = message.loading('正在添加')

    // 执行api
    // await effect(NDemo, EPost, fields)

    hide()
    message.success('添加成功')
    handleModalVisible(false)

    if (actionRef.current) {
      actionRef.current.reload()
    }
  }
  const handleUpdate = async (value: any) => {
    console.log('handleUpdate: ', value)
    const hide = message.loading('正在更新')

    // 执行api
    // await effect(NDemo, EPut, { ...value, id: currentRow.id })

    hide()
    message.success('更新成功')
    setCurrentRow(undefined)
    if (actionRef.current) {
      actionRef.current.reload()
    }
  }
  const handleRemove = async (selectedRows: any) => {
    const hide = message.loading('正在删除')
    for (let row of selectedRows) {
      // await effect(NDemo, EDelete, row.id)
    }
    hide()
    message.success('删除成功，即将刷新')
  }

  /*--------------------- 渲染 ---------------------*/
  return (
    <PageContainer>
      <ProTable<any, any>
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true)
            }}>
            <PlusOutlined /> 新建
          </Button>,
        ]}
        request={async (params = {}) => (await effect(NCompany, EGet, params)) as any}
        pagination={{
          pageSize: 2,
        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows)
          },
        }}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              已选择{' '}
              <a
                style={{
                  fontWeight: 600,
                }}>
                {selectedRowsState.length}
              </a>{' '}
              项 &nbsp;&nbsp;
              <span>服务调用次数总计 {selectedRowsState.reduce((pre: any, item: { callNo: any }) => pre + item.callNo!, 0)} 万</span>
            </div>
          }>
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState)
              setSelectedRows([])
              actionRef.current?.reloadAndRest?.()
            }}>
            批量删除
          </Button>
          <Button type="primary">批量审批</Button>
        </FooterToolbar>
      )}
      <ModalForm
        title={'新建规则'}
        width="400px"
        visible={createModalVisible}
        onVisibleChange={handleModalVisible}
        onFinish={async value => {
          await handleAdd(value)
        }}>
        <ProFormText
          rules={[
            {
              required: true,
              message: '规则名称为必填项',
            },
          ]}
          width="md"
          name="name"
        />
        <ProFormTextArea width="md" name="desc" />
      </ModalForm>
      <UpdateForm
        onSubmit={async value => {
          handleUpdate(value)
        }}
        onCancel={() => {
          setCurrentRow(undefined)
        }}
        updateModalVisible={!!currentRow}
        values={currentRow || {}}
      />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined)
          setShowDetail(false)
        }}
        closable={false}>
        {currentRow?.name && (
          <ProDescriptions<any>
            column={2}
            title={currentRow?.name}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.name,
            }}
            columns={columns as ProDescriptionsItemProps<any>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  )
}

export default TableList
