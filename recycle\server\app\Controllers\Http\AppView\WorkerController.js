'use strict'

const _ = require('lodash')
const moment = require('moment')
const Env = use('Env')
const Database = use('Database')
const { Worker, WorkerInsure, Order, WorkerPay } = require('../../../Models')
const { ERR, E } = require('../../../../../constants')
const { CryptUtil, Configs } = require('../../../Util')

//上门师傅信息
class WorkerController {
  async index({ request, response }) {
    let { page = 1, perPage = 10, name, isUse = E.WorkerAccountStatus.Agree } = request.all()
    let worker = request.worker
    let query = Worker.query().with('company', builder => {
      builder.select('id', 'companyName', 'city')
      return builder
    })
    if (!worker.companyID) {
      throw ERR.USER_EXISTS
    }
    if (worker.type !== "超级管理员") {
      query.where('companyID', worker.companyID)
    }
    if (name) {
      query.select('id', 'type', 'mobile', 'workerName', 'companyID', 'wallet', 'city').where('workerName', 'like', `%${name}%`)
    }
    let vo = await query.where('isUse', E.WorkerAccountStatus.Agree).fetch()
    response.json(vo)
  }
  // 添加小工
  async addAssistant({ request, response }) {
    try {
      const { workerName, mobile, workerID, password } = request.all()

      // 验证参数
      if (!workerName || !mobile || !workerID) {
        throw ERR.INVALID_PARAMS
      }

      // 检查手机号是否已存在
      const existingAssistant = await Worker.query()
        .where('mobile', mobile)
        .first()

      if (existingAssistant) {
        return response.status(400).json({
          success: false,
          message: '该手机号已被注册'
        })
      }

      // 检查所属师傅是否存在
      const worker = await Worker.find(workerID)
      if (!worker) {
        return response.status(400).json({
          success: false,
          message: '所属师傅不存在'
        })
      }
      let passwd = password || mobile.slice(-6)
      let enPw = CryptUtil.md5(CryptUtil.encryptData256(passwd, Env.get('APP_KEY')))

      // 创建小工账号
      const assistant = await Worker.create({
        workerName: workerName,
        mobile,
        managerID: workerID,
        isUse: '同意',
        forbidden: '1',
        companyID: worker.companyID,
        password: enPw, // 默认密码为手机号后6位
        wallet: 0 // 钱包余额默认为0
      })

      // 返回成功信息
      response.json({
        success: true,
        message: '添加小工成功',
        data: assistant
      })
    } catch (error) {
      console.error('添加小工失败:', error)
      response.status(500).json({
        success: false,
        message: '添加小工失败',
        error: error.message
      })
    }
  }
  // 获取小工订单
  async getAssistantOrders({ request, response }) {
    try {
      const { assistantID, status, page = 1, pageSize = 10 } = request.all()
      if (!assistantID) {
        throw ERR.INVALID_PARAMS
      }
      // 查询关联了小工的订单
      const query = Order.query()
        .where('workerID', assistantID)
        .where('status', status)
        .orderBy('createdAt', 'desc')
      const orders = await query.paginate(page, pageSize)
      response.json(orders)
    } catch (error) {
      console.error('获取小工订单失败:', error)
      response.status(500).json({
        success: false,
        message: '获取小工订单失败',
        error: error.message
      })
    }
  }
  async register({ request, response }) {
    try {
      let {
        address,
        city,
        district,
        idCardBackFileUrl,
        idCardFrontFileUrl,
        idCardNo,
        mobile,
        password,
        province,
        qualificationFileUrls,
        workerName
      } = request.all()
      if (!mobile) {
        throw ERR.INVALID_PARAMS
      }
      let isReg = await Worker.findBy('mobile', mobile)
      if (isReg) {
        throw ERR.USER_EXISTS
      }
      let enPw = CryptUtil.md5(CryptUtil.encryptData256(password, Env.get('APP_KEY')))
      let vo = await Worker.create({
        openid: 0,
        companyID: 36,
        address,
        city,
        district,
        idCardImg: idCardFrontFileUrl,
        idCardBackImg: idCardBackFileUrl,
        idCardNo: idCardNo,
        mobile,
        password: enPw,
        province,
        workerName
      })
      response.json({
        token: CryptUtil.jwtEncode({ userID: vo.id }),
        user: vo
      })
      // 将逗号分隔的字符串转换为对象数组格式
      const fileUrlsArray = qualificationFileUrls ? qualificationFileUrls.split(',').map(url => ({ url: url.trim() })) : [];
      await WorkerInsure.create({ workerID: vo.id, companyID: 36, files: fileUrlsArray })
    } catch (e) {
      response.json(e)
    }
  }
  async getCoWorker({ request, response }) {
    let workerID = request.worker.id
    let vo = await Worker.query()
      .select('id', 'workerName', 'managerID', 'wallet', 'isUse', 'mobile', 'forbidden', 'companyID')
      .with('order',
        builder => {
          builder.where('status', '进行中')
          return builder
        })
      .where('managerID', workerID)
      .whereNotNull('workerName')
      .where('forbidden', 1)
      .where('isUse', E.WorkerAccountStatus.Agree).fetch()
    response.json(vo)
  }

  async show({ request, params, response }) {
    let workerID = request.worker.id
    let vo = await Worker.query().where('id', workerID).first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
  //worker信息更新
  async update({ request, params, response }) {
    let workerID = request.worker.id
    let { mobile, insureFiles } = request.all()
    let vo = await Worker.find(workerID)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    if (mobile) {
      let passwd = mobile.slice(-6)
      let enPw = CryptUtil.md5(CryptUtil.encryptData256(passwd, Env.get('APP_KEY')))
      let data = request.all()
      delete data.insureFiles
      _.assign(vo, data, { isUse: E.WorkerAccountStatus.Init, password: enPw })
    } else {
      let data = request.all()
      delete data.insureFiles
      _.assign(vo, data, { isUse: E.WorkerAccountStatus.Init })
    }

    // 处理保险文件
    if (insureFiles && insureFiles.length > 0) {
      const companyID = vo.companyID
      // 检查是否已有保险记录
      let workerInsure = await WorkerInsure.findBy('workerID', workerID)

      if (!workerInsure) {
        // 创建新的保险记录
        await WorkerInsure.create({
          workerID,
          companyID,
          files: insureFiles,
        })
      } else {
        // 更新现有保险记录
        workerInsure.files = insureFiles
        workerInsure.updatedAt = moment().format('YYYY-MM-DD HH:mm:ss')
        await workerInsure.save()
      }
    }
    await vo.save()
    response.json(vo)
  }
  async getProfileData({ request, response }) {
    let workerID = request.worker.id
    let vo = {}
    // 本月完成订单量，总单量，保险文件，我的成员，团队成员，团队本月完单量,本月信息费
    let monthOrderCount = await Order.query().where('workerID', workerID).where('status', E.OrderStatus.Completed)
      .whereBetween('finishedAt', [moment().startOf('month').format('YYYY-MM-DD 00:00:00'), moment().endOf('month').format('YYYY-MM-DD 23:59:59')])
      .getCount()
    let totalOrderCount = await Order.query().where('workerID', workerID).getCount()
    let insureFiles = await WorkerInsure.query().where('workerID', workerID).first()
    let myTeamMemberCount = await Worker.query().where('managerID', workerID)
      .where('isUse', E.WorkerAccountStatus.Agree)
      .where('forbidden', 1)
      .getCount()
    let myTeamMemberList = await Worker.query().select('id', 'workerName', 'mobile', 'wallet', 'managerID').with('order')
      .where('managerID', workerID)
      .where('isUse', E.WorkerAccountStatus.Agree)
      .where('forbidden', 1)
      .fetch()
    let teamOrderCount = await Order.query()
      .whereIn('workerID', myTeamMemberList.rows.map(item => item.id))
      .where('status', E.OrderStatus.Completed)
      .whereBetween('finishedAt', [moment().startOf('month').format('YYYY-MM-DD 00:00:00'), moment().endOf('month').format('YYYY-MM-DD 23:59:59')])
      .getCount()
    let monthInfoFee = await Order.query()
      .select(Database.raw('SUM(infoFee) as sumInfoFee'))
      .where('workerID', workerID)
      .where('status', E.OrderStatus.Completed)
      .whereBetween('finishedAt', [moment().startOf('month').format('YYYY-MM-DD 00:00:00'), moment().endOf('month').format('YYYY-MM-DD 23:59:59')])
      .first()
    vo = {
      monthOrderCount,
      totalOrderCount,
      monthInfoFee: monthInfoFee.toJSON() ? monthInfoFee.toJSON().sumInfoFee : 0,
      insureFiles,
      myTeamMemberCount,
      myTeamMemberList,
      teamOrderCount
    }
    response.json(vo)
  }
  async loginBypasswd({ request, response }) {
    let { phone, passwd, newPasswd } = request.all()
    console.log({ phone, passwd, newPasswd });

    let enPw = CryptUtil.md5(CryptUtil.encryptData256(passwd, Env.get('APP_KEY')))
    let vo = await Worker.query().where('mobile', phone).where('password', enPw).first()
    if (!vo) {
      throw ERR.SQL_DUP_NAME_OR_PASSWORD
    }
    if (newPasswd) {
      vo.password = CryptUtil.md5(CryptUtil.encryptData256(newPasswd, Env.get('APP_KEY')))
      await vo.save()
    }
    response.json({
      token: CryptUtil.jwtEncode({ userID: vo.id }),
      openID: vo.openid,
      user: vo
    })
  }
  // 师傅修改密码
  async changePassword({ request, response }) {
    let { newPassword } = request.all()
    let workerID = request.worker.id
    let vo = await Worker.find(workerID)
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    let passwd = newPassword.slice(-6)
    let enPw = CryptUtil.md5(CryptUtil.encryptData256(passwd, Env.get('APP_KEY')))
    _.assign(vo, { password: enPw })
    await vo.save()
    response.json({
      message: '密码修改成功'
    })
  }
  // 师傅看板数据
  async getWorkerBoardData({ request, response }) {
    let { timeRange, startDate, endDate } = request.all()
    let workerID = request.worker.id
    let vo = {}
    // 时间筛选选项

    switch (timeRange) {
      case '7days':
        startDate = moment().subtract(7, 'days').format('YYYY-MM-DD')
        endDate = moment().add(1, 'days').format('YYYY-MM-DD')
        break
      case '30days':
        startDate = moment().subtract(30, 'days').format('YYYY-MM-DD')
        endDate = moment().add(1, 'days').format('YYYY-MM-DD')
        break
      case '90days':
        startDate = moment().subtract(90, 'days').format('YYYY-MM-DD')
        endDate = moment().add(1, 'days').format('YYYY-MM-DD')
        break
      case 'year':
        startDate = moment().startOf('year').format('YYYY-MM-DD')
        endDate = moment().endOf('year').format('YYYY-MM-DD')
        break
      default:
        startDate = moment().startOf('month').format('YYYY-MM-DD')
        endDate = moment().endOf('month').format('YYYY-MM-DD')
        break
    }
    // 本月完成订单量，较上月增长比例，本月完工金额，较上月完工金额增长比例， 本月充值金额，较上月充值金额增长比例
    // 本月取消订单量，较上月取消订单量增长比例，本月平均响应时长，较上月平均响应时长增长比例
    // 近7天订单趋势  品类分布  状态（待接单，进行中，已完成，取消）分布 
    // 本月订单趋势
    let query = await Order.query().whereNot('status', E.OrderStatus.Cancelled)
      .where('workerID', workerID)
      .select(Database.raw("workerID,DATE_FORMAT(finishedAt, '%Y-%m-%d') AS date,COUNT(*) AS count"))
      .groupBy('date')
      .whereBetween('finishedAt', [moment(startDate).format('YYYY-MM-DD 00:00:00'), moment(endDate).format('YYYY-MM-DD 23:59:59')])
      .orderBy('date', 'asc')
      .fetch()
    let orderTrend = query.toJSON()

    // 品类分布
    let category = await Order.query().whereNot('status', E.OrderStatus.Cancelled)
      .where('workerID', workerID)
      .select(Database.raw("workerID,type,COUNT(*) AS count"))
      .groupBy('type')
      .whereBetween('finishedAt', [moment(startDate).format('YYYY-MM-DD 00:00:00'), moment(endDate).format('YYYY-MM-DD 23:59:59')])
      .fetch()
    let categoryDistribution = category.toJSON()

    // 本月完成订单量
    let monthOrderCount = await Order.query().where('workerID', workerID).where('status', E.OrderStatus.Completed)
      .whereBetween('finishedAt', [moment().startOf('month').format('YYYY-MM-DD 00:00:00'),
      moment().endOf('month').format('YYYY-MM-DD 23:59:59')])
      .select(Database.raw('COUNT(*) AS count'))
      .first()
    // 今日单量
    let totalOrderCount = await Order.query().where('workerID', workerID)
      .whereBetween('createdAt', [moment().subtract(1, 'days').format('YYYY-MM-DD 00:00:00'), moment().add(1, 'days').format('YYYY-MM-DD 23:59:59')])
      .select(Database.raw('COUNT(*) AS count'))
      .first()
    // 昨日订单量
    let yesterdayOrderCount = await Order.query().where('workerID', workerID)
      .whereBetween('createdAt', [moment().subtract(1, 'days').format('YYYY-MM-DD 00:00:00'), moment().format('YYYY-MM-DD 23:59:59')])
      .select(Database.raw('COUNT(*) AS count'))
      .first()
    // // 本月信息费
    // let monthInfoFee = await Order.query()
    //   .select(Database.raw('SUM(infoFee) as sumInfoFee'))
    //   .where('workerID', workerID)
    //   .where('status', E.OrderStatus.Completed)
    //   .whereBetween('finishedAt', [moment().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
    //   moment().endOf('month').format('YYYY-MM-DD HH:mm:ss')])
    //   .first()
    // 本月充值金额
    let monthRechargeAmount = await WorkerPay.query().where('workerID', workerID)
      .where('status', E.OrderStatus.Completed)
      .whereBetween('createdAt', [moment().startOf('month').format('YYYY-MM-DD 00:00:00'),
      moment().endOf('month').format('YYYY-MM-DD 23:59:59')])
      .select(Database.raw('SUM(totalPay) as sumTotalPay'))
      .first()
    // 上月完成订单量
    let lastMonthOrderCount = await Order.query().where('workerID', workerID).where('status', E.OrderStatus.Completed)
      .whereBetween('finishedAt', [moment().startOf('month').subtract(1, 'month').format('YYYY-MM-DD 00:00:00'),
      moment().endOf('month').subtract(1, 'month').format('YYYY-MM-DD 23:59:59')])
      .select(Database.raw('COUNT(*) AS count'))
      .first()
    // // 上月信息费
    // let lastMonthInfoFee = await Order.query()
    //   .select(Database.raw('SUM(infoFee) as sumInfoFee'))
    //   .where('workerID', workerID)
    //   .where('status', E.OrderStatus.Completed)
    //   .whereBetween('finishedAt', [moment().startOf('month').subtract(1, 'month').format('YYYY-MM-DD HH:mm:ss'),
    //   moment().endOf('month').subtract(1, 'month').format('YYYY-MM-DD HH:mm:ss')])
    //   .first()
    // 本月取消订单量
    let monthCancelOrderCount = await Order.query().where('workerID', workerID)
      .where('status', E.OrderStatus.Cancelled)
      .whereBetween('createdAt', [moment().startOf('month').format('YYYY-MM-DD 00:00:00'),
      moment().endOf('month').format('YYYY-MM-DD 23:59:59')])
      .select(Database.raw('COUNT(*) AS count'))
      .first()
    // 上月取消订单量
    let lastMonthCancelOrderCount = await Order.query().where('workerID', workerID)
      .where('status', E.OrderStatus.Cancelled)
      .whereBetween('createdAt', [moment().startOf('month').subtract(1, 'month').format('YYYY-MM-DD 00:00:00'),
      moment().endOf('month').subtract(1, 'month').format('YYYY-MM-DD 23:59:59')])
      .select(Database.raw('COUNT(*) AS count'))
      .first()
    // 上月充值金额
    let lastMonthRechargeAmount = await WorkerPay.query().where('workerID', workerID)
      .where('status', E.OrderStatus.Completed)
      .whereBetween('createdAt', [moment().startOf('month').subtract(1, 'month').format('YYYY-MM-DD 00:00:00'),
      moment().endOf('month').subtract(1, 'month').format('YYYY-MM-DD 23:59:59')])
      .select(Database.raw('SUM(totalPay) as sumTotalPay'))
      .first()

    // 本月平均响应时长
    let monthAverageResponseTime = await Order.query().where('workerID', workerID)
      .where('status', E.OrderStatus.Completed)
      .whereBetween('createdAt', [moment().startOf('month').format('YYYY-MM-DD 00:00:00'),
      moment().endOf('month').format('YYYY-MM-DD 23:59:59')])
      .select(Database.raw('AVG(TIMESTAMPDIFF(SECOND, createdAt, takeTime)) / 3600 as avgResponseTime'))
      .first()
    // 本月完工时效
    let monthFinishTime = await Order.query().where('workerID', workerID)
      .where('status', E.OrderStatus.Completed)
      .whereBetween('createdAt', [moment().startOf('month').format('YYYY-MM-DD 00:00:00'),
      moment().endOf('month').format('YYYY-MM-DD 23:59:59')])
      .select(Database.raw('AVG(TIMESTAMPDIFF(SECOND, createdAt, finishedAt)) / 3600 as avgResponseTime'))
      .first()
    // 上月平均响应时长
    let lastMonthAverageResponseTime = await Order.query().where('workerID', workerID)
      .where('status', E.OrderStatus.Completed)
      .whereBetween('createdAt', [moment().startOf('month').subtract(1, 'month').format('YYYY-MM-DD 00:00:00'),
      moment().endOf('month').subtract(1, 'month').format('YYYY-MM-DD 23:59:59')])
      .select(Database.raw('AVG(TIMESTAMPDIFF(SECOND, createdAt, takeTime)) / 3600 as avgResponseTime'))
      .first()
    // 上月完工时效
    let lastMonthFinishTime = await Order.query().where('workerID', workerID)
      .where('status', E.OrderStatus.Completed)
      .whereBetween('createdAt', [moment().startOf('month').subtract(1, 'month').format('YYYY-MM-DD 00:00:00'),
      moment().endOf('month').subtract(1, 'month').format('YYYY-MM-DD 23:59:59')])
      .select(Database.raw('AVG(TIMESTAMPDIFF(SECOND, createdAt, finishedAt)) / 3600 as avgResponseTime'))
      .first()
    vo = {
      monthAverageResponseTime: monthAverageResponseTime && monthAverageResponseTime.toJSON() && monthAverageResponseTime.toJSON().avgResponseTime ? parseFloat(monthAverageResponseTime.toJSON().avgResponseTime).toFixed(2) : 0,
      monthFinishTime: monthFinishTime && monthFinishTime.toJSON() && monthFinishTime.toJSON().avgResponseTime ? parseFloat(monthFinishTime.toJSON().avgResponseTime).toFixed(2) : 0,
      lastMonthAverageResponseTime: lastMonthAverageResponseTime && lastMonthAverageResponseTime.toJSON() && lastMonthAverageResponseTime.toJSON().avgResponseTime ? parseFloat(lastMonthAverageResponseTime.toJSON().avgResponseTime).toFixed(2) : 0,
      lastMonthFinishTime: lastMonthFinishTime && lastMonthFinishTime.toJSON() && lastMonthFinishTime.toJSON().avgResponseTime ? parseFloat(lastMonthFinishTime.toJSON().avgResponseTime).toFixed(2) : 0,
      monthOrderCount: monthOrderCount.toJSON() ? monthOrderCount.toJSON().count : 0,
      totalOrderCount: totalOrderCount.toJSON() ? totalOrderCount.toJSON().count : 0,
      // monthInfoFee: monthInfoFee.toJSON() ? monthInfoFee.toJSON().sumInfoFee : 0,
      monthRechargeAmount: monthRechargeAmount.toJSON() ? monthRechargeAmount.toJSON().sumTotalPay : 0,
      lastMonthOrderCount: lastMonthOrderCount.toJSON() ? lastMonthOrderCount.toJSON().count : 0,
      // lastMonthInfoFee: lastMonthInfoFee.toJSON() ? lastMonthInfoFee.toJSON().sumInfoFee : 0,
      lastMonthCancelOrderCount: lastMonthCancelOrderCount.toJSON() ? lastMonthCancelOrderCount.toJSON().count : 0,
      orderTrend,
      monthCancelOrderCount: monthCancelOrderCount.toJSON() ? monthCancelOrderCount.toJSON().count : 0,
      lastMonthRechargeAmount: lastMonthRechargeAmount.toJSON() ? lastMonthRechargeAmount.toJSON().sumTotalPay : 0,
      categoryDistribution,
      yesterdayOrderCount: yesterdayOrderCount.toJSON() ? yesterdayOrderCount.toJSON().count : 0,
    }
    response.json(vo)
  }
}

module.exports = WorkerController
