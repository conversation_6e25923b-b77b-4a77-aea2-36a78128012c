'use strict'

const Model = use('Model')

//用户
class AdminUser extends Model {
  static get table() {
    return 'admin_user'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return 'updatedAt'
  }
  company() {
    return this.belongsTo('App/Models/Company', 'companyID', 'id')
  }

  authority() {
    return this.belongsTo('App/Models/AdminUserPermission', 'authorityID', 'id')
  }
}

module.exports = AdminUser
