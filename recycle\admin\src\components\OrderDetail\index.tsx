import { Image, Modal, Table } from "antd"
import dayjs from "dayjs"
import { reducer } from "dva17"
import { Fragment } from "react"
import { NOrder, RSetState } from "../../common/action"
import { pageStatus } from "../../common/enum"
import { getOrderLog } from "../../services/order"
import styles from './index.module.less'

export default (text: any, whichOrder: string, type: number) => {
  const getLog = async () => {
    let data = await getOrderLog({ id: text.id })
    reducer(NOrder, RSetState, { logData: data, visibleLog: true })
  }
  const computeMoney = (text: any) => {
    let value = 0
    value = text.infoFee
    return value
  }
  Modal.info({
    title: `${whichOrder === pageStatus.Pending ? '回收物明细' : '订单详情'}`,
    content: (
      <div className={styles.item_wrapper} >
        <div className={styles.item} style={{ marginTop: 30 }}>
          <span className={styles.item_title}> 下单时间：</span>
          <div className={styles.item_content} > {dayjs(text.createdAt).format('YYYY-MM-DD HH:mm:ss')} </div>
          <div className={styles.item_log} onClick={() => {
            getLog()
          }}> 订单记录</div>
        </div>
        <div className={styles.item} style={{ marginTop: 20 }}>
          <span className={styles.item_title}> 信息费：</span>
          <div className={styles.item_content} > ¥{text?.infoFee}元 </div>
        </div>
        {
          whichOrder === pageStatus.Cancelled || (whichOrder === pageStatus.Pending && text.cancel)
            ? [
              { title: '取消时间：', content: text.cancel ? dayjs(text.cancel.createdAt).format('YYYY-MM-DD HH:mm:ss') : '-' },
              { title: '取消原因：', content: text.cancel ? text.cancel.cancelReason : '-' },
              { title: '谁取消：', content: text.cancel && text.cancel.whoCancel ? text.cancel.whoCancel : '-' }
            ].map((value: any, index: number) => (
              <div className={styles.item} key={value + index}>
                <span className={styles.item_title}> {value.title} </span>
                < div className={styles.item_content} > {value.content} </div>
              </div>
            ))
            : null}
        {
          whichOrder === pageStatus.Pending && text.cancel
            ? [{ title: '回收人员：', content: text.cancel.worker?.workerName }, { title: '联系方式：', content: text.cancel.worker?.mobile }].map(
              (value: any, index: number) => (
                <div className={styles.item} key={value + index}>
                  <span className={styles.item_title}> {value.title} </span>
                  < div className={styles.item_content} > {value.content} </div>
                </div>
              )
            )
            : null}
        {/* <div className={styles.item}>
          <span className={styles.item_title}> 用户上传：</span>
          < div className={styles.item_content} style={{ flexWrap: 'wrap' }}>
            {
              text.userUpload && text.userUpload !== 'null'
                ? JSON.parse(text.userUpload).map((img: string, index: number) => <Image className={styles.item_pic} src={img} key={img} />)
                : ''
            }
          </div>
        </div> */}
        <div className={styles.item}>
          <span className={styles.item_title}> 预约时间：</span>
          < div className={styles.item_content} style={{ flexWrap: 'wrap' }}>
            {dayjs(text.workTime).format('YYYY-MM-DD A')}
          </div>
        </div>
        <div className={styles.item}>
          <span className={styles.item_title}> 回收物：</span>
          < div className={styles.item_content} style={{ flexWrap: 'wrap' }}>
            {text.type}
          </div>
        </div>
        <div className={styles.item} style={{ flexDirection: 'column' }
        }>
          <span className={styles.item_title}> 回收物特征：</span>
          < div className={styles.item_content} style={{ marginTop: '20px' }}>
            {text.extendFields}
          </div>
        </div>
        {
          whichOrder === pageStatus.Pending || whichOrder === pageStatus.Dispatched || whichOrder === pageStatus.InProgress ? (
            <Fragment>
              <div className={styles.item} >
                <span className={styles.item_title}> 备注：</span>
                < div className={styles.item_content} > {text.remark ? text.remark : '--'} </div>
              </div>

              {
                text.worker ? (
                  <Fragment>
                    <div className={styles.item} >
                      <span className={styles.item_title}> 回收员姓名：</span>
                      < div className={styles.item_content} > {text.worker?.workerName} </div>
                    </div>
                    < div className={styles.item} >
                      <span className={styles.item_title}> 回收员电话：</span>
                      < div className={styles.item_content} > {text.worker?.mobile} </div>
                    </div>
                    < div className={styles.item} >
                      <span className={styles.item_title}> 接单时间：</span>
                      < div className={styles.item_content} > {text.takeTime} </div>
                    </div>
                  </Fragment>
                ) : null
              }
            </Fragment>
          ) : (
            <Fragment>
              <div className={styles.item} >
                <span className={styles.item_title}> 客户姓名：</span>
                < div className={styles.item_content} > {text.userName} </div>
              </div>
              < div className={styles.item} >
                <span className={styles.item_title}> 客户电话：</span>
                < div className={styles.item_content} > {text.userMobile ? text.userMobile : '-'} </div>
              </div>
              {
                whichOrder === pageStatus.Completed ? (
                  <>
                    <div className={styles.item} >
                      <span className={styles.item_title}> 支付系统价：</span>
                      < div className={styles.item_content} >¥{computeMoney(text)} </div>
                    </div>
                    <div className={styles.item} >
                      <span className={styles.item_title}> 信息费：</span>
                      < div className={styles.item_content} >¥{text.commission} </div>
                    </div>
                    <div className={styles.item} >
                      <span className={styles.item_title}> 回收预估价：</span>
                      < div className={styles.item_content} >¥{text.apprizeAmount} </div>
                    </div>
                  </>) : null
              }
              <div className={styles.item}>
                <span className={styles.item_title}> 回收地址：</span>
                < div className={styles.item_content} >
                  {
                    text.province + text.city + text.address
                  }
                </div>
              </div>
              {
                text.worker ? (
                  <Fragment>
                    <div className={styles.item} >
                      <span className={styles.item_title}> 回收员姓名：</span>
                      < div className={styles.item_content} > {text.worker?.workerName} </div>
                    </div>
                    < div className={styles.item} >
                      <span className={styles.item_title}> 回收员电话：</span>
                      < div className={styles.item_content} > {text.worker?.mobile} </div>
                    </div>
                    < div className={styles.item} >
                      <span className={styles.item_title}> 接单时间：</span>
                      < div className={styles.item_content} > {text.takeTime} </div>
                    </div>
                  </Fragment>
                ) : null
              }
              {
                whichOrder === pageStatus.Completed ? (
                  <Fragment>
                    <div className={styles.item} >
                      <span className={styles.item_title}> 回收物图片：</span>
                      < div className={styles.item_content} style={{ marginTop: '20px', flexWrap: 'wrap' }
                      }>
                        {text?.doneInfo?.imageUrl ? JSON.parse(text?.doneInfo?.imageUrl).map((img: string, index: number) => <Image className={styles.item_pic} src={img} key={img} />) : null}
                      </div>
                    </div>
                    < div className={styles.item} >
                      <span className={styles.item_title}> 完成时间：</span>
                      < div className={styles.item_content} > {dayjs(text.finishedAt).format('YYYY-MM-DD HH:mm:ss')} </div>
                    </div>
                    < div className={styles.item} >
                      <span className={styles.item_title}> 用户签名：</span>
                      < div className={styles.item_content} >  <Image className={styles.item_pic} src={text?.doneInfo?.sign} /> </div>
                    </div>
                    {/* < div className={styles.item} >
                      <span className={styles.item_title}> 支付记录：</span>
                      < div className={styles.item_content} >
                        {
                          text.pay ? (
                            <Table dataSource={text.pay} columns={payItem} rowKey={record => JSON.stringify(record)} pagination={false} />
                          ) : null}
                      </div>
                    </div> */}
                    < div className={styles.item} >
                      <span className={styles.item_title}> 订单评价：</span>
                      {
                        text.rating ? (
                          <div className={styles.item_content} > {text.rating.workerRating === 1 ? '不满意' : text.rating.workerRating === 2 ? '一般' : '满意'} </div>
                        ) : (
                          '未评价'
                        )
                      }
                    </div>
                  </Fragment>
                ) : null}
            </Fragment>
          )}
        {
          text.company ? (
            <Fragment>
              < div className={styles.item} >
                <span className={styles.item_title}> 服务商：</span>
                < div className={styles.item_content} > {text.company.companyName} </div>
              </div>
              < div className={styles.item} >
                <span className={styles.item_title}> 服务商电话：</span>
                < div className={styles.item_content} > {text.company.mobile} </div>
              </div>
            </Fragment>
          ) : null
        }
      </div>
    ),
    okText: '知道了',
    onOk() { },
    width: 820
  })
}
