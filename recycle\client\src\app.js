import { Component, useEffect } from 'react'
import Taro from '@tarojs/taro'
import { useSelector, useDispatch, Provider } from 'react-redux'
import { View } from '@tarojs/components'
import './app.less'

import store from './store'
import { NUser } from './config/constants'

const Dom = () => {
  const dispatch = useDispatch()
  useEffect(() => {
    const userInfo = Taro.getStorageSync('userInfo')
    const token = Taro.getStorageSync('token')
    const { router } = Taro.getCurrentInstance()
    if (userInfo) {
      const userData = JSON.parse(userInfo)
      dispatch.NUser[NUser.ESetState]({
        userInfo: userData,
      })
    }
    if (!userInfo && !token) {
      if (router.path === 'pages/home/<USER>' && router.params.salesman) {
        dispatch.NUser[NUser.ELogin]({salesman:router.params.salesman})
      } else {
        dispatch.NUser[NUser.ELogin]({})
      }
    }
  }, [])
  return <View></View>
}
const App = props => {
  return (
    <Provider store={store}>
      {props.children}
      <Dom />
    </Provider>
  )
}

export default App
