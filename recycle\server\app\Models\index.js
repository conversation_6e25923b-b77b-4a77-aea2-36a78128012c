'use strict'

const OrderImport = use("App/Models/OrderImport")
const Attribute = use('App/Models/Attribute')
const AttributeType = use('App/Models/AttributeType')
const Company = use('App/Models/Company')
const Order = use('App/Models/Order')
const OrderWaste = use('App/Models/OrderWaste')
const User = use('App/Models/User')
const AdminUser = use('App/Models/AdminUser')
const UserAddress = use('App/Models/UserAddress')
const WorkerInvite = use('App/Models/WorkerInvite')
const Waste = use('App/Models/Waste')
const Waste1stCategory = use('App/Models/Waste1stCategory')
const Waste2ndCategory = use('App/Models/Waste2ndCategory')
const WasteType = use('App/Models/WasteType')
const Worker = use('App/Models/Worker')
const WorkerDistrict = use('App/Models/WorkerDistrict')
const WorkerDistrictV = use('App/Models/WorkerDistrictV')
const Pay = use('App/Models/Pay')
const LogExternalApiRequest = use('App/Models/LogExternalApiRequest')
const LogMsg = use('App/Models/LogMsg')
const CompanySelfWastePrice = use('App/Models/CompanySelfWastePrice')
const CompanySelfAttribute = use('App/Models/CompanySelfAttribute')
const WasteSentinel = use('App/Models/WasteSentinel')
const OrderRating = use('App/Models/OrderRating')
const OrderOperate = use('App/Models/OrderOperate')
const OrderWasteV = use('App/Models/OrderWasteV')
const Discovery = use('App/Models/Discovery')
const DiscoveryComment = use('App/Models/DiscoveryComment')
const CompanyArea = use('App/Models/CompanyArea')
const WorkerArea = use('App/Models/WorkerArea')
const WorkerInsure = use('App/Models/WorkerInsure')
const PriceSet = use('App/Models/PriceSet')
const CLaba = use('App/Models/CLaba')
const CPath = use('App/Models/CPath')
const CBanner = use('App/Models/CBanner')
const COrder = use('App/Models/COrder')


const WorkerPay = use('App/Models/WorkerPay')
const DiscoveryUpvote = use('App/Models/DiscoveryUpvote')
const OrderCancel = use('App/Models/OrderCancel')
const AdminUserPermission = use('App/Models/AdminUserPermission')
const OrderComplaint = use('App/Models/OrderComplaint')
const MallPoints = use('App/Models/MallPoints')
const WorkerWalletLog = use('App/Models/WorkerWalletLog')
const OrderLog = use('App/Models/OrderLog')
const ClientOrder = use('App/Models/ClientOrder')
const ClientOrderLog = use('App/Models/ClientOrderLog')
const ClientOrderOperate = use('App/Models/ClientOrderOperate')
const ClientOrderCancel = use('App/Models/ClientOrderCancel')
const ClientOrderComplaint = use('App/Models/ClientOrderComplaint')
const ClientOrderRating = use('App/Models/ClientOrderRating')

const MessageSub = use('App/Models/MessageSub')
const WorkerPoint = use('App/Models/WorkerPoint')
const WorkerPayRefund = use('App/Models/WorkerPayRefund')
const SysArea = use('App/Models/SysArea')
const ReqLog = use('App/Models/ReqLog')
const Tracklog = use('App/Models/Tracklog')
const Salesman = use('App/Models/Salesman')
const JoinTeam = use('App/Models/JoinTeam')
const ChargePrice = use('App/Models/ChargePrice')
const ClientCommission = use('App/Models/ClientCommission')
const ClientEstimate = use('App/Models/ClientEstimate')
const AppPay = use('App/Models/AppPay')
const Banners = use('App/Models/Banners')
const MetalPrice = use('App/Models/MetalPrice')


const YCWorker = use('App/Models/YCWorker')
const YCOrder = use('App/Models/YCOrder')
const YCWorkerWalletLog = use('App/Models/YCWorkerWalletLog')
const YCWorkerPay = use('App/Models/YCWorkerPay')
const YCWorkerMaintain = use('App/Models/YCWorkerMaintain')
const YCCOWorker = use('App/Models/YCCOWorker')
const YCPrice = use('App/Models/YCPrice')
const YCOrderMaintain = use('App/Models/YCOrderMaintain')
const YCOrderLog = use('App/Models/YCOrderLog')
const YCOrderCancel = use('App/Models/YCOrderCancel')


const JDAdminUser = use('App/Models/JDAdminUser')
const JDWorker = use('App/Models/JDWorker')
const JDOrder = use('App/Models/JDOrder')
const JDWorkerWalletLog = use('App/Models/JDWorkerWalletLog')
const JDWorkerPay = use('App/Models/JDWorkerPay')
const JDWorkerMaintain = use('App/Models/JDWorkerMaintain')
const JDCOWorker = use('App/Models/JDCOWorker')
const JDPrice = use('App/Models/JDPrice')
const JDOrderMaintain = use('App/Models/JDOrderMaintain')

// 嗨回收模块
const HiOrder = use('App/Models/HiOrder')
const HiOrderMaintain = use('App/Models/HiOrderMaintain')
const HiWorker = use('App/Models/HiWorker')
const HiWorkerWalletLog = use('App/Models/HiWorkerWalletLog')
const HiPrice = use('App/Models/HiPrice')
const HiCOWorker = use('App/Models/HiCOWorker')
const HiWorkerMaintain = use('App/Models/HiWorkerMaintain')
const HiPriceLog = use('App/Models/HiPriceLog')

const PageForm = use('App/Models/PageForm')
const PageOrder = use('App/Models/PageOrder')
const PageNews = use('App/Models/PageNews')

const PriceSetLog = use('App/Models/PriceSetLog')

module.exports = {

  CLaba,
  CPath,
  CBanner,
  COrder,
  WorkerPayRefund,
  MallPoints,
  LogExternalApiRequest,
  LogMsg,
  AdminUser,
  Attribute,
  AttributeType,
  Company,
  Order,
  OrderWaste,
  User,
  UserAddress,
  Waste,
  Waste1stCategory,
  Waste2ndCategory,
  WasteType,
  Worker,
  WorkerPay,
  WorkerDistrict,
  WorkerDistrictV,
  WorkerWalletLog,
  Pay,
  CompanySelfWastePrice,
  CompanySelfAttribute,
  WasteSentinel,
  OrderRating,
  WorkerPoint,
  OrderOperate,
  Discovery,
  DiscoveryComment,
  CompanyArea,
  WorkerArea,
  WorkerInsure,
  DiscoveryUpvote,
  OrderCancel,
  WorkerInvite,
  AdminUserPermission,
  OrderComplaint,
  OrderLog,
  MessageSub,
  PriceSet,
  OrderImport,
  SysArea,
  OrderWasteV,
  ReqLog,
  Salesman,
  JoinTeam,
  ChargePrice,
  Tracklog,
  ClientOrder,
  ClientOrderLog,
  ClientOrderOperate,
  ClientOrderCancel,
  ClientOrderComplaint,
  ClientOrderRating,
  ClientCommission,
  ClientEstimate,
  AppPay,
  Banners,
  MetalPrice,

  JDAdminUser,
  JDWorker,
  JDWorkerWalletLog,
  JDWorkerPay,
  JDWorkerMaintain,
  JDOrderMaintain,
  JDCOWorker,
  JDOrder,
  JDPrice,

  // YC回收模块
  YCWorker,
  YCOrder,
  YCWorkerWalletLog,
  YCWorkerPay,
  YCWorkerMaintain,
  YCCOWorker,
  YCPrice,
  YCOrderMaintain,
  YCOrderLog,
  YCOrderCancel,

  // 嗨回收模块
  HiOrder,
  HiOrderMaintain,
  HiWorker,
  HiWorkerWalletLog,
  HiPrice,
  HiCOWorker,
  HiWorkerMaintain,
  HiPriceLog,

  PageForm,
  PageOrder,
  PageNews,

  PriceSetLog,
}
