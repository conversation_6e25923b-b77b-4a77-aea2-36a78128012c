import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'

/*-------------------中转站信息管理-------------------------*/
export async function getWaste(payload:any) {　　　　　　　　　// 列表页面
  return requestGet('/tsWaste', payload)
}
export async function getWasteAdmin(payload:any) {　　　　　　　　　// 列表页面
  return requestGet('/tsWasteAdmin', payload)
}
export async function putWasteStatus(payload:any) {
  return requestPut(`/wasteStatus/${payload.id}`, payload)
}
export async function postPrice(payload:any) {
  return requestPost(`/price`, payload)
}
export async function postSortedStorage(payload:any) {
  return requestPost(`/sortedStorage`, payload)
}
export async function putSortedCleared(payload:any) {
  return requestPut(`/sortedCleared/${payload.id}`)
}
export async function postChangeStorage(payload:any) {　　　　　//修改库存
  return requestPost(`/changeStorage`, payload)
}

export async function postOutStorage(payload:any) {
  return requestPost(`/outStorage`, payload)
}
export async function postInStorage(payload:any) {
  return requestPost(`/inStorage`, payload)
}
export async function getWasteStorageData(payload:any) {
  return requestGet(`/wasteStorageData`, payload)
}
export async function postWorktime(payload:any) {
  return requestPost(`/worktime`, payload)
}
export async function getStationUser(payload:any) {
  return requestGet(`/stationUser`, payload)
}
export async function putStationUserStatus(payload:any) {
  return requestPut(`/stationUserStatus/${payload.id}`)
}
// 废品信息
export async function getTsOrder(payload:any) {
  return requestGet(`/tsOrder`, payload)
}
// 废品信息
export async function getTsWorkerOrder(payload:any) {
  return requestGet(`/tsWorkerOrder`, payload)
}

export async function getTsCorrectionList(payload:any) {
  return requestGet(`tsCorrectionList`, payload)
}
export async function getTsSortList(payload:any) {
  return requestGet(`tsSortList`, payload)
}

// 用户相关
export async function postUser(payload:any) {
  return requestPost(`stationUser`, payload)
}
export async function putUser(payload:any) {
  return requestPut(`stationUser/${payload.id}`, payload)
}
export async function deleteUser(payload:any) {
  return requestDelete(`stationUser/${payload.id}`, payload)
}
export async function putPw(payload:any) {
  return requestPut(`stationUserPw/${payload.id}`, payload)
}

export async function exportExcel(payload:any) {
  return requestGet(`tsOrderExcel`, payload)
}
// 修改订单为已支付状态
export async function orderStatus(payload:any) {
  return requestPut(`orderStatus/${payload.id}`)
}
// 删除订单
export async function deleteOrder(payload:any) {
  return requestPut(`deleteOrder/${payload.id}`)
}

// 用户充值记录
export async function userPayLog(payload:any) {
  return requestGet(`userPayLog/${payload.id}`, payload)
}

// 一级标签
export async function firstType(payload:any) {
  return requestGet(`firstType`, payload)
}
// 新建、编辑废品类型
export async function createWaste(payload:any) {
  return requestPost(`createWaste`, payload)
}
export async function updateWaste(payload:any) {
  return requestPost(`updateWaste`, payload)
}