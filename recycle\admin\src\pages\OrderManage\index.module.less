.page {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.order_status_wrapper {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 30px;

  .order_status {
    width: 120px;
    height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f5f6fa;
    cursor: pointer;
    border-radius: 4px;
    box-shadow: 0 2px 8px #cccccc;
    font-weight: 600;

    >span {
      &:first-child {
        margin-bottom: 10px;
      }
    }
  }

  .order_status_active {
    background: #1890ff;
    color: #ffffff;
  }
}

.item_wrapper {
  .item_wrapper_reasonList {
    display: flex;
    flex-wrap: wrap;

    .reasonList_item {
      border: 1px solid #1890ff;
      border-radius: 3px;
      padding: 5px 10px;
      cursor: pointer;
      margin-right: 15px;
    }
  }

  .item {
    display: flex;
    margin-bottom: 20px;
    letter-spacing: 0.8px;

    // flex-wrap: wrap;
    //border-bottom: 1px solid #cccccc;
    &:last-child {
      border-bottom: none;
    }

    .item_title {
      display: inline-block;
      width: 90px;
      text-align: right;
      font-weight: 600;
    }

    .item_log {
      display: inline-block;
      width: 90px;
      text-align: right;
      font-weight: 600;
      font-weight: bolder;
      cursor: pointer;
      text-decoration: underline;
      color: #1890ff;
    }

    .item_content {
      display: flex;
      flex: 1;
      flex-wrap: wrap;

      .item_pic {
        height: 100px;
        width: 100px;
        border-radius: 4px;
        margin-right: 10px;
        margin-bottom: 10px;
        box-shadow: 0 2px 4px #cccccc;
      }

      .item_content_type {
        display: inline-block;
        width: 110px;
        height: 34px;
        line-height: 34px;
        text-align: center;
        border-radius: 4px;
        border: 1px solid #1890ff;
        color: #1890ff;
        margin: 0 8px 8px 0;
      }

      .waste_wrapper {
        width: 100%;

        .waste_wrapper_title {
          width: 100%;
          display: flex;
          font-weight: 600;

          .title {
            width: 50%;
            text-align: center;
          }
        }
      }

      .content_wrapper {
        width: 100%;
        display: flex;
        margin: 10px 0;

        .name {
          width: 50%;
          text-align: center;
        }

        .price {
          width: 50%;
          text-align: center;
        }
      }
    }
  }
}

.item_wrapper {
  .item_wrapper_reasonList {
    display: flex;
    flex-wrap: wrap;

    .reasonList_item {
      border: 1px solid #1890ff;
      border-radius: 3px;
      padding: 5px 10px;
      cursor: pointer;
      margin-right: 15px;
    }
  }

  .item {
    display: flex;
    margin-bottom: 20px;
    letter-spacing: 0.8px;

    // flex-wrap: wrap;
    //border-bottom: 1px solid #cccccc;
    &:last-child {
      border-bottom: none;
    }

    .item_title {
      display: inline-block;
      width: 90px;
      text-align: right;
      font-weight: 600;
    }

    .item_content {
      display: flex;
      flex: 1;
      flex-wrap: wrap;

      .item_pic {
        height: 100px;
        width: 100px;
        border-radius: 4px;
        margin-right: 10px;
        margin-bottom: 10px;
        box-shadow: 0 2px 4px #cccccc;
      }

      .item_content_type {
        display: inline-block;
        width: 110px;
        height: 34px;
        line-height: 34px;
        text-align: center;
        border-radius: 4px;
        border: 1px solid #1890ff;
        color: #1890ff;
        margin: 0 8px 8px 0;
      }

      .waste_wrapper {
        width: 100%;

        .waste_wrapper_title {
          width: 100%;
          display: flex;
          font-weight: 600;

          .title {
            width: 50%;
            text-align: center;
          }
        }
      }

      .content_wrapper {
        width: 100%;
        display: flex;
        margin: 10px 0;

        .name {
          width: 50%;
          text-align: center;
        }

        .price {
          width: 50%;
          text-align: center;
        }
      }
    }
  }
}

.each {
  width: 100%;
  display: flex;
  flex-wrap: wrap;

  .each_one {
    width: 12vw;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border: 1px solid #cccccc;
    margin-right: 20px;
    margin-bottom: 15px;
    border-radius: 4px;
    cursor: pointer;
  }

  .each_selected {
    background: #1890ff;
    color: #ffffff;
    border: none;
  }
}