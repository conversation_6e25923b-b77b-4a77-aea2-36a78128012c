import { useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { reducer, useConnect } from 'dva17'
import { Button } from 'antd'
import { RightOutlined } from '@ant-design/icons'

import images from '../../assets/images'
import styles from './index.module.less'

import { NUser, RAdd, RSetState } from '../../common/action'

export default () => {
  const { count } = useConnect(NUser) //使用state中model数据
  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    console.log('Home start') //页面启动
    return () => {
      console.log('Home end') //页面结束
      reducer(NUser, RSetState, { count: 0 }) //清除count
    }
  }, [])

  /*--------------------- 响应 ---------------------*/
  const onCountClick = () => {
    reducer(N<PERSON><PERSON>, <PERSON>dd, {}) //使用reducer更新models数据
  }

  /*--------------------- 渲染 ---------------------*/
  return (
    <div className={styles.page}>
      <img src={images.logo} className={styles.logo} />
      <p>欢迎使用前端框架</p>
      <Button type="text" onClick={onCountClick}>
        count is: {count}
      </Button>
      <p>
        <Link to={'login'}>
          登录页 <RightOutlined />
        </Link>
      </p>
    </div>
  )
}
