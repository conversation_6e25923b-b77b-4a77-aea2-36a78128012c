.order {
  width: 100vw;
  min-height: 100vh;
  background-color: #f5f6f8;
  letter-spacing: 1px;

  .address_wrapper {
    height: 184px;
    border-top: 6px solid #f3f3f3;
    display: flex;
    align-items: center;
    background: #ffffff;

    image {
      width: 60px;
      height: 60px;
      margin-left: 48px;
      margin-right: 26px;
    }

    .address_text_wrapper {
      width: 480px;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;

      > Text {
        font-size: 24px;
        color: #7c8696;
        letter-spacing: 1px;
      }

      .top_info {
        font-size: 30px;
        color: #333333;
        letter-spacing: 1px;

        > Text {
          &:first-child {
            margin-right: 36px;
          }
        }
      }

      .bottom_info {
        width: 96%;
        margin-top: 6px;
        font-size: 24px;
        color: #7c8696;
        line-height: 34px;
        letter-spacing: 1px;
      }
    }

    .devide {
      height: 160px;
      width: 1px;
      border-right: 2px dotted rgba(124, 134, 150, 0.5);
    }

    .address_button {
      height: 100%;
      width: 136px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #333333;
      font-size: 26px;
      letter-spacing: 2px;
    }
  }

  .add_information {
    display: flex;
    background-color: #ffffff;
    width: 100vw;

    .add_information_box {
      width: 94.4vw;
      height: 26.1vw;
      margin: 4.2vw auto;
      background-color: rgba(243, 243, 243, 1);
      color: rgba(16, 16, 16, 1);
      font-size: 3.7vw;
      font-family: Arial;
      border-radius: 1vw;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .addInformationBox {
      width: 94.4vw;
      height: 26.1vw;
      margin: 4.2vw auto;
      padding: 3.2vw;
      background-color: rgba(243, 243, 243, 1);

      .address_name_phone {
        width: 100%;

        //    border:1px solid red;
        .address_name {
          display: inline-block;
          padding-right: 6.7vw;
          color: rgba(51, 51, 51, 1);
          font-size: 4.2vw;
          font-family: PingFangSC-bold;
        }

        .address_phone {
          color: rgba(102, 102, 102, 1);
          font-size: 4.2vw;
          font-family: PingFangSC-regular;
        }
      }

      .address_information {
        width: 79.2vw;
        padding: 3vw 0;
        border-bottom: 1px solid rgba(243, 243, 243, 1);
        position: relative;
        top: 0;
        left: 0;

        // border:1px solid red;
        .information {
          color: rgba(102, 102, 102, 1);
          font-size: 3.5vw;
          text-align: left;
          font-family: PingFangSC-regular;
        }

        .order_addredd_Icon {
          position: absolute;
          right: -8vw;
          top: 3.8vw;
          width: 4vw;
          height: 4vw;
          // background-color: rgba(51, 51, 51, 1);
        }
      }
    }
  }

  .add_ {
    height: 14.1vw;
    width: 100vw;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 1.4vw solid #f3f3f3;
    background-color: #ffffff;

    .add_text {
      margin-left: 48px;
      color: #333333;
      font-size: 26px;
      text-align: left;
      font-family: PingFangSC-regular;
    }

    .add_choice {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .add_des {
        color: #7c8696;
        font-size: 26px;
      }

      .add_Icon {
        height: 31.5px;
        width: 31.5px;
        margin-right: 32px;
        margin-left: 18px;
      }
    }
  }

  .promoter_ {
    padding: 24px 0;
    width: 100vw;
    border-top: 1.4vw solid #f3f3f3;
    background-color: #ffffff;

    .add_text {
      margin-left: 48px;
      color: #333333;
      font-size: 26px;
      text-align: left;
    }

    .dot_item {
      margin: 15px 48px 0px 48px;
      border: 2px solid rgba(124, 134, 150, 0.5);
      border-radius: 15px;
      padding-left: 0;
      width: 80%;

      .dot_text {
        font-size: 35px;
      }
    }

    // 选中后的 背景样式 （红色背景 无边框 可根据UI需求自己修改）
    .dot_item_selected {
      border-color: #15b381 !important;
      background: #15b381 !important;
      margin: 15px 48px 0px 48px;
      border: 2px solid #15b381;
      border-radius: 15px;
      padding-left: 0;
      width: 80%;

      .dot_text {
        font-size: 35px;
      }
    }
  }

  .order_bottom {
    position: absolute;
    bottom: 30px;
    left: 50px;
    width: 650px;
    height: 90px;
    border-radius: 45px;
    line-height: 90px;
    color: #999;
    background: #e5e5e5;
    &.canSubmit {
      color: #fff;
      background: #15b381;
    }
    .text_wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .add_item {
      height: 32px;
      width: 100%;
    }

    .order_bottom_remind {
      position: absolute;
      top: -12vw;
      left: 0;
      width: 100vw;
      color: rgba(153, 153, 153, 1);
      font-size: 3.7vw;
      text-align: center;
      font-family: PingFangSC-regular;
    }
  }
}

.add_bottom {
  padding: 24px 0;
  width: 100vw;
  border-top: 1.4vw solid #f3f3f3;
  background-color: #ffffff;

  .add_text {
    margin-left: 48px;
    color: #333333;
    font-size: 26px;
    text-align: left;
  }

  .add_des1 {
    margin-left: 48px;
    border: none;
    padding-left: 0;
    width: 684px;
    /*color     : #333333;
    font-size   : 24px;*/
  }
}

.count_remind {
  display: flex;
  justify-content: flex-end;
  padding-right: 32px;
  font-size: 24px;
  color: #7c8696;
}

.at-textarea__textarea {
  font-size: 24px;
  color: #333333;
}
