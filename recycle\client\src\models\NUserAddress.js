import Taro from '@tarojs/taro'
import {
  getUserAddressList,
  saveAddress,
  editAddress,
  deleteAddress,
  searchAddress,
  setDefault,
  getJsonAddress,
} from '../services/userAddress'
import { NUserAddress } from '../config/constants.js'

const {
  EGetUserAddressList,
  ESaveAddress,
  EEditAddress,
  EDeleteAddress,
  ESearchAddress,
  ESetDefault,
  EGetJsonAddress,
  ESetState,
} = NUserAddress

export default {
  state: {
    addressList: [],
    streetJsonList: null,
    isGetAddressList: false,
  },
  effects: dispatch => ({
    async [EGetUserAddressList](payload, rootState) {
      await this.RSetState({ isGetAddressList: false })
      const response = await getUserAddressList(payload)
      await this.RSetState({ addressList: response })
      await this.RSetState({ isGetAddressList: true })
    },
    async [ESaveAddress](payload, rootState) {
      const response = await saveAddress(payload)
      let user = Taro.getStorageSync('userInfo') && JSON.parse(Taro.getStorageSync('userInfo'))
      const responses = await getUserAddressList({ id: user.id })
      await this.RSetState({ addressList: responses })
      Taro.navigateBack()
    },
    async [EEditAddress](payload, rootState) {
      const response = await editAddress(payload)
      let user = Taro.getStorageSync('userInfo') && JSON.parse(Taro.getStorageSync('userInfo'))
      const responses = await getUserAddressList({ id: user.id })
      await this.RSetState({ addressList: responses })
      //Taro.navigateTo({url: '/pages/administration/administration'})
      Taro.navigateBack()
    },
    async [EDeleteAddress](payload, rootState) {
      const response = await deleteAddress({ id: payload.id })
      const responses = await getUserAddressList({ id: payload.userID })
      await this.RSetState({ addressList: responses })
    },
    async [ESearchAddress](payload, rootState) {
      const response = await searchAddress(payload)
      await this.RSetState({ addressList: response })
    },
    async [ESetDefault](payload, rootState) {
      const response = await setDefault(payload)
    },
    async [EGetJsonAddress](payload, rootState) {
      const response = await getJsonAddress(payload)
      await this.RSetState({ streetJsonList: response })
    },
    async [ESetState](payload, rootState) {
      await this.RSetState({...payload})
    },
  }),
  reducers: {
    RSetState(state, payload) {
      return {
        ...state,
        ...payload,
      }
    },
  },
}
