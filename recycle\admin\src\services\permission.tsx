import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'

export async function getAccountList(payload: any) {
  return requestGet('/adminUser', payload)
}
export async function createAccount(payload: any) {
  return requestPost('/adminUser', payload)
}
export async function editAccount(payload: any) {
  return requestPut(`/adminUser/${payload.id}`, payload)
}
export async function getPermissionContent(payload: any) {
  return requestGet('permission', payload)
}
export async function savePermission(payload: any) {
  return requestPost('permission', payload)
}
