import Taro, {checkIsSupportFacialRecognition } from '@tarojs/taro'
import React, { Component } from 'react'
import {connect} from 'react-redux'
import { View, Image, Button, Text, Checkbox } from '@tarojs/components'
import { AtModal } from 'taro-ui'
import './qrcode.less'
import E from '../../config/E'
import QRCode from '../../utils/qrcode'
import T from "../../config/T"
class Qrcode extends Component {
  constructor() {
    super(...arguments)
    this.state = {
      imgData: null,
      locale: 'zh_CN',
    }
  }

  config = {}

  //-----------------------生命周期-------------------------//
  componentWillMount() {
  }

  componentDidMount() {
    const locale = Taro.getStorageSync('locale')
    let { user } = this.props
    let data = JSON.stringify({ userID: user.id, openid: user.openid, nickName: user.nickName, mobile: user.mobile })
    var imgData = QRCode.createQrCodeImg(data)
    this.setState({
      imgData, locale
    })
    Taro.setNavigationBarTitle({ title: locale == 'en' ? 'QR Code' : '个人二维码' })
  }

  componentWillUnmount() {}

  componentDidShow() {}

  componentDidHide() {}

  //-----------------------事件-------------------------//

  //-----------------------渲染-------------------------//
  render() {
    return (
      <View className="successfulOrder">
        <View className="title">
          { T.qrcodePage.title }
        </View>
        <View className="content_wrapper">
          <Image src={this.state.imgData} style={{ width: '70vw', height: '70vw' }}></Image>
        </View>
        <View className="introduce">
          { T.qrcodePage.desc }
        </View>
        {/* <Button
          onClick={() => {
            this.scanCode()
          }}>
          扫码
        </Button> */}
      </View>
    )
  }
}

export default connect(({ NUser: { userInfo } }) => ({
  user:userInfo
}))(Qrcode)
