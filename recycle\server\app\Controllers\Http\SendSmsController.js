"use strict";

const _ = require("lodash");
const moment = require("moment");

const { AliYunService } = require("../../Services");
const { ERR, E } = require("../../../../../constants");

//订单废品关联表
class SendSmsController {
  //SMS_183150423 通用回收下单后通知模板
  //SMS_183145510 大家具服务回收
  //SMS_183150390 大家电服务短信
  async smsTest({ request, response }) {
    // response.json('sms1')
    let { mobile, smsCode, smsParam } = request.all()
    if (!mobile || !smsCode) {
      throw ERR.API_ERROR
    }
    if (!smsParam) {
      smsParam = ''
    }
    let smsRes = await AliYunService.sendSMS(
      mobile,
      smsCode,
      smsParam
    )
    response.json(smsRes)
  }

  async smsSend({ request, response }) {
    // response.json('sms1')
    let { mobile, smsCode, smsParam } = request.all()
    if (!mobile || !smsCode) {
      throw ERR.API_ERROR
    }
    if (!smsParam) {
      smsParam = ''
    }
    let smsRes = await AliYunService.sendSMS(
      mobile,
      smsCode,
      smsParam
    )
    response.json(smsRes)
  }

}

module.exports = SendSmsController;
