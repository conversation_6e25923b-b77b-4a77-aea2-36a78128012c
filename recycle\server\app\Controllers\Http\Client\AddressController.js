'use strict'

const _ = require('lodash')
const moment = require('moment')
const array = require('../../../../public/address')

//省市区街道级联
class AddressController {
  async index({ request, response }) {
    let { code } = request.all()
    let length = code.length
    let address = []

    if (!code) {
      //获取省份
      array.forEach((value, index) => {
        address.push({
          name: value.name,
          code: value.code
        })
      })
    }

    if (length === 2) {
      //省份下城市
      array.forEach((value, index) => {
        if (value.code === code) {
          //address.push(value)
          value.children.forEach((city, cityIndex) => {
            address.push({
              name: city.name,
              code: city.code
            })
          })
        }
      })
    } else if (length === 4) {
      //城市下区
      let province = code.substring(0, 2)
      array.forEach((value, index) => {
        if (value.code === province) {
          value.children.forEach((city, cityIndex) => {
            if (city.code === code) {
              city.children.forEach((area, areaIndex) => {
                address.push({
                  name: area.name,
                  code: area.code
                })
              })
            }
          })
        }
      })
    } else if (length === 6) {
      //区下街道
      let province = code.substring(0, 2)
      let cityCode = code.substring(0, 4)
      array.forEach((value, index) => {
        if (value.code === province) {
          value.children.forEach((city, cityIndex) => {
            if (city.code === cityCode) {
              city.children.forEach((area, areaIndex) => {
                if (area.code === code) {
                  area.children.forEach((street, streetIndex) => {
                    address.push({
                      name: street.name,
                      code: street.code
                    })
                  })
                }
              })
            }
          })
        }
      })
    }
    return address
  }
}

module.exports = AddressController
