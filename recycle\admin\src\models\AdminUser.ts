import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'
import { EDelete, EGet, EGetAddress, EGetIssues, EPost, EPut, NAdminUser, NCompany, RAdd, RSetState } from '../common/action'
import { adapterPaginationResult } from '../common/utils'

export default {
  namespace: NAdminUser,
  state: {},
  reducers: {
    [RSetState](state: any, payload: any) {
      return { ...state, ...payload }
    },
    [RAdd](state: { count: any }, payload: any) {
      return { ...state, count: state.count + payload }
    },
  },
  effects: {
    //  标准CURD示例
    async [EGet]({ payload }: any) {
      let result = await requestGet('adminUser', payload)
      return adapterPaginationResult(result)
    },

    // 列表及翻页
    async [EGetAddress]({ payload }: any) {
      return await requestGet('address', payload)
    },
  },
}
