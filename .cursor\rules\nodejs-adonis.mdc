---
globs: *.js
description: Node.js + Adonis.js 后端开发规范
---

# Node.js + Adonis.js 后端开发规范

## 适用范围
- `recycle/server/` - API 服务后端

## Adonis.js 架构
- MVC 模式：Model-View-Controller
- 路由定义：`start/routes.js`
- 控制器：`app/Controllers/Http/`
- 模型：`app/Models/`
- 中间件：`app/Middleware/`
- 服务：`app/Services/`

## 控制器开发规范
```javascript
'use strict'

class OrderController {
  /**
   * 获取订单列表
   * @param {Object} request - 请求对象
   * @param {Object} response - 响应对象
   */
  async index({ request, response }) {
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 20);
      
      const orders = await Order.query()
        .paginate(page, limit);
        
      return response.json({
        code: 200,
        data: orders,
        message: '获取成功'
      });
    } catch (error) {
      return response.status(500).json({
        code: 500,
        message: '服务器错误',
        error: error.message
      });
    }
  }
}

module.exports = OrderController
```

## 数据模型规范
- 使用 Lucid ORM 进行数据库操作
- 模型文件放在 `app/Models/` 目录
- 定义模型关系和验证规则
- 使用软删除和时间戳

## 路由组织
- 管理端路由：`routes_admin.js`
- 客户端路由：`routes_client.js`
- 应用端路由：`routes_app.js`

## API 响应格式
```javascript
// 成功响应
{
  code: 200,
  data: {},
  message: '操作成功'
}

// 错误响应
{
  code: 400,
  message: '参数错误',
  error: 'validation failed'
}
```

## 中间件使用
- 认证中间件：JWT 验证
- 权限中间件：角色和权限检查
- 日志中间件：请求日志记录
- 跨域中间件：CORS 处理

## 数据库迁移
- 迁移文件：`database/migrations/`
- 工厂文件：`database/factory.js`
- 种子文件：数据初始化

## 错误处理
- 统一异常处理器：`app/Exceptions/Handler.js`
- 自定义异常类型
- 详细的错误日志记录

## 关键文件
- 路由入口：[recycle/server/start/routes.js](mdc:recycle/server/start/routes.js)
- 应用配置：[recycle/server/config/app.js](mdc:recycle/server/config/app.js)
- 数据库配置：[recycle/server/config/database.js](mdc:recycle/server/config/database.js)
- 控制器目录：[recycle/server/app/Controllers/](mdc:recycle/server/app/Controllers/)
- 模型目录：[recycle/server/app/Models/](mdc:recycle/server/app/Models/)

## 常用 Adonis 命令
```bash
# 创建控制器
adonis make:controller OrderController

# 创建模型
adonis make:model Order

# 创建中间件
adonis make:middleware Auth

# 运行迁移
adonis migration:run
```