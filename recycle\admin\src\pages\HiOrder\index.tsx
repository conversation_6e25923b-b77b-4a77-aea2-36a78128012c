import { CopyOutlined, DownloadOutlined, PlusOutlined } from '@ant-design/icons'
import type { ProFormInstance } from '@ant-design/pro-components'
import { Button, Input, Modal, Badge, Select, Upload, message, notification, Space, Spin } from 'antd'
import ProTable, { } from '@ant-design/pro-table'
import type { ActionType, ProColumns } from '@ant-design/pro-table'
import ProCard from '@ant-design/pro-card'
import { effect, useConnect } from 'dva17'
import { Fragment, useEffect, useRef, useState } from 'react'
import { EDeleteImport, EGetImport, EBatchDestroyImport, NHiCollection, NUser, EHiOrderMaintainList, EOrderMaintainUpdate } from '../../common/action'
import { recycleTypeEnum } from '../../common/enum'
import { SERVER_HOME, SERVER_HOME_File } from '../../common/config'
import dayjs from 'dayjs'
import qs from 'qs'
import copy from 'copy-to-clipboard'
import styles from './index.module.less'
import { changeOrderImportStatus } from '../../services/order'

type Item = {
  offTime: any
  id: number
  number: number
  orderNo: string
  from: string
  createdAt: any
  finishedAt: any
  takeTime: any
  mobile: string
  wasteType: string
  estimatedMoney: number
  company: {
    companyName: string
  }
}

type OrderMaintainItem = {
  id: number
  orderNo: string
  status: string
  createdAt: string
  updatedAt: string
  remark: string
}
const { Option } = Select

export default () => {
  const { currentUser } = useConnect(NUser)
  const { lastSearch, orderMaintain } = useConnect(NHiCollection)
  const formRef = useRef<ProFormInstance>()
  const [visibleImport, setVisibleImport] = useState<boolean>(false)
  const [visibleMaintain, setVisibleMaintain] = useState<boolean>(false)
  const actionRef = useRef<ActionType>()
  const [visible, setVisible] = useState<boolean>(false)
  const [loading, setLoading] = useState<boolean>(false)
  const [showData, setShowData] = useState<any>(null)
  const [selectedRows, setSelectedRows] = useState<any>([])
  const [inputValue, setInputValue] = useState<any>(null)
  const [totalCount, setTotalCount] = useState<number>(0)
  const [totalInfoFee, setTotalInfoFee] = useState<number>(0)

  // 防重复请求的状态管理
  const [importing, setImporting] = useState<boolean>(false) // 文件导入中
  const [refreshing, setRefreshing] = useState<boolean>(false) // 页面刷新中
  const [maintaining, setMaintaining] = useState<boolean>(false) // 处理维护订单中
  const lastRequestTime = useRef<number>(0) // 上次请求时间
  const REQUEST_DEBOUNCE_DELAY = 1000 // 防抖延迟1秒

  useEffect(() => {
    effect(NHiCollection, EHiOrderMaintainList, { status: '待维护' })
  }, [])

  const handleEdit = async (row: any) => {
    setVisible(true)
    setShowData(row)
  }

  const orderCancel = () => {
    setVisible(false)
  }

  const orderOk = async () => {
    if (!dayjs(inputValue).isValid()) {
      message.error({ content: '日期格式错误，请按照"年-月-日"格式', duration: 3000 })
    } else {
      showData.workTime = dayjs(inputValue).format('YYYY-MM-DD') + dayjs(inputValue).format(' HH:mm:ss')
      console.log(showData)
      if (showData) {
        delete showData.updatedAt
        delete showData.finishedAt
        delete showData.createdAt
        delete showData.company
        delete showData.worker
        await changeOrderImportStatus(showData).then(async () => {
          notification.success({
            message: '成功！',
            description: '修改成功',
            duration: 2,
          })
          setVisible(false)
          refreshPage()
        })
      } else {
        notification.success({
          message: '成功！',
          description: '创建成功',
          duration: 2,
        })
        setVisible(false)
        refreshPage()
      }
    }
  }

  const changeValue = (vo: any) => {
    if (vo.workTime) {
      vo.workTime = showData?.workTime && dayjs(showData?.workTime).format('YYYY-MM-DD ') + vo.workTime
    }
    if (vo.dateTime) {
      setInputValue(vo.dateTime)
      delete vo.dateTime
    }
    if (vo.address) {
      showData.countyCode = 1
    }
    setShowData({ ...showData, ...vo })
  }

  const handleDel = async (row: any) => {
    Modal.confirm({
      title: '确认删除该数据',
      content: <div>删除后数据无法恢复！</div>,
      okText: '确认删除',
      cancelText: '退出',
      onOk: async () => {
        effect(NHiCollection, EDeleteImport, { id: row })
        notification.success({
          message: '成功！',
          description: '删除成功',
          duration: 2,
        })
        refreshPage()
      },
      width: 700,
    })
  }

  const expandedRowRender = (row: any) => {
    return (
      <div className={styles.item_wrapper} >
        <div className={styles.item} style={{ marginTop: 30 }}>
          <span className={styles.item_title}> 下单时间：</span>
          <div className={styles.item_content} > {dayjs(row.createdAt).format('YYYY-MM-DD HH:mm:ss')} </div>
        </div>
        <div className={styles.item} style={{ marginTop: 20 }}>
          <span className={styles.item_title}> 信息费：</span>
          <div className={styles.item_content} > ¥{row?.infoFee}元 </div>
        </div>
        <div className={styles.item}>
          <span className={styles.item_title}> 预约时间：</span>
          < div className={styles.item_content} style={{ flexWrap: 'wrap' }}>
            {dayjs(row.workTime).format('YYYY-MM-DD A')}
          </div>
        </div>
        <div className={styles.item}>
          <span className={styles.item_title}> 回收物：</span>
          < div className={styles.item_content} style={{ flexWrap: 'wrap' }}>
            {row.type}
          </div>
        </div>
        <div className={styles.item}>
          <span className={styles.item_title}>备注：</span>
          < div className={styles.item_content} style={{ flexWrap: 'wrap' }}>
            {row.remark}
          </div>
        </div>
        <Fragment>
          <div className={styles.item} >
            <span className={styles.item_title}> 客户姓名：</span>
            < div className={styles.item_content} > {row.userName} </div>
          </div>
          < div className={styles.item} >
            <span className={styles.item_title}> 客户电话：</span>
            < div className={styles.item_content} > {row.userMobile ? row.userMobile : '-'} </div>
          </div>
          <>
            <div className={styles.item} >
              <span className={styles.item_title}> 回收预估价：</span>
              < div className={styles.item_content} >¥{row.apprizeAmount} </div>
            </div>
          </>
          <div className={styles.item}>
            <span className={styles.item_title}> 回收地址：</span>
            < div className={styles.item_content} >
              {row.address}
            </div>
          </div>
          {
            row.worker ? (
              <Fragment>
                <div className={styles.item} >
                  <span className={styles.item_title}> 回收员姓名：</span>
                  < div className={styles.item_content} > {row.worker?.workerName} </div>
                </div>
                < div className={styles.item} >
                  <span className={styles.item_title}> 回收员电话：</span>
                  < div className={styles.item_content} > {row.worker?.mobile} </div>
                </div>
                < div className={styles.item} >
                  <span className={styles.item_title}> 接单时间：</span>
                  < div className={styles.item_content} > {row.takeTime} </div>
                </div>
              </Fragment>
            ) : null
          }
        </Fragment>
      </div>
    )
  }

  const copyText = async (row: any) => {
    let text = row.orderNo + '\n'
    text += row.userMobile + '\n'
    text += row.address + '\n'
    text += row.type + '\n'
    text += dayjs(row.workTime).format('YYYY-MM-DD HH:mm') + '\n'
    text += row.remark
    copy(text)
    notification.success({
      message: '成功！',
      description: '复制成功',
      duration: 2,
    })
  }

  // 防重复的页面刷新函数
  const refreshPage = async () => {
    const now = Date.now()

    // 防抖检查：如果距离上次请求时间小于延迟时间，则忽略本次请求
    if (now - lastRequestTime.current < REQUEST_DEBOUNCE_DELAY) {
      console.log('请求过于频繁，已忽略本次刷新请求')
      return
    }

    // 防止重复请求
    if (refreshing) {
      console.log('页面正在刷新中，已忽略本次请求')
      return
    }

    try {
      setRefreshing(true)
      lastRequestTime.current = now
      await actionRef.current?.reload()
    } catch (error) {
      console.error('页面刷新失败:', error)
      notification.error({
        message: '刷新失败',
        description: '页面数据刷新时发生错误，请稍后重试',
      })
    } finally {
      setRefreshing(false)
    }
  }

  // 防重复的批量删除函数
  const batchDeleteOrders = async (selectedRows: any) => {
    if (importing || refreshing) {
      notification.warning({
        message: '操作中',
        description: '系统正在处理其他操作，请稍后再试',
      })
      return
    }

    Modal.confirm({
      title: '确认批量删除',
      content: <div>是否确认删除选中的{selectedRows.length}条订单数据？删除后数据无法恢复！</div>,
      okText: '确认删除',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true)
          await effect(NHiCollection, EBatchDestroyImport, { ids: selectedRows.map((row: any) => row.id) })
          notification.success({
            message: '成功！',
            description: `成功删除 ${selectedRows.length} 条订单数据`,
            duration: 3,
          })
          // 延迟刷新确保删除操作完成
          setTimeout(() => {
            refreshPage()
          }, 300)
        } catch (error: any) {
          console.error('批量删除失败:', error)
          notification.error({
            message: '删除失败',
            description: error?.message || '批量删除订单时发生错误',
          })
        } finally {
          setLoading(false)
        }
      },
      width: 700,
    })
  }

  // 防重复的文件上传处理函数
  const handleChangeFile = async ({ fileList }: any) => {
    if (fileList.length === 0) {
      return
    }

    // 防止重复上传
    if (importing) {
      notification.warning({
        message: '导入中',
        description: '文件正在导入中，请勿重复操作',
      })
      return
    }

    try {
      setImporting(true)
      setLoading(true)

      const formData = new FormData()
      formData.append('file', fileList[0].originFileObj)

      notification.info({
        message: '开始导入',
        description: '正在上传并处理Excel文件，请耐心等待...',
        duration: 3,
      })

      const response = await fetch(`${SERVER_HOME_File}uploadDGHiOrder`, {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || '文件上传失败')
      }

      const result = await response.json()

      notification.success({
        message: '导入成功',
        description: `成功导入 ${result.data?.success || 0} 条订单数据`,
        duration: 5,
      })

      // 延迟刷新页面，确保后端数据已处理完成
      setTimeout(async () => {
        await refreshPage()
      }, 500)

      setVisibleImport(false)
    } catch (error: any) {
      console.error('文件导入失败:', error)
      notification.error({
        message: '导入失败',
        description: error?.message || '文件导入过程中发生错误，请检查文件格式后重试',
        duration: 8,
      })
    } finally {
      setImporting(false)
      setLoading(false)
    }
  }

  // 防重复的维护订单处理函数
  const handleMaintain = async (row: any) => {
    if (maintaining) {
      notification.warning({
        message: '处理中',
        description: '正在处理维护订单，请勿重复操作',
      })
      return
    }

    Modal.info({
      title: '处理待维护订单',
      content: (
        <div>
          <p>订单号：{row.orderNo}</p>
          <p>状态：{row.status}</p>
          <p>备注：{row.remark}</p>
          <Select
            placeholder="选择处理方式"
            style={{ width: '100%', marginTop: 10 }}
            disabled={maintaining}
            onChange={async (value) => {
              if (maintaining) {
                notification.warning({
                  message: '处理中',
                  description: '正在处理中，请稍等...',
                })
                return
              }

              try {
                setMaintaining(true)
                await effect(NHiCollection, EOrderMaintainUpdate, { id: row.id, action: value })
                notification.success({
                  message: '处理成功',
                  description: `订单 ${row.orderNo} 已${value === 'ignore' ? '忽略' : '修复'}`,
                })
                // 延迟刷新，确保数据更新完成
                setTimeout(() => {
                  refreshPage()
                }, 300)
              } catch (error: any) {
                console.error('处理维护订单失败:', error)
                notification.error({
                  message: '处理失败',
                  description: error?.message || '处理维护订单时发生错误',
                })
              } finally {
                setMaintaining(false)
              }
            }}
          >
            <Option value="ignore">忽略</Option>
            <Option value="fix">修复</Option>
          </Select>
        </div>
      ),
      okText: '知道了',
      width: 500,
    })
  }

  const handleExportHiOrder = async () => {
    try {
      notification.info({
        message: '导出中',
        description: '正在准备导出文件，请稍候...',
      })
      let qsQuery = qs.stringify(lastSearch)
      window.open(`${SERVER_HOME}exportHiOrder?${qsQuery}`)

      notification.success({
        message: '导出成功',
        description: '嗨回收订单数据已成功导出为Excel文件',
      })
    } catch (error) {
      notification.error({
        message: '导出失败',
        description: '导出数据时发生错误',
      })
    }
  }

  const handleExportOrderMaintain = async () => {
    try {
      notification.info({
        message: '导出中',
        description: '正在准备导出文件，请稍候...',
      })

      window.open(`${SERVER_HOME}exportHiOrderMaintain`)

      notification.success({
        message: '导出成功',
        description: '待维护订单数据已成功导出为Excel文件',
      })
    } catch (error) {
      notification.error({
        message: '导出失败',
        description: '导出数据时发生错误',
      })
    }
  }

  const beforeUpload = (file: any) => {
    const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel'
    if (!isExcel) {
      message.error('只能上传Excel文件!')
    }
    return isExcel
  }

  const columns: ProColumns<Item>[] = [
    {
      title: '订单号',
      dataIndex: 'orderNo',
      width: '10%',
      copyable: false,
      render: (_, row: any) => (
        <>
          <div style={{ textAlign: 'center' }}>{row?.orderNo}</div>
          <a
            style={{ marginLeft: 15 }}
            onClick={() => {
              copyText(row)
            }}
            key="3">
            <CopyOutlined />
            复制
          </a>
        </>
      ),
    },
    // {
    //   title: '来源',
    //   dataIndex: 'from',
    //   ellipsis: false,
    // },
    {
      title: '导入时间',
      dataIndex: 'createdAt',
      ellipsis: true,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '导入时间',
      dataIndex: 'createdAt',
      ellipsis: true,
      hideInTable: true,
      valueType: 'dateRange',
      search: {
        transform: value => {
          return {
            startDate: value[0],
            endDate: value[1],
          }
        },
      },
    },
    {
      title: '接单',
      dataIndex: 'takeTime',
      copyable: false,
      sorter: (a, b) => a.takeTime - b.takeTime,
      search: false,
      renderText: (_, row) => { return (row.takeTime ? dayjs(row.takeTime).format('YYYY-MM-DD HH:mm:ss') : '-') },
    },
    {
      title: '接单',
      dataIndex: 'takeTime',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            takeStartDate: value[0],
            takeEndDate: value[1],
          }
        },
      },
    },
    {
      title: '类型',
      dataIndex: 'wasteType',
      ellipsis: false,
      hideInSearch: true,
      render: (_, row: any) => {
        return (
          <div >
            {row.type}
          </div>
        )
      },
    },
    {
      title: '类型',
      dataIndex: 'type',
      hideInTable: true,
    },
    {
      title: '省',
      dataIndex: 'province',
      ellipsis: false,
    },
    {
      title: '市',
      dataIndex: 'city',
      ellipsis: false,
    },
    {
      title: '地址',
      width: '18%',
      dataIndex: 'address',
      copyable: false,
      render: (_, row: any) => {
        let province = row.province
        let city = row.city
        let address = row.address
        let receiverTown = row.receiverTown
        return (<>{province}{city}{receiverTown}{address}</>)
      },
    },
    {
      title: '姓名',
      dataIndex: 'userName',
      copyable: false,
      ellipsis: true,
    },
    {
      title: '渠道',
      dataIndex: 'from',
      copyable: false,
      ellipsis: true,
    },
    {
      title: '完成',
      dataIndex: 'finishedAt',
      copyable: false,
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: (a, b) => a.finishedAt - b.finishedAt,
    },
    {
      title: '完成',
      dataIndex: 'finishedAt',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            finishStartDate: value[0],
            finishEndDate: value[1],
          }
        },
      },
    },
    {
      title: '揽收',
      dataIndex: 'offTime',
      copyable: false,
      ellipsis: true,
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: (a, b) => a.offTime - b.offTime,
    },
    {
      title: '揽收',
      dataIndex: 'offTime',
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            offStartDate: value[0],
            offEndDate: value[1],
          }
        },
      },
    },
    {
      title: '回收人员',
      dataIndex: 'workerName',
      hideInTable: true,
    },
    {
      title: '信息费',
      dataIndex: 'infoFee',
      copyable: false,
      ellipsis: true,
      search: false
    },
    { title: '回收人', dataIndex: 'wokerName', copyable: false, ellipsis: true, search: false },
    { title: '结算手机', dataIndex: 'countPhone', copyable: false, ellipsis: true },
    {
      title: '结算人', dataIndex: 'countName', copyable: false, ellipsis: true, search: false,
      render: (_, row: any) => {
        return (
          <div>
            {row.worker?.workerName}
          </div>
        )
      },
    },
    { title: '结算人', dataIndex: 'countName', ellipsis: true, hideInTable: true },
    {
      title: '操作', width: '8%', copyable: false, ellipsis: true, search: false,
      render: (_, row: any) => {
        return (
          <>
            {/* <Button
              onClick={() => {
                handleEdit(row)
              }}>
              编辑
            </Button> */}
            <Button
              type='primary'
              disabled={currentUser.name !== "兰姐" && currentUser.name !== "系统"}
              onClick={() => {
                handleDel(row.id)
              }}>
              删除
            </Button>
          </>
        )
      },
    },
  ]

  return (
    <ProCard>
      <Spin spinning={loading}>
        <ProTable<Item>
          formRef={formRef}
          actionRef={actionRef}
          columns={columns}
          expandable={{
            expandedRowRender: (record) => expandedRowRender(record),
          }}
          request={async (params = {}, sorter) => {
            const result = await effect(NHiCollection, EGetImport, { ...params, ...sorter }) as any
            // 计算统计数据
            if (result && result.data) {
              setTotalCount(result.total || result.data.length || 0)
              // 计算信息费合计
              const infoFeeSum = result.totalInfoFee
              setTotalInfoFee(infoFeeSum)
            }

            return result
          }}
          pagination={{}}
          rowKey="id"
          dateFormatter="string"
          headerTitle={
            <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
              <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
                订单量: {totalCount && totalCount.toLocaleString()}
              </span>
              <span style={{ fontWeight: 'bold', color: '#52c41a' }}>
                信息费合计: ¥{totalInfoFee && totalInfoFee.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </span>
            </div>
          }
          search={{}}
          toolBarRender={() => [
            <Button
              disabled={currentUser.level == '服务商' || importing || loading}
              loading={importing}
              style={{ marginRight: 20 }}
              type="primary"
              onClick={() => {
                if (importing || loading) {
                  notification.warning({
                    message: '操作中',
                    description: '系统正在处理中，请稍后再试',
                  })
                  return
                }
                setVisibleImport(true)
              }}>
              {importing ? '导入中...' : '导入'}
            </Button>,
            <Button
              key="exportOrder"
              type="primary"
              onClick={handleExportHiOrder}
            >
              导出订单数据
            </Button>,
            <Badge count={orderMaintain?.total || 0}>
              <Button
                disabled={currentUser.level == '服务商'}
                key="5"
                onClick={() => {
                  setVisibleMaintain(true)
                }}>
                待维护订单列表
              </Button>
            </Badge>,
          ]}
          tableAlertRender={({ selectedRowKeys, selectedRows, onCleanSelected }) => (
            <Space size={24}>
              <span>
                已选 {selectedRowKeys.length} 项
              </span>
              <Button
                size="small"
                onClick={() => batchDeleteOrders(selectedRows)}
                disabled={selectedRows.length === 0 || importing || refreshing || loading}
                loading={loading && selectedRows.length > 0}
              >
                {loading && selectedRows.length > 0 ? '删除中...' : '批量删除'}
              </Button>
            </Space>
          )}
          rowSelection={{
            onChange: (_, selectedRows) => {
              setSelectedRows(selectedRows)
            },
          }}
        />
      </Spin>

      {/* 导入订单弹窗 */}
      <Modal
        title="导入嗨回收订单"
        open={visibleImport}
        onCancel={() => setVisibleImport(false)}
        footer={null}
        width={600}
      >
        <Upload
          beforeUpload={beforeUpload}
          onChange={handleChangeFile}
          showUploadList={false}
          accept=".xlsx,.xls"
          disabled={importing || loading}
        >
          <Button
            icon={<PlusOutlined />}
            loading={importing}
            disabled={importing || loading}
          >
            {importing ? '上传中...' : '选择Excel文件'}
          </Button>
        </Upload>
        <div style={{ marginTop: 16, color: '#666' }}>
          支持格式：Excel文件(.xlsx, .xls)
        </div>
      </Modal>

      {/* 待维护订单弹窗 */}
      <Modal
        title="待维护订单列表"
        open={visibleMaintain}
        onCancel={() => setVisibleMaintain(false)}
        footer={[
          <Button key="export" onClick={handleExportOrderMaintain}>
            导出待维护订单
          </Button>,
          <Button key="close" onClick={() => setVisibleMaintain(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        <ProTable<OrderMaintainItem>
          search={false}
          toolBarRender={false}
          pagination={{ pageSize: 10 }}
          dataSource={orderMaintain?.data || []}
          columns={[
            { title: '订单号', dataIndex: 'orderNo' },
            { title: '状态', dataIndex: 'status' },
            { title: '备注', dataIndex: 'remark' },
            { title: '创建时间', dataIndex: 'createdAt', valueType: 'dateTime' },
            {
              title: '操作',
              render: (_, row) => (
                <Button size="small" onClick={() => handleMaintain(row)}>
                  处理
                </Button>
              ),
            },
          ]}
        />
      </Modal>

      {/* 编辑订单弹窗 */}
      <Modal
        title="编辑订单"
        open={visible}
        onOk={orderOk}
        onCancel={orderCancel}
        width={600}
      >
        <div>
          <Input
            placeholder="请输入新的工作时间"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
          />
        </div>
      </Modal>
    </ProCard>
  )
}
