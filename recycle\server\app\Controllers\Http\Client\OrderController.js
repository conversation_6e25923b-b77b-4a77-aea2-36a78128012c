'use strict'

const _ = require('lodash')
const moment = require('moment')
const Env = use('Env')
const {
  <PERSON><PERSON><PERSON>e, UserAddress,
  User, Worker,
  CompanyArea, ClientOrderCancel, OrderComplaint,
  ReqLog, ClientOrder, ClientCommission,
  ClientOrderLog,
  Waste,
  ClientEstimate,
  COrder
} = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { WXService } = require('../../../Services')
const Config = require('../../../Util/Config')
const { _remindMaster, _record } = require('../../../Services/OrderService')
const DebugUtil = require('../../../Util/DebugUtil')

//订单
class OrderController {
  async index({ request, response }) {
    let { page = 1, perPage = 10, sort = 'asc' } = request.all()
    let query = ClientOrder.query().orderBy('id', sort)
    let vo = await query.paginate(page, perPage)
    response.json(vo)
  }
  //获取订单详情
  async show({ request, params, response }) {
    let { user } = request
    let userID = user.id
    let vo = await ClientOrder.query()
      .where('id', params.id)
      .where('userID', userID)
      .with('worker')
      .with('userAddress')
      .with('wasteInfo')
      .with('cancel', (b) => b.whereIn('whoCancel', [E.CancleOrderStatus.Client, E.CancleOrderStatus.Master, E.CancleOrderStatus.Admin]))
      .with('complaint')
      .first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
  async showDetail({ request, params, response }) {
    let { user } = request
    let userID = user.id
    let vo = await COrder.query()
      .where('id', params.id)
      .where('userID', userID)
      .with('worker')
      .with('cancel', (b) => b.whereIn('whoCancel', [E.CancleOrderStatus.Client, E.CancleOrderStatus.Master, E.CancleOrderStatus.Admin]))
      .first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
  //创建订单
  async store({ request, response }) {
    let { userID } = request
    let {
      workTime,
      remark,
      userAddressID,
      wasteType,
      waste_1st_ID,
      userName,
      userMobile,
      estimatedMoney,
      userUpload,
      wasteID,
      from = '冬瓜回收',
    } = request.all()
    let commission = 0
    let infoFee = 0
    if (!workTime || !userAddressID || !waste_1st_ID || !from || !wasteID) {
      throw ERR.INVALID_PARAMS
    }
    // 接口记录
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '小程序 创建订单' })
    waste_1st_ID = parseInt(waste_1st_ID)
    const address = await UserAddress.find(userAddressID)
    let companyA = await CompanyArea.query()
      .where('area', address.toJSON().district)
      .where('name', address.toJSON().subDistrct)
      .where('type', waste_1st_ID).first()
    let Wasteinfo = await ClientEstimate.find(wasteID)
    let commissionInfo = await ClientCommission.query()
      .where('area', address.toJSON().province)
      .where('type', Wasteinfo.type).first()
    if (!commissionInfo) {
      commissionInfo = await ClientCommission.query()
        .where('area', address.toJSON().city)
        .where('type', Wasteinfo.type).first()
    }
    if (!commissionInfo) {
      commissionInfo = await ClientCommission.query()
        .where('area', address.toJSON().district)
        .where('type', Wasteinfo.type).first()
    }
    if (commissionInfo && commissionInfo.toJSON().priceType === 'fixed') {
      commission = commissionInfo.toJSON().price
      infoFee = commissionInfo.toJSON().infoFee
    } else {
      commission = 0
      infoFee = 0
    }
    const createData = {
      wasteID: wasteID,
      workTime,
      remark,
      userAddressID,
      wasteType,
      waste_1st_ID,
      userName,
      userMobile,
      userUpload,
      userID,
      estimatedMoney,
      commission,
      infoFee,
      companyID: companyA && companyA.companyID,
      from: E.OrderSource.Donggua,
      status: E.OrderStatus.Reservation,
    }
    let userData = await User.findBy({ id: userID })
    if (userData.salesman_id) {
      createData.salesman_id = userData.salesman_id
    }
    // 创建预约订单
    let vo = await ClientOrder.create(createData)
    vo = await ClientOrder.query().where('id', vo.id).first()
    vo.orderNO = moment().format('YYYYMMDD') + vo.id

    vo.keywords = address.toJSON().province + address.toJSON().city
      + address.toJSON().district + address.toJSON().subDistrct
      + address.toJSON().address + address.toJSON().realname
      + address.toJSON().mobile + moment().format('YYYYMMDD') + vo.id
    await vo.save()
    response.json(vo)
    let order = vo.toJSON()
    await ClientOrderLog.create({
      userID,
      orderID: vo.id,
      content: remark,
      status: E.OrderLogStatus.Create,
    })
    await ReqLog.create({ res: JSON.stringify(order), source: '小程序 创建订单' })
  }
  async clientPostOrder({ request, response }) {
    let { userID } = request
    let {
      phone,
      remark,
      spu,
      from = '冬瓜回收小程序',
      appointmentDate,
      appointmentTime,
      estimatedPrice,
      useraddress,
      name,
      images,
      coldType,
      functions,
      outside,
      years,
      province,
      city,
      district,
      subType,
      category,
    } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '小程序订单创建' })
    let user = await User.findBy({ id: userID })
    if (!user) {
      throw ERR.RESTFUL_GET_ID
    }
    if (!userID || !phone || !category || !name || !useraddress) {
      throw ERR.INVALID_PARAMS
    }

    let infoFee = 0
    let commissionInfo = null
    let type = DebugUtil.regDGReturn(category, subType)
    let commissionQuery = ClientCommission.query()
      .where('source', '冬瓜回收')
      .where('type', type)
    if (['江苏省', '浙江省', '上海市'].includes(province)) {
      commissionInfo = await commissionQuery
        .where('area', province).first()
    } else if (['安徽省'].includes(province) && ['合肥市'].includes(city)) {
      commissionInfo = await commissionQuery
        .where('area', city).first()
    } else if (['安徽省'].includes(province) && city !== '合肥市') {
      commissionInfo = await commissionQuery
        .where('area', province).first()
    } else {
      commissionInfo = await commissionQuery
        .where('area', city).first()
    }
    if (commissionInfo) {
      infoFee = commissionInfo.toJSON().price
    }
    const createData = {
      userID,
      companyID: 36,
      salesman_id: user.salesman_id,
      category,
      province,
      city,
      town: district,
      userName: name,
      userPhone: phone,
      address: useraddress,
      infoFee,
      from,
      spu,
      coldType,
      functions,
      outside,
      years,
      subType,
      images: [images.map(item => item.serverUrl).join(',')],
      workDate: appointmentDate,
      workTime: appointmentTime,
      estimatedPrice: estimatedPrice,
      status: E.OrderStatus.Reservation,
      remark,
      keyword: useraddress + name + phone + spu + coldType + functions + outside + years + subType + category,
    }
    let vo = await COrder.create(createData)
    vo.orderNo = moment().format('YYYYMMDD') + vo.id
    await vo.save()
    await ClientOrderLog.create({
      userID,
      orderID: vo.id,
      content: remark,
      status: E.OrderLogStatus.Create,
    })
    response.json(vo)
  }
  //更新订单内容
  async updateInfo({ request, params, response }) {
    let id = params.id,
      vo = await ClientOrder.find(id),
      { order: data, orderWastes } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '小程序 更新订单' })
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    if (data) {
      _.assign(vo, data)
      await vo.save()
    }
    if (orderWastes && orderWastes.length > 0) {
      await vo.orderWastes().delete()

      orderWastes.forEach(async function (itm) {
        let orderWaste = new OrderWaste()
        _.assign(orderWaste, itm)
        await orderWaste.save()
      })
    }
    await ReqLog.create({ res: JSON.stringify(vo), source: '小程序 更新订单' })
    response.json(vo)
  }
  async updateImage({ request, params, response }) {
    let { imgs } = request.all()
    if (imgs.length <= 0) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    let vo = await ClientOrder.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    _.forEach(imgs, function (value, key) {
      let keyName = 'img' + (key + 1)
      vo[keyName] = value
    })

    await vo.save()
    response.json(vo)
  }
  //更新订单状态
  async updateStatus({ request, params, response }) {
    let { status, cancelReason } = request.all()
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '小程序 更新订单状态' })
    let user = request.user
    if (!status || status !== E.OrderStatus.Cancelled) {
      throw ERR.INVALID_PARAMS
    }
    let vo = await ClientOrder.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    vo.status = status
    if (status == E.OrderStatus.Cancelled && vo.waste_1st_ID === 1) {
      console.log('旧衣旧书')
    }
    if (status == E.OrderStatus.Cancelled) {
      let orderCancel = await ClientOrderCancel.create({
        orderID: vo.id,
        userID: user.id,
        companyID: vo.companyID,
        cancelReason,
        whoCancel: E.CancleOrderStatus.Client,
        workerID: vo.workerID ? vo.workerID : null,
      })
    }
    await vo.save()
    await _record(vo, status)
    response.json(vo)
    let order = vo.toJSON()
    if (vo && vo.workerID) {
      let workerInfo = await Worker.find(vo.workerID)
      _remindMaster(workerInfo, order, E.OrderStatus.Cancelled)
    }
    await ClientOrderLog.create({
      userID: user.id,
      orderID: vo.id,
      content: cancelReason,
      status: E.OrderLogStatus.Cancelled,
    })
    await ReqLog.create({ res: JSON.stringify(order), source: '小程序 更新订单状态' })
  }
  //订单的投诉
  async complaint({ request, response }) {
    let { orderID, content } = request.all()
    let { user } = request
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '小程序 订单投诉' })
    let vo = await ClientOrder.query().where('id', orderID).where('userID', user.id).first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    let complaint = await OrderComplaint.query().where('orderID', orderID).where('userID', user.id).first()
    if (!complaint) {
      complaint = await OrderComplaint.create({
        orderID,
        userID: user.id,
        workerID: vo.workerID,
        companyID: vo.companyID,
        content,
        wasteType: vo.wasteType,
        orderNO: vo.orderNO,
      })
    } else {
      complaint.content = content
      await complaint.save()
    }
    response.json(complaint)
    await ReqLog.create({ res: JSON.stringify(complaint), source: '小程序 订单投诉' })
  }
  async getOrderList({ request, response }) {
    let { userID, status, page = 1, perPage = 10, sort = 'asc' } = request.all()
    if (!status || !userID) {
      throw ERR.INVALID_PARAMS
    }
    let query = COrder.query()
      .where('userID', userID)
      .where('status', status)
      .with('worker')
      .orderBy('id', sort)
    let vo = await query.paginate(page, perPage)
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
  //各种状态的订单列表
  async userList({ request, params, response }) {
    let { userID, status, page = 1, perPage = 10, sort = 'asc' } = request.all()
    userID = request.user.id
    if (!status || !userID) {
      throw ERR.INVALID_PARAMS
    }
    let query = ClientOrder.query()
      .where('userID', userID)
      .where('status', status)
      .whereNotNull('userAddressID')
      .with('worker')
      .with('userAddress')
      .with('wasteInfo')
      .orderBy('id', sort)
    let vo = await query.paginate(page, perPage)
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }

  async _pickWorker(workers, workTime) {
    workers = workers.toJSON()
    let workerIDS = []
    // console.log(workers.length)
    for (let index = 0; index < workers.length; index++) {
      // 回收人员当时已有订单情况
      await ClientOrder.query()
        .where('workTime', workTime)
        .where('workerID', workers[index].workerID)
        .getCount()
        .then((res) => {
          if (!res) {
            workerIDS.push(workers[index].workerID)
          }
        })
    }
    return workerIDS
  }
  async _pickWorkerForbidden(workerIDS) {
    // console.log(workerIDS.length)
    let forbiddenIDS = []
    for (let index = 0; index < workerIDS.length; index++) {
      // 回收人员当时已有订单情况
      await Worker.query()
        .where('id', workerIDS[index])
        .where('forbidden', 0)
        .getCount()
        .then((res) => {
          if (!res) {
            forbiddenIDS.push(workerIDS[index])
          }
        })
    }
    return forbiddenIDS
  }
}

module.exports = OrderController
