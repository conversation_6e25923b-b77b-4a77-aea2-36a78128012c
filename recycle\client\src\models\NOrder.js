import Taro from '@tarojs/taro'
import {
  createOrder,
  updateImage,
  getOrderList,
  updateOrder,
  updateStatus,
  getOrderDetail,
  getAllOrder,
  payOrder,
  sendSMS,
  getRating,
  createOrderRating,
  orderComplaint,
  getWorkerInfo,
} from '../services/order'
import { NOrder } from '../config/constants'
import E from '../config/E'
const {
  ETheOrderInfo,
  ECreateOrder,
  EGetAimTime,
  EGetOrderList,
  EUpdateOrder,
  EOrderComplaint,
  EUpdateOrderStatus,
  EGetOrderDetail,
  ERemarkOrder,
  ESelectAddress,
  EGetAllOrder,
  EPayOrder,
  EUserPay,
  EGetRating,
  ECreateOrderRating,
  EGetWorker,
  ESetState,
} = NOrder

export default {
  state: {
    addressList: null,
    streetJsonList: null,
    isGetAddressList: false,
    time: null,
    isGetTime: false,
    orderID: null,
    orderList: null,
    orderDetail: null,
    selectAddress: false,
    addressValue: null,
    isOrderListGet: false,
    messageRemind: [null, null, null, null],
    orderRemark: null,
    isGetDetail: false,
    ratingText: null,
    dotInfo: null,
    orderInfo: null,
    workerInfo: null,
  }, // initial state
  effects: dispatch => ({
    async [ETheOrderInfo](payload, rootState) {
      await this.RSetState({ orderInfo: payload })
      Taro.navigateTo({ url: '/pages/order/index' })
    },
    //创建订单
    async [ECreateOrder](payload, rootState) {
      const response = await createOrder(payload)
      if (response && response.id) {
        await this.RSetState({ orderID: response.id })
        Taro.navigateTo({ url: '/pages/successfulOrder/successfulOrder' })
      }
    },
    //获取上门时间
    async [EGetAimTime](payload, rootState) {
      this.RSetState({
        isGetTime: false,
        time: payload.time,
        isGetTime: true,
      })
    },
    async [EGetWorker](payload, rootState) {
      await this.RSetState({ workerInfo: null })
      const response = await getWorkerInfo(payload)
      await this.RSetState({ workerInfo: response })
    },
    //获取订单列表
    async [EGetOrderList](payload, rootState) {
      await this.RSetState({ isOrderListGet: false })
      const response = await getOrderList(payload)
      await this.RSetState({ orderList: response })
      await this.RSetState({ isOrderListGet: true })
    },
    async [EUpdateOrder](payload, rootState) {
      const response = await updateOrder(payload)

      if (payload.workTime) {
        const response = await updateStatus({ id: payload.id, status: E.OrderStatus.Reservation })
      }
    },
    //订单投诉
    async [EOrderComplaint](payload, rootState) {
      const response = await orderComplaint(payload)
      await Taro.navigateBack()
      Taro.showToast({
        title: '投诉成功',
        icon: 'success',
        duration: 2000,
      })
    },
    async [EUpdateOrderStatus](payload, rootState) {
      const response = await updateStatus(payload)
      await Taro.reLaunch({ url: '/pages/my/index' })
      // if (payload.manage) {
      //   // await Taro.navigateBack()
      //   Taro.navigateTo({ url: `/pages/orderManage/index?manage=${E.OrderStatus.Cancelled}` })
      // } else {
      //   Taro.navigateTo({ url: `/pages/orderManage/index?manage=${E.OrderStatus.Cancelled}` })
      // }
      if (payload.status === E.OrderStatus.Cancelled) {
        setTimeout(() => {
          Taro.showToast({
            title: '取消成功',
            icon: 'success',
            duration: 2000,
          })
        }, 1000)
      }
    },
    async [EGetOrderDetail](payload, rootState) {
      await this.RSetState({ isGetDetail: false })
      const response = await getOrderDetail(payload)
      await this.RSetState({ orderDetail: response })
      await this.RSetState({ isGetDetail: true })
    },
    async [ERemarkOrder](payload, rootState) {
      const response = await getOrderDetail(payload)
      await this.RSetState({ orderRemark: response })
    },
    async [ESelectAddress](payload, rootState) {
      await this.RSetState({ selectAddress: false })
      await this.RSetState({ addressValue: payload })
      await this.RSetState({ selectAddress: true })
      await Taro.navigateBack()
    },
    async [EGetAllOrder](payload, rootState) {
      const reservation = await getOrderList({ status: E.OrderStatus.Reservation, perPage: 50, ...payload })
      const inProgress = await getOrderList({ status: E.OrderStatus.InProgress, perPage: 50, ...payload })
      const completed = await getOrderList({ status: E.OrderStatus.Completed, perPage: 50, ...payload })
      let length = 0
      await completed.data.forEach((order, index) => {
        if (order.isRead === 0) {
          length++
        }
      })
      await this.RSetState({ messageRemind: [reservation.data.length, inProgress.data.length, length, 0] })
    },
    async [EPayOrder](payload, rootState) {
      Taro.showToast({
        icon: 'loading',
        mask: true,
      })
      const response = await createOrder(payload)
      const changeStatus = await updateStatus({ id: response.id, status: E.OrderStatus.InProgress })
      let money = Math.floor(Math.random() * (200 - 1) + 1)
      const updatePayMoney = await updateOrder({ payMoney: money, id: changeStatus.id })
      const { pay, payObj } = await payOrder({ type: '微信支付', orderID: updatePayMoney.id })
      let payIt = await new Promise((resolve, reject) => {
        // wx.requestPayment({
        //   ...payObj,
        //   success(res) {
        //     Taro.showToast({
        //       title: '成功',
        //     })
        //     //resolve(1);
        //   },
        //   fail(res) {
        //     resolve(-1)
        //   },
        // })
      })
    },
    async [EUserPay](payload, rootState) {
      const { pay, payObj } = await payOrder({ type: '微信支付', orderID: payload.id })
      let payIt = await new Promise((resolve, reject) => {
        // wx.requestPayment({
        //   ...payObj,
        //   success(res) {
        //     Taro.showToast({
        //       title: '成功',
        //     })
        //     setTimeout(() => {
        //       Taro.navigateBack()
        //       Taro.navigateTo({ url: `/pages/orderManage/index?manage=${E.OrderStatus.Completed}` })
        //       Taro.navigateTo({ url: `/pages/orderDetail/index?orderNumber=${payload.id}&manage=${E.OrderStatus.Completed}` })
        //     })
          // },
          // fail(res) {
            // resolve(-1)
          // },
        // })
      })
    },
    async [EGetRating](payload, rootState) {
      const response = await getRating(payload)
      await this.RSetState({ ratingText: response })
      return response
    },
    async [ECreateOrderRating](payload, rootState) {
      const response = await createOrderRating(payload)
      await Taro.navigateBack()
      Taro.showToast({
        title: '评价成功',
        icon: 'success',
        duration: 2000,
      })
    },
    async [ESetState](payload, rootState) {
      await this.RSetState({ ...payload })
    },
  }),
  reducers: {
    RSetState(state, payload) {
      return {
        ...state,
        ...payload,
      }
    },
  },
}
