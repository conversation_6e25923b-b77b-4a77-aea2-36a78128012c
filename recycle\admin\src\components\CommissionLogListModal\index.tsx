import { Modal, Table, Tag, But<PERSON>, DatePicker, Input, Space, Form } from 'antd'
import { useEffect, useState } from 'react'
import { getPriceSetLogList  } from '../../services/price'
import { SERVER_HOME } from '../../common/config'
import dayjs from 'dayjs'

interface CommissionLogListModalProps {
  visible: boolean;
  onClose: () => void;
}

const CommissionLogListModal = ({ visible, onClose }: CommissionLogListModalProps) => {
  const [form] = Form.useForm();
  const [commissionLogs, setCommissionLogs] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });

  useEffect(() => {
    if (visible) {
      fetchCommissionLogs();
    }
  }, [visible]);

  const fetchCommissionLogs = async (params: any = {}) => {
    setLoading(true);
    try {
      const searchParams = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...params
      };
      
      const response = await getPriceSetLogList(searchParams);
      setCommissionLogs(response?.data || []);
      setPagination(prev => ({
        ...prev,
        total: response?.total || 0
      }));
    } catch (error) {
      console.error('获取佣金修改记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    form.validateFields().then(values => {
      const searchParams = {
        ...values,
        startDate: values.dateRange?.[0]?.format('YYYY-MM-DD'),
        endDate: values.dateRange?.[1]?.format('YYYY-MM-DD'),
      };
      delete searchParams.dateRange;
      
      setPagination(prev => ({ ...prev, current: 1 }));
      fetchCommissionLogs(searchParams);
    });
  };

  const handleReset = () => {
    form.resetFields();
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchCommissionLogs();
  };



  const columns = [
    {
      title: '修改时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (createdAt: any) => (
        <span>{createdAt ? dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss') : '-'}</span>
      ),
    },
    {
      title: '区域',
      dataIndex: 'before_data',
      key: 'before_data',
      width: 100,
      render: (before_data: any) => <span>{before_data?.area}</span>,
    },
    {
      title: '品类',
      dataIndex: 'before_data',
      key: 'before_data',
      width: 120,
      render: (before_data: any) => <span>{before_data?.type}</span>,
    },
    {
      title: '平台',
      dataIndex: 'before_data',
      key: 'before_data',
      width: 120,
      render: (before_data: any) => <span>{before_data?.source}</span>,
    },
    {
      title: '修改前金额',
      dataIndex: 'before_data',
      key: 'before_data',
      width: 120,
      render: (before_data: any) => <span>¥{before_data?.price}</span>,
    },
    {
      title: '修改后金额',
      dataIndex: 'after_data',
      key: 'after_data',
      width: 120,
      render: (after_data: any) => <span>¥{after_data?.price}</span>,
    },
    {
      title: '变化',
      key: 'change',
      width: 100,
      render: (record: any) => {
        const change = record.after_data?.price - record.before_data?.price || 0;
        return (
          <Tag color={change >= 0 ? 'green' : change < 0 ? 'red' : 'default'}>
            {change > 0 ? '+' : ''}{change}
          </Tag>
        );
      },
    },
    {
      title: '修改人',
      dataIndex: 'operatorName',
      key: 'operatorName',
      width: 100,
      render: (name: string, record: any) => (
        <span>{name || record.operator?.name || '-'}</span>
      ),
    },
    {
      title: '修改原因',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      render: (remark: string) => <span>{remark || '-'}</span>,
    },
  ];

  return (
    <Modal
      title="佣金修改记录"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1200}
      destroyOnClose
    >
      <Form
        form={form}
        layout="inline"
        style={{ marginBottom: 16 }}
      >
        <Form.Item name="dateRange" label="修改日期">
          <DatePicker.RangePicker />
        </Form.Item>
        <Form.Item name="area" label="区域">
          <Input placeholder="请输入区域" style={{ width: 120 }} />
        </Form.Item>
        <Form.Item name="type" label="品类">
          <Input placeholder="请输入品类" style={{ width: 120 }} />
        </Form.Item>
        <Form.Item name="operatorName" label="修改人">
          <Input placeholder="请输入修改人" style={{ width: 120 }} />
        </Form.Item>
        <Form.Item>
          <Space>
            <Button type="primary" onClick={handleSearch}>
              搜索
            </Button>
            <Button onClick={handleReset}>
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>

      <Table
        columns={columns}
        loading={loading}
        dataSource={commissionLogs}
        rowKey={(record) => record.id || JSON.stringify(record)}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          onChange: (page, pageSize) => {
            setPagination(prev => ({ ...prev, current: page, pageSize: pageSize || 20 }));
            fetchCommissionLogs({ page, pageSize });
          }
        }}
      />
    </Modal>
  );
};

export default CommissionLogListModal;