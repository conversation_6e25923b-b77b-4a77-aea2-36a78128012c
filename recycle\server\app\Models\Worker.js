'use strict'

const Model = use('Model')
const Database = use('Database')

//上门师傅信息
class Worker extends Model {
  static get table() {
    return 'worker'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return 'updatedAt'
  }

  company() {
    return this.belongsTo('App/Models/Company', 'companyID', 'id')
  }
  order() {
		return this.hasMany('App/Models/Order', 'id', 'workerID')
			.select(Database.raw('workerID,status,sum(infoFee) as totalMoney,count(id) as count'))
			.groupBy('workerID')
			.groupBy('status')
	}
  insure() {
    return this.hasOne('App/Models/WorkerInsure', 'id', 'workerID')
  }
  countUser() {
    return this.hasMany('App/Models/JDCOWorker', 'id', 'managerID')
  }
  HiCountUser() {
    return this.hasMany('App/Models/HiCOWorker', 'id', 'managerID')
  }
  manager() {
    return this.belongsTo('App/Models/Worker', 'managerID', 'id')
  }
  assistants() {
    return this.hasMany('App/Models/Worker', 'id', 'managerID')
  }
}

module.exports = Worker
