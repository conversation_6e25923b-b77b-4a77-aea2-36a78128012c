.orderManage {
  width: 100%;
  min-height: 100vh;
  background: #f5f6f8;
  .top_title {
    width: 100%;
    height: 106px;
    background: #ffffff;
    display: flex;
    justify-content: space-between;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 100;
    box-shadow: 0px 4px 11px #cccccc;
    .manage_item {
      padding-top: 40px;
      height: 100%;
      width: 160px;
      display: flex;
      justify-content: center;
      font-size: 28px;
      color: #7c8696;
      .item_mark {
        margin-top: 10px;
        width: 100%;
        height: 12px;
        display: flex;
        justify-content: center;
      }
    }
  }
  .make_content {
    width: 100%;
    height: 106px;
  }
  .order_item {
    width: 708px;
    background: #ffffff;
    margin: 40px auto;
    padding: 24px 26px 0 26px;
    font-size: 26px;
    color: #556073;
    position: relative;
    .line2 {
      padding-bottom: 28px;
      color: #556073;
      font-weight: 700;
    }
    .line1 {
      display: flex;
      justify-content: space-between;
      width: 100%;
      color: #15b381;
      > view:first-of-type {
        display: flex;
        image {
          margin-right: 10px;
        }
      }
      > view:last-of-type {
        padding: 6px 12px;
        border: 1px solid #15b381;
        background: rgba(232, 166, 187, 0.1);
        &.cancelled {
          color: #ff616f;
          border: 1px solid #ff616f;
          background: rgba(255, 97, 111, 0.1);
        }
      }
    }
    .line3 {
      padding-bottom: 20px;
      font-size: 24px;
      color: #556073;
    }
  }
}
.bottom_item {
  width: 100%;
  height: 0.5vh;
  background: #f5f6f8;
}
.inprogress_item {
  border-top: 1px solid #f3f3f3;
  width: 656px;
  margin: 0 auto;
  height: 70px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #556073;
  font-size: 24px;
  .right_item {
    height: 100%;
    display: flex;
    align-items: center;
    image {
      margin-left: 16px;
      width: 32px;
      height: 32px;
    }
  }
}
.mask_item {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.call_worker {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 70px;
}

.downDragBox {
  width: 100%;
  top: 0px;
  display: flex;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  font-size: 30px;
  position: absolute;
}
.upDragBox {
  bottom: 0px;
  width: 100%;
  display: flex;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  font-size: 30px;
  position: absolute;
}
.dragUpdata {
  height: 100%;
  width: 100%;
  position: absolute;
  .xiadan{
    padding-top: 160px;
    font-size: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    image{
      width: 360px;
      margin-bottom: 50px;
    }
  }
}
.downText {
  margin-left: 20px;
}
.at-badge {
  position: absolute;
  right: 0;
  top: 0;
}
