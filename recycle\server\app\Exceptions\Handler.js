'use strict'

const { ERR } = require('../../../../constants')
const BaseExceptionHandler = use('BaseExceptionHandler')

/**
 * This class handles all exceptions thrown during
 * the HTTP request lifecycle.
 *
 * @class ExceptionHandler
 */
class ExceptionHandler extends BaseExceptionHandler {
  /**
   * <PERSON>le exception thrown during the HTTP lifecycle
   *
   * @method handle
   *
   * @param  {Object} error
   * @param  {Object} options.request
   * @param  {Object} options.response
   *
   * @return {void}
   */
  async handle(error, { request, response }) {
    if (error && error.error) {
      delete error.status
      console.warn(error)
      response.status(error === ERR.AUTH_FAILED ? 401 : 400).json(error)
    } else if (error.code === 'ER_DUP_ENTRY') {
      let vo = {
        "success": true,
        "errorCode": "",
        "errorMsg": ""
      }
      // response.status(400).json(ERR.SQL_DUPLICATION)
      response.status(200).json(vo)
    } else {
      response.status(error.status).send(error.message)
    }
  }

  /**
   * Report exception for logging or debugging.
   *
   * @method report
   *
   * @param  {Object} error
   * @param  {Object} options.request
   *
   * @return {void}
   */
  async report(error, { request }) {
  }
}

module.exports = ExceptionHandler
