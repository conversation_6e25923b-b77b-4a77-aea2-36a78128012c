'use strict'

const Model = use('Model')

//订单评价
class Discovery extends Model {
  static get table() {
    return 'discovery'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return 'updatedAt'
  }
  comments() {
    return this.hasMany('App/Models/DiscoveryComment', 'id', 'discoveryID').where('isJudge', 1)
  }
  commentsAll() {
    return this.hasMany('App/Models/DiscoveryComment', 'id', 'discoveryID')
  }
}

module.exports = Discovery
