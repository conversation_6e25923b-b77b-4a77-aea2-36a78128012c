import {
  EGetJoin<PERSON>eam<PERSON>ist,
  EGetJoinTeamDetail,
  ECreateJoinTeam,
  EUpdateJoinTeam,
  EDeleteJoinTeam,
  NJoinTeam,
  RSetState,
} from '../common/action'
import { adapterPaginationResult } from '../common/utils'
import {
  getJoinTeamList,
  getJoinTeamDetail,
  createJoinTeam,
  updateJoinTeam,
  deleteJoinTeam,
} from '../services/joinTeam'

export default {
  namespace: NJoinTeam,
  state: {
    joinTeamList: [],
    joinTeamDetail: null,
    lastSearch: {},
    isLoading: false,
  },
  reducers: {
    [RSetState](state: any, payload: any) {
      return { ...state, ...payload }
    },
  },
  effects: {
    // 获取加入团队列表
    async [EGetJoinTeamList]({ payload }: any, { reducer }: any) {
      reducer(RSetState, { isLoading: true })
      const response = await getJoinTeamList(payload)
      reducer(RSetState, { 
        joinTeamList: response.data, 
        lastSearch: payload,
        isLoading: false 
      })
      return adapterPaginationResult(response)
    },

    // 获取加入团队详情
    async [EGetJoinTeamDetail]({ payload }: any, { reducer }: any) {
      const response = await getJoinTeamDetail(payload.id)
      reducer(RSetState, { joinTeamDetail: response })
      return response
    },

    // 创建加入团队记录
    async [ECreateJoinTeam]({ payload }: any, { reducer }: any) {
      const response = await createJoinTeam(payload)
      return response
    },

    // 更新加入团队记录
    async [EUpdateJoinTeam]({ payload }: any, { reducer }: any) {
      const response = await updateJoinTeam(payload.id, payload)
      return response
    },

    // 删除加入团队记录
    async [EDeleteJoinTeam]({ payload }: any, { reducer }: any) {
      const response = await deleteJoinTeam(payload.id)
      return response
    },
  },
} 