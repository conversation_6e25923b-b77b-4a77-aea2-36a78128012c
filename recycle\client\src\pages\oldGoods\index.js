import { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { View, Text, Button, Image } from '@tarojs/components'

import Taro from '@tarojs/taro'
import React, { Component } from 'react'
import T from '../../config/T'
import oldGoodsImg from '../../assets/oldGoods/index'
import { NOldGoods, NUser } from '../../config/constants'
import BaseLayout from '../../components/baseLayout'
import './index.less'

const RecyclingInstructions = [
  {
    title: '旧衣说明',
    content: [
      '1、 您下单后，我们将安排快递人员上门回收，快递工作人员与您联系确认具体回收时间',
      '2、 回收时间：9：00-18：00；',
      '3、 如果您对服务不满意，可以在“我的”个人中心“客服与投诉”予以投诉，您可以直接电话或留言投诉；',
      '4、 回收的物品我们将进行环保处理，避免二次污染和资源浪费；',
      '5、 我们提供专业化的废旧物品回收信息平台，您的支持是我们最大的动力；',
    ],
  },
  {
    title: '旧书说明',
    content: [
      '1、 不接收高中以下教材、教辅、考试考证类辅导书、单独磁带光碟；不接收报刊、杂志类等时效性刊物；不接收非法出版物、盗版、严重污染、破损、影响阅读的书籍；',
      '2、 您下单后，我们将安排快递人员上门回收，快递工作人员与您联系确认具体回收时间；',
      '3、 回收时间：9：00-18：00；',
      '4、 如果您对服务不满意，可以在“我的”个人中心“客服与投诉”予以投诉，您可以直接电话或留言投诉；',
      '5、 回收的物品我们将进行环保处理，避免二次污染和资源浪费；',
      '6、 我们提供专业化的废旧物品回收信息平台，您的支持是我们最大的动力；',
    ],
  },
  {
    title: '其他',
    content: [
      '1、 您下单后，我们将安排回收人员上门回收，回收人员与您联系确认具体回收时间；',
      '2、 回收时间：9：00-18：00；',
      '3、 如果您对服务不满意，可以在“我的”个人中心“客服与投诉”予以投诉，您可以直接电话或留言投诉；',
      '4、 回收的物品我们将进行环保处理，避免二次污染和资源浪费；',
      '5、 我们提供专业化的废旧物品回收信息平台，您的支持是我们最大的动力；',
    ],
  },
]
const Index = props => {
  const dispatch = useDispatch()
  const { id = 1 } = Taro.getCurrentInstance().router.params
  const { head, headId, wasteList } = useSelector(state => state.NOldGoods)
  const { userInfo, isPutUserInfo } = useSelector(state => state.NUser)
  const { isAddItem } = useSelector(state => state.NSystem)
  const [wuFei, setWuFei] = useState(false)
  const [showRemind, setShowRemind] = useState(false)
  const [selectID, setSelectID] = useState([])
  const [itemSelect, setItemSelect] = useState([])
  function onClickTitle(i) {
    dispatch.NOldGoods.RSetState({ headId: i })
    if (id == 1) {
      const daraY = [0, 400]
      Taro.pageScrollTo({
        scrollTop: daraY[i],
        duration: 600,
      })
    } else if (id == 2) {
      const daraY = [0, 245, 670, 1000]
      Taro.pageScrollTo({
        scrollTop: daraY[i],
        duration: 600,
      })
    }
  }

  function check(secondKind, id, waste, mark) {
    if (selectID.includes(id)) {
      selectID.forEach((theID, index) => {
        if (theID === id) {
          selectID.splice(index, 1)
        }
      })
      itemSelect.forEach((item, value) => {
        if (item.id === secondKind.id) {
          item.item.forEach((value, index) => {
            if (value.id === id) {
              item.item.splice(index, 1)
            }
          })
        }
      })
    } else {
      selectID.push(id)
      let thereIS = true
      if (itemSelect.length > 0) {
        itemSelect.forEach((item, index) => {
          if (item.id === secondKind.id) {
            item.item.push(waste)
            thereIS = false
          }
        })
        if (thereIS) {
          itemSelect.push({ title: secondKind.name, id: secondKind.id, item: [waste], enTitle: secondKind.enName })
        }
      } else {
        itemSelect.push({ title: secondKind.name, id: secondKind.id, item: [waste], enTitle: secondKind.enName })
      }
    }
    setSelectID(selectID)
    setItemSelect(itemSelect)

    dispatch.NOldGoods.RSetState({ haveChosen: itemSelect })
  }
  async function getUserInfo() {
    let user = Taro.getStorageSync('userInfo') && JSON.parse(Taro.getStorageSync('userInfo'))
    if (selectID.length > 0) {
      if (!user.nickName) {
        await Taro.getUserProfile({
          desc: '用于完善用户资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
          success: res => {
            dispatch.NUser[NUser.EPutUserInfo]({
              ...res.userInfo,
              id: user.id,
              page: 'img',
            })
          },
        })
      } else {
        createOrder()
      }
    }
  }
  function createOrder() {
    Taro.navigateTo({ url: '/pages/uploadImg/index' })
  }

  function closeRmind() {
    setShowRemind(false)
  }
  useEffect(() => {
    Taro.setNavigationBarTitle({ title: '环保回收' })
    dispatch.NOldGoods[NOldGoods.EWhichType]({
      id,
    })

    let r3_remind_next = Taro.getStorageSync('r3_remind_next')
    if (r3_remind_next) {
      setShowRemind(false)
    }
    // type: 'NOldGoods/EGetSecondKind',
    dispatch.NOldGoods[NOldGoods.EGetSecondKind]({
      id: id,
      companyID: 1, //服務商ID，對應不同的基準價格，若是服務商未設置，則使用總部設置價格
    })
  }, [])

  useEffect(() => {
    if (isPutUserInfo) {
      createOrder(isPutUserInfo)
    }
  }, [isPutUserInfo])

  return (
    <View className={`oldGoods ${showRemind ? 'cant_move' : null}`}>
      <View className="oldGoods_head">
        <View className="oldGoods_head_category">
          {head &&
            head.map((v, i) => (
              <View
                className="oldGoods_head_category_item"
                key={JSON.stringify(v)}
                onClick={() => {
                  onClickTitle(i)
                }}
              >
                <View
                  className="oldGoods_head_category_item_text"
                  key={v.name}
                  style={headId == i ? { color: '#333333' } : { color: '#7c8696' }}
                >
                  {v.name}
                </View>
                <View style={headId == i ? { display: 'block' } : { display: 'none' }} className="oldGoods_head_category_item_line"></View>
              </View>
            ))}
        </View>
      </View>

      <View className="oldGoods_content">
        {wasteList
          ? wasteList.map((value, index) => (
              <View className="old_clothes" key={value + index}>
                <View className="page-title">
                  {value.name}
                  <Text
                    onClick={() => {
                      if (id === '2') {
                        setWuFei(true)
                      } else {
                        setShowRemind(true)
                      }
                    }}
                  >
                    回收说明
                  </Text>
                </View>
                <View className="item_wrapper">
                  {//Clothing && Clothing.length > 0 ?
                  value.list.map((item, mark) => (
                    <View
                      style={selectID.includes(item.id) ? { borderColor: '#15b381' } : null}
                      className="item"
                      onClick={() => {
                        check(value, item.id, item, mark + 1)
                      }}
                      key={JSON.stringify(item)}
                    >
                      <Image
                        src={item.img}
                        className="img"
                        mode="aspectFit"
                        style={item.id === 13 || item.id === 18 ? { width: '15vw' } : null}
                      />
                      <View className="name">
                        {/* {item.id === 7 || item.id === 13 ? (
                        <View>
                          <Text>{item.id === 7 ? '易拉罐' : '利乐包装'}</Text>
                          <Text>{item.id === 7 ? '(压扁存放)' : '(剪开甩干)'}</Text>
                        </View>
                      ) : (
                        <Text>{item.name}</Text>
                      )} */}
                        <Text>{item.name}</Text>
                      </View>
                      {id === '2' ? (
                        <Text className="price" style={{ color: '#db8888' }}>
                          ￥{item.price.toFixed(2)}/kg
                        </Text>
                      ) : (
                        <Text className="price" style={{ color: '#15b381' }}>
                          回收
                        </Text>
                      )}

                      {selectID && selectID.includes(item.id) ? <Image src={oldGoodsImg.check} className="check_item" /> : null}
                    </View>
                  ))
                  //: null
                  }
                </View>
              </View>
            ))
          : null}
      </View>

      <View className="oldGoods_bottom">
        <View className="top_wrapper">
          <View className="oldGoods_bottom_shoppingCar">
            <View className="shoppingCar_box">
              <Image src={selectID && selectID.length > 0 ? oldGoodsImg.case_open : oldGoodsImg.case_close} className="shoppingCar" />
            </View>
            <Text className="shoppingCar_text">
              {selectID && selectID.length > 0 ? `已选择类型:` : '选择类型'}
              {selectID && selectID.length > 0 ? <Text style={{ fontSize: '4.7vw' }}>{selectID.length}</Text> : null}
            </Text>
          </View>

          <View className="button_getUser">
            <Button
              className="oldGoods_bottom_text"
              open-type="getUserInfo"
              onClick={() => {
                getUserInfo()
              }}
              style={{ opacity: `${selectID && selectID.length > 0 ? '1' : '0.5'}` }}
            >
              <Text>一键回收</Text>
              <Image src={oldGoodsImg.btn_arrow} className="btn_arrow" />
            </Button>
          </View>
        </View>
        {isAddItem ? <View className="bottom_wrapper"></View> : null}
      </View>

      {showRemind ? (
        <View className="remind_wrapper">
          <View className="message_wrapper">
            <Text className="remindTitle">旧衣、旧书回收说明</Text>
            <View className="content_wrapper">
              {RecyclingInstructions.map((kind, index) => (
                <View>
                  <Text className="remind_title" style={!showRemind ? { display: 'none' } : null}>
                    {kind.title}
                  </Text>
                  {kind.content.map((remind, mark) => (
                    <Text className="remind_text" key={remind + mark}>
                      {remind}
                    </Text>
                  ))}
                </View>
              ))}
            </View>
            <View className="know_button_wrapper">
              <View
                className="know_button"
                onClick={() => {
                  closeRmind()
                }}
              >
                我知道了
              </View>
            </View>
          </View>
        </View>
      ) : null}
      {wuFei ? (
        <View className="remind_wrapper">
          <View className="message_wrapper">
            <Text className="remindTitle">生活废品说明</Text>
            <View className="content_wrapper">
              {RecyclingInstructions[2].content.map((remind, mark) => (
                <Text className="remind_text" key={remind + mark}>
                  {remind}
                </Text>
              ))}
            </View>
            <View className="know_button_wrapper">
              <View
                className="know_button"
                onClick={() => {
                  setWuFei(false)
                }}
              >
                我知道了
              </View>
            </View>
          </View>
        </View>
      ) : null}
    </View>
  )
}

definePageConfig({
  navigationBarTitleText: '首页',
  navigationStyle: 'default',
  // "navigationStyle":"custom"
})
export default Index

{
  /* <BaseLayout menuIndex={0} title="环保回收" navigationBar={true} >
</BaseLayout> */
}
