import { requestGet, requestPost, requestPut } from '../utils/request'

export async function createOrder(payload) {
  return requestPost('order', payload)
}
export async function updateImage(payload) {
  return requestPost(`order/${payload.id}/updateImage`, payload)
}
export async function getOrderList(payload) {
  return requestPost('order/userList', payload)
}
export async function getWorkerInfo(payload) {
  return requestGet('getWorker', payload)
}
export async function updateOrder(payload) {
  return requestPut(`order/update/${payload.id}`, !payload.orderWastes ? { order: payload, orderWastes: [] } : { orderWastes: payload.orderWastes })
}
export async function updateStatus(payload) {
  return requestPost(`order/${payload.id}/updateStatus`, payload)
}
export async function getOrderDetail(payload) {
  return requestGet(`order/${payload.id}`)
}
export async function getAllOrder(payload) {
  return requestGet('order', payload)
}
export async function payOrder(payload) {
  return requestPost('pay', payload)
}

export async function sendSMS(payload) {
  return requestPost('sms', payload)
}
export async function getRating(payload) {
  return requestGet('orderRating', payload)
}
export async function createOrderRating(payload) {
  return requestPost('orderRating', payload)
}
export async function orderComplaint(payload) {
  return requestPost('order/complaint', payload)
}