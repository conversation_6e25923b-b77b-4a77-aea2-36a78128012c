import { DeleteOutlined, DownloadOutlined } from '@ant-design/icons'
import {
  notification, Table, Badge, Modal, Button, Switch, Image,
  InputNumber, Input,
  message,
} from 'antd'
import type { ActionType, ProColumns } from '@ant-design/pro-table'
import ProTable, { } from '@ant-design/pro-table'
import ProCard from '@ant-design/pro-card'
import { useEffect, useState, useRef } from 'react'
import { masterStatusEnum } from '../../common/enum'
import styles from './index.module.less'
import { SERVER_HOME } from '../../common/config'
import { effect, useConnect } from 'dva17'
import {
  EGet, EChangeWorkerUse, NUser,
  EPostWorkerWallet,
  EGetWorkerPaylist,
  EGetWorkerWalletlog,
  NJDWorker,
  EPostCoWorker,
  EChangeCoWorker,
  EGetWorkerMaintain,
  EPutWorkerMaintain,
  EDeleteCoWorker,
} from '../../common/action'
import { useParams } from 'react-router-dom'
import dayjs from 'dayjs'
import { computeAuthority } from '../../utils/Authorized/authority'
import 'antd/dist/antd.css';

type Item = {
  id: number
  workerName: string
  mobile: string
  order: {
    status: string
    totalMoney: number
    count: number
  }[]
  wallet: number
  idCardNo: string
  openid: string
  company: {
    companyName: string
    id: number
  }
  district: string
  type: string
  forbidden: number
}
const { Search } = Input;
export default () => {
  const any: any = null
  const { workerPaylist, workerMaintainList } = useConnect(NJDWorker)
  const { currentUser } = useConnect(NUser)
  const actionRef = useRef<ActionType>()
  const params = useParams()
  const [visibleLog, setVisibleLog] = useState(false)
  const [visibleMaintain, setVisibleMaintain] = useState(any)
  const [companyID, setCompanyID] = useState(any)
  let [coWorkerMobile, setCoWorkerMobile] = useState<any>(null)
  let [coWorkerName, setCoWorkerName] = useState<any>(null)
  const [visibleWalletLog, setWalletVisibleLog] = useState(any)
  const [chargeNum, setChargeNum] = useState(Number)
  const [workerID, setworkerID] = useState(Number)
  const [visibleCharge, setVisibleCharge] = useState(any)
  const [item, setItem] = useState(any)
  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    let id = params.id
    let companyID
    if (id && id.indexOf('id') > 0) {
      companyID = currentUser.companyID
    } else {
      companyID = id
    }
    setCompanyID(companyID)
    effect(NJDWorker, EGetWorkerMaintain, { companyID, status: '待维护' })
    return () => {
      setCompanyID(null)
    }
  }, [])

  /*--------------------- 响应 ---------------------*/
  const chargeIn = (row: any) => {
    setItem(row)
    setVisibleCharge(true)
  }
  const exportExcel = () => {
    window.open(
      `${SERVER_HOME}JDexportMasterXLS`
    )
  }
  const exportwalletExcel = () => {
    window.open(
      `${SERVER_HOME}JDexportWalletXLS?workerID=${workerID}`
    )
  }
  const chargeWallet = async (row: any) => {
    await effect(NJDWorker, EPostWorkerWallet, { ...row })
    notification.success({
      message: '成功！',
      description: '充值',
      duration: 2,
    })
    refreshPage()
  }

  const refreshPage = () => {
    if (actionRef.current) {
      actionRef.current.reload()
    }
  }
  const seeDetail = (row: any) => {
    Modal.info({
      title: '师傅明细',
      content: (
        <div className={styles.item_wrapper}>
          <div className={styles.item} style={{ marginTop: 30, paddingBottom: 10 }}>
            <span className={styles.item_title}>姓名：</span>
            <div className={styles.item_content}>{row.workerName}</div>
          </div>
          <div className={styles.item}>
            <span className={styles.item_title}>手机号：</span>
            <div className={styles.item_content}>{row.mobile}</div>
            <Search
              placeholder="请输入新手机号"
              allowClear
              enterButton="提交"
              size='middle'
              style={{ width: 280 }}
              onChange={e => {
                console.log(e?.target?.value);
              }}
              onSearch={async (e) => {
                await effect(NJDWorker, EChangeWorkerUse, { id: row.id, mobile: e })
                  .then(() => {
                    notification.success({
                      message: "成功",
                      description: '修改成功！',
                      duration: 2
                    })
                    effect(NJDWorker, EGet, { pageSize: 50 })
                    refreshPage()
                  })
              }}
            />
          </div>
          <div className={styles.item}>
            <span className={styles.item_title}>地址：</span>
            <div className={styles.item_content}>{row.province + row.city + row.district + row.subDistrct + row.address}</div>
          </div>
          <div className={styles.item}>
            <span className={styles.item_title}>身份证号码：</span>
            <div className={styles.item_content}>{row.idCardNo}</div>
          </div>
          <div className={styles.item}>
            <span className={styles.item_title}>身份证图片：</span>
            <div className={styles.item_content}>
              <Image src={row.idCardImg} style={{ width: 200, height: 200 }} />
              <Image src={row.idCardBackImg} style={{ width: 200, height: 200 }} />
            </div>
          </div>
          <div className={styles.item}>
            <Button type='primary' onClick={async () => {
              await effect(NJDWorker, EChangeWorkerUse, { id: row.id, mobile: row.mobile })
                .then(() => {
                  notification.success({
                    message: "成功",
                    description: '重置成功！',
                    duration: 2
                  })
                  effect(NJDWorker, EGet, { pageSize: 50 })
                  refreshPage()
                })
            }}>重置密码</Button>
          </div>
        </div>
      ),
      okText: '知道了',
      onOk() { },
      width: 700,
    })
  }
  const ADDWorker = (row: any, type: any = "新增") => {
    Modal.info({
      title: '添加子师傅',
      content: (
        <div className={styles.item_wrapper}>
          <div className={styles.item} style={{ marginTop: 30, paddingBottom: 10 }}>
            <span className={styles.item_title}>姓名：</span>
            <Input
              defaultValue={type === '编辑' ? row?.workerName : null}
              placeholder="请输入子师傅姓名"
              allowClear
              style={{ width: 280 }}
              onChange={e => {
                setCoWorkerName(e?.target?.value)
                coWorkerName = e?.target?.value
              }}
            />
          </div>
          <div className={styles.item}>
            <span className={styles.item_title}>手机号：</span>
            <Input
              defaultValue={type === '编辑' ? row?.mobile : null}
              placeholder="请输入子师傅手机号"
              allowClear
              type='number'
              onChange={e => {
                setCoWorkerMobile(e?.target?.value)
                coWorkerMobile = e?.target?.value
              }}
              style={{ width: 280 }} />
          </div>
          <div className={styles.item}>
            <Button type='primary' onClick={async () => {
              console.log({
                managerID: row.id, mobile: coWorkerMobile, workerName: coWorkerName
              });
              if (type === "编辑") {
                await effect(NJDWorker, EChangeCoWorker, {
                  id: row.id, mobile: coWorkerMobile, workerName: coWorkerName
                })
                  .then(() => {
                    notification.success({
                      message: "成功",
                      description: '提交成功！',
                      duration: 2
                    })
                    effect(NJDWorker, EGet, { pageSize: 50 })
                    refreshPage()
                  })
              } else {
                await effect(NJDWorker, EPostCoWorker, {
                  managerID: row.id, mobile: coWorkerMobile, workerName: coWorkerName
                })
                  .then(() => {
                    notification.success({
                      message: "成功",
                      description: '提交成功！',
                      duration: 2
                    })
                    effect(NJDWorker, EGet, { pageSize: 50 })
                    refreshPage()
                  })
              }

            }}>提交子师傅</Button>
          </div>
        </div>
      ),
      okText: '关闭',
      width: 700,
    })
  }
  /*--------------------- 渲染 ---------------------*/
  const maintainColumns: any = [
    { title: '姓名', render: (record: any) => <span>{record?.name}</span> },
    { title: '联系电话', render: (record: any) => <span>{record.phone}</span> },
    { title: '时间', dataIndex: 'createdAt', render: (createdAt: any) => <span>{createdAt ? dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss') : '-'}</span> },
    { title: '状态', render: (record: any) => <span>{record.status}</span> },
    {
      title: '操作',
      render: (record: any) => (
        <div className={styles.operate_wrapper}>
          <Button onClick={() => {
            Modal.confirm({
              title: '确认已维护',
              content: '确认已维护该师傅吗？',
              onOk: async () => {
                await effect(NJDWorker, EPutWorkerMaintain, { id: record.id, status: '完成' })
                notification.success({
                  message: '成功',
                  description: '已维护',
                  duration: 2,
                })
                refreshPage()
                effect(NJDWorker, EGetWorkerMaintain, { companyID, status: '待维护' })
              },
            })
          }}>已维护</Button>
        </div>
      ),
    },
  ]
  const logColumns: any = [
    { title: '姓名', render: (record: any) => <span>{record?.worker?.workerName}</span> },
    { title: '充值方式', render: (record: any) => <span>{record.type}</span> },
    { title: '充值金额', render: (record: any) => <span>{(record?.totalPay) / 100}</span> },
    { title: '充值时间', dataIndex: 'createdAt', render: (createdAt: any) => <span>{createdAt ? dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss') : '-'}</span> },
  ]
  const WalletlogColumns: any = [
    { title: '订单ID', render: (record: any) => <span>{record?.order?.orderNo}</span> },
    { title: '备注', render: (record: any) => <span>{record.remark}</span> },
    { title: '消费金额', render: (record: any) => <span>{(record?.money)}</span> },
    { title: '说明', render: (record: any) => <span>{(record?.sign)}</span> },
    { title: '时间', dataIndex: 'createdAt', render: (createdAt: any) => <span>{createdAt ? dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss') : '-'}</span> },
  ]
  const columns: ProColumns<Item>[] = [
    { title: '姓名', dataIndex: 'workerName', copyable: false, ellipsis: true, },
    { title: '联系电话', dataIndex: 'mobile', copyable: false, ellipsis: true, },

    {
      title: '隶属公司', dataIndex: ['company', 'companyName'], copyable: false, search: false,
    },
    {
      title: '钱包余额', dataIndex: 'wallet', search: false, copyable: false, ellipsis: true,
      sorter: (a, b) => a.wallet - b.wallet,
      render: (_, row: any) => {
        return (<>{row?.wallet < 500 ? <div style={{ color: 'red' }}>{row?.wallet}</div> : <div style={{ color: 'black' }}>{row?.wallet}</div>}</>
        )
      }
    },
    {
      title: '钱包余额',
      dataIndex: 'wallet',
      copyable: false,
      ellipsis: true,
      valueType: 'digitRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            lowWallet: value[0],
            hightWallet: value[1],
          }
        },
      },
    },
    {
      title: '充值记录', dataIndex: 'recor', search: false, render: (_, row: any) => {
        return (<>
          {row?.openid && <Button
            onClick={() => {
              setVisibleLog(true)
              setworkerID(row?.id)
              effect(NJDWorker, EGetWorkerPaylist, { status: '完成', current: 1, pageSize: 20, workerID: row?.id })
            }}>
            查看
          </Button >}
        </>)
      }
    },
    {
      title: '下属师傅',
      dataIndex: 'countUser',
      search: false,
      render: (_, row: any) => <span>{row.countUser ? row.countUser.length : 0}</span>,
    },
    {
      title: '钱包消费记录', dataIndex: 'recor', search: false, render: (_, row: any) => {
        return (<>
          {row?.openid && <Button
            onClick={() => {
              setWalletVisibleLog(true)
              setworkerID(row?.id)
              effect(NJDWorker, EGetWorkerWalletlog, { current: 1, pageSize: 20, workerID: row?.id })
            }}>
            查看
          </Button>}
        </>)
      }
    },
    {
      title: '启用状态', dataIndex: 'forbidden', copyable: false, ellipsis: true, valueEnum: masterStatusEnum,
      render: (_, row) => {
        return (<>
          {row?.openid && <Switch
            checkedChildren="启动"
            unCheckedChildren="停用"
            disabled={!computeAuthority('回收人员编辑')}
            checked={row.forbidden === 0 ? false : true}
            onChange={e => {
              effect(NJDWorker, EChangeWorkerUse, {
                id: row.id,
                forbidden: e ? 1 : 0,
              }).then(() => {
                notification.success({
                  message: '成功！',
                  description: '更改成功',
                  duration: 2,
                })
                refreshPage()
              })
            }}
          />}</>)
      }
    },
    {
      title: '操作', width: '18%', copyable: false, ellipsis: true, search: false,
      render: (_, row: any) => {
        if (!row?.openid) {
          return (
            <div>
              <Button
                type='primary'
                onClick={() => {
                  ADDWorker(row, '编辑')
                }}>
                编辑
              </Button>
              <Button
                type='primary'
                danger
                icon={<DeleteOutlined />}
                onClick={() => {
                  Modal.confirm({
                    title: '确认删除师傅',
                    content: '确认删除该师傅吗？',
                    onOk: async () => {
                      await effect(NJDWorker, EDeleteCoWorker, { id: row.id })
                      notification.success({
                        message: '成功',
                        description: '删除成功',
                        duration: 2,
                      })
                      refreshPage()
                    },
                  })
                }}>
                删除师傅
              </Button>
            </div>
          )
        } else {
          return (
            <div>
              {/* <Button
                icon={<DeleteOutlined />}
                type='primary'
                danger
                onClick={() => {
                  Modal.confirm({
                    title: '确认删除师傅',
                    content: '确认删除该师傅吗？',
                    onOk: async () => {
                      await effect(NJDWorker, EDeleteWorker, { id: row.id })
                      notification.success({
                        message: '成功',
                        description: '删除成功',
                        duration: 2,
                      })
                      refreshPage()
                    },
                  })
                }}>
                删除师傅
              </Button> */}
              <Button
                onClick={() => {
                  seeDetail(row)
                }}>
                个人信息
              </Button>
              <div style={{ display: 'flex', gap: 10, marginTop: 10 }}>
                <Button
                  disabled={currentUser.name !== "兰姐" && currentUser.name !== "系统"}
                  onClick={() => {
                    chargeIn(row)
                  }}>
                  充值
                </Button>
                <Button
                  type='primary'
                  onClick={() => {
                    ADDWorker(row, '新增')
                  }}>
                  添加下属人员
                </Button>
              </div>
            </div >
          )
        }

      },
    },
  ]

  return (
    <div>
      <ProCard>
        <ProTable<Item>
          actionRef={actionRef}
          columns={columns}
          request={async (params = {}, sorter) => (await effect(NJDWorker, EGet, { ...params, ...sorter, companyID })) as any}
          pagination={{
          }}
          expandable={{ childrenColumnName: 'countUser' }}
          rowKey={record => {
            let rowkey
            if (record.idCardNo) {
              rowkey = record.idCardNo + '_' + record.id
            } else {
              rowkey = record.id
            }

            return rowkey
          }}
          dateFormatter="string"
          headerTitle=""
          toolBarRender={() => [
            <Button
              disabled={!computeAuthority('回收人员审核') || currentUser.level == '服务商'}
              key="4"
              onClick={() => {
                exportExcel()
              }}>
              <DownloadOutlined />
              导出
            </Button>,
            <Badge count={workerMaintainList?.length || 0}>
              <Button
                disabled={!computeAuthority('回收人员审核') || currentUser.level == '服务商'}
                key="5"
                onClick={() => {
                  setVisibleMaintain(true)
                }}>
                待维护人员列表
              </Button>
            </Badge>,
          ]}
        />
      </ProCard>

      <Modal
        title="待维护人员列表"
        open={visibleMaintain}
        onOk={() => {
          setVisibleMaintain(false)
        }}
        onCancel={() => {
          setVisibleMaintain(false)
        }}
        destroyOnClose
        footer={null}
        width={800}>
        <Table
          columns={maintainColumns}
          loading={workerMaintainList ? false : true}
          dataSource={workerMaintainList ? workerMaintainList : []}
          rowKey={(record: any) => JSON.stringify(record)}
          pagination={false}
        />
      </Modal>
      <Modal
        title="师傅充值与扣款"
        open={visibleCharge}
        onOk={async () => {
          setVisibleCharge(false)
          Modal.confirm({
            title: '确认充值',
            content: <div> 请确认为 {item?.workerName}充值 {chargeNum}元 </div>,
            okText: '确认充值',
            cancelText: '退出',
            onOk: async () => {
              const hide = message.loading('正在充值')
              await chargeWallet({ workerID: item?.id, chargeNum: chargeNum })
              hide()
            },
            width: 700,
          })
        }}
        onCancel={() => {
          setVisibleCharge(false)
        }}
        destroyOnClose
        width={800}>
        <div className={styles.item_wrapper}>
          <div className={styles.item} style={{ marginTop: 30, paddingBottom: 10 }}>
            <span className={styles.item_title}>姓名：</span>
            <div className={styles.item_content}>{item?.workerName}</div>
          </div>
          <div className={styles.item}>
            <span className={styles.item_title}>手机号：</span>
            <div className={styles.item_content}>{item?.mobile}</div>
          </div>
          <div className={styles.item}>
            <span className={styles.item_title}>余额：</span>
            <div className={styles.item_content}>{item?.wallet}</div>
          </div>
          <div className={styles.item}>
            <span className={styles.item_title}>充值：</span>
            <div className={styles.item_content}><InputNumber onChange={(row: any) => {
              setChargeNum(row)
            }} /></div>
          </div>
        </div>
      </Modal>
      <Modal
        title="师傅钱包记录"
        open={visibleWalletLog}
        onOk={() => {
          setWalletVisibleLog(false)
        }}
        onCancel={() => {
          setWalletVisibleLog(false)
        }}
        destroyOnClose
        footer={null}
        width={800}>
        <Button type='primary' onClick={() => { exportwalletExcel() }}>导出</Button>
        <Table
          columns={WalletlogColumns}
          loading={workerPaylist ? false : true}
          dataSource={workerPaylist ? workerPaylist?.data : []}
          rowKey={(record: any) => JSON.stringify(record)}
          pagination={
            {
              showSizeChanger: true,
              showQuickJumper: true,
              pageSize: workerPaylist?.perPage || 20,
              current: workerPaylist?.page || 1,
              total: workerPaylist?.total || 0,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              onChange: (page, pageSize) => {
                effect(NJDWorker, EGetWorkerWalletlog, { current: page, pageSize: pageSize, workerID: workerID })
              }
            }
          }
        />
      </Modal>
      <Modal
        title="师傅充值记录"
        open={visibleLog}
        onOk={() => {
          setVisibleLog(false)
        }}
        onCancel={() => {
          setVisibleLog(false)
        }}
        destroyOnClose
        footer={null}
        width={800}>
        <Button type='primary' onClick={() => { exportwalletExcel() }}>导出</Button>
        <Table
          columns={logColumns}
          loading={workerPaylist ? false : true}
          dataSource={workerPaylist ? workerPaylist?.data : []}
          rowKey={(record: any) => JSON.stringify(record)}
          pagination={
            {
              showSizeChanger: true,
              showQuickJumper: true,
              pageSize: workerPaylist?.perPage || 20,
              current: workerPaylist?.page || 1,
              total: workerPaylist?.total || 0,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              onChange: (page, pageSize) => {
                effect(NJDWorker, EGetWorkerPaylist, { status: '完成', current: page, pageSize: pageSize, workerID: workerID })
              }
            }
          }
        />
      </Modal>
    </div>
  )
}
