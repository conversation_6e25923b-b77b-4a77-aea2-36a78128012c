// 引入冬瓜回收设计变量
@import './styles/variables.less';

// 全局样式重置
view{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

view, text, image, button {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

page {
  background-color: @background-light;
  font-size: @font-md;
  color: @text-primary;
  line-height: 1.6;
}

// 全局动画类
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(20rpx); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

// 通用卡片样式
.card {
  background: @background;
  border-radius: @radius-lg;
  box-shadow: @shadow-light;
  padding: @spacing-lg;
  margin-bottom: @spacing-md;
  
  &.shadow-medium {
    box-shadow: @shadow-medium;
  }
  
  &.shadow-strong {
    box-shadow: @shadow-strong;
  }
}

// 通用按钮样式
.btn {
  border: none;
  border-radius: @radius-xl;
  font-weight: bold;
  text-align: center;
  transition: @transition-normal;
  position: relative;
  overflow: hidden;
  
  &::after {
    display: none;
  }
  
  &:active {
    transform: scale(0.98);
  }
}

.btn-primary {
  background: @gradient-primary;
  color: @text-white;
  padding: @spacing-lg @spacing-xl;
  font-size: @font-lg;
  box-shadow: @shadow-medium;
  
  &:active {
    background: @primary-dark;
  }
}

.btn-secondary {
  background: @background;
  color: @primary-color;
  border: 2rpx solid @primary-color;
  padding: @spacing-lg @spacing-xl;
  font-size: @font-lg;
  
  &:active {
    background: @primary-color;
    color: @text-white;
  }
}

.btn-ghost {
  background: transparent;
  color: @text-white;
  border: 2rpx solid @text-white;
  padding: @spacing-md @spacing-lg;
  font-size: @font-md;
  
  &:active {
    background: @text-white;
    color: @primary-color;
  }
}

// 通用文本样式
.text-primary {
  color: @text-primary;
}

.text-secondary {
  color: @text-secondary;
}

.text-light {
  color: @text-light;
}

.text-white {
  color: @text-white;
}

.text-center {
  text-align: center;
}

.text-bold {
  font-weight: bold;
}

// 通用间距类
.mt-xs { margin-top: @spacing-xs; }
.mt-sm { margin-top: @spacing-sm; }
.mt-md { margin-top: @spacing-md; }
.mt-lg { margin-top: @spacing-lg; }
.mt-xl { margin-top: @spacing-xl; }

.mb-xs { margin-bottom: @spacing-xs; }
.mb-sm { margin-bottom: @spacing-sm; }
.mb-md { margin-bottom: @spacing-md; }
.mb-lg { margin-bottom: @spacing-lg; }
.mb-xl { margin-bottom: @spacing-xl; }

.pt-xs { padding-top: @spacing-xs; }
.pt-sm { padding-top: @spacing-sm; }
.pt-md { padding-top: @spacing-md; }
.pt-lg { padding-top: @spacing-lg; }
.pt-xl { padding-top: @spacing-xl; }

.pb-xs { padding-bottom: @spacing-xs; }
.pb-sm { padding-bottom: @spacing-sm; }
.pb-md { padding-bottom: @spacing-md; }
.pb-lg { padding-bottom: @spacing-lg; }
.pb-xl { padding-bottom: @spacing-xl; }

// 隐藏类
.hidden {
  display: none !important;
}