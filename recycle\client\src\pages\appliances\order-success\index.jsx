import React from 'react'
import { View, Text, Button, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import './index.less'

const OrderSuccess = () => {
  // 返回首页
  const goHome = () => {
    Taro.switchTab({ url: '/pages/home/<USER>' })
  }
  
  // 查看订单
  const viewOrders = () => {
    Taro.navigateTo({ url: '/pages/orderManage/orderManage' })
  }

  return (
    <View className='order-success'>
      <View className='success-container'>
        <View className='success-icon'>✓</View>
        <View className='success-title'>订单提交成功</View>
        <View className='success-desc'>
          我们已收到您的回收申请，工作人员将尽快与您联系确认上门时间，请保持手机畅通。
        </View>
        
        <View className='process-steps'>
          <View className='step'>
            <View className='step-icon'>📋</View>
            <View className='step-title'>提交申请</View>
          </View>
          <View className='step-divider' />
          <View className='step'>
            <View className='step-icon'>📞</View>
            <View className='step-title'>电话确认</View>
          </View>
          <View className='step-divider' />
          <View className='step'>
            <View className='step-icon'>🚚</View>
            <View className='step-title'>上门回收</View>
          </View>
          <View className='step-divider' />
          <View className='step'>
            <View className='step-icon'>💰</View>
            <View className='step-title'>现金结算</View>
          </View>
        </View>
        
        <View className='tips'>
          <Text className='tips-title'>温馨提示</Text>
          <Text className='tips-content'>
            • 工作人员会提前电话联系您，请保持手机畅通<br />
            • 上门前会再次确认地址和时间<br />
            • 回收人员上门后会进行专业评估，并当场给出最终回收价格<br />
            • 如同意回收，将现场付款
          </Text>
        </View>

        <View className='action-buttons'>
          <Button className='action-btn secondary' onClick={viewOrders} onTap={viewOrders}>查看订单</Button>
          <Button className='action-btn primary' onClick={goHome} onTap={goHome}>返回首页</Button>
        </View>
      </View>
    </View>
  )
}

export default OrderSuccess
