'use strict'

const { CryptUtil } = require('..//Util')
const { ERR } = require('../../../../constants')
const { Worker } = require('../Models')

class MasterAuth {
  async handle({ request }, next) {
    const payload = CryptUtil.jwtDecode(request.header('Authorization') || request.input('token'))
    if (!payload) {
      throw ERR.AUTH_FAILED
    } else {
      if (!payload.userID) {
        throw ERR.AUTH_FAILED
      }
      request.userID = payload.userID
      let worker = await Worker.find(request.userID)
      request.worker = worker
    }
    await next()
  }
}

module.exports = MasterAuth
