import { requestDelete, requestGet, requestPost, requestPut } from 'dva17'

/**
 * 嗨回收价格配置相关的API服务
 */

// 获取价格配置列表
export async function getHiPriceList(payload: any) {
  return requestGet('/hi-prices', payload)
}

// 获取价格配置详情
export async function getHiPriceDetail(payload: any) {
  return requestGet(`/hi-prices/${payload.id}`)
}

// 创建价格配置
export async function createHiPrice(payload: any) {
  return requestPost('/hi-prices', payload)
}

// 更新价格配置
export async function updateHiPrice(payload: any) {
  return requestPut(`/hi-prices/${payload.id}`, payload)
}

// 删除价格配置
export async function deleteHiPrice(payload: any) {
  return requestDelete(`/hi-prices/${payload.id}`)
}

// 批量更新价格
export async function batchUpdateHiPrice(payload: any) {
  return requestPost('/hi-prices/batch-price', payload)
}

// 批量更新状态
export async function batchUpdateHiPriceStatus(payload: any) {
  return requestPost('/hi-prices/batch-status', payload)
}

// 导出价格配置表格
export async function exportHiPriceTable(payload: any) {
  return requestGet('/hi-prices/export', payload)
}

// 根据条件查询价格
export async function queryHiPriceByCondition(payload: any) {
  return requestGet('/hi-prices/query-price', payload)
}

// 复制价格配置
export async function copyHiPriceConfig(payload: any) {
  return requestPost('/hi-prices/copy', payload)
} 