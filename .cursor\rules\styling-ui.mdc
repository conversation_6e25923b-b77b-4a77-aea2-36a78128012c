---
globs: *.less,*.css,*.scss
description: 样式和 UI 设计规范
---

# 样式和 UI 设计规范

## 设计主题
基于冬瓜回收官网风格，采用绿色环保主题设计

## 色彩体系
- 主色调：#15b381 (绿色环保色)
- 辅助色：
  - 浅绿：#a8e6cf
  - 深绿：#0d8f5f
  - 灰色：#f5f5f5, #e8e8e8, #cccccc
  - 文字色：#333333, #666666, #999999
  - 警告色：#ff7875
  - 成功色：#52c41a

## 字体规范
- 主字体：系统默认字体
- 字体大小：
  - 标题：24px, 20px, 18px
  - 正文：16px, 14px
  - 辅助：12px, 10px

## 间距规范
- 基础间距单位：8px
- 常用间距：8px, 16px, 24px, 32px, 48px
- 页面边距：16px
- 组件内间距：8px, 12px

## 响应式设计
- 移动端优先 (Mobile First)
- 断点设置：
  - 手机：< 768px
  - 平板：768px - 1024px
  - 桌面：> 1024px

## 小程序样式规范
- 使用 rpx 单位进行屏幕适配
- 750rpx = 375px (iPhone 6 标准)
- 常用 rpx 值：16rpx, 32rpx, 48rpx, 64rpx

```less
// 变量定义
@primary-color: #15b381;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #ff7875;
@text-color: #333333;
@text-color-secondary: #666666;
@text-color-light: #999999;
@border-color: #e8e8e8;
@background-color: #f5f5f5;

// 基础间距
@spacing-xs: 8rpx;
@spacing-sm: 16rpx;
@spacing-md: 24rpx;
@spacing-lg: 32rpx;
@spacing-xl: 48rpx;

// 字体大小
@font-size-xl: 36rpx;
@font-size-lg: 32rpx;
@font-size-md: 28rpx;
@font-size-base: 28rpx;
@font-size-sm: 24rpx;
@font-size-xs: 20rpx;
```

## 组件样式规范
- 使用 BEM 命名规范：块__元素--修饰符
- 组件样式作用域化
- 避免深层嵌套选择器

## 常用样式类
```less
// 布局类
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 文字类
.text-center { text-align: center; }
.text-primary { color: @primary-color; }
.text-secondary { color: @text-color-secondary; }

// 间距类
.m-16 { margin: 16rpx; }
.p-16 { padding: 16rpx; }
.mt-16 { margin-top: 16rpx; }
.mb-16 { margin-bottom: 16rpx; }
```

## 关键样式文件
- 全局变量：[recycle/client/src/styles/variables.less](mdc:recycle/client/src/styles/variables.less)
- 公共样式：[recycle/client/src/pages/pulic.less](mdc:recycle/client/src/pages/pulic.less)
- 应用样式：[recycle/client/src/app.less](mdc:recycle/client/src/app.less)

## Ant Design 定制
- 使用 Ant Design 主题定制
- 覆盖默认样式变量
- 保持组件一致性

## 动画和交互
- 使用 CSS3 动画
- 过渡时间：0.3s
- 缓动函数：ease-in-out
- 微交互增强用户体验