'use strict'

const Model = use('Model')

//师傅支付日志
class WorkerWalletLog extends Model {
  static get table() {
    return 'worker_wallet_log'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return null
  }
  worker() {
    return this.belongsTo('App/Models/Worker', 'workerID', 'id')
  }
  order() {
    return this.belongsTo('App/Models/Order', 'orderID', 'id')
  }
  clientOrder() {
    return this.belongsTo('App/Models/COrder', 'orderID', 'id')
  }
}

module.exports = WorkerWalletLog
