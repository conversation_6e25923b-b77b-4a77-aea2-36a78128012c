
import Taro from '@tarojs/taro'
import React, { Component } from 'react'
import T from "../../config/T"
import {connect} from 'react-redux'
import { View, Image, Button, Text, Checkbox, Swiper, SwiperItem } from '@tarojs/components'
import { AtModal } from 'taro-ui'
import './priceEvaluation.less'
import E from '../../config/E'
import dayjs from 'dayjs'

class PriceEvaluation extends Component {
  constructor() {
    super(...arguments)
    this.state = {}
  }

  config = {}

  //-----------------------生命周期-------------------------//
  componentWillMount() {
    Taro.setNavigationBarTitle({ title: '价格评估' })
  }

  componentDidMount() {}

  componentWillUnmount() {}

  componentDidShow() {}

  componentDidHide() {}

  //-----------------------事件-------------------------//
  selectValue(value) {
    let { currentSelect, currentSlide, currentObject } = this.state
    let { attribute, attributeType } = this.props
    let length = attributeType.length
    attribute.map((item, index) => {
      if (currentSelect.indexOf(item.id) >= 0) {
        currentSelect.forEach((select, mark) => {
          if (select === item.id) {
            currentSelect.splice(mark, 1)
            currentObject.splice(mark, 1)
          }
        })
      }
    })
    currentSelect.push(value.id)
    currentObject.push(value)
    this.setState({
      currentSelect,
      currentObject,
      currentSlide: this.state.currentSlide < length - 1 ? this.state.currentSlide + 1 : this.state.currentSlide
    })
    if (this.state.currentSlide === length - 1) {
      this.props.dispatch({
        type: 'NOldGoods/ESelectWhichAttribute',
        payload: {
          currentObject
        }
      })
    }
  }

  createOrder() {
    let user = Taro.getStorageSync('userInfo') && JSON.parse(Taro.getStorageSync('userInfo'))
  }

  //-----------------------渲染-------------------------//
  render() {
    let { currentSelect, currentSlide } = this.state
    let { whichAppliances, currentObject, isAddItem, haveChosen, basicPrice, attributePrice } = this.props
    return (
      <View className="priceEvaluation">
        <View className="which">
          预估价格：<Text>¥{basicPrice + attributePrice}</Text>
        </View>
        <View className="devide"></View>
        <View className="remind_text">
          <Text>最终价格以面议为准</Text>
        </View>
        <View className="devide"></View>
        <View className="selected_attribute">
          {currentObject.map((value, index) => (
            <View className="attribute">{value.name}</View>
          ))}
        </View>
        <View
          className="administration_add_address"
          onClick={() => {
            this.createOrder()
          }}>
          <View className="text_wrapper">
            <Text>一件回收</Text>
          </View>
          {isAddItem ? <View className="add_item"></View> : null}
        </View>
      </View>
    )
  }
}
export default connect(({ NOldGoods: { whichAppliances, currentObject, haveChosen, attributePrice, basicPrice }, NSystem: { isAddItem } }) => ({
  whichAppliances,
  currentObject,
  isAddItem,
  haveChosen,
  attributePrice,
  basicPrice
}))(PriceEvaluation)
