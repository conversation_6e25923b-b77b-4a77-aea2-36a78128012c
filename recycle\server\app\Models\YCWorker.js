'use strict'

const Model = use('Model')
const Database = use('Database')

//上门师傅信息
class YCWorker extends Model {
  static get table() {
    return 'yc_worker'
  }
  static get primaryKey() {
    return 'id'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return 'updatedAt'
  }
  company() {
    return this.belongsTo('App/Models/Company', 'companyID', 'id')
  }
}

module.exports = YCWorker
