'use strict'

const Model = use('Model')

//废品一级分类
class Waste1stCategory extends Model {
	static get table(){return 'waste_1st_category'}
	static get primaryKey () { return 'id' }
	static get createdAtColumn (){return 'createdAt'}
	static get updatedAtColumn (){return 'updatedAt'}

	secondCategory () {
		return this.hasMany('App/Models/Waste2ndCategory','id','firstCateID')
	}
}

module.exports = Waste1stCategory