.login_contain {
  min-height: 100vh;
  overflow: auto;
  background-image: url('../../assets/images/loginBg.png');
  background-repeat: no-repeat;
  background-size: cover;
  background-color: rgba(0, 0, 0, 1);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .login_box {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    .login_title {
      width: 214px;
      height: 60px;
      object-fit: contain;
    }
    .login_form {
      margin: 90px 0 60px 0;
      .form_item {
        .label {
          color: #fff;
          font-size: 14px;
          margin-bottom: 8px;
        }
        .input {
          border: 1px solid #48ffff;
          height: 40px;
          padding: 0 12px;
          :global {
            .adm-input-element {
              font-size: 14px;
              font-weight: bold;
              color: #48ffff;
              &::placeholder {
                font-size: 14px;
              }
            }
          }
        }
      }
      .password {
        margin-top: 30px;
      }
      .password_input {
        position: relative;
        .input {
          padding-right: 42px;
        }
        .icon {
          width: 18px;
          height: 12px;
          object-fit: contain;
          position: absolute;
          right: 12px;
          top: 50%;
          transform: translateY(-50%);
          z-index: 9;
        }
      }
    }
    .login_btn {
      width: 212px;
      height: 82px;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }
}
