'use strict'

const _ = require("lodash");
const moment = require("moment");
const { <PERSON><PERSON><PERSON><PERSON>, Worker, YCWorkerWalletLog, YCOrderMaintain
} = require('../../../Models')
const Excel = require('exceljs')
const { ERR } = require("../../../../../constants");
const ExcelService = require('../../../Services/ExcelService');

class YCOrderImportController {
  async index({ request, response }) {
    let { createdAt, workTime,
      current = 1, pageSize = 10, status = "完成", orderNo,
      waste_1st_type, userMobile, userName, workerName, from,
      startDate, endDate,
      workStartDate, workEndDate, orderType, type, finishedAt,
      finishStartDate, finishEndDate, model, offTime,
      offStartDate, offEndDate, countName, countPhone,
      address, province, city
    } = request.all()
    let query = YCOrder.query().with('company')
    // 统计查询订单的信息费总和
    let totalQuery = YCOrder.query()
    query.where('status', status)
    totalQuery.where('status', status)
    if (from) {
      query.whereRaw('from like ?', [`%${from}%`])
      totalQuery.whereRaw('from like ?', [`%${from}%`])
    }
    if (orderType) {
      query.where('orderType', orderType)
      totalQuery.where('orderType', orderType)
    }
    if (model) {
      query.where('model', model)
      totalQuery.where('model', model)
    }
    if (countName) {
      let worker = await Worker.query().where('workerName', countName).first()
      if (worker) {
        query.where('workerID', worker.id)
        totalQuery.where('workerID', worker.id)
      } else {
        query.where('countName', 'LIKE', `%${countName}%`)
        totalQuery.where('countName', 'LIKE', `%${countName}%`)
      }
    }
    if (countPhone) {
      query.where('countPhone', 'LIKE', `%${countPhone}%`)
      totalQuery.where('countPhone', 'LIKE', `%${countPhone}%`)
    }
    if (startDate && endDate) {
      query.where('offTime', '>=', moment(startDate).toDate()).where('offTime', '<=', moment(endDate).add(1, 'd').toDate())
      totalQuery.where('offTime', '>=', moment(startDate).toDate()).where('offTime', '<=', moment(endDate).add(1, 'd').toDate())
    }
    if (workStartDate && workEndDate) {
      query.where('workTime', '>=', moment(workStartDate).toDate()).where('workTime', '<=', moment(workEndDate).add(1, 'd').toDate())
      totalQuery.where('workTime', '>=', moment(workStartDate).toDate()).where('workTime', '<=', moment(workEndDate).add(1, 'd').toDate())
    }
    if (finishStartDate && finishEndDate) {
      query.where('finishedAt', '>=', moment(finishStartDate).toDate()).where('finishedAt', '<=', moment(finishEndDate).add(1, 'd').toDate())
      totalQuery.where('finishedAt', '>=', moment(finishStartDate).toDate()).where('finishedAt', '<=', moment(finishEndDate).add(1, 'd').toDate())
    }
    if (offStartDate && offEndDate) {
      query.where('offTime', '>=', moment(offStartDate).toDate()).where('offTime', '<=', moment(offEndDate).add(1, 'd').toDate())
      totalQuery.where('offTime', '>=', moment(offStartDate).toDate()).where('offTime', '<=', moment(offEndDate).add(1, 'd').toDate())
    }
    if (province) {
      query.whereRaw('province like ?', [`%${province}%`])
      totalQuery.whereRaw('province like ?', [`%${province}%`])
    }
    if (city) {
      query.whereRaw('city like ?', [`%${city}%`])
      totalQuery.whereRaw('city like ?', [`%${city}%`])
    }
    switch (createdAt) {
      case 'descend':
        query.orderBy('createdAt', 'desc')
        break;
      case 'ascend':
        query.orderBy('createdAt', 'asc')
        break;
      default:
        break;
    }
    switch (offTime) {
      case 'descend':
        query.orderBy('offTime', 'desc')
        break;
      case 'ascend':
        query.orderBy('offTime', 'asc')
        break;
    }
    switch (finishedAt) {
      case 'descend':
        query.orderBy('finishedAt', 'desc')
        break;
      case 'ascend':
        query.orderBy('finishedAt', 'asc')
        break;
      default:
        break;
    }
    switch (workTime) {
      case 'descend':
        query.orderBy('workTime', 'desc')
        break;
      case 'ascend':
        query.orderBy('workTime', 'asc')
        break;
      default:
        break;
    }
    switch (address) {
      case 'descend':
        query.orderBy('address', 'desc')
        break;
      case 'ascend':
        query.orderBy('address', 'asc')
        break;
      default:
        break;
    }
    if (workerName) {
      query.where('wokerName', 'LIKE', `%${workerName}%`)
      totalQuery.where('wokerName', 'LIKE', `%${workerName}%`)
    }
    if (orderNo) {
      query.whereRaw('orderNo like ?', [`%${orderNo}%`])
      totalQuery.whereRaw('orderNo like ?', [`%${orderNo}%`])
    }
    if (type) {
      query.whereRaw('type like ?', [`%${type}%`])
      totalQuery.whereRaw('type like ?', [`%${type}%`])
    }
    if (waste_1st_type) {
      query.where('waste_1st_ID', waste_1st_type)
      totalQuery.where('waste_1st_ID', waste_1st_type)
    }
    if (userMobile) {
      query.whereRaw('userMobile like ?', [`%${userMobile}%`])
      totalQuery.whereRaw('userMobile like ?', [`%${userMobile}%`])
    }
    if (userName) {
      query.whereRaw('userName like ?', [`%${userName}%`])
      totalQuery.whereRaw('userName like ?', [`%${userName}%`])
    }
    if (address) {
      query.whereRaw('address like ?', [`%${address}%`])
      totalQuery.whereRaw('address like ?', [`%${address}%`])
    }
    let vo = await query.with('worker').paginate(current, pageSize)
    // 计算信息费总和
    let totalInfoFee = await totalQuery.sum('infoFee as totalFee')
    response.json({ ...vo.toJSON(), totalInfoFee: totalInfoFee[0].totalFee || 0 })
  }
  async show({ request, params, response }) {
    let vo = await YCOrder.query()
      .where('id', params.id)
      .fetch()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
  async store({ request, response }) {

  }
  async update({ request, params, response }) {
    let vo = await YCOrder.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
  }
  // 删除订单
  async destroy({ request, params, response }) {
    let vo = await YCOrder.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_DELETE_ID
    }
    let worker = await Worker.query().where('mobile', vo.countPhone).first()
    const deductionAmount = vo.infoFee ? parseInt(vo.infoFee) * parseInt(1) : 0;
    worker.wallet += deductionAmount
    let log = await YCWorkerWalletLog.query().where('orderID', vo.id).first()
    await worker.save()
    if (log) {
      await log.delete()
    }
    await vo.delete()
    response.json(vo)
  }
  async exportYCOrder({ request, response }) {
    let { createdAt, workTime,
      status = "完成", orderNo,
      waste_1st_type, userMobile, userName, workerName, from,
      startDate, endDate,
      workStartDate, workEndDate, orderType, type, countName, countPhone,
      finishStartDate, finishEndDate,
      offStartDate, offEndDate, lowPrice, hightPrice,
      address, price, city, four, province } = request.all()

    // 创建Excel工作簿和工作表
    let workbook = new Excel.Workbook()
    let worksheet = workbook.addWorksheet('Sheet 1')

    // 设置列头和列宽
    let font = { name: 'Times New Roman', size: 12 }
    worksheet.columns = [
      { header: '订单号', key: 'orderNo', width: 18, style: { font } },
      { header: '结算人', key: 'countName', width: 15, style: { font } },
      { header: '四级分类', key: 'type', width: 15, style: { font } },
      { header: '来源', key: 'from', width: 12, style: { font } },
      { header: '客户姓名', key: 'userName', width: 15, style: { font } },
      { header: '联系电话', key: 'userMobile', width: 15, style: { font } },
      { header: '信息费', key: 'infoFee', width: 12, style: { font } },
      { header: '地址', key: 'address', width: 35, style: { font } },
      { header: '省', key: 'province', width: 15, style: { font } },
      { header: '市', key: 'city', width: 15, style: { font } },
      { header: '区', key: 'town', width: 15, style: { font } },
      { header: '县', key: 'county', width: 15, style: { font } },
      { header: '上门时间', key: 'workTime', width: 20, style: { font } },
      { header: '回收人员', key: 'wokerName', width: 15, style: { font } },
      { header: '工作电话', key: 'workerPhone', width: 15, style: { font } },
      { header: '完成时间', key: 'finishedAt', width: 20, style: { font } },
      { header: '揽收时间', key: 'offTime', width: 20, style: { font } },
      { header: '下单时间', key: 'createdAt', width: 20, style: { font } },
      { header: '同步类型', key: 'model', width: 20, style: { font } },
      { header: '备注', key: 'remark', width: 25, style: { font } },
    ]

    try {
      // 首先获取符合条件的总记录数
      const countQuery = this.buildQuery({
        orderType, type, status, startDate, endDate, workStartDate, workEndDate,
        province, lowPrice, hightPrice, orderNo, from, waste_1st_type,
        userMobile, userName, address, price, city, four, countPhone,
        offStartDate, offEndDate, finishStartDate, finishEndDate, workerName
      });
      if (countName) {
        let worker = await Worker.query().where('workerName', countName).first()
        if (worker) {
          countQuery.where('workerID', worker.id)
        } else {
          countQuery.where('countName', 'LIKE', `%${countName}%`)
        }
      }
      const countResult = await countQuery.count('* as total');
      console.log(countResult);

      const totalCount = countResult[0].total;

      console.log(`总记录数: ${totalCount}`);

      // 如果没有记录，直接返回空Excel
      if (totalCount === 0) {
        const fileName = `蚁巢订单数据${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`;
        await workbook.xlsx.writeFile(`./${fileName}`);
        return response.attachment(`./${fileName}`);
      }

      // 分批查询数据
      const batchSize = 2000;
      const totalBatches = Math.ceil(totalCount / batchSize);
      let rowCount = 1; // 标题行占用第一行

      console.log(`开始导出数据，共${totalCount}条，分${totalBatches}批处理`);

      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        const offset = batchIndex * batchSize;

        console.log(`处理第${batchIndex + 1}/${totalBatches}批，偏移量: ${offset}`);

        // 构建查询
        const query = this.buildQuery({
          orderType, type, status, startDate, endDate, workStartDate, workEndDate,
          province, lowPrice, hightPrice, orderNo, from, waste_1st_type,
          userMobile, userName, address, price, city, four, countPhone,
          offStartDate, offEndDate, finishStartDate, finishEndDate, workerName
        });
        if (countName) {
          let worker = await Worker.query().where('workerName', countName).first()
          if (worker) {
            query.where('workerID', worker.id)
          } else {
            query.where('countName', 'LIKE', `%${countName}%`)
          }
        }
        // 添加分页
        query.offset(offset).limit(batchSize);

        // 执行查询
        const batchResult = await query.with('worker').fetch();
        const batchData = batchResult.toJSON();
        console.log(`查询到${batchData.length}条记录`);
        // 添加数据到Excel
        for (const item of batchData) {
          worksheet.addRow({
            orderNo: item.orderNo,
            from: item.from,
            userName: item.userName,
            countName: item.worker && item.worker.workerName || item.wokerName || '',
            type: item.type,
            userMobile: item.userMobile,
            infoFee: item.infoFee ? parseInt(item.infoFee) : 0,
            address: `${item.address}`,
            workTime: item.workTime ? moment(item.workTime).format('YYYY-MM-DD HH:mm') : '',
            workerPhone: item.workerPhone || '',
            wokerName: item.wokerName || '',
            province: item.province || '',
            city: item.city || '',
            town: item.town || '',
            county: item.county || '',
            finishedAt: item.finishedAt ? moment(item.finishedAt).format('YYYY-MM-DD HH:mm') : '',
            offTime: item.offTime ? moment(item.offTime).format('YYYY-MM-DD HH:mm') : '',
            createdAt: item.createdAt ? moment(item.createdAt).format('YYYY-MM-DD HH:mm') : '',
            model: item.model || '',
            remark: item.remark || '',
          });
          rowCount++;
        }

        console.log(`已处理${rowCount - 1}/${totalCount}条记录，完成度: ${Math.round(((rowCount - 1) / totalCount) * 100)}%`);
      }

      // 生成文件名
      const fileName = `京东订单数据${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`;

      // 写入文件并返回
      await workbook.xlsx.writeFile(`./${fileName}`);
      console.log(`导出完成，文件名: ${fileName}`);

      return response.attachment(`./${fileName}`);
    } catch (error) {
      console.error('导出过程中发生错误:', error);
      return response.status(500).json({
        code: 500,
        message: '导出失败',
        error: error.message
      });
    }
  }
  async exportOrderMaintain({ request, response }) {
    try {
      let { status = "待维护", orderNo, remark } = request.all()
      let query = YCOrderMaintain.query().where('status', status)
      if (orderNo) {
        query.where('orderNo', 'LIKE', `%${orderNo}%`)
      }
      if (remark) {
        query.where('remark', 'LIKE', `%${remark}%`)
      }

      let vo = await query.fetch()
      let data = []
      vo.rows.forEach((value, index) => {
        let arrInner = []
        arrInner.push(value.id)
        arrInner.push(value.orderNo)
        arrInner.push(value.status)
        arrInner.push(value.remark || '')
        arrInner.push(moment(value.createdAt).format('YYYY-MM-DD HH:mm:ss'))
        arrInner.push(moment(value.updatedAt).format('YYYY-MM-DD HH:mm:ss'))
        data.push(arrInner)
      })

      let theTitle = [
        'ID',
        '订单号',
        '状态',
        '备注',
        '创建时间',
        '更新时间'
      ]

      let theWidth = [
        { wch: 8 },
        { wch: 20 },
        { wch: 10 },
        { wch: 30 },
        { wch: 22 },
        { wch: 22 }
      ]

      let fileTitle = '待维护订单-' + `${moment().format('YYYY-MM-DD-HH-mm')}`
      await ExcelService.generateExcel(fileTitle, theTitle, data, response, { '!cols': theWidth })
    } catch (error) {
      console.error('导出过程中发生错误:', error)
      return response.status(500).json({
        code: 500,
        message: '导出失败',
        error: error.message
      })
    }
  }
  async orderMaintainList({ request, response }) {
    let { status = "待维护", orderNo, current = 1, pageSize = 10, remark } = request.all()
    let vo = YCOrderMaintain.query().where('status', status)
    if (orderNo) {
      vo.where('orderNo', 'LIKE', `%${orderNo}%`)
    }
    if (remark) {
      vo.where('remark', 'LIKE', `%${remark}%`)
    }
    vo = await vo.paginate(current, pageSize)
    response.json(vo)
  }

  async orderMaintainUpdate({ request, params, response }) {
    let vo = await YCOrderMaintain.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
  }
  // 抽取查询构建逻辑为单独的方法，便于复用
  buildQuery({
    orderType, type, status, startDate, endDate, workStartDate, workEndDate,
    province, lowPrice, hightPrice, orderNo, from, waste_1st_type,
    userMobile, userName, address, price, city, four, countPhone,
    offStartDate, offEndDate, finishStartDate, finishEndDate, workerName
  }) {
    let query = YCOrder.query();
    if (orderNo) { query.where('orderNo', 'LIKE', `%${orderNo}%`); }
    if (orderType) {
      query.where('orderType', orderType);
    }
    if (type) {
      query.whereRaw('type like ?', [`%${type}%`]);
    }
    if (status) {
      query.where('status', status);
    }
    if (startDate && endDate) {
      query.where('createdAt', '>=', moment(startDate).toDate()).where('createdAt', '<=', moment(endDate).add(1, 'd').toDate())
    }
    if (workStartDate && workEndDate) {
      query.where('workTime', '>=', moment(workStartDate).toDate()).where('workTime', '<=', moment(workEndDate).add(1, 'd').toDate())
    }
    if (finishStartDate && finishEndDate) {
      query.where('finishedAt', '>=', moment(finishStartDate).toDate()).where('finishedAt', '<=', moment(finishEndDate).add(1, 'd').toDate())
    }
    if (offStartDate && offEndDate) {
      query.where('offTime', '>=', moment(offStartDate).toDate()).where('offTime', '<=', moment(offEndDate).add(1, 'd').toDate())
    }

    if (province) {
      query.whereRaw('province like ?', [`%${province}%`]);
    }
    if (lowPrice && hightPrice) {
      query.whereBetween('price', [lowPrice, hightPrice]);
    }
    if (countPhone) {
      query.where('countPhone', 'LIKE', `%${countPhone}%`)
    }
    if (from) {
      query.whereRaw('from like ?', [`%${from}%`]);
    }
    if (waste_1st_type) {
      query.where('waste_1st_ID', waste_1st_type);
    }
    if (userMobile) {
      query.whereRaw('userMobile like ?', [`%${userMobile}%`]);
    }
    if (userName) {
      query.whereRaw('userName like ?', [`%${userName}%`]);
    }
    if (address) {
      query.whereRaw('address like ?', [`%${address}%`]);
    }
    if (price === 'descend') {
      query.orderBy('price', 'desc');
    } else if (price === 'ascend') {
      query.orderBy('price', 'asc');
    }
    if (city) {
      query.whereRaw('city like ?', [`%${city}%`]);
    }
    if (four) {
      query.whereRaw('four like ?', [`%${four}%`]);
    }

    if (workerName) {
      query.where('wokerName', 'LIKE', `%${workerName}%`);
    }

    return query;
  }

  // 批量删除订单
  async batchDestroy({ request, response }) {
    try {
      const { ids } = request.all()

      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        throw ERR.API_ERROR
      }

      // 批量删除订单
      for (const id of ids) {
        const order = await YCOrder.find(id)
        let worker = await Worker.query().where('mobile', order.countPhone).first()
        const deductionAmount = order.infoFee ? parseInt(order.infoFee) * parseInt(1) : 0;
        worker.wallet += deductionAmount
        let log = await YCWorkerWalletLog.query().where('orderID', order.id).first()
        if (log) {
          await YCWorkerWalletLog.create({
            orderID: order.id,
            workerID: worker.id,
            sign: order.orderNo,
            money: +(deductionAmount),
            remark: `${order.wokerName}京东订单红充`
          })
        }
        if (order) {
          // 删除订单
          await order.delete()
          await worker.save()
        }
      }
      return response.json({
        code: 0,
        message: '批量删除订单成功'
      })
    } catch (error) {
      console.error('批量删除订单失败:', error)
      throw ERR.API_ERROR
    }
  }
  // 后台完成订单
  async comfirmOrder({ request, params, response }) {
    let { remark = "后台完单", orderID } = request.all()
    let id = orderID
    await ReqLog.create({ req: JSON.stringify(request.all()), source: '后台YC订单确认' })
    let data = await YCOrder.find(id)
    if (data.toJSON().status === OrderStatus.Cancelled) {
      throw ERR.API_ERROR
    }
    let infoFee = parseInt(data.infoFee)
    let remuneration = infoFee
    let workerVO = await Worker.find(data.workerID)
    let moneyRes
    let uniqOrder = await YCWorkerWalletLog.query().where("orderID", id).first()
    if (uniqOrder) {
      throw ERR.INVALID_PARAMS;
    }
    await YCWorkerWalletLog.create({
      workerID: data.workerID,
      money: -infoFee,
      remuneration,
      commission: infoFee,
      payMoney: infoFee,
      companyID: data.companyID,
      remark,
      orderID: id,
    })
    moneyRes = parseFloat(workerVO.toJSON().wallet) - parseInt(infoFee)
    workerVO.wallet = moneyRes
    await workerVO.save()
    data.status = '完成'
    data.infoFee = infoFee
    data.commission = infoFee
    data.finishedAt = moment().format('YYYY-MM-DD HH:mm:ss')
    await data.save()
    await YCOrderLog.create({
      status: E.OrderLogStatus.Completed,
      orderID: id,
      workerID: data.workerID,
      content: remark,
    })
    response.json(workerVO)
    data = data.toJSON()
    await ReqLog.create({ res: JSON.stringify(workerVO), source: '后台YC订单确认' })
  }
}

module.exports = YCOrderImportController
