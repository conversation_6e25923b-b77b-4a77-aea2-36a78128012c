.container {
  .dateFilter {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #f5f5f5;
    border-radius: 6px;
    margin-bottom: 16px;
  }

  .rechargeItem {
    text-align: center;
    padding: 10px 0;

    .rechargeTitle {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .rechargeValue {
      font-size: 24px;
      font-weight: bold;
      line-height: 1.2;
    }
  }

  .detailItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .detailLabel {
      font-size: 14px;
      color: #666;
    }

    .detailValue {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }

  .chartContainer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

:global {
  .ant-statistic-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }

  .ant-statistic-content {
    font-size: 20px;
    font-weight: bold;
  }

  .ant-card-small > .ant-card-head {
    min-height: 38px;
    padding: 0 12px;
    font-size: 14px;
  }

  .ant-card-small > .ant-card-body {
    padding: 12px;
  }
} 