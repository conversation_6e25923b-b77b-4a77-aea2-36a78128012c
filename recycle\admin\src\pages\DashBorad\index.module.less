.page {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.section-title {
  font-size: 18px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 16px;
  padding-left: 8px;
  border-left: 4px solid #1890ff;
  display: flex;
  align-items: center;
  gap: 8px;

  .title-icon {
    font-size: 20px;
    color: #1890ff;
  }
}

.core-metric {
  background: linear-gradient(135deg, #ffffff 0%, #f0f5ff 100%);
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(24, 144, 255, 0.1);
  }

  .stat-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;

    .stat-icon {
      font-size: 20px;
      
      &.shopping { color: #1890ff; }
      &.success { color: #52c41a; }
      &.warning { color: #faad14; }
      &.danger { color: #ff4d4f; }
    }

    span {
      font-size: 15px;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}

.channel-card {
  height: auto !important;
  border-radius: 12px;
  transition: all 0.3s ease;
  
  &.jd-channel {
    background: linear-gradient(135deg, #ffffff 0%, #e6f7ff 100%);
  }
  
  &.self-channel {
    background: linear-gradient(135deg, #ffffff 0%, #f6ffed 100%);
  }
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }

  .channel-header {
    padding: 16px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    margin-bottom: 16px;
  }

  .channel-title {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .channel-icon {
      font-size: 20px;
      color: #1890ff;
    }
    
    span {
      font-size: 16px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
  }

  .channel-stats {
    padding: 0 12px;
  }

  .channel-footer {
    padding: 12px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 0 0 12px 12px;
    
    .footer-item {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }

      .trend-icon {
        font-size: 14px;
        
        &.up {
          color: #52c41a;
        }
        
        &.down {
          color: #ff4d4f;
        }
      }

      .amount {
        color: #1890ff;
        font-weight: 500;
      }
    }
  }
}

.stat-footer {
  display: flex;
  justify-content: space-between;
  padding: 0 12px;
  
  .footer-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    
    span {
      &:first-child {
        color: rgba(0, 0, 0, 0.45);
        font-size: 12px;
      }
    }
    
    .value {
      color: #1890ff;
      font-weight: 500;
      font-size: 14px;
    }
  }
}

alignCard {
  margin-top: 24px;

  :global {
    .ant-card-head {
      min-height: 48px;
      padding: 0 24px;
    }

    .ant-card-body {
      padding: 24px;
    }
  }
}

size {
  height: 120px;
}

// 高亮颜色
.highlight {
  &-blue { color: #1890ff !important; }
  &-green { color: #52c41a !important; }
  &-warning { color: #faad14 !important; }
  &-red { color: #ff4d4f !important; }
}

// 嗨回收数据分布样式
.hi-distribution {
  margin-top: 32px;
  
  .distribution-section {
    margin-bottom: 24px;
    
    .section-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 20px;
      padding: 12px 0;
      border-bottom: 2px solid #f0f0f0;
      
      .header-icon {
        font-size: 22px;
        color: #13c2c2;
      }
      
      .header-title {
        font-size: 18px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
  
  .distribution-card {
    background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
    border-radius: 16px;
    border: 1px solid #e6f7ff;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 12px 24px rgba(19, 194, 194, 0.15);
      border-color: #91d5ff;
    }
    
    :global {
      .ant-card-head {
        background: linear-gradient(90deg, #13c2c2 0%, #36cfc9 100%);
        border-radius: 16px 16px 0 0;
        border-bottom: none;
        
        .ant-card-head-title {
          color: #ffffff;
          font-weight: 600;
          font-size: 16px;
        }
      }
      
      .ant-card-extra {
        .filter-container {
          .ant-picker {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            
            &:hover {
              border-color: #ffffff;
            }
          }
        }
      }
      
      .ant-card-body {
        padding: 20px;
        background: #ffffff;
        border-radius: 0 0 16px 16px;
      }
    }
    
    .chart-container {
      position: relative;
      
      .chart-overlay {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        pointer-events: none;
        
        .overlay-title {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.45);
          margin-bottom: 4px;
        }
        
        .overlay-value {
          font-size: 20px;
          font-weight: 600;
          color: #13c2c2;
        }
      }
    }
  }
}

// 过滤器容器样式
.filter-container {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .ant-select {
    min-width: 120px;
  }
  
  .ant-picker {
    border-radius: 8px;
  }
}

// 响应式调整
@media screen and (max-width: 1400px) {
  .core-metric {
    .stat-title {
      .stat-icon {
        font-size: 18px;
      }
      span {
        font-size: 14px;
      }
    }
  }
  
  .channel-card {
    .channel-title {
      .channel-icon {
        font-size: 18px;
      }
      span {
        font-size: 15px;
      }
    }
  }
  
  .hi-distribution {
    .distribution-card {
      :global {
        .ant-card-head-title {
          font-size: 14px;
        }
      }
    }
  }
}