'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Discovery } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { CryptUtil } = require('../../../Util')
const Env = use('Env')

//公司信息
class DiscoveryController {
  //获取帖子列表
  async index({ request, response }) {
    let { type, current = 1, pageSize = 10, sort = 'desc', theKey } = request.all()
    if (!type) {
      throw ERR.INVALID_PARAMS
    }
    let query = Discovery.query()
      .where('type', type)
      .where('deletedAt', 0)
    if (theKey) {
      query.whereRaw('content like ?', [`%${theKey}%`])
    }

    let vo = await query.orderBy('id', sort).paginate(current, pageSize)
    response.json(vo)
  }
  //创建帖子
  async store({ request, response }) {
    let { type, content, images } = request.all()
    if (!type || !content) {
      throw ERR.INVALID_PARAMS
    }
    let vo = await Discovery.create({
      type,
      content,
      images
    })
    response.json(vo)
  }
  //帖子内容更新
  async update({ request, params, response }) {
    let vo = await Discovery.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
  }
  //帖子删除
  async destroy({ request, params, response }) {
    let vo = await Discovery.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_DELETE_ID
    } else {
      vo.deletedAt = moment().unix()
      await vo.save()
    }
    response.json(vo)
  }
}

module.exports = DiscoveryController
