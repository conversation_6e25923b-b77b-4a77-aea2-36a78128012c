import { PlusOutlined } from '@ant-design/icons'
import { Button, Tabs, Input, Switch, Modal, notification, Upload } from 'antd'
import type { ActionType, ProColumns } from '@ant-design/pro-table'
import ProTable, { } from '@ant-design/pro-table'
import ProCard from '@ant-design/pro-card'
import { effect } from 'dva17'
import { useEffect, useRef, useState } from 'react'
import { EGetDiscoveryList, EPostDiscoveryList, EPutDiscoveryList, NWorker } from '../../common/action'
import { DiscoveryType, } from '../../common/enum'
import { delDiscovery } from "../../services/discovery"
import styles from './index.module.less'
import { SERVER_HOME } from '../../common/config'
const { TabPane } = Tabs
const { TextArea } = Input

type Item = {
  id: number
  content: string
  createdAt: string
  images: string
  updatedAt: string
  type: string
  isReport: number
}



export default () => {
  const actionRef = useRef<ActionType>()
  const [type, setType] = useState<any>(DiscoveryType.WELFARE)
  const [visible, setVisible] = useState<any>(false)
  const [vo, setVo] = useState<any>(null)
  const [content, setContent] = useState<any>(null)
  const [imgs, setImgs] = useState<any>([])
  let [files, setFiles] = useState<any>([])
  let [previewVisible, setPreviewVisible] = useState<any>(false)
  let [previewImage, setPreviewImage] = useState<any>(null)

  const columns: ProColumns<Item>[] = [
    {
      title: '内容',
      dataIndex: 'content',
      width: '30%',
      copyable: false,
      search: false,
    },
    {
      title: '启用状态',
      dataIndex: 'isReport',
      copyable: false,
      ellipsis: true,
      search: false,
      render: (_, row) => <Switch
        checkedChildren="启动"
        unCheckedChildren="停用"
        defaultChecked={Boolean(row.isReport)}
        onChange={
          e => {
            changeForbidden(e, row)
          }}
      />,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      copyable: false,
      search: false,
    },
    {
      title: '修改时间',
      dataIndex: 'updatedAt',
      copyable: false,
      search: false,
    },
    {
      title: '操作',
      copyable: false,
      ellipsis: true,
      search: false,
      render: (_, row: any) => (
        <>
          <Button
            style={{ marginRight: 10 }} onClick={() => {
              setVisible(true)
              setVo(row)
            }}>编辑</Button>
          <Button
            type='primary'
            onClick={() => {
              deleteItem(row)
            }}>删除</Button>
        </>
      ),
    },
  ]
  const deleteItem = (e: any) => {
    Modal.confirm({
      title: '确认删除该条数据',
      content: <div>删除后数据无法恢复！</div>,
      okText: '确认删除',
      cancelText: '退出',
      onOk: async () => {
        await delDiscovery({ id: e.id })
        refreshPage()
        notification.success({
          message: '成功！',
          description: '删除成功',
          duration: 2
        })
      },
      width: 700
    })
  }
  const changeForbidden = async (e: any, record: any) => {
    console.log(e, record);
    await effect(NWorker, EPutDiscoveryList, { id: record.id, isReport: e }).then(() => {
      notification.success({
        message: '成功！',
        description: '修改成功',
        duration: 2
      })
    })
    refreshPage()
  }
  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
    if (vo) {
      setContent(vo.content)
      setFiles(JSON.parse(vo.images))
      setImgs(vo.images && JSON.parse(vo.images).map((e: string) => {
        return { uid: Math.random(), url: e, name: e.split('/').pop() }
      }))
    }
  }, [vo])

  /*--------------------- 响应 ---------------------*/
  const refreshPage = () => {
    if (actionRef.current) {
      actionRef.current.reload()
    }
  }
  function getBase64(file: any) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  }
  const onPreview = async (file: any) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setPreviewImage(file.url || file.preview)
    setPreviewVisible(true)
  }
  //上传Banner图
  const beforeUploadPic = (file: any) => {
    let isImage;
    if (['image/png', 'image/jpg', 'image/jpeg', 'image/gif', 'image/svg'].includes(file.type)) {
      isImage = true
    } else {
      isImage = false;
      alert('请上传正确的格式')
    }
    return isImage
  };
  //上传详情图片
  const handleChangePicture = async ({ fileList }: any) => {
    setImgs(fileList)
    if (fileList.length > 0) {
      fileList.map((vo: any, index: number) => {
        if (vo.status === 'done') {
          files.push(vo.response.url)
        }
      })
      setFiles([...new Set(files)])
    }
  }
  /*--------------------- 渲染 ---------------------*/
  return (
    <ProCard>
      <Tabs defaultActiveKey={DiscoveryType.WELFARE} onChange={(e) => {
        setType(e)
        refreshPage()
      }}>
        <TabPane tab="公益" key={DiscoveryType.WELFARE} />
        <TabPane tab="活动" key={DiscoveryType.ACTIVITIES} />
        <TabPane tab="媒体报道" key={DiscoveryType.MEDIA} />
      </Tabs>
      <ProTable<Item>
        actionRef={actionRef}
        columns={columns}
        request={async (params = {}) => (await effect(NWorker, EGetDiscoveryList, { ...params, type: type })) as any}
        pagination={{
        }}
        rowKey="id"
        dateFormatter="string"
        headerTitle="文章管理"
        toolBarRender={() => [
          <Button
            onClick={() => {
              setVisible(true)
              setVo(null)
            }} key="3" type="primary">
            <PlusOutlined />
            新建
          </Button>,
        ]}
      />
      <Modal open={previewVisible} footer={null} onCancel={() => setPreviewVisible(false)}>
        <img alt="example" style={{ width: '100%' }} src={previewImage} />
      </Modal>
      <Modal open={visible}
        title={vo ? '编辑文章' : '新建文章'}
        onOk={() => {
          setVisible(false)
        }}
        onCancel={() => {
          setVisible(false)
        }}>

      </Modal>
      <Modal open={visible} title={vo ? '编辑文章' : '新建文章'}
        onOk={async () => {
          if (!vo) {
            setVisible(false)
            await effect(NWorker, EPostDiscoveryList, { type, content, images: JSON.stringify(files) }).then(() => {
              refreshPage()
              setContent(null)
              setFiles([])
              setImgs([])
            })
          } else {
            setVisible(false)
            await effect(NWorker, EPutDiscoveryList, { id: vo.id, type, content, images: JSON.stringify(files) }).then(() => {
              refreshPage()
              setContent(null)
              setFiles([])
              setImgs([])
            })
          }
        }}
        onCancel={() => {
          setVisible(false)
        }} width={800}>
        <div className={styles.item}>
          <span className={styles.item_title}>文字内容：</span>
          <div className={styles.item_content}>
            <TextArea
              value={content}
              onChange={(e: any) => {
                setContent(e.target.value)
              }}
            />
          </div>
        </div>
        <div className={styles.item} style={{ marginTop: '30px' }}>
          <span className={styles.item_title}>相关图片：</span>
          <div className={styles.item_content}>
            <div className={styles.imag_wrapper}>
              <div>
                <Upload
                  listType="picture-card"
                  accept="image/*"
                  action={`${SERVER_HOME}file`}
                  fileList={imgs}
                  beforeUpload={beforeUploadPic}
                  onChange={handleChangePicture}
                  onPreview={onPreview}
                >
                  {imgs.length >= 6 ? null : <div>
                    <div className="ant-upload-text">点击上传图片</div>
                  </div>}
                </Upload>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </ProCard>
  )
}
