import { Image } from 'antd'
import styles from './index.module.less'
import { useEffect, Fragment } from 'react'
import dayjs from 'dayjs'
import { getOrderLog } from '../../services/order'
import { reducer } from 'dva17'
import { NOrder, RSetState } from '../../common/action'
import { pageStatus, } from '../../common/enum'


export default (record: any, whichOrder: string) => {
  let row=record.row
  /*--------------------- 生命周期 ---------------------*/
  /*--------------------- 响应 ---------------------*/
  const getLog = async () => {
    let data = await getOrderLog({ id: row.id })
    reducer(NOrder, RSetState, { logData: data, visibleLog: true })
  }
  /*--------------------- 渲染 ---------------------*/
  return (
    <div className={styles.item_wrapper} >
      <div className={styles.item} style={{ marginTop: 30 }}>
        <span className={styles.item_title}> 下单时间：</span>
        <div className={styles.item_content} > {dayjs(row.createdAt).format('YYYY-MM-DD HH:mm:ss')} </div>
        <div className={styles.item_log} onClick={() => {
          getLog()
        }}> 订单记录</div>
      </div>
      <div className={styles.item} style={{ marginTop: 20 }}>
        <span className={styles.item_title}> 信息费：</span>
        <div className={styles.item_content} > ¥{row?.infoFee}元 </div>
      </div>
      {
        whichOrder === pageStatus.Cancelled || (whichOrder === pageStatus.Pending && row.cancel)
          ? [
            { title: '取消时间：', content: row.cancel ? dayjs(row.cancel.createdAt).format('YYYY-MM-DD HH:mm:ss') : '-' },
            { title: '取消原因：', content: row.cancel ? row.cancel.cancelReason : '-' },
            { title: '谁取消：', content: row.cancel && row.cancel.whoCancel ? row.cancel.whoCancel : '-' }
          ].map((value: any, index: number) => (
            <div className={styles.item} key={value + index}>
              <span className={styles.item_title}> {value.title} </span>
              < div className={styles.item_content} > {value.content} </div>
            </div>
          ))
          : null}
      {
        whichOrder === pageStatus.Pending && row.cancel
          ? [{ title: '回收人员：', content: row.cancel.worker?.workerName }, { title: '联系方式：', content: row.cancel.worker?.mobile }].map(
            (value: any, index: number) => (
              <div className={styles.item} key={value + index}>
                <span className={styles.item_title}> {value.title} </span>
                < div className={styles.item_content} > {value.content} </div>
              </div>
            )
          )
          : null}
      <div className={styles.item}>
        <span className={styles.item_title}> 预约时间：</span>
        < div className={styles.item_content} style={{ flexWrap: 'wrap' }}>
          {dayjs(row.workTime).format('YYYY-MM-DD A')}
        </div>
      </div>
      <div className={styles.item}>
        <span className={styles.item_title}> 回收物：</span>
        < div className={styles.item_content} style={{ flexWrap: 'wrap' }}>
          {row.type}
        </div>
      </div>
      {
        whichOrder === pageStatus.Pending || whichOrder === pageStatus.Dispatched || whichOrder === pageStatus.InProgress ? (
          <Fragment>
            <div className={styles.item} >
              <span className={styles.item_title}> 备注：</span>
              < div className={styles.item_content} > {row.remark ? row.remark : '--'} </div>
            </div>
            <div className={styles.item} style={{ flexDirection: 'column' }
            }>
              <span className={styles.item_title}> 回收物特征：</span>
              < div className={styles.item_content} style={{ marginTop: '20px' }}>
                {row.extendFields}
              </div>
            </div>
            {
              row.worker ? (
                <Fragment>
                  <div className={styles.item} >
                    <span className={styles.item_title}> 回收员姓名：</span>
                    < div className={styles.item_content} > {row.worker?.workerName} </div>
                  </div>
                  < div className={styles.item} >
                    <span className={styles.item_title}> 回收员电话：</span>
                    < div className={styles.item_content} > {row.worker?.mobile} </div>
                  </div>
                </Fragment>
              ) : null
            }
          </Fragment>
        ) : (
          <Fragment>
            <div className={styles.item} >
              <span className={styles.item_title}> 客户姓名：</span>
              < div className={styles.item_content} > {row.userName} </div>
            </div>
            < div className={styles.item} >
              <span className={styles.item_title}> 客户电话：</span>
              < div className={styles.item_content} > {row.userMobile ? row.userMobile : '-'} </div>
            </div>
            {
              whichOrder === pageStatus.Completed ? (
                <>
                  <div className={styles.item} >
                    <span className={styles.item_title}> 支付系统价：</span>
                    < div className={styles.item_content} >¥{row.infoFee} </div>
                  </div>
                  <div className={styles.item} >
                    <span className={styles.item_title}> 信息费：</span>
                    < div className={styles.item_content} >¥{row.commission} </div>
                  </div>
                  <div className={styles.item} >
                    <span className={styles.item_title}> 回收预估价：</span>
                    < div className={styles.item_content} >¥{row.apprizeAmount} </div>
                  </div>
                </>) : null
            }
            <div className={styles.item}>
              <span className={styles.item_title}> 回收地址：</span>
              < div className={styles.item_content} >
                {
                  row.province + row.city + row.address
                }
              </div>
            </div>
            {
              row.worker ? (
                <Fragment>
                  <div className={styles.item} >
                    <span className={styles.item_title}> 回收员姓名：</span>
                    < div className={styles.item_content} > {row.worker?.workerName} </div>
                  </div>
                  < div className={styles.item} >
                    <span className={styles.item_title}> 回收员电话：</span>
                    < div className={styles.item_content} > {row.worker?.mobile} </div>
                  </div>
                </Fragment>
              ) : null
            }
            {
              whichOrder === pageStatus.Completed ? (
                <Fragment>
                  <div className={styles.item} >
                    <span className={styles.item_title}> 回收物图片：</span>
                    < div className={styles.item_content} style={{ marginTop: '20px', flexWrap: 'wrap' }
                    }>
                      {row?.doneInfo?.imageUrl ? JSON.parse(row?.doneInfo?.imageUrl).map((img: string, index: number) => <Image className={styles.item_pic} src={img} key={img} />) : null}
                    </div>
                  </div>
                  < div className={styles.item} >
                    <span className={styles.item_title}> 完成时间：</span>
                    < div className={styles.item_content} > {dayjs(row.finishedAt).format('YYYY-MM-DD HH:mm:ss')} </div>
                  </div>
                  < div className={styles.item} >
                    <span className={styles.item_title}> 用户签名：</span>
                    < div className={styles.item_content} >  <Image className={styles.item_pic} src={row?.doneInfo?.sign} /> </div>
                  </div>
                  < div className={styles.item} >
                    <span className={styles.item_title}> 订单评价：</span>
                    {
                      row.rating ? (
                        <div className={styles.item_content} > {row.rating.workerRating === 1 ? '不满意' : row.rating.workerRating === 2 ? '一般' : '满意'} </div>
                      ) : (
                        '未评价'
                      )
                    }
                  </div>
                </Fragment>
              ) : null}
          </Fragment>
        )}
      {
        row.company ? (
          <Fragment>
            < div className={styles.item} >
              <span className={styles.item_title}> 服务商：</span>
              < div className={styles.item_content} > {row.company.companyName} </div>
            </div>
            < div className={styles.item} >
              <span className={styles.item_title}> 服务商电话：</span>
              < div className={styles.item_content} > {row.company.mobile} </div>
            </div>
          </Fragment>
        ) : null
      }
    </div>
  );
}
