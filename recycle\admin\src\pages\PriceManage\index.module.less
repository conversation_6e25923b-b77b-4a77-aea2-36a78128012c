.page {
  flex          : 1;
  display       : flex;
  flex-direction: column;
  align-items   : center;
}

.select_wrapper {
  display  : flex;
  flex-wrap: wrap;

  .select {
    width        : 120px;
    height       : 36px;
    text-align   : center;
    border       : 1px solid #cccccc;
    cursor       : pointer;
    border-radius: 4px;
    margin       : 0 10px;
    margin-top   : 20px;
    font-weight  : 500;
  }

  .selected {
    background: #1890ff;
    color     : #ffffff;
  }
}

.wrapper {
  display: flex;

  >span {
    margin-top : 20px;
    display    : inline-block;
    min-width  : 120px;
    height     : 36px;
    line-height: 36px;
    text-align : right;
    font-weight: 600;
  }

  >p {
    height     : 36px;
    line-height: 36px;
    margin     : 0;
    margin-top : 20px;
  }
}

.price_wrapper {
  margin-top : 20px;
  margin-left: 10px;

  .title_wrapper {
    display: flex;

    .title {
      height     : 36px;
      line-height: 36px;
      width      : 160px;
      text-align : center;
    }
  }

  .content {
    display   : flex;
    margin-top: 16px;

    .tag {
      height     : 36px;
      line-height: 36px;
      width      : 160px;
      text-align : center;
    }

    .item {
      width          : 140px;
      display        : flex;
      justify-content: center;
      align-items    : center;
      margin-left    : 10px;
      position       : relative;

      .price_input {
        position  : absolute;
        left      : 0;
        top       : 0;
        border    : none;
        text-align: center;
        width     : 100%;
        height    : 100%;
        opacity   : 0;
      }
    }
  }
}