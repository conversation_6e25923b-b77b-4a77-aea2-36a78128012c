const xlsx = require("node-xlsx");
const workSheetsFromFile = xlsx.parse(`${__dirname}/品类.xlsx`);
const _ = require('lodash');
// let data0 = workSheetsFromFile;

//let [{ data }] = workSheetsFromFile
let data = workSheetsFromFile[1].data;

let name = fetchValue([2, 1], data);
console.log('name', name)

// // 取出标题
// let name = fetchValue([2, 3], data);
// // // 取出一个矩形的数据
// let category = fetchRectangleData([6, 2], [8, 6], data);
// 取出数据

let result = {};
// result[fetchValue([2, 1], data)] = parseData1(
//   fetchRectangleData([6, 2], [8, 6], data)
// );
// result[fetchValue([2, 9], data)] = parseData1(
//   fetchRectangleData([6, 10], [8, 16], data)
// );
// result[fetchValue([2, 19], data)] = parseData1(
//   fetchRectangleData([6, 20], [9, 25], data)
// );
// result[fetchValue([2, 28], data)] = parseData1(
//   fetchRectangleData([6, 29], [8, 34], data)
// );
// result[fetchValue([2, 37], data)] = parseData1(
//   fetchRectangleData([6, 38], [8, 42], data)
// );

// result[fetchValue([2, 1], data)] = parseData1(
//   fetchRectangleData([13, 2], [16, 3], data)
// );
// result[fetchValue([2, 8], data)] = parseData1(
//   fetchRectangleData([13, 9], [23, 15], data)
// );
// result[fetchValue([2, 19], data)] = parseData1(
//   fetchRectangleData([13, 20], [18, 26], data)
// );

// result[fetchValue([2, 28], data)] = parseData1(
//   fetchRectangleData([13, 29], [17, 32], data)
// );

result[fetchValue([2, 36], data)] = parseData1(
  fetchRectangleData([13, 37], [19, 43], data)
);
result[fetchValue([2, 47], data)] = parseData1(
  fetchRectangleData([13, 48], [14, 54], data)
);
result[fetchValue([2, 57], data)] = parseData1(
  fetchRectangleData([13, 58], [15, 64], data)
);

// result[fetchValue([2, 66], data)] = parseData1(
//   fetchRectangleData([13, 67], [14, 72], data)
// );



// console.log(name);
// console.log(result);
// console.log(result['电视']);
// console.log(result['电视'][0]);
console.log(result)
console.log('-----------------------')
_.forEach(result, async function (data0, k0) {
  console.log(k0)
  //k0 值插入2ndCat
  _.forEach(data0, async function (data1, k1) {
    console.log(k1)
    console.log(data1.name)
    _.forEach(data1.attributes, async function (dataAttr, keyAttr) {
      console.log(keyAttr)
      console.log(dataAttr)
    })
  })
})


function fetchValue([x1, y1], sources) {
  return sources[y1 - 1][x1 - 1];
}
function fetchRectangleData([x1, y1], [x2, y2], sources) {
  let = ret = [];
  for (let y = y1; y <= y2; y++) {
    let row = sources[y - 1].slice(x1 - 1, x2);
    ret.push(row);
  }
  return ret;
}
function parseData1(sources) {
  let result = [];
  sources.forEach((row, index) => {
    if (index == 0) {
      // 第一行
      for (let i in row) {
        if (i !== 0) {
          result[i - 1] = { name: row[i], attributes: {} };
        }
      }
    } else {
      let sortName = null;
      for (let i in row) {
        if (i == 0) {
          // 分类名
          sortName = row[i];
        } else {
          // 分类值
          result[i - 1].attributes[sortName] = row[i].split(/、|，|,/g);
        }
      }
    }
  });
  return result;
}
