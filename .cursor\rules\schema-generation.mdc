---
description: 数据库结构设计和代码生成规范
---

# 数据库结构设计和代码生成规范

## 数据库设计原则

### 表命名规范
```sql
-- 表名使用复数形式
users                -- 用户表
categories           -- 分类表
orders               -- 订单表
order_items          -- 订单项表
workers              -- 师傅表
worker_areas         -- 师傅服务区域表
```

### 字段命名规范
```sql
-- 使用下划线命名法
id                   -- 主键
user_id              -- 外键(关联到users表的id)
first_name           -- 名字
last_name            -- 姓氏
created_at           -- 创建时间
updated_at           -- 更新时间
is_active            -- 布尔值字段(是否激活)
```

### 通用字段设计
```sql
-- 每个表应包含的基础字段
CREATE TABLE users (
  id INT UNSIGNED NOT NULL AUTO_INCREMENT,  -- 主键
  status VARCHAR(20) NOT NULL DEFAULT 'active',  -- 状态
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 创建时间
  updated_at TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,  -- 更新时间
  deleted_at TIMESTAMP NULL DEFAULT NULL,  -- 软删除时间(支持软删除)
  PRIMARY KEY (id)
);
```

## 数据库表结构示例

### 用户表设计
```sql
CREATE TABLE users (
  id INT UNSIGNED NOT NULL AUTO_INCREMENT,
  phone VARCHAR(20) NOT NULL,
  password VARCHAR(100) NOT NULL,
  name VARCHAR(50) NULL,
  avatar VARCHAR(255) NULL,
  gender ENUM('male', 'female', 'unknown') DEFAULT 'unknown',
  birthday DATE NULL,
  address TEXT NULL,
  status ENUM('active', 'inactive', 'banned') NOT NULL DEFAULT 'active',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (id),
  UNIQUE INDEX idx_phone (phone)
);
```

### 订单表设计
```sql
CREATE TABLE orders (
  id INT UNSIGNED NOT NULL AUTO_INCREMENT,
  order_no VARCHAR(50) NOT NULL COMMENT '订单编号',
  user_id INT UNSIGNED NOT NULL COMMENT '用户ID',
  worker_id INT UNSIGNED NULL COMMENT '师傅ID',
  category_id INT UNSIGNED NOT NULL COMMENT '分类ID',
  total_price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '总价格',
  address VARCHAR(255) NOT NULL COMMENT '回收地址',
  contact_name VARCHAR(50) NOT NULL COMMENT '联系人姓名',
  contact_phone VARCHAR(20) NOT NULL COMMENT '联系电话',
  appointment_time DATETIME NOT NULL COMMENT '预约时间',
  status ENUM('pending', 'assigned', 'in_progress', 'completed', 'cancelled') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
  payment_status ENUM('unpaid', 'paid', 'refunded') NOT NULL DEFAULT 'unpaid' COMMENT '支付状态',
  payment_method VARCHAR(20) NULL COMMENT '支付方式',
  remark TEXT NULL COMMENT '备注',
  images TEXT NULL COMMENT '图片(JSON格式)',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (id),
  UNIQUE INDEX idx_order_no (order_no),
  INDEX idx_user_id (user_id),
  INDEX idx_worker_id (worker_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
);
```

### 订单项表设计
```sql
CREATE TABLE order_items (
  id INT UNSIGNED NOT NULL AUTO_INCREMENT,
  order_id INT UNSIGNED NOT NULL COMMENT '订单ID',
  category_id INT UNSIGNED NOT NULL COMMENT '分类ID',
  name VARCHAR(100) NOT NULL COMMENT '物品名称',
  brand VARCHAR(50) NULL COMMENT '品牌',
  model VARCHAR(50) NULL COMMENT '型号',
  age INT NULL COMMENT '使用年限',
  condition VARCHAR(50) NULL COMMENT '新旧程度',
  price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '回收价格',
  images TEXT NULL COMMENT '图片(JSON格式)',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  INDEX idx_order_id (order_id)
);
```

### 师傅表设计
```sql
CREATE TABLE workers (
  id INT UNSIGNED NOT NULL AUTO_INCREMENT,
  user_id INT UNSIGNED NULL COMMENT '关联的用户ID',
  name VARCHAR(50) NOT NULL COMMENT '姓名',
  phone VARCHAR(20) NOT NULL COMMENT '电话',
  id_card VARCHAR(18) NOT NULL COMMENT '身份证号',
  avatar VARCHAR(255) NULL COMMENT '头像',
  address VARCHAR(255) NULL COMMENT '地址',
  company_id INT UNSIGNED NULL COMMENT '所属公司ID',
  status ENUM('active', 'inactive', 'reviewing') NOT NULL DEFAULT 'reviewing' COMMENT '状态',
  rating DECIMAL(2,1) NOT NULL DEFAULT 5.0 COMMENT '评分',
  order_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单数量',
  balance DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '账户余额',
  bank_account VARCHAR(50) NULL COMMENT '银行账号',
  bank_name VARCHAR(50) NULL COMMENT '开户行',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (id),
  UNIQUE INDEX idx_phone (phone),
  INDEX idx_company_id (company_id),
  INDEX idx_status (status)
);
```

## Adonis.js 数据迁移

### 迁移文件示例
```javascript
// database/migrations/1_users_schema.js
'use strict'

const Schema = use('Schema')

class UsersSchema extends Schema {
  up() {
    this.create('users', (table) => {
      table.increments()
      table.string('phone', 20).notNullable().unique()
      table.string('password', 100).notNullable()
      table.string('name', 50).nullable()
      table.string('avatar').nullable()
      table.enum('gender', ['male', 'female', 'unknown']).defaultTo('unknown')
      table.date('birthday').nullable()
      table.text('address').nullable()
      table.enum('status', ['active', 'inactive', 'banned']).notNullable().defaultTo('active')
      table.timestamp('created_at').notNullable().defaultTo(this.fn.now())
      table.timestamp('updated_at').nullable()
      table.timestamp('deleted_at').nullable()
    })
  }

  down() {
    this.drop('users')
  }
}

module.exports = UsersSchema

// database/migrations/2_orders_schema.js
'use strict'

const Schema = use('Schema')

class OrdersSchema extends Schema {
  up() {
    this.create('orders', (table) => {
      table.increments()
      table.string('order_no', 50).notNullable().unique().comment('订单编号')
      table.integer('user_id').unsigned().notNullable().comment('用户ID')
      table.integer('worker_id').unsigned().nullable().comment('师傅ID')
      table.integer('category_id').unsigned().notNullable().comment('分类ID')
      table.decimal('total_price', 10, 2).notNullable().defaultTo(0).comment('总价格')
      table.string('address', 255).notNullable().comment('回收地址')
      table.string('contact_name', 50).notNullable().comment('联系人姓名')
      table.string('contact_phone', 20).notNullable().comment('联系电话')
      table.datetime('appointment_time').notNullable().comment('预约时间')
      table.enum('status', ['pending', 'assigned', 'in_progress', 'completed', 'cancelled'])
        .notNullable().defaultTo('pending').comment('订单状态')
      table.enum('payment_status', ['unpaid', 'paid', 'refunded'])
        .notNullable().defaultTo('unpaid').comment('支付状态')
      table.string('payment_method', 20).nullable().comment('支付方式')
      table.text('remark').nullable().comment('备注')
      table.text('images').nullable().comment('图片(JSON格式)')
      table.timestamp('created_at').notNullable().defaultTo(this.fn.now())
      table.timestamp('updated_at').nullable()
      table.timestamp('deleted_at').nullable()
      
      // 外键关联
      table.foreign('user_id').references('id').inTable('users')
      table.foreign('worker_id').references('id').inTable('workers')
      table.foreign('category_id').references('id').inTable('categories')
    })
  }

  down() {
    this.drop('orders')
  }
}

module.exports = OrdersSchema
```

## Adonis.js 模型设计

### 基础模型示例
```javascript
// app/Models/User.js
'use strict'

const Model = use('Model')
const Hash = use('Hash')

class User extends Model {
  static boot() {
    super.boot()
    
    // 密码哈希钩子
    this.addHook('beforeSave', async (userInstance) => {
      if (userInstance.dirty.password) {
        userInstance.password = await Hash.make(userInstance.password)
      }
    })
  }
  
  // 软删除配置
  static get traits() {
    return ['@provider:Adonis/Lucid/SoftDeletes']
  }
  
  // 隐藏字段（API返回时）
  static get hidden() {
    return ['password', 'deleted_at']
  }
  
  // 日期字段
  static get dates() {
    return super.dates.concat(['deleted_at', 'birthday'])
  }
  
  // 字段格式转换
  static get casts() {
    return {
      is_active: 'boolean'
    }
  }
  
  // 关联定义
  orders() {
    return this.hasMany('App/Models/Order')
  }
  
  worker() {
    return this.hasOne('App/Models/Worker')
  }
  
  addresses() {
    return this.hasMany('App/Models/UserAddress')
  }
}

module.exports = User

// app/Models/Order.js
'use strict'

const Model = use('Model')

class Order extends Model {
  static boot() {
    super.boot()
    
    // 生成订单号钩子
    this.addHook('beforeCreate', async (orderInstance) => {
      orderInstance.order_no = `ORD${Date.now()}${Math.floor(Math.random() * 1000)}`
    })
  }
  
  // 软删除配置
  static get traits() {
    return ['@provider:Adonis/Lucid/SoftDeletes']
  }
  
  // JSON处理器
  static get casts() {
    return {
      images: 'json'
    }
  }
  
  // 关联定义
  user() {
    return this.belongsTo('App/Models/User')
  }
  
  worker() {
    return this.belongsTo('App/Models/Worker')
  }
  
  category() {
    return this.belongsTo('App/Models/Category')
  }
  
  items() {
    return this.hasMany('App/Models/OrderItem')
  }
  
  logs() {
    return this.hasMany('App/Models/OrderLog')
  }
  
  // 范围查询
  static scopeActive(query) {
    return query.where('status', '!=', 'cancelled')
  }
  
  static scopePending(query) {
    return query.where('status', 'pending')
  }
  
  static scopeOfUser(query, userId) {
    return query.where('user_id', userId)
  }
}

module.exports = Order
```

## 模型关联设计

### 关联关系示例
```javascript
// 一对一关系
// User.js
worker() {
  return this.hasOne('App/Models/Worker')
}

// Worker.js
user() {
  return this.belongsTo('App/Models/User')
}

// 一对多关系
// User.js
orders() {
  return this.hasMany('App/Models/Order')
}

// Order.js
user() {
  return this.belongsTo('App/Models/User')
}

// 多对多关系
// Worker.js
areas() {
  return this.belongsToMany('App/Models/Area')
    .pivotTable('worker_areas')
    .withPivot(['status', 'created_at'])
}

// Area.js
workers() {
  return this.belongsToMany('App/Models/Worker')
    .pivotTable('worker_areas')
    .withPivot(['status', 'created_at'])
}

// 通过中间表关联
// User.js
categories() {
  return this.manyThrough('App/Models/Order', 'category')
}

// 多态关系
// Comment.js
commentable() {
  return this.morphTo([
    'App/Models/Order',
    'App/Models/Worker'
  ])
}

// Order.js
comments() {
  return this.morphMany('App/Models/Comment', 'commentable')
}
```

## TypeScript 接口定义

### 模型接口示例
```typescript
// src/types/models.d.ts
export interface User {
  id: number;
  phone: string;
  name: string;
  avatar?: string;
  gender: 'male' | 'female' | 'unknown';
  birthday?: string;
  address?: string;
  status: 'active' | 'inactive' | 'banned';
  created_at: string;
  updated_at?: string;
}

export interface Order {
  id: number;
  order_no: string;
  user_id: number;
  worker_id?: number;
  category_id: number;
  total_price: number;
  address: string;
  contact_name: string;
  contact_phone: string;
  appointment_time: string;
  status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'cancelled';
  payment_status: 'unpaid' | 'paid' | 'refunded';
  payment_method?: string;
  remark?: string;
  images?: string[];
  created_at: string;
  updated_at?: string;
  
  // 关联数据
  user?: User;
  worker?: Worker;
  category?: Category;
  items?: OrderItem[];
}

export interface OrderItem {
  id: number;
  order_id: number;
  category_id: number;
  name: string;
  brand?: string;
  model?: string;
  age?: number;
  condition?: string;
  price: number;
  images?: string[];
  created_at: string;
  updated_at?: string;
  
  // 关联数据
  category?: Category;
}

export interface Worker {
  id: number;
  user_id?: number;
  name: string;
  phone: string;
  id_card: string;
  avatar?: string;
  address?: string;
  company_id?: number;
  status: 'active' | 'inactive' | 'reviewing';
  rating: number;
  order_count: number;
  balance: number;
  bank_account?: string;
  bank_name?: string;
  created_at: string;
  updated_at?: string;
  
  // 关联数据
  user?: User;
  company?: Company;
  areas?: Area[];
}
```

### API 响应接口示例
```typescript
// src/types/api.d.ts
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

export interface PaginatedResponse<T> {
  code: number;
  message: string;
  data: {
    list: T[];
    pagination: {
      total: number;
      current: number;
      pageSize: number;
      total_pages: number;
    };
  };
}

export interface OrderListParams {
  page?: number;
  limit?: number;
  status?: string;
  user_id?: number;
  worker_id?: number;
  start_time?: string;
  end_time?: string;
  keyword?: string;
}

export interface OrderCreateParams {
  user_id: number;
  category_id: number;
  address: string;
  contact_name: string;
  contact_phone: string;
  appointment_time: string;
  remark?: string;
  images?: string[];
  items: Array<{
    category_id: number;
    name: string;
    brand?: string;
    model?: string;
    age?: number;
    condition?: string;
    price?: number;
    images?: string[];
  }>;
}
```

## 代码生成工具

### 模型生成示例
```javascript
// tools/generate-model.js
const fs = require('fs');
const path = require('path');

// 模型模板
const modelTemplate = `'use strict'

const Model = use('Model')

class {{className}} extends Model {
  static boot() {
    super.boot()
  }
  
  {{softDeletes}}
  
  {{dates}}
  
  {{casts}}
  
  {{relations}}
}

module.exports = {{className}}
`;

// 生成模型
function generateModel(tableName, config = {}) {
  // 将表名转换为类名
  const className = tableName
    .split('_')
    .map(part => part.charAt(0).toUpperCase() + part.slice(1))
    .join('');
  
  // 单数形式的模型名
  const singularName = tableName.endsWith('s')
    ? tableName.slice(0, -1)
    : tableName;
  
  // 处理软删除
  const softDeletes = config.softDeletes
    ? `static get traits() {
    return ['@provider:Adonis/Lucid/SoftDeletes']
  }`
    : '';
  
  // 处理日期字段
  const datesFields = config.dates || [];
  const dates = datesFields.length
    ? `static get dates() {
    return super.dates.concat(['${datesFields.join("', '")}'])
  }`
    : '';
  
  // 处理类型转换
  const castsFields = config.casts || {};
  const casts = Object.keys(castsFields).length
    ? `static get casts() {
    return {
      ${Object.entries(castsFields).map(([field, type]) => `${field}: '${type}'`).join(',\n      ')}
    }
  }`
    : '';
  
  // 处理关联关系
  const relationFields = config.relations || [];
  const relations = relationFields
    .map(relation => {
      const { name, type, model, foreignKey, pivotTable, pivotFields } = relation;
      
      switch (type) {
        case 'hasOne':
          return `${name}() {
    return this.hasOne('App/Models/${model}', '${foreignKey || 'id'}', '${singularName}_id')
  }`;
          
        case 'hasMany':
          return `${name}() {
    return this.hasMany('App/Models/${model}', '${singularName}_id', '${foreignKey || 'id'}')
  }`;
          
        case 'belongsTo':
          return `${name}() {
    return this.belongsTo('App/Models/${model}', '${name}_id', '${foreignKey || 'id'}')
  }`;
          
        case 'belongsToMany':
          return `${name}() {
    return this.belongsToMany('App/Models/${model}')
      ${pivotTable ? `.pivotTable('${pivotTable}')` : ''}
      ${pivotFields ? `.withPivot(['${pivotFields.join("', '")}'])` : ''}
  }`;
          
        default:
          return '';
      }
    })
    .join('\n\n  ');
  
  // 填充模板
  const modelContent = modelTemplate
    .replace(/{{className}}/g, className)
    .replace('{{softDeletes}}', softDeletes)
    .replace('{{dates}}', dates)
    .replace('{{casts}}', casts)
    .replace('{{relations}}', relations);
  
  // 保存模型文件
  const modelPath = path.join(__dirname, '..', 'app', 'Models', `${className}.js`);
  fs.writeFileSync(modelPath, modelContent);
  
  console.log(`模型 ${className} 已创建: ${modelPath}`);
}

// 使用示例
generateModel('orders', {
  softDeletes: true,
  dates: ['deleted_at', 'appointment_time'],
  casts: {
    images: 'json',
    total_price: 'float'
  },
  relations: [
    {
      name: 'user',
      type: 'belongsTo',
      model: 'User'
    },
    {
      name: 'worker',
      type: 'belongsTo',
      model: 'Worker'
    },
    {
      name: 'items',
      type: 'hasMany',
      model: 'OrderItem'
    }
  ]
});
```