import Taro from '@tarojs/taro'
import { get_2nd_Category, getWaste, getSecondKind, getThirdKind, getAttributeType, getAttribute, getEstimate } from './../services/oldGoodsAPI'
import { NOldGoods } from '../config/constants'
const {
  EGetTowLevelData,
  EGetThireLevelData,
  EGetSecondKind,
  EFormatData,
  EGoodsGetOneLevel,
  EGoodsGetTwoLevel,
  EGoodsGetAttributeType,
  EGoodsGetAttribute,
  ESelectWhichOne,
  ESelectWhichAttribute,
  EWhichType,
  ESetState,
  EGetEstimate
} = NOldGoods
export default {
  state: {
    head: null,
    headId: 0,
    // 选中
    haveChosen: [],
    // 种类提交数组
    pageArr: [],
    // 获取授权
    LoginAuthorization: false,
    // 拍照图片
    takePhotoUrl: [],
    // 图片拍照还是相册选择组件
    upload_choose: false,
    // 时间选择模块
    choose_time: false,
    //预约时间
    appointmentTime: null,

    secondKindList: null,

    wasteList: null,
    twoLevel: null,
    threeLevel: null,
    currentObject: null,
    whichAppliances: null,

    //哪种订单 旧衣旧书 生活废品 家电 家具
    whichType: null,
    //属性附加值
    attributePrice: 0,
    //基础价值
    basicPrice: null,
    //估价
    estimate: null,
  }, // initial state
  effects: dispatch => ({
    async [EGetTowLevelData](payload, rootState) {
      const response = await get_2nd_Category(payload.id)
    },
    async [EGetThireLevelData](payload, rootState) {
      const response = await getWaste(payload)
    },
    async [EGetSecondKind](payload, rootState) {
      const response = await getSecondKind(payload)
      await this.RSetState({ wasteList: response, head: response })
    },
    async [EFormatData](payload, rootState) {
      await this.RSetState({
        wasteList: null,
        twoLevel: null,
        threeLevel: null,
      })
    },
    async [EGoodsGetOneLevel](payload, rootState) {
      const response = await getSecondKind(payload)
      if (response.length > 0) {
        let id = response[0].id
        const response1 = await getWaste({ id })
        await this.RSetState({ threeLevel: response1 })
      }
      await this.RSetState({ twoLevel: response })
    },
    async [EGoodsGetTwoLevel](payload, rootState) {
      const response = await getWaste(payload)
      await this.RSetState({ threeLevel: response })
    },
    async [EGoodsGetAttributeType](payload, rootState) {
      const response = await getAttributeType(payload)
      if (response.length > 0) {
        const response1 = await getAttribute({ id: response[0].id, companyID: 1 })
        await this.RSetState({ attribute: response1 })
      }
      await this.RSetState({ attributeType: response })

      Taro.navigateTo({ url: '/pages/attributeSelect/attributeSelect' })
    },
    async [EGoodsGetAttribute](payload, rootState) {
      const response = await getAttribute(payload)
      await this.RSetState({ attribute: response, companyID: 1 })
    },
    async [ESelectWhichOne](payload, rootState) {
      await this.RSetState({ whichAppliances: payload.value })

      let price = payload.value.item[0].price
      await this.RSetState({ basicPrice: price ? price : 0 })
    },
    async [EGetEstimate](payload, rootState) {
      const response = await getEstimate(payload)
      if (payload.level === 1) {
        await this.RSetState({ twoLevel: response })
        const response1 = await getEstimate({ level: 2, type: response[0].name })
        await this.RSetState({ threeLevel: response1 })
      } else if (payload.level === 2) {
        await this.RSetState({ threeLevel: response })
      } else if (payload.level === 3) {
        if (response.length > 0) {
          const response1 = await getEstimate({ level: 4, name: payload.type, type: response[0].path })
          await this.RSetState({ attribute: response1 })
        }
        await this.RSetState({ attributeType: response })
        Taro.navigateTo({ url: '/pages/attributeSelect/attributeSelect' })
      } else if (payload.level === 4) {
        await this.RSetState({ attribute: response })
      } else if (payload.level === 5) {
        await this.RSetState({ estimate: response })
        await this.RSetState({ basicPrice: response.price })
      }
    },
    async [ESelectWhichAttribute](payload, rootState) {
      await this.RSetState({ currentObject: payload.currentObject })

      let price
      let plus = 0,
        minus = 0
      payload.currentObject.forEach((value, index) => {
        if (value.plusPrice) {
          plus += value.plusPrice
        }
        if (value.minusPrice) {
          plus += value.minusPrice
        }
      })
      price = plus - minus
      await this.RSetState({ attributePrice: price })

      //Taro.navigateTo({url: '/pages/priceEvaluation/priceEvaluation'})
    },
    async [EWhichType](payload, rootState) {
      await this.RSetState({ whichType: payload })
    },
    async [ESetState](payload, rootState) {
      await this.RSetState({ ...payload })
    },

  }),
  reducers: {
    RSetState(state, payload) {
      return {
        ...state,
        ...payload,
      }
    },
  },
}
