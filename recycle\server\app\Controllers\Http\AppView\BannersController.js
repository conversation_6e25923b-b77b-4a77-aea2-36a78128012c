'use strict'

const _ = require('lodash')
const {
  Banners
} = require('../../../Models/index')
const { ERR } = require('../../../../../../constants')

//轮播图
class BannersController {
  async index({ request, response }) {
    let { status = 1 } = request.all()
    let query = Banners.query()
      .where('status', status)

    let list = await query.orderBy('sort_order', 'asc').fetch()
    response.json(list)
  }
  //详情
  async show({ request, params, response }) {
    let query = await Banners.find(params.id)
    if (!query) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(query)
  }
  //创建
  async store({ request, response }) {
    let { worker } = request
    let { title, image, button_text, type, path, sort_order, status } = request.all()
    let query = await Banners.create(request.all())
    response.json(query)
  }
  //更新
  async update({ request, params, response }) {
    let { title, image, button_text, type, path, sort_order, status } = request.all()
    let query = await Banners.find(params.id)
    query.merge(request.all())
    await query.save()
    response.json(query)
  }
  //删除
  async destroy({ request, params, response }) {
    let query = await Banners.find(params.id)
    await query.delete()
    response.json({ message: '删除成功' })
  }
}

module.exports = BannersController
