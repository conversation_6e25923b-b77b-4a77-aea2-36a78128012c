import Taro from '@tarojs/taro'
import { View, Text, Image, Button } from '@tarojs/components'
import { useEffect, useState } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import './index.less'

const menu = [
  {
    name: '首页',
    url: '/pages/home/<USER>',
    imgUrl: 'https://oss.evergreenrecycle.cn/donggua/client/images/footer1.png',
  },
  {
    name: '发现',
    url: '/pages/discover/index',
    imgUrl: 'https://oss.evergreenrecycle.cn/donggua/client/images/footer2.png',
  },
  {
    name: '个人中心',
    url: '/pages/my/index',
    imgUrl: 'https://oss.evergreenrecycle.cn/donggua/client/images/footer3.png',
  },
]

const BaseLayout = props => {
  const { menuIndex, title, navigationBar, footer } = props
  const dispatch = useDispatch()
  const [systemInfoX, setSystemInfoX] = useState(false)
  const [statusBarHeight, setStatusBarHeight] = useState('')
  const [titleBarHeight, setTitleBarHeight] = useState('')
  useEffect(() => {
    Taro.getWindowInfo({
      success: res => {
        setStatusBarHeight(res.statusBarHeight + 'px') //状态栏高度
        const barHeight = 90
        setTitleBarHeight(barHeight)
      },
    })
  }, [])
  return (
    <View className="BaseLayout">
      {navigationBar && (
        <View className="navigationBar">
          <View className="statusBarHeight" style={{ height: statusBarHeight }}></View>
          <View className="titleBar" style={{ height: titleBarHeight }}>
            {title}
          </View>
        </View>
      )}

      <View className="container" key={90}>{props.children}</View>
      {footer && (
        <View className={`footer img${menuIndex + 1}`}>
          {menu.map((v, i) => {
            return (
              <View
                className="button"
                onClick={() => {
                  Taro.reLaunch({ url: v.url })
                }}
              ></View>
            )
          })}
        </View>
      )}
    </View>
  )
}
export default BaseLayout
