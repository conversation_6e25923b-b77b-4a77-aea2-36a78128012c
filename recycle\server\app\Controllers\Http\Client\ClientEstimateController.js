'use strict'

const _ = require('lodash')
const moment = require('moment')

const { ClientEstimate } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { CryptUtil } = require('../../../Util')
const Env = use('Env')
// 客户估价 type,subType,coldType,price,years,functions,outside,spu
class ClientEstimateController {
  async getEstimate({ request, response }) {
    let { level, type, subType, coldType, years, functions, outside, spu, name } = request.all()
    let query = ClientEstimate.query()
    switch (parseInt(level)) {
      case 1:
        // 获取所有去重type字段
        let types = await query.distinct('type').fetch()
        types.rows.map((item, index) => {
          item.name = item.type
          item.id = index + 1
          item.enName = item.type
        })
        return response.json(types)

      case 2:
        // 根据type获取subType字段
        if (!type) {
          throw ERR.INVALID_PARAMS
        }
        let subTypes = await query.where('type', type).with('imgs').distinct('subType').fetch()
        subTypes = subTypes.toJSON()
        subTypes.map((item, index) => {
          item.img = item.imgs && item.imgs.img
          item.name = item.subType
          item.id = index + 1
          item.enName = item.subType
        })
        return response.json(subTypes)

      case 3:
        if (['窗机', '柜机', '挂机'].includes(type)) {
          // 返回固定数组
          return response.json([
            {
              id: 1,
              name: "规格",
              path: "spu"
            },
            {
              id: 2,
              name: "使用年限",
              path: "years"
            },
            {
              id: 3,
              name: "功能",
              path: "functions"
            },
            {
              id: 4,
              name: "外观",
              path: "outside"
            },
            {
              id: 5,
              name: "制冷类型",
              path: "coldType"
            },
          ])
        } else {
          return response.json([
            {
              id: 1,
              name: "规格",
              path: "spu"
            },
            {
              id: 2,
              name: "使用年限",
              path: "years"
            },
            {
              id: 3,
              name: "功能",
              path: "functions"
            },
            {
              id: 4,
              name: "外观",
              path: "outside"
            }
          ])
        }
      case 4:
        // 根据条件返回price
        if (!type || !name) {
          throw ERR.INVALID_PARAMS
        }
        // 获取所有去重type字段
        let rowsVo = await query.select('subType', 'coldType', 'functions', 'years', 'outside', 'spu', 'id').where('subType', name).groupBy(type).fetch()
        rowsVo = rowsVo.toJSON()
        rowsVo.map((item, index) => {
          item.name = item[type]
          item.enName = item[type]
        })
        return response.json(rowsVo)
      case 5:
        query.where('subType', subType)
        if (years) {
          query.where('years', years)
        }
        if (functions) {
          query.where('functions', functions)
        }
        if (spu) {
          query.where('spu', spu)
        }
        if (outside) {
          query.where('outside', outside)
        }
        if (coldType && coldType !== 'null' && coldType !== 'undefined') {
          query.where('coldType', coldType)
        }
        let result = await query.first()
        return response.json(result)
      default:
        throw ERR.INVALID_PARAMS
    }
  }
  //估价列表
  async index({ request, response }) {
    let { current = 1, pageSize = 10, sort = 'desc', type, subType, coldType, price, years, functions, outside, spu } = request.all()
    let query = ClientEstimate.query()
    if (type) {
      query.where('type', 'like', `%${type}%`)
    }
    if (subType) {
      query.where('subType', 'like', `%${subType}%`)
    }
    if (coldType) {
      query.where('coldType', 'like', `%${coldType}%`)
    }
    if (price) {
      switch (price) {
        case 'asc':
          query.orderBy('price', 'asc')
          break
        case 'desc':
          query.orderBy('price', 'desc')
          break
        default:
          query.orderBy('price', 'desc')
          break
      }
    }
    if (years) {
      query.where('years', 'like', `%${years}%`)
    }
    if (functions) {
      query.where('functions', 'like', `%${functions}%`)
    }
    if (outside) {
      query.where('outside', 'like', `%${outside}%`)
    }
    if (spu) {
      query.where('spu', 'like', `%${spu}%`)
    }
    let vo = await query.orderBy('id', sort).paginate(current, pageSize)
    response.json(vo)
  }
  //创建估价
  async store({ request, response }) {
    let { type, subType, coldType, price, years, functions, outside, spu } = request.all()
    let vo = await ClientEstimate.create({ type, subType, coldType, price, years, functions, outside, spu })
    response.json(vo)
  }
  //估价信息修改
  async update({ request, params, response }) {
    let { } = request.all()
    let vo = await ClientEstimate.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
  }


}

module.exports = ClientEstimateController
