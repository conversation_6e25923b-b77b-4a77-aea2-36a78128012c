'use strict'

const _ = require('lodash')
const moment = require('moment')

const { UserAddress } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')

//用户地址
class UserAddressController {
  async index({ request, response }) {
    let { page = 1, perPage = 10 } = request.all()
    let query = UserAddress.query()
    let vo = await query.paginate(page, perPage)
    response.json(vo)
  }
  async show({ request, params, response }) {
    let vo = await UserAddress.query()
      .where('id', params.id)
      .first()
    if (!vo) {
      throw ERR.RESTFUL_GET_ID
    }
    response.json(vo)
  }
  //地址创建
  async store({ request, response }) {
    let {
      userID,
      realname,
      gender,
      mobile,
      province,
      provinceCode,
      city,
      cityCode,
      district,
      districtCode,
      subDistrct,
      subDistrctCode,
      pickerSelect,
      address,
      longitude,
      latitude,
      isDefault
    } = request.all()
    if (!userID || !realname || !gender || !mobile || !province || !city || !district || !subDistrct || !address) {
      throw ERR.INVALID_PARAMS
    }
    let vo = await UserAddress.create({
      userID,
      realname,
      gender,
      mobile,
      province,
      provinceCode,
      city,
      cityCode,
      district,
      districtCode,
      subDistrct,
      subDistrctCode,
      pickerSelect,
      address,
      longitude,
      latitude,
      isDefault
    })
    response.json(vo)
  }
  //地址更新
  async update({ request, params, response }) {
    let vo = await UserAddress.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
  }
  //地址删除
  async destroy({ request, params, response }) {
    let vo = await UserAddress.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_DELETE_ID
    }
    await vo.delete()
    response.json(vo)
  }
}

module.exports = UserAddressController
