import Taro from '@tarojs/taro'
import { View, Image, Button, Text, Checkbox } from '@tarojs/components'
import { AtModal, AtTextarea, AtMessage, AtSearchBar } from 'taro-ui'
import './index.less'
import T from '../../config/T'
import E from '../../config/E'

import { NOldGoods, NOrder, NUserAddress } from '../../config/constants'
import { useEffect, useState } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import dayjs from 'dayjs'
import ChooseTime from './../../components/chooseTime/chooseTime'

const Index = () => {
  const dispatch = useDispatch()
  const { attributePrice, basicPrice, whichType, choose_time, estimate } = useSelector(state => state.NOldGoods)
  const { time, isGetTime, selectAddress, addressValue } = useSelector(state => state.NOrder)
  const { isAddItem } = useSelector(state => state.NSystem)
  const { codeSource } = useSelector(state => state.NUser)
  const { addressList } = useSelector(state => state.NUserAddress)
  const type = Number(whichType.id)
  const [remark, setRemark] = useState('')
  const [count, setCount] = useState(0)
  const [whichAddressID, setWhichAddressID] = useState(0)
  const [whichAddress, setWhichAddress] = useState(0)
  const [get, setGet] = useState(false)
  const [canSubmit, setCanSubmit] = useState(false)

  function goToSelectAddress() {
    Taro.navigateTo({ url: `/pages/administration/administration?select=1` })
  }
  function getRemark(e) {
    let value = e
    if (value.length > 50) {
    } else {
      setRemark(value)
      setCount(value.length)
    }
  }

  async function message() {
    await Taro.requestSubscribeMessage({
      tmplIds: ['KhwysVMAAUVoYCIWLK29Yn2EopVXu3missGIQMPfaJ0'],
      success: res => {
        console.log('已授权接收订阅消息index', res)
        Taro.getSetting({
          success(res) {
            // console.log('res', res.authSetting)
          },
        })
      },
      fail: res => {
        // console.log('失败')
      },
    })
    await submitAppointment()
  }
  function computeType(type) {
    let value
    if (type === 1) {
      value = E.WasteType.ClothingAndBook
    } else if (type === 2) {
      value = E.WasteType.LivingWaste
    } else if (type === 3) {
      value = E.WasteType.Appliances
    } else if (type === 4) {
      value = E.WasteType.Furniture
    }
    return value
  }
  async function submitAppointment() {
    let wasteType = await computeType(type)
    let theTime = time.day + ' ' + `${time.hour === '上午' ? '09:00' : '13:00'}`
    let options = {
      workTime: theTime,
      userAddressID: whichAddressID,
      remark,
      waste_1st_ID: type,
      wasteType,
      userName: whichAddress.realname,
      userMobile: whichAddress.mobile,
      wasteID: estimate.id,
      codeSource: codeSource || '',
    }
    if (type === 3 || type === 4) {
      options = { ...options, estimatedMoney: (basicPrice + attributePrice) * 100 }
    }
    if (type === 5) {
      options = { ...options }
    }
    dispatch.NOrder[NOrder.ECreateOrder]({ ...options })
  }
  // function initDay() {
  //   let dayList = []
  //   let znList = []
  //   for (let i = 0; i < 7; i++) {
  //     dayList.push(
  //       dayjs()
  //         .add(i + 1, 'days')
  //         .format('YYYY-MM-DD')
  //     )
  //   }
  //   dayList.forEach((day, index) => {
  //     znList.push(dayjs(day).format('d'))
  //   })
  //   setDayList(dayList)
  // }

  useEffect(() => {
    let user = Taro.getStorageSync('userInfo') && JSON.parse(Taro.getStorageSync('userInfo'))
    setTimeout(() => {
      dispatch.NUserAddress[NUserAddress.EGetUserAddressList]({
        id: user.id,
      })
    })
    // initDay()
    Taro.setNavigationBarTitle({ title: '预约确认' })
  }, [])
  useEffect(() => {
    if (selectAddress) {
      setGet(true)
      setWhichAddress(addressValue)
      setWhichAddressID(addressValue.id)
    } else {
      let list = addressList
      list.forEach((address, index) => {
        if (address.isDefault === 1) {
          setWhichAddressID(address.id)
          setWhichAddress(address)
          return
        }
      })
    }
    if (!selectAddress) {
      setGet(true)
      setWhichAddress(addressValue)
      if (addressValue) {
        setWhichAddressID(addressValue.id)
      }
    }
    if (time && whichAddressID) {
      setCanSubmit(true)
    } else {
      setCanSubmit(false)
    }
  }, [selectAddress, isGetTime, whichAddress])
  return (
    <View className="order">
      <View className="address_wrapper">
        <Image src={'https://oss.evergreenrecycle.cn/donggua/client/images/address_icon.png'} />
        <View className="address_text_wrapper">
          {whichAddress ? (
            <View>
              <View className="top_info">
                <Text>{whichAddress.realname}</Text>
                <Text>{whichAddress.mobile}</Text>
              </View>
              <View className="bottom_info">
                {whichAddress.province + whichAddress.city + whichAddress.district + whichAddress.subDistrct + whichAddress.address}
              </View>
            </View>
          ) : (
            <Text
              className="add_information_box"
              onClick={() => {
                goToSelectAddress()
              }}
            >
              {T.order.addInfoTxt}
            </Text>
          )}
        </View>
        <View className="devide"></View>
        <View
          className="address_button"
          style={{
            width: '136rpx',
          }}
          onClick={() => {
            goToSelectAddress()
          }}
        >
          {T.order.addressBook}
        </View>
      </View>

      <View
        className="add_"
        onClick={() => {
          dispatch.NOldGoods[NOldGoods.ESetState]({
            choose_time: !choose_time,
          })
        }}
      >
        <Text className="add_text">{T.order.selectTimeVisit}</Text>
        <View className="add_choice">
          <Text className="add_des">{time ? `${time.day} ${time.hour}` : T.order.selectTime}</Text>
          <Image src={require('./../../assets/icon/rili.png')} className="add_Icon" />
        </View>
      </View>

      <View className="add_bottom">
        <Text className="add_text">{T.order.remarks}：</Text>
        <AtTextarea
          className="add_des1"
          count={false}
          value={remark}
          onChange={getRemark}
          maxLength={50}
          placeholder={T.order.remarksDesc}
          placeholderStyle="font-size: 12px;color: #7C8696;"
        />
        <View className="count_remind">
          {count}/50{T.order.words}
        </View>
      </View>
      <View
        className={`
         order_bottom  ${canSubmit ? "canSubmit" : ""}`}
        onClick={() => {
          if (canSubmit) {
            message()
          }
        }}
      >
        <View className="text_wrapper">
          <Text
            style={{
              fontSize: '30rpx',
            }}
          >
            {T.order.submit}
          </Text>
        </View>
        {isAddItem ? <View className="add_item"></View> : null}
      </View>
      {choose_time ? <ChooseTime /> : null}
      <AtMessage />
    </View>
  )
}
export default Index
