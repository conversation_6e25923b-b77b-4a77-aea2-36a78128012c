'use strict'

const { test,before,beforeEach,trait } = use('Test/Suite')('Admin User')
// const { } = use('App/')
// trait('Test/ApiAdmin')

const Database = use('Database')
let Authorization = null

before(async (client)=>{
  console.log('test start')
  // await login(client)

})

beforeEach(async ()=>{
  console.log('beforeEach')
})

async function login(client){
  console.log('client',client)
  return
  let res = await client.post('/admin/v1/user/login').send({username: '小张',password: '21'}).end()
  console.log('res',res)
  Authorization = `Bearer ${res.body.token}`
}

test('make sure 2 + 2 is 4', async ({ assert }) => {
  assert.equal(2 + 2, 4)
})
