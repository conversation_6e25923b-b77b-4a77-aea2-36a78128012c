'use strict'

const _ = require('lodash')
const moment = require('moment')

const { Order, ReqLog, Tracklog } = require('../../../Models')
const { callBack } = require('../../../Util/DebugUtil')
const { eventCode } = require('../../../../../constants/E')
const { SpecCODElIST } = require('../../../../../../constants/E')
const { ERR } = require('../../../../../../constants')

class TrackingController {
    async check({ request, response }) {
        let { orderID, trackingCode } = request.all()
        if (!orderID || !trackingCode) {
            response.json({
                code: 400,
                msg: '参数错误'
            })
            return
        }
        let order = await Order.find(orderID)
        let orderVo = order.toJSON()
        let checkVO = {
            trackingCode,
            mailNo: orderVo.mailNo,
            logisticProviderID: orderVo.cpCode,
            txLogisticID: orderVo.orderNo,
            occurTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        }
        // console.log(checkVO);
        let res = await callBack('TMS_TRACKING_CODE_QUERY', orderVo.cpCode, checkVO, "aone_diccenter-sdc")
        let trackquery = await Tracklog.query().where('trackCode', trackingCode)
            .where('status', 2).first()
        if (trackquery) {
            throw ERR.CODE_DUPLICATION
        } else {
            await Tracklog.create({
                orderNo: orderVo.orderNo,
                trackCode: trackingCode,
                status: 0,//0:查询 1:失败 2:完成
            })
        }
        response.json(checkVO)
    }
    async store({ request, response }) {
        let { orderID, trackingCode, itemType = "空调",
            brand = "其他",
            sortTime = moment().format('YYYY-MM-DD HH:mm:ss'),
            itemTypeCode = "AIRCDT", recycleFlow = "DISASSEMBLE",
            disassembleTime = moment().format('YYYY-MM-DD HH:mm:ss'),
            itemSpec = "其他规格-空调", itemSpecCode = "AIRCDT_OTHER",
            productionDate = moment().subtract(10, 'years').format('YYYY-MM-DD'),
            pickUpTime = moment().format('YYYY-MM-DD HH:mm:ss'),
            recycleTime = moment().format('YYYY-MM-DD HH:mm:ss') } = request.all()
        await ReqLog.create({ req: JSON.stringify(request.all()), source: '溯源码上传' })
        if (!orderID || !trackingCode) {
            return ({
                code: 200,
                msg: '参数错误'
            })
        }
        if (itemType === "电热水器" || itemType === "燃气热水器" || itemType === "空气热泵热水器") {
            return ({
                code: 200,
                msg: '参数错误'
            })
        }
        if (itemSpec) {
            let specItem = _.filter(SpecCODElIST, { Ch: itemSpec })[0]
            itemSpecCode = specItem.En
            itemTypeCode = specItem.typeCode
            itemType = specItem.type
        }
        let order = await Order.find(orderID)
        if (!order) {
            return ({
                code: 200,
                msg: '订单不存在'
            })
        }
        order.isTrack = 1
        await order.save()
        let orderVo = order.toJSON()
        let tvo = {
            "mailNo": orderVo.mailNo,
            "eventCode": eventCode.TRACKING_INFO_CALLBACK.name,
            "logisticProviderID": orderVo.cpCode,
            "txLogisticID": orderVo.orderNo,
            "occurTime": moment().format('YYYY-MM-DD HH:mm:ss'),
            "extendFields": [
                {
                    "value": trackingCode,
                    "key": "trackingCode",
                    "desc": "溯源码"
                },
                {
                    "value": brand,
                    "key": "brand",
                    "desc": "旧机品牌"
                },
                {
                    "value": itemType,
                    "key": "itemType",
                    "desc": "旧机类型"
                },
                {
                    "value": itemTypeCode,
                    "key": "itemTypeCode",
                    "desc": "旧机类型编码"
                },
                {
                    "value": itemSpec,
                    "key": "itemSpec",
                    "desc": "旧机规格"
                },
                {
                    "value": itemSpecCode,
                    "key": "itemSpecCode",
                    "desc": "旧机规格编码"
                },
                {
                    "value": productionDate,
                    "key": "productionDate",
                    "desc": "旧机出⼚⽇期"
                },
                {
                    "value": pickUpTime,
                    "key": "pickUpTime",
                    "desc": "上⻔取件时间"
                },
                {
                    "value": recycleTime,
                    "key": "recycleTime",
                    "desc": "回收完成时间 "
                },
                {
                    "value": sortTime,
                    "key": "sortTime",
                    "desc": "分拣时间"
                },
                {
                    "value": disassembleTime,
                    "key": "disassembleTime",
                    "desc": "送拆时间"
                },
                {
                    "value": recycleFlow,
                    "key": "recycleFlow",
                    "desc": "回收流向"
                },
            ],
        }
        let res = await callBack('TMS_EVENT_CALLBACK', orderVo.cpCode, tvo)
        await Tracklog.query().where('trackCode', trackingCode).update({ status: 2 })
        response.json(res)
    }
    async update({ request, params, response }) {
    }
}

module.exports = TrackingController
