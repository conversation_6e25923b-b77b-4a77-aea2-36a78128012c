'use strict'

const Model = use('Model')

class YCOrderCancel extends Model {
  static get primaryKey() {
    return 'id'
  }
  static get table() {
    return 'yc_order_cancel'
  }
  static get createdAtColumn() {
    return 'createdAt'
  }
  static get updatedAtColumn() {
    return null
  }
  worker() {
    return this.belongsTo('App/Models/Worker', 'workerID', 'id').select('id', 'workerName', 'mobile', 'companyID')
  }
  order() {
    return this.belongsTo('App/Models/YCOrder', 'orderID', 'id').select('id', 'orderNO', 'from', 'userName')
  }
}

module.exports = YCOrderCancel
