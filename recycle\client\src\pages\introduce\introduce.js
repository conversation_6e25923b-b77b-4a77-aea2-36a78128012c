import Taro, {checkIsSupportFacialRecognition } from '@tarojs/taro'
import React, { Component } from 'react'
import {connect} from 'react-redux'
import { View, Image, Button, Text, Checkbox } from '@tarojs/components'
import { AtModal } from 'taro-ui'
import './introduce.less'
import E from '../../config/E'

class Introduce extends Component {
  constructor() {
    super(...arguments)
    this.state = {}
  }

  config = {}

  //-----------------------生命周期-------------------------//
  componentWillMount() {
    Taro.setNavigationBarTitle({ title: '关于冬瓜回收' })
  }

  componentDidMount() {}

  componentWillUnmount() {}

  componentDidShow() {}

  componentDidHide() {}

  //-----------------------事件-------------------------//
  seeDetail(index) {
    this.setState({
      currentIndex: this.state.currentIndex === index ? null : index
    })
  }

  //-----------------------渲染-------------------------//
  render() {
    return (
      <View className="successfulOrder">
        <Image src={require('./../../assets/icon/recycleLogo.jpg')} className="logo" />
        <View className="content_wrapper">
          {E.Introduce.map((value, index) => (
            <View className={index === 0 ? 'title' : 'content'} key={value + index}>
              {value}
            </View>
          ))}
        </View>
      </View>
    )
  }
}

export default connect(({}) => ({}))(Introduce)
