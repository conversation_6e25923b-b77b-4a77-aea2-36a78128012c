/**
 * Created by fengbo on 25/04/2017.
 */
'use strict'

const Env = use('Env')
const jwt = require('jsonwebtoken')
const crypto = require('crypto')

const APP_KEY = Env.get('APP_KEY')

const CryptUtil = {
  jwtDecode(token) {
    if (token) {
      token = token.substr(0, 7) == 'Bearer ' ? token.substr(7) : token
      let secret = APP_KEY
      try {
        let payload = jwt.verify(token, secret)
        delete payload.exp
        delete payload.iat
        return payload
      } catch (err) {
        console.error('[JWT failed]', err)
        return null
      }
    } else {
      return null
    }
  },
  jwtEncodeInExpire(payload, second) {
    return jwt.sign(payload, APP_KEY, { expiresIn: second })
  },
  jwtEncode(payload, noExpire = true) {
    if (noExpire) {
      return jwt.sign(payload, APP_KEY)
    } else {
      return jwt.sign(payload, APP_KEY, { expiresIn: '7d' })
    }
  },

  md5(p_value) {
    console.log('ready to md5:', p_value)
    return crypto.createHash('md5').update(p_value, 'utf8').digest('hex')
  },
  base64(value) {
    console.log(value)
    return Buffer.from(value).toString('base64')
  },
  wechatSign(sendObj, key) {
    let arr = []
    for (let key in sendObj) {
      arr.push(key + '=' + sendObj[key])
    }
    arr.sort((a, b) => (a > b ? 1 : -1))
    let str = `${arr.join('&')}&key=${key}`
    let ret = this.md5(str).toUpperCase()
    return ret
  },
  qimenSign(sendObj, body, secret = '') {
    let arr = []
    for (let key in sendObj) {
      arr.push(key + sendObj[key])
    }
    arr.sort((a, b) => (a > b ? 1 : -1))
    let str = `${secret}${arr.join('')}${body}${secret}`
    let ret = this.md5(str).toUpperCase()
    console.log(str, ret)
    return ret
  },
  OTMSSign(sendObj, secret = '') {
    let arr = []
    for (let key in sendObj) {
      arr.push(key + sendObj[key])
    }
    arr.sort((a, b) => (a > b ? 1 : -1))
    let str = `${secret}${arr.join('')}${secret}`
    let ret = this.md5(str).toUpperCase()
    console.log(str, ret)
    return ret
  },

  decryptData256(encryptedData, key) {
    // base64 decode
    encryptedData = new Buffer(encryptedData, 'base64')

    var iv = ''
    let decipher = crypto.createDecipheriv('aes-256-ecb', CryptUtil.md5(key), iv)
    decipher.setAutoPadding(true)
    let decoded = decipher.update(encryptedData, 'base64', 'utf8')
    decoded += decipher.final('utf8')
    return decoded
  },
  encryptData256(data, key, iv = '') {
    // base64 decode
    key = Buffer.from(key, 'binary')
    let clearEncoding = 'utf8'
    let cipherEncoding = 'base64'
    let cipherChunks = []
    let cipher = crypto.createCipheriv('aes-256-ecb', key, iv)
    cipher.setAutoPadding(true)
    cipherChunks.push(cipher.update(data, clearEncoding, cipherEncoding))
    cipherChunks.push(cipher.final(cipherEncoding))
    return cipherChunks.join('')
  },
  meituanSign(sendObj, secret = '') {
    let arr = []
    for (let key in sendObj) {
      arr.push(key + sendObj[key])
    }
    arr.sort((a, b) => (a > b ? 1 : -1))
    let str = `${secret}${arr.join('')}${secret}`
    let ret = this.md5(str).toLowerCase()
    console.log(str, ret)
    return ret
  },
  calculateSign(userName, password, content, timestamp) {
    const md5Password = crypto.createHash('md5').update(password).digest('hex');
    const combinedString = `${userName}${content}${timestamp}${md5Password}`;
    return crypto.createHash('md5').update(combinedString).digest('hex');
  },

}

module.exports = CryptUtil
