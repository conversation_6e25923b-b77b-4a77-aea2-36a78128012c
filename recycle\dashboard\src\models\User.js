import { bindJWTToken, requestGet, requestPost } from '../utils/dva17'
import { EPost, NUser, RSetState,EGetCharts, EGetData, EGetLineCharts, } from '../config/Constants'

export default {
  namespace: NUser,
  state: {
    userInfo: null,
    dashData: {},
    chartData: {},
    lineData: [],
  },
  reducers: {
    [RSetState](state, payload) {
      return { ...state, ...payload }
    },
  },
  effects: {
    async [EPost]({ payload }, { reducer }) {
      const { adminUser, token } = await requestPost('user/login', payload)
      localStorage.setItem('token', token)
      localStorage.setItem('userInfo', JSON.stringify(adminUser))
      bindJWTToken(token)
      reducer(RSetState, { userInfo: adminUser })
    },
    
    async [EGetData]({ payload }, { reducer }) {
      let result = await requestGet('getData', payload)
      reducer(RSetState, { dashData: result })
    },
    async [EGetCharts]({ payload }, { reducer }) {
      let result = await requestGet('getCharts', payload)
      reducer(RSetState, { chartData: result, lastSearch: payload })
    },
    async [EGetLineCharts]({ payload }, { reducer }) {
      let result = await requestGet('getLineCharts', payload)
      reducer(RSetState, { lineData: result, lastSearch: payload })
    },
  },
}
