import { requestGet, requestPut, requestPost, requestDelete } from "../utils/request";
;
import { request } from "@tarojs/taro";

export async function getUserAddressList(payload) {
    return requestGet(`user/${payload.id}/userAddress`, payload);
}
export async function saveAddress(payload) {
    return requestPost('userAddress',payload)
}
export async function editAddress(payload){
    return requestPut(`userAddress/${payload.id}`,payload)
}
export async function deleteAddress(payload){
    return requestDelete(`userAddress/${payload.id}`)
}
export async function searchAddress(payload){
    return requestPost(`user/${payload.id}/searchUserAddress`,{realname: payload.realname,mobile: payload.mobile})
}
export async function setDefault(payload){
    return requestPost(`user/${payload.id}/setDefaultUserAddress`,{userAddressID: payload.addressID})
}
export async function getJsonAddress(payload){
    return requestGet(`address`,payload)
}
