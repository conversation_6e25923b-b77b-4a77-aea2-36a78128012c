---
description: 家电回收业务逻辑规范
---

# 家电回收业务逻辑规范

## 业务流程概述
家电回收平台的完整业务流程：用户下单 → 评估定价 → 预约上门 → 师傅回收 → 处理加工 → 销售变现 → 售后服务

## 核心业务实体

### 用户端业务逻辑
```javascript
// 用户下单流程
const createOrderFlow = async (orderData) => {
  // 1. 验证用户信息
  const user = await validateUser(orderData.user_id);
  
  // 2. 家电分类验证
  const category = await validateCategory(orderData.category_id);
  
  // 3. 地址服务范围验证
  const isInServiceArea = await checkServiceArea(orderData.address);
  
  // 4. 预约时间验证
  const isValidTime = await validateAppointmentTime(orderData.appointment_time);
  
  // 5. 创建订单
  const order = await Order.create({
    ...orderData,
    order_no: generateOrderNo(),
    status: 'pending',
    estimated_price: await calculateEstimatedPrice(orderData)
  });
  
  // 6. 发送通知
  await sendOrderNotification(order);
  
  return order;
};
```

### 师傅端业务逻辑
```javascript
// 师傅接单流程
const acceptOrderFlow = async (orderId, workerId) => {
  // 1. 验证师傅状态
  const worker = await validateWorkerStatus(workerId);
  
  // 2. 检查服务区域
  const order = await Order.findOrFail(orderId);
  const canService = await checkWorkerServiceArea(worker, order.address);
  
  // 3. 更新订单状态
  await order.merge({
    worker_id: workerId,
    status: 'assigned',
    assigned_at: new Date()
  }).save();
  
  // 4. 通知用户
  await notifyUserOrderAssigned(order);
  
  return order;
};

// 上门回收流程
const doorServiceFlow = async (orderId, serviceData) => {
  const order = await Order.findOrFail(orderId);
  
  // 1. 现场评估
  const actualItems = serviceData.actual_items;
  const actualPrice = await calculateActualPrice(actualItems);
  
  // 2. 用户确认
  if (serviceData.user_confirmed) {
    await order.merge({
      actual_items: JSON.stringify(actualItems),
      actual_price: actualPrice,
      status: 'in_progress',
      service_started_at: new Date()
    }).save();
    
    // 3. 完成回收
    await completeRecycleService(order, serviceData);
  } else {
    // 用户拒绝，订单取消
    await cancelOrder(orderId, '用户拒绝服务');
  }
};
```

## 价格计算规则

### 家电评估算法
```javascript
// 家电价格评估
const calculateEstimatedPrice = async (orderData) => {
  const { category_id, brand, model, age, condition } = orderData;
  
  // 1. 获取基础价格
  const basePrice = await CategoryPrice.getBasePrice(category_id);
  
  // 2. 品牌系数
  const brandMultiplier = getBrandMultiplier(brand);
  
  // 3. 新旧程度折扣
  const ageDiscount = calculateAgeDiscount(age);
  
  // 4. 成色评分
  const conditionScore = getConditionScore(condition);
  
  // 5. 市场价格波动
  const marketFactor = await getMarketFactor(category_id);
  
  const estimatedPrice = basePrice * brandMultiplier * ageDiscount * conditionScore * marketFactor;
  
  return Math.max(estimatedPrice, 0); // 确保价格不为负数
};

// 品牌价值系数
const getBrandMultiplier = (brand) => {
  const brandValues = {
    '海尔': 1.2,
    '美的': 1.15,
    '格力': 1.1,
    '小米': 1.05,
    '其他': 0.9
  };
  return brandValues[brand] || brandValues['其他'];
};

// 使用年限折扣
const calculateAgeDiscount = (age) => {
  if (age <= 1) return 0.9;
  if (age <= 3) return 0.8;
  if (age <= 5) return 0.6;
  if (age <= 8) return 0.4;
  return 0.2;
};
```

## 订单状态管理

### 状态流转规则
```javascript
const ORDER_STATUS_FLOW = {
  'pending': ['assigned', 'cancelled'],      // 待分配 → 已分配/已取消
  'assigned': ['in_progress', 'cancelled'],  // 已分配 → 进行中/已取消
  'in_progress': ['completed', 'cancelled'], // 进行中 → 已完成/已取消
  'completed': [],                           // 已完成（终态）
  'cancelled': []                            // 已取消（终态）
};

// 状态验证
const validateStatusTransition = (currentStatus, newStatus) => {
  const allowedStatuses = ORDER_STATUS_FLOW[currentStatus] || [];
  return allowedStatuses.includes(newStatus);
};

// 状态更新
const updateOrderStatus = async (orderId, newStatus, operatorId, reason = '') => {
  const order = await Order.findOrFail(orderId);
  
  if (!validateStatusTransition(order.status, newStatus)) {
    throw new Error(`不能从 ${order.status} 状态变更为 ${newStatus}`);
  }
  
  // 记录状态变更历史
  await OrderStatusLog.create({
    order_id: orderId,
    from_status: order.status,
    to_status: newStatus,
    operator_id: operatorId,
    reason: reason,
    created_at: new Date()
  });
  
  await order.merge({ status: newStatus }).save();
  
  // 触发相应的业务逻辑
  await handleStatusChange(order, newStatus);
};
```

## 服务区域管理

### 地址匹配算法
```javascript
// 检查服务区域
const checkServiceArea = async (address) => {
  // 1. 地址解析
  const addressInfo = await parseAddress(address);
  
  // 2. 获取服务区域列表
  const serviceAreas = await ServiceArea.query()
    .where('status', 'active')
    .fetch();
  
  // 3. 区域匹配
  for (let area of serviceAreas.rows) {
    if (isAddressInArea(addressInfo, area)) {
      return {
        inService: true,
        areaId: area.id,
        serviceFee: area.service_fee
      };
    }
  }
  
  return { inService: false };
};

// 师傅服务区域匹配
const checkWorkerServiceArea = async (worker, address) => {
  const workerAreas = await WorkerArea.query()
    .where('worker_id', worker.id)
    .where('status', 'active')
    .fetch();
  
  const addressArea = await checkServiceArea(address);
  
  return workerAreas.rows.some(area => 
    area.area_id === addressArea.areaId
  );
};
```

## 通知系统

### 消息推送逻辑
```javascript
// 订单状态变化通知
const sendOrderStatusNotification = async (order, newStatus) => {
  const notifications = {
    'assigned': {
      user: '您的订单已分配师傅，师傅将在预约时间上门服务',
      worker: '您有新的回收订单，请及时联系用户确认服务时间'
    },
    'in_progress': {
      user: '师傅已开始为您服务，请保持电话畅通',
      worker: '服务已开始，请按照标准流程完成回收服务'
    },
    'completed': {
      user: '回收服务已完成，感谢您对环保事业的支持！',
      worker: '订单已完成，请及时提交服务报告'
    }
  };
  
  const messages = notifications[newStatus];
  if (!messages) return;
  
  // 发送用户通知
  await sendWeChatMessage(order.user_id, messages.user);
  
  // 发送师傅通知
  if (order.worker_id) {
    await sendWorkerNotification(order.worker_id, messages.worker);
  }
};
```

## 数据验证规则

### 业务数据验证
```javascript
// 订单数据验证
const validateOrderData = (orderData) => {
  const rules = {
    // 基础信息验证
    user_id: 'required|integer|exists:users,id',
    category_id: 'required|integer|exists:categories,id',
    
    // 联系信息验证
    contact_name: 'required|string|max:50|regex:/^[\u4e00-\u9fa5a-zA-Z\s]+$/',
    contact_phone: 'required|regex:/^1[3-9]\d{9}$/',
    
    // 地址信息验证
    address: 'required|string|max:200',
    detailed_address: 'string|max:100',
    
    // 预约时间验证
    appointment_time: 'required|date|after:now|business_hours',
    
    // 家电信息验证
    items: 'required|array|min:1',
    'items.*.name': 'required|string|max:50',
    'items.*.brand': 'string|max:30',
    'items.*.model': 'string|max:50',
    'items.*.age': 'integer|min:0|max:50'
  };
  
  return validator.validate(orderData, rules);
};

// 自定义验证规则
validator.extend('business_hours', (value) => {
  const hour = new Date(value).getHours();
  return hour >= 8 && hour <= 18; // 工作时间 8:00-18:00
});
```

## 关键业务常量
```javascript
// 家电分类
const APPLIANCE_CATEGORIES = {
  WASHING_MACHINE: 1,    // 洗衣机
  REFRIGERATOR: 2,       // 冰箱
  AIR_CONDITIONER: 3,    // 空调
  TV: 4,                 // 电视
  COMPUTER: 5,           // 电脑
  FURNITURE: 6           // 家具
};

// 服务时间段
const SERVICE_TIME_SLOTS = [
  '9:00-11:00',
  '14:00-16:00',
  '16:00-18:00'
];

// 最大服务距离（公里）
const MAX_SERVICE_DISTANCE = 50;

// 订单有效期（小时）
const ORDER_EXPIRY_HOURS = 24;
```