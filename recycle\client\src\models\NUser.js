import Taro from '@tarojs/taro'
import Config from '../config/config'
import { NUser } from '../config/constants'
import {
  postUser,
  putUserInfo,
  putUserPhone,
  getAddress,
  register,
  getUserPhone,
  postEvaluate,
  postProxy,
  getProxy,
} from '../services/user'

const {
  ELogin,
  EPutUserPhone,
  EPutUserPhoneNew,
  EPutUserInfo,
  EPutUserAvatar,
  EFirstGetAddress,
  EGetAddress,
  EGetProxy,
  EPostProxy,
  ESetState,
  EPostEvaluate,
} = NUser
export default {
  state: {
    codeSource: '', // 扫码来源
    userInfo: null,
    page: null,
    provinceList: [],
    cityList: [],
    areaList: [],
    streetList: [],
    isUpload: false,

    province: null,
    city: null,
    area: null,
    street: null,
    proxyer: null,
    // 省市区与街道模块显示
    levelTowAddressShow: true,
    // 地址模块选择
    chooseAddressShow: false,
    userPhoneNumber: null,
    isPhoneGet: false,
    isPutUserInfo: false,
  }, // initial state
  effects: dispatch => ({
    async [ELogin](payload) {
      const { code } = await Taro.login()
      let user, token
      const reqData = {
        code: code,
        appID: Config.APP_ID,
      }
      if (payload) {
        reqData.salesman = payload.salesman
      }

      const response = await postUser(reqData)

      if (response.token === 0) {
        const responseRegister = await register({
          openid: response.openID,
          checkAddressID: Taro.getStorageSync('launchCheckAddressID'),
        })
        user = responseRegister.user
        token = responseRegister.token
      } else {
        user = response.user
        token = response.token
      }
      await this.RSetState({ userInfo: user })
      Taro.setStorageSync('userInfo', JSON.stringify(user))
      Taro.setStorageSync('token', token)
      //py.setOpenid(user.openid); //调用上传openid
    },
    async [EPutUserPhone](payload) {
      let code = Taro.getStorageSync('current_code')
      let { iv, encryptedData } = payload
      await this.RSetState({ isPhoneGet: false })
      const response = await putUserPhone({ iv, encryptedData, code })
      await this.RSetState({ userPhoneNumber: response.phoneNumber })
      await this.RSetState({ isPhoneGet: true })
    },
    async [EPutUserPhoneNew](payload) {
      await this.RSetState({ isPhoneGet: false })
      const response = await getUserPhone(payload)
      let user = Taro.getStorageSync('userInfo') && JSON.parse(Taro.getStorageSync('userInfo'))
      user.mobile = response.mobile
      Taro.setStorageSync('userInfo', JSON.stringify(user))
      await this.RSetState({ userPhoneNumber: response.mobile })
      await this.RSetState({ isPhoneGet: true })
    },
    async [EPutUserAvatar](payload) {

      const response = await putUserInfo({
        id: payload.id,
        nickName: payload.nickName,
        avatarUrl: payload.avatarUrl,
      })
      Taro.setStorageSync('userInfo', JSON.stringify(response))
      await this.RSetState({ userInfo: response })
    },

    async [EPutUserInfo](payload) {
      await this.RSetState({ isPutUserInfo: false })
      const response = await putUserInfo({
        nickName: payload.nickName,
        avatarUrl: payload.avatarUrl,
        gender: payload.gender,
        country: payload.country,
        province: payload.province,
        mobile: payload.mobile,
        city: payload.city,
        id: payload.id,
        wechatObject: JSON.stringify(payload),
      })
      Taro.setStorageSync('userInfo', JSON.stringify(response))
      await this.RSetState({ userInfo: response })
      if (payload.page === 'user') {
        Taro.reLaunch({ url: '/pages/my/index' })
      } else {
        await this.RSetState({ isPutUserInfo: true })
      }
    },

    // 第一次请求数据 默认是北京的数据
    async [EFirstGetAddress]() {
      const province = await getAddress({ code: '' })
      await this.RSetState({ provinceList: province })
      const city = await getAddress({ code: '11' })
      await this.RSetState({ cityList: city })
      const area = await getAddress({ code: '1101' })
      await this.RSetState({ areaList: area })
      const street = await getAddress({ code: '110101' })
      await this.RSetState({ streetList: street })
    },

    async [EPostEvaluate](payload) {
      let res = await postEvaluate(payload)
    },
    async [EPostProxy](payload) {
      let res = await postProxy(payload)
    },
    async [EGetProxy](payload) {
      let res = await getProxy(payload)
      await this.RSetState({ proxyer: res?.data })
    },

    async [EGetAddress](payload) {
      await this.RSetState({ isUpload: false })
      let length = payload.code.length
      if (length === 2) {
        const city = await getAddress({ code: payload.code })
        await this.RSetState({ cityList: city })
        const area = await getAddress({ code: `${city[0].code}` })
        await this.RSetState({ areaList: area })
        const street = await getAddress({ code: `${area[0].code}` })
        await this.RSetState({ streetList: street })
      }
      if (length === 4) {
        const area = await getAddress({ code: `${payload.code}` })
        await this.RSetState({ areaList: area })
        const street = await getAddress({ code: `${area[0].code}` })
        await this.RSetState({ streetList: street })
      }
      if (length === 6) {
        const street = await getAddress({ code: `${payload.code}` })
        await this.RSetState({ streetList: street })
      }
      await this.RSetState({ isUpload: true })
    },
    async [ESetState](payload) {
      await this.RSetState({ ...payload })
    },
  }),
  reducers: {
    RSetState(state, payload) {
      return {
        ...state,
        ...payload,
      }
    },
  },
}
