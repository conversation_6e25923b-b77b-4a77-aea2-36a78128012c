import { DownloadOutlined } from '@ant-design/icons'
import type { ProFormInstance } from '@ant-design/pro-components'
import { <PERSON><PERSON>, Modal } from 'antd'
import type { ProColumns } from '@ant-design/pro-table'
import ProTable, { } from '@ant-design/pro-table'
import ProCard from '@ant-design/pro-card'
import { effect, reducer, useConnect } from 'dva17'
import { useEffect, useRef } from 'react'
import { EGetList, NCollection, NOrder, NWorker, RSetState } from '../../common/action'
import { computeAuthority } from '../../utils/Authorized/authority'
type Item = {
  id: number
  openid: string
  msgBody: string
  createdAt: any
}

export default () => {
  const formRef = useRef<ProFormInstance>()
  const { total, totalPay } = useConnect(NCollection)

  const columns: ProColumns<Item>[] = [
    { title: '编号', width: '8%', dataIndex: 'id', copyable: false, search: false, ellipsis: true },
    { title: '手机号', width: '15%', dataIndex: 'openid', copyable: false, ellipsis: true },
    {
      title: '内容',
      dataIndex: 'msgBody',
      width: '60%',
      copyable: false,
      ellipsis: true,
      search: false
    },
    {
      title: '内容',
      dataIndex: 'keyword',
      hideInTable: true,
      copyable: false,
      ellipsis: true,
    },
    {
      title: '下发时间',
      dataIndex: 'createdAt',
      copyable: false,
      ellipsis: true,
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            startDate: value[0],
            endDate: value[1],
          }
        },
      },
    },
    {
      title: '下发时间',
      dataIndex: 'createdAt',
      copyable: false,
      ellipsis: true,
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: (a, b) => a.createdAt - b.createdAt,
    },

  ]

  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
  }, [])
  /*--------------------- 响应 ---------------------*/
  let wal = parseInt(String(totalPay - total / 10))
  /*--------------------- 渲染 ---------------------*/

  return (
    <ProCard>
      <ProTable<Item>
        formRef={formRef}
        columns={columns}
        request={async (params = {}, sorter) => {
          return (await effect(NCollection, EGetList, { ...params, ...sorter })) as any
        }}
        pagination={{
        }}
        rowKey="id"
        dateFormatter="string"
        headerTitle=" "
        search={{}}
        toolBarRender={() => [
          <div>条数：{total}</div>,
          <div>余额：¥{wal}</div>
        ]}
      />
    </ProCard>
  )
}