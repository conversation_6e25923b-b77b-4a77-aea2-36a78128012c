import type { ProFormInstance } from '@ant-design/pro-components'
import { Divider, Modal } from 'antd'
import type { ProColumns } from '@ant-design/pro-table'
import ProTable, { } from '@ant-design/pro-table'
import ProCard from '@ant-design/pro-card'
import { effect, reducer, useConnect } from 'dva17'
import { useEffect, useRef, useState } from 'react'
import { EGetList, EGetSmsLog, NCollection } from '../../common/action'
type Item = {
  id: number
  openid: string
  msgBody: string
  createdAt: any
}

export default () => {
  const formRef = useRef<ProFormInstance>()
  const { total, totalPay, smsLog } = useConnect(NCollection)
  const [showRecharge, setShowRecharge] = useState(false)
  const columns: ProColumns<Item>[] = [
    { title: '编号', width: '8%', dataIndex: 'id', copyable: false, search: false, ellipsis: true },
    { title: '手机号', width: '15%', dataIndex: 'openid', copyable: false, ellipsis: true },
    {
      title: '内容',
      dataIndex: 'msgBody',
      width: '60%',
      copyable: false,
      ellipsis: true,
      search: false
    },
    {
      title: '内容',
      dataIndex: 'keyword',
      hideInTable: true,
      copyable: false,
      ellipsis: true,
    },
    {
      title: '下发时间',
      dataIndex: 'createdAt',
      copyable: false,
      ellipsis: true,
      valueType: 'dateRange',
      hideInTable: true,
      search: {
        transform: value => {
          return {
            startDate: value[0],
            endDate: value[1],
          }
        },
      },
    },
    {
      title: '下发时间',
      dataIndex: 'createdAt',
      copyable: false,
      ellipsis: true,
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: (a, b) => a.createdAt - b.createdAt,
    },

  ]

  /*--------------------- 生命周期 ---------------------*/
  useEffect(() => {
  }, [])
  /*--------------------- 响应 ---------------------*/
  let wal = parseInt(String(totalPay - total / 10))
  /*--------------------- 渲染 ---------------------*/

  return (
    <ProCard>
      <ProTable<Item>
        formRef={formRef}
        columns={columns}
        request={async (params = {}, sorter) => {
          return (await effect(NCollection, EGetList, { ...params, ...sorter })) as any
        }}
        pagination={{
        }}
        rowKey="id"
        dateFormatter="string"
        headerTitle=" "
        search={{}}
        toolBarRender={() => [
          <div onClick={() => {
            setShowRecharge(true);
            effect(NCollection, EGetSmsLog, {});
          }}
            style={{ color: 'blue', textDecoration: 'underline', cursor: 'pointer' }}>
            <div>充值记录</div>
          </div>,
          <div style={{ marginLeft: '10px' }}>余额：¥{wal}</div>
        ]}
      />
      <Modal open={showRecharge} onCancel={() => { setShowRecharge(false) }}>
        <ProCard>
          <div style={{ fontSize: '16px', fontWeight: 'bold' }}>充值记录</div>
          {smsLog.map((item: any) => (
            <div key={item.id}>
              <div> 充值时间：{item.createdAt}</div>
              <div>充值：<span style={{ color: 'red' }}>{item.pay}</span></div>
              <Divider dashed />
              
            </div>
          ))}
        </ProCard>
      </Modal>
    </ProCard>
  )
}