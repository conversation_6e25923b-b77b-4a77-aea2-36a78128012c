@import '../../styles/variables.less';

.home_page {
  background: @background;
  min-height: 100vh;
  
  // 城市选择触发器样式
  .city-selector-trigger {
    display: flex;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: 40rpx;
    padding: 10rpx 24rpx;
    box-sizing: border-box;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    width: fit-content;
    margin: 10rpx 32rpx 20rpx;
    
    .city-name {
      color: #fff;
      font-size: 28rpx;
      font-weight: 500;
      margin-right: 12rpx;
    }
    
    .city-arrow {
      color: #fff;
      font-size: 24rpx;
      background-color: rgba(255, 255, 255, 0.2);
      padding: 4rpx 10rpx;
      border-radius: 20rpx;
    }
  }

  // 轮播广告位样式
  .banner-section {
    position: relative;
    height: 380rpx;
    overflow: hidden;
    border-radius: 0 0 40rpx 40rpx;
    box-sizing: border-box;
    margin-bottom: 30rpx;

    .banner-swiper {
      width: 100%;
      height: 380rpx;
    }

    .banner-item {
      width: 100%;
      height: 100%;
      position: relative;
    }

    .banner-image {
      width: 100%;
      height: 100%;
      border-radius: 0 0 40rpx 40rpx;
    }
  }
  
  // 英雄区域样式 - 全新风格
  .hero-section {
    position: relative;
    height: 380rpx;
    overflow: hidden;
    background: linear-gradient(135deg, @primary-color, @primary-light);
    border-radius: 0 0 40rpx 40rpx;
    padding: @spacing-md;
    box-sizing: border-box;
    margin-bottom: 50rpx;

    .hero-background {
      position: relative;
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .logo-section {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      height: 100rpx;

      .logo-mini {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      .news-tag {
        background-color: #fff;
        padding: 8rpx 24rpx;
        border-radius: 30rpx;
        border: 2rpx solid #333;
        font-size: 24rpx;
        font-weight: bold;
        color: #333;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .main-title-section {
      padding: @spacing-lg 0;

      .main-title {
        font-size: 80rpx;
        line-height: 1.2;
        font-weight: 800;
        color: #333;
        text-shadow: 2px 2px 0px rgba(255, 255, 255, 0.5);
        letter-spacing: 2px;
        margin-bottom: 20rpx;
      }

      .subtitle-wrapper {
        background: #ffffff;
        padding: 16rpx 32rpx;
        border-radius: 40rpx;
        width: fit-content;
        max-width: 90%;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        position: relative;
        display: flex;
        align-items: center;

        &::before {
          content: '';
          display: block;
          width: 24rpx;
          height: 24rpx;
          background-color: @primary-color;
          border-radius: 50%;
          margin-right: 16rpx;
        }

        .subtitle {
          font-size: 28rpx;
          font-weight: 600;
          color: #333;
        }
      }
    }
  }

  // 通用区域样式
  .section-title {
    font-size: @font-xl;
    font-weight: 600;
    color: @text-primary;
    text-align: center;
    margin-bottom: @spacing-xl;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -@spacing-xs;
      left: 50%;
      transform: translateX(-50%);
      width: 60rpx;
      height: 4rpx;
      background: linear-gradient(135deg, @primary-color, @primary-light);
      border-radius: @radius-xs;
    }
  }

  // 滚动信息区域
  .scrolling-section {
    padding: 0 @spacing-md @spacing-lg @spacing-md;
    margin-top: -30rpx;
    z-index: 10;
    position: relative;
    
    .scrolling-container {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 255, 251, 0.95));
      border-radius: @radius-lg;
      padding: @spacing-md;
      display: flex;
      align-items: center;
      gap: @spacing-sm;
      box-shadow: 0 6px 16px rgba(21, 179, 129, 0.08);
      border: 1px solid rgba(21, 179, 129, 0.1);
      backdrop-filter: blur(10px);
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, 
          transparent 0%, 
          rgba(21, 179, 129, 0.03) 25%, 
          rgba(21, 179, 129, 0.03) 75%, 
          transparent 100%);
        animation: shimmer 2s infinite;
      }
      
      .scrolling-icon {
        font-size: 28rpx;
        min-width: 36rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2;
      }
      
      .scrolling-content {
        flex: 1;
        overflow: hidden;
        z-index: 2;
        position: relative;
        height: 40rpx;
        display: flex;
        align-items: center;
        width: 100%;
        margin: 0;
        white-space: nowrap;
      }
      
      .scrolling-text {
        font-size: 26rpx;
        color: @text-primary;
        font-weight: 500;
        line-height: 1.4;
        white-space: nowrap;
        display: inline-block;
        min-width: 100%;
        animation: marquee 12s linear infinite;
        padding-right: 80rpx; // 增加右侧间距，防止内容重叠
        transition: animation-play-state 0.3s;
        will-change: transform; // 优化动画性能

        // 支持触摸暂停（移动端友好）
        &:hover,
        &:active {
          animation-play-state: paused;
        }
      }
    }
  }

  // 滚动信息动画
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }
  
  @keyframes fadeInText {
    0% {
      opacity: 0;
      transform: translateY(10rpx);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes marquee {
    0% {
      transform: translateX(100%);
    }
    100% {
      transform: translateX(-100%);
    }
  }

  // 主要服务分类区域
  .main-categories-section {
    padding: @spacing-lg;
    background: linear-gradient(to bottom, #f0f9f5, #e8f5ef);
    border-radius: @radius-lg;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
    margin: 0 20rpx;
    height: 240rpx; // 增加高度，确保内容完全显示

    .categories-scroll {
      display: flex;
      white-space: nowrap;
      overflow-x: auto;
      height: 190rpx; // 明确设置滚动区域高度
      
      &::-webkit-scrollbar {
        display: none;
      }
    }

    .category-item {
      display: inline-flex; // 改为 inline-flex 以便更好地控制布局
      flex-direction: column; // 确保垂直布局
      width: 150rpx;
      padding: @spacing-xs;
      margin-right: @spacing-md;
      text-align: center;
      align-items: center;
      justify-content: center;
      transition: transform 0.2s ease;

      &:active {
        transform: scale(0.96);
      }

      .category-icon {
        width: 90rpx; // 增加宽度
        height: 90rpx; // 增加高度
        object-fit: contain; // 确保图片完整显示
        margin-bottom: 16rpx; // 增加与文本的间距
      }

      .category-name {
        font-weight: bolder;
        font-size: 24rpx;
        color: @text-primary;
        white-space: normal;
        word-wrap: break-word;
        line-height: 1.2;
        height: 60rpx;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
  }

  // 次要服务分类区域
  .secondary-categories-section {
    padding: @spacing-md;
    margin-top: @spacing-lg;

    .secondary-categories-grid {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      background: linear-gradient(to bottom, #fff, #f9f9f9);
      padding: @spacing-md;
      border-radius: @radius-lg;
      border: 1px solid rgba(230, 230, 230, 0.5);
    }

    .secondary-category-item {
      width: 18%;
      padding: @spacing-xs 0;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transition: transform 0.2s ease;

      &:active {
        transform: scale(0.96);
      }

      .secondary-category-icon {
        width: 80rpx;
        height: 80rpx;
        object-fit: contain;
        margin-bottom: 10rpx;
      }

      .secondary-category-name {
        font-size: 24rpx;
        color: @text-primary;
        margin-top: 6rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
      }
    }
  }

  // 预约按钮区域
  .booking-section {
    padding: @spacing-lg;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .booking-button {
      width: 100%;
      height: 88rpx;
      background: linear-gradient(135deg, @primary-color, @primary-light);
      color: #fff;
      border-radius: @radius-lg;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 32rpx;
      font-weight: 600;
      box-shadow: 0 8px 16px rgba(21, 179, 129, 0.25);
      transition: all 0.2s;
      
      &:active {
        transform: translateY(2px);
        box-shadow: 0 2px 6px rgba(21, 179, 129, 0.1);
      }
    }
    
    .booking-desc {
      font-size: 22rpx;
      color: #FA5151;
      margin-top: @spacing-sm;
      align-self: flex-start;
      padding: 0 @spacing-xs;
    }
  }

  // 推荐服务区域
  .recommended-section {
    padding: @spacing-md;
    margin-top: @spacing-md;
    
    .recommended-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20rpx;
    }
    
    .recommended-item {
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(245, 245, 245, 0.9));
      border-radius: @radius-lg;
      padding: 24rpx 0;
      text-align: center;
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.05);
      transition: all 0.3s;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 200rpx;
      border: 1px solid rgba(230, 230, 230, 0.5);
      
      &:active {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(21, 179, 129, 0.12);
        border-color: rgba(21, 179, 129, 0.2);
      }
      
      .recommended-icon {
        width: 80rpx;
        height: 80rpx;
        margin-bottom: @spacing-xs;
      }
      
      .recommended-content {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      
      .recommended-title {
        font-size: 26rpx;
        color: @text-primary;
        font-weight: 600;
        margin-bottom: 4rpx;
      }
      
      .recommended-desc {
        font-size: 22rpx;
        color: @text-secondary;
      }
    }
  }
  
  // 回收指南区域
  .guide-section {
    padding: @spacing-lg;
    position: relative;
    margin-top: @spacing-lg;
    border-top: 12rpx solid #f5f5f5;
    background: linear-gradient(to bottom, #fff, #f9f9f9);
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: -6rpx;
      width: 8rpx;
      height: 36rpx;
    }
    
    .guide-title {
      font-size: 32rpx;
      font-weight: 600;
      color: @text-primary;
      margin-bottom: @spacing-lg;
      padding-left: 16rpx;
    }
    
    .guide-grid {
      display: flex;
      justify-content: space-between;
      padding: 0 @spacing-md;
    }
    
    .guide-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 22%;
      
      .guide-icon {
        width: 80rpx;
        height: 80rpx;
        margin-bottom: @spacing-xs;
      }
      
      .guide-text {
        font-size: 24rpx;
        color: @text-primary;
        text-align: center;
      }
    }
  }
  
  // 新闻资讯区域 - 简化版
  .news-section {
    padding: @spacing-lg;
    position: relative;
    margin-top: @spacing-lg;
    border-top: 12rpx solid #f5f5f5;
    background-color: #fff;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: -6rpx;
      width: 8rpx;
      height: 36rpx;
      background-color: @primary-color;
    }
    
    .news-title {
      font-size: 32rpx;
      font-weight: 600;
      color: @text-primary;
      margin-bottom: @spacing-md;
      padding-left: 16rpx;
      display: flex;
      align-items: center;
      
      &:before {
        content: '';
        width: 6rpx;
        height: 32rpx;
        background-color: @primary-color;
        margin-right: 16rpx;
        border-radius: 3rpx;
      }
    }
    
    .news-list {
      display: flex;
      flex-direction: column;
    }
    
    .news-item {
      padding: @spacing-md 0;
      border-bottom: 1rpx solid #eee;
      
      &:last-child {
        border-bottom: none;
      }
      
      .news-item-title {
        font-size: 28rpx;
        color: @text-primary;
        margin-bottom: @spacing-sm;
        line-height: 1.4;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      
      .news-item-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .news-date {
        font-size: 22rpx;
        color: @text-light;
      }
      
      .news-qr {
        width: 60rpx;
        height: 60rpx;
      }
    }
  }

  // 隐私政策弹窗样式优化
  .remind_wrapper {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(4px);

    .message_wrapper {
      border-radius: @radius-lg;
      height: 896rpx;
      width: 600rpx;
      background: @background;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      box-shadow: @shadow-strong;
      overflow: hidden;

      .remindTitle {
        font-size: @font-lg;
        font-weight: 600;
        color: @text-primary;
        margin-top: @spacing-xl;
        margin-bottom: @spacing-xl;
        text-align: center;
        padding: 0 @spacing-lg;
      }

      .content_wrapper {
        width: 500rpx;
        height: 520rpx;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        padding: 0 @spacing-sm;

        &::-webkit-scrollbar {
          width: 4rpx;
        }

        &::-webkit-scrollbar-thumb {
          background: @primary-light;
          border-radius: @radius-xs;
        }

        .remind_text, .remind_text2, .remind_text3 {
          font-size: @font-sm;
          color: @text-secondary;
          line-height: 1.6;
          margin-bottom: @spacing-sm;
        }

        .remind_title {
          font-size: @font-md;
          font-weight: 500;
          color: @text-primary;
          margin-bottom: @spacing-md;
        }
      }

      .know_button_wrapper {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: @spacing-xl;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 500rpx;

        .know_button {
          height: 88rpx;
          width: 100%;
          background: @gradient-primary;
          line-height: 88rpx;
          text-align: center;
          border-radius: @radius-lg;
          color: @text-white;
          font-weight: 600;
          font-size: @font-md;
          box-shadow: @shadow-medium;
          transition: @transition-normal;

          &:active {
            transform: translateY(1px);
            box-shadow: @shadow-light;
          }
        }

        .radio {
          font-size: @font-sm;
          color: @text-secondary;
          margin-bottom: @spacing-md;
          display: flex;
          align-items: center;

          .wx-radio-input {
            width: 32rpx;
            height: 32rpx;
            margin-right: @spacing-sm;
            border-color: @border-color;
          }

          .wx-radio-input.wx-radio-input-checked {
            border-color: @primary-color !important;
            background: @primary-color !important;
          }

          .wx-radio-input.wx-radio-input-checked::before {
            width: 32rpx;
            height: 32rpx;
            line-height: 32rpx;
            text-align: center;
            font-size: 24rpx;
            color: @text-white;
            background: transparent;
            transform: translate(-50%, -50%) scale(1);
          }
        }
      }
    }
  }

  // 加载动画
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30rpx);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30rpx);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  // 为不同的元素应用动画
  .category-item,
  .secondary-category-item,
  .recommended-item,
  .guide-item,
  .news-item,
  .benefit-item {
    animation: fadeInUp 0.6s ease forwards;
  }
  
  .banner-section {
    animation: fadeIn 1s ease forwards;
  }
  
  .recruitment-card,
  .official-account-card {
    animation: scaleIn 0.7s ease forwards;
  }
  
  .qrcode-image {
    animation: fadeIn 1s ease 0.3s forwards;
    opacity: 0;
    animation-fill-mode: forwards;
  }

  // 为不同的项目添加延迟动画
.category-item:nth-child(1) { animation-delay: 0.1s; }
.category-item:nth-child(2) { animation-delay: 0.15s; }
.category-item:nth-child(3) { animation-delay: 0.2s; }
.category-item:nth-child(4) { animation-delay: 0.25s; }
.category-item:nth-child(5) { animation-delay: 0.3s; }
.category-item:nth-child(6) { animation-delay: 0.35s; }

.process-step:nth-child(2) { animation-delay: 0.15s; } // 第一个流程步骤
.process-step:nth-child(3) { animation-delay: 0.25s; } // 第二个流程步骤
.process-step:nth-child(4) { animation-delay: 0.35s; } // 第三个流程步骤
.process-step:nth-child(5) { animation-delay: 0.45s; } // 第四个流程步骤

  .secondary-category-item:nth-child(1) { animation-delay: 0.15s; }
  .secondary-category-item:nth-child(2) { animation-delay: 0.2s; }
  .secondary-category-item:nth-child(3) { animation-delay: 0.25s; }
  .secondary-category-item:nth-child(4) { animation-delay: 0.3s; }
  .secondary-category-item:nth-child(5) { animation-delay: 0.35s; }

  .recommended-item:nth-child(1) { animation-delay: 0.2s; }
  .recommended-item:nth-child(2) { animation-delay: 0.25s; }
  .recommended-item:nth-child(3) { animation-delay: 0.3s; }

  .guide-item:nth-child(1) { animation-delay: 0.15s; }
  .guide-item:nth-child(2) { animation-delay: 0.2s; }
  .guide-item:nth-child(3) { animation-delay: 0.25s; }
  .guide-item:nth-child(4) { animation-delay: 0.3s; }
  
  .news-item:nth-child(1) { animation-delay: 0.2s; }
  .news-item:nth-child(2) { animation-delay: 0.3s; }
  
  .benefit-item:nth-child(1) { animation-delay: 0.25s; }
  .benefit-item:nth-child(2) { animation-delay: 0.3s; }
  .benefit-item:nth-child(3) { animation-delay: 0.35s; }
  .benefit-item:nth-child(4) { animation-delay: 0.4s; }

  // 响应式设计
  @media (max-width: 600px) {
    .advantages-grid {
      grid-template-columns: 1fr;
    }
    
    .statistics-grid {
      grid-template-columns: 1fr;
    }
    
    .services-grid {
      flex-direction: column;
    }
  }
}

// 招募区域样式
.recruitment-section {
  padding: @spacing-lg;
  margin-top: @spacing-lg;
  
  .recruitment-card {
    background: linear-gradient(135deg, #15b381, #41c794);
    border-radius: @radius-lg;
    padding: @spacing-lg;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 20px rgba(21, 179, 129, 0.15);
    overflow: hidden;
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      // background: url('https://oss.dongguahuishou.com/images/pattern.png');
      opacity: 0.07;
      z-index: 1;
    }
    
    .recruitment-content {
      flex: 1;
      z-index: 2;
    }
    
    .recruitment-title {
      font-size: 36rpx;
      font-weight: 700;
      color: #fff;
      margin-bottom: @spacing-sm;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .recruitment-desc {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: @spacing-md;
    }
    
    .benefits-list {
      margin-bottom: @spacing-md;
    }
    
    .benefit-item {
      display: flex;
      align-items: center;
      margin-bottom: @spacing-xs;
      
      .benefit-dot {
        width: 12rpx;
        height: 12rpx;
        border-radius: 50%;
        background-color: #fbbf24;
        margin-right: @spacing-xs;
      }
      
      .benefit-text {
        font-size: 22rpx;
        color: rgba(255, 255, 255, 0.95);
      }
    }
    
    .recruitment-button {
      background-color: #fbbf24;
      padding: @spacing-sm @spacing-lg;
      border-radius: 40rpx;
      color: #333;
      font-size: 24rpx;
      font-weight: 600;
      display: inline-block;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s;
      
      &:active {
        transform: translateY(2px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
    
    .recruitment-image {
      width: 180rpx;
      height: 180rpx;
      z-index: 2;
    }
  }
}

// 回收流程区域样式
.process-section {
  padding: @spacing-lg;
  margin: @spacing-lg @spacing-md 0;
  background-color: #fff;
  border-radius: @radius-lg;
  position: relative;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: -6rpx;
    width: 8rpx;
    height: 36rpx;
  }
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: @text-primary;
    margin-bottom: @spacing-xl;
    padding-left: 16rpx;
  }
  
  .process-steps {
    display: flex;
    justify-content: space-between;
    padding: @spacing-md @spacing-xs;
    position: relative;
  }
  
  .process-line {
    position: absolute;
    top: 50rpx;
    left: 10%;
    width: 80%;
    height: 4rpx;
    background: @primary-lighter;
    z-index: 1;
  }
  
  .process-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 25%;
    position: relative;
    z-index: 2;
    
    .icon-wrapper {
      width: 100rpx;
      height: 100rpx;
      background: linear-gradient(135deg, @primary-color, @primary-light);
      border-radius: @radius-round;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: @spacing-sm;
      box-shadow: 0 6px 12px rgba(21, 179, 129, 0.2);
    }
    
    .icon {
      font-size: 40rpx;
      color: #fff;
    }
    
    .title {
      font-size: 28rpx;
      font-weight: 500;
      color: @text-primary;
      margin-bottom: @spacing-xs;
      text-align: center;
    }
    
    .desc {
      font-size: 22rpx;
      color: @text-light;
      text-align: center;
      line-height: 1.4;
      white-space: pre-line;
    }
  }
}

// 用户评价区域样式
.review-section {
  padding: @spacing-lg @spacing-md;
  margin: @spacing-lg @spacing-md 0;
  position: relative;
  background-color: #fff;
  border-radius: @radius-lg;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  
  .section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: @text-primary;
    margin-bottom: @spacing-lg;
    padding-left: 18rpx;
  }
  
  .review-list {
    display: flex;
    white-space: nowrap;
    overflow-x: auto;
    padding: @spacing-md;
    
    &::-webkit-scrollbar {
      display: none;
    }
  }
  
  .review-card {
    display: inline-block;
    width: 500rpx;
    min-width: 500rpx;
    background: #f5f9ff;
    border-radius: @radius-lg;
    padding: @spacing-lg;
    margin-right: @spacing-md;
    box-sizing: border-box;
    
    &:last-child {
      margin-right: 0;
    }
  }
  
  .review-user {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @spacing-sm;
  }
  
  .review-username {
    font-size: 28rpx;
    font-weight: 500;
    color: #4080ff;
  }
  
  .review-rating {
    display: flex;
    align-items: center;
    
    .star {
      font-size: 24rpx;
      margin-left: 2rpx;
      color: #ffc107;
    }
  }
  
  .review-text {
    font-size: 28rpx;
    color: @text-primary;
    line-height: 1.5;
    white-space: normal;
  }
}

// 公众号区域样式
.official-account-section {
  padding: @spacing-lg;
  margin-top: @spacing-lg;
  margin-bottom: 120rpx; // 为底部tabbar留出空间
  
  .section-title {
    position: relative;
    text-align: center;
    margin-bottom: @spacing-xl;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -10rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 60rpx;
      height: 6rpx;
      background: @primary-color;
      border-radius: 3rpx;
    }
  }
  
  .official-account-card {
    border-radius: @radius-lg;
    padding: @spacing-xl;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #edf2f7;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 6rpx;
    }
  }
  
  .official-account-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: @spacing-lg;
  }
  
  .official-account-title {
    font-size: 36rpx;
    font-weight: 600;
    color: @text-primary;
    margin-bottom: @spacing-sm;
  }
  
  .official-account-desc {
    font-size: 26rpx;
    color: @text-secondary;
    margin-bottom: @spacing-lg;
    line-height: 1.5;
    max-width: 90%;
  }
  
  .official-account-wrapper {
    width: 100%;
  }
  
  .official-account-component {
    width: 100%;
    min-height: 88rpx;
    border-radius: @radius-sm;
    overflow: hidden;
    
    // 修复微信开放能力组件的样式
    :global {
      .wechat-mina {
        width: 100% !important;
        height: 100% !important;
        min-height: 88rpx !important;
        display: block !important;
      }
    }
  }
  
  // 分隔线样式
  .divider {
    display: flex;
    align-items: center;
    width: 80%;
    margin: @spacing-lg auto;
    
    .divider-line {
      flex: 1;
      height: 1px;
      background: linear-gradient(to right, transparent, #e0e0e0, transparent);
    }
    
    .divider-text {
      padding: 0 @spacing-md;
      color: @text-light;
      font-size: 24rpx;
      background: #f9f9f9;
    }
  }
  
  .scan-text {
    font-size: 26rpx;
    color: @primary-color;
    margin-bottom: @spacing-md;
    font-weight: 500;
    display: inline-block;
    position: relative;
    
    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      width: 30rpx;
      height: 1px;
      background-color: @primary-color;
      opacity: 0.6;
    }
    
    &::before {
      left: -40rpx;
    }
    
    &::after {
      right: -40rpx;
    }
  }
  
  .qrcode-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #fff;
    padding: @spacing-md;
    border-radius: @radius-lg;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
    border: 1px dashed rgba(21, 179, 129, 0.3);
    position: relative;
  }
  
  .qrcode-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: @spacing-sm;
    border: none;
    border-radius: @radius-sm;
  }
  
  .qrcode-caption {
    font-size: 22rpx;
    color: @text-light;
    background: rgba(21, 179, 129, 0.08);
    padding: 6rpx 20rpx;
    border-radius: 30rpx;
  }
}

page {
  scroll-behavior: smooth;
}