.home_page {
  .coverSwiper {
    height: 545px;

    .wx-swiper-dot {
      position: relative;
      bottom: 80px;
    }

    .cover_warpper {
      image {
        width: 100vw;
      }
    }
  }

  .navigation {
    transform: translateY(-80px);
    overflow: hidden;
    padding: 40px 20px;
    border-radius: 40px 40px;
    background: #fff;
    display: flex;
    flex-direction: column;

    .title {
      padding-bottom: 40px;
      justify-content: center;
      display: flex;

      image {
        width: 40%;
      }
    }

    .tabBox {
      display: flex;
      justify-content: space-between;
      padding: 0 40px;

      image {
        width: 148px;
      }
    }
  }

  .guidance {
    display: flex;
    justify-content: center;

    image {
      width: 622px;
    }
  }

  .remind_wrapper {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    letter-spacing: 1.2px;

    .message_wrapper {
      border-radius: 8px;
      height: 896px;
      width: 600px;
      background: #ffffff;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      .remindTitle {
        font-size: medium;
        margin-top: 34px;
        margin-bottom: 50px;
        display: inline-block;
        width: 50%;
        height: 6%;
      }

      .content_wrapper {
        width: 500px;
        height: 520px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;

        .remind_text {
          font-size: 24px;
          color: #7c8696;
          line-height: 36px;
          margin-bottom: 24px;
        }

        .remind_text2 {
          font-size: 24px;
          color: #7c8696;
          line-height: 36px;
          margin-bottom: 18px;
        }

        .remind_text3 {
          font-size: 24px;
          color: #7c8696;
          line-height: 36px;
          margin-bottom: 12px;
        }

        .remind_title {
          font-size: 28px;
          color: #444444;
          margin-bottom: 20px;
        }
      }

      .know_button_wrapper {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 40px;
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 0.42667rem;

        .know_button {
          height: 88px;
          width: 500px;
          background: #15b381;
          line-height: 88px;
          text-align: center;
          border-radius: 44px;
          color: #ffffff;
          font-weight: 700;
        }

        .radio {
          font-size: 20px;
          color: #7c8696;
          margin-bottom: 20px;

          //    未选中的 背景样式
          .wx-radio-input {
            width: 20px;
            height: 20px;
            margin-right: 16px;
          }

          // 选中后的 背景样式 （红色背景 无边框 可根据UI需求自己修改）
          .wx-radio-input.wx-radio-input-checked {
            border-color: #15b381 !important;
            background: #15b381 !important;
          }

          .wx-radio-input.wx-radio-input-checked::before {
            width: 20px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            font-size: 15px;
            color: #fff;
            background: transparent;
            transform: translate(-50%, -50%) scale(1);
            -webkit-transform: translate(-50%, -50%) scale(1);
          }
        }
      }
    }
  }
}