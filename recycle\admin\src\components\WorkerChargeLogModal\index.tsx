import { Modal, Button, Table } from 'antd'
import { useConnect } from 'dva17'
import { NWorker } from '../../common/action'
import { SERVER_HOME } from '../../common/config'
import dayjs from 'dayjs'

// 师傅充值记录组件
interface WorkerChargeLogModalProps {
  visible: boolean;
  workerID: number;
  onClose: () => void;
}

const WorkerChargeLogModal = ({ visible, workerID, onClose }: WorkerChargeLogModalProps) => {
  const { workerPaylist } = useConnect(NWorker);
  
  const columns = [
    { title: '姓名', render: (record: any) => <span>{record?.worker?.workerName}</span> },
    { title: '充值方式', render: (record: any) => <span>{record.type}</span> },
    { title: '充值金额', render: (record: any) => <span>{(record?.totalPay) / 100}</span> },
    { 
      title: '充值时间', 
      dataIndex: 'createdAt', 
      render: (createdAt: any) => <span>{createdAt ? dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss') : '-'}</span> 
    },
  ];

  const exportWalletExcel = () => {
    window.open(`${SERVER_HOME}exportWalletXLS?workerID=${workerID}`);
  };

  return (
    <Modal
      title="师傅充值记录"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
    >
      <Button type='primary' onClick={exportWalletExcel} style={{ marginBottom: 16 }}>导出</Button>
      <Table
        columns={columns}
        loading={!workerPaylist}
        dataSource={workerPaylist?.data || []}
        rowKey={(record) => JSON.stringify(record)}
        pagination={false}
      />
    </Modal>
  );
};

export default WorkerChargeLogModal; 