import { Modal, <PERSON><PERSON>, Table } from 'antd'
import { effect, useConnect } from 'dva17'
import { EGetWorkerPaylist, NWorker } from '../../common/action'
import { SERVER_HOME } from '../../common/config'
import dayjs from 'dayjs'
import { useEffect } from 'react'

// 师傅充值记录组件
interface WorkerChargeLogModalProps {
  visible: boolean;
  workerID: number;
  onClose: () => void;
}

const WorkerChargeLogModal = ({ visible, workerID, onClose }: WorkerChargeLogModalProps) => {
  const { workerPaylist } = useConnect(NWorker);
  
  const columns = [
    { title: '姓名', render: (record: any) => <span>{record?.worker?.workerName}</span> },
    { title: '充值方式', render: (record: any) => <span>{record.type}</span> },
    { title: '充值金额', render: (record: any) => <span>{(record?.totalPay) / 100}</span> },
    { 
      title: '充值时间', 
      dataIndex: 'createdAt', 
      render: (createdAt: any) => <span>{createdAt ? dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss') : '-'}</span> 
    },
  ];

  useEffect(() => {
    if (visible) {
      effect(NWorker, EGetWorkerPaylist, { status: '完成', current: 1, pageSize: 20, workerID: workerID })
    }
  }, [workerID])

  const exportWalletExcel = () => {
    window.open(`${SERVER_HOME}exportWalletXLS?workerID=${workerID}`);
  };

  return (
    <Modal
      title="师傅充值记录"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
    >
      <Button type='primary' onClick={exportWalletExcel} style={{ marginBottom: 16 }}>导出</Button>
      <Table
        columns={columns}
        loading={!workerPaylist}
        dataSource={workerPaylist?.data || []}
        rowKey={(record) => JSON.stringify(record)}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          pageSize: workerPaylist?.perPage || 20,
          current: workerPaylist?.page || 1,
          total: workerPaylist?.total || 0,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          // 翻页时触发获取数据
          onChange: (page: number, pageSize: number) => {
            effect(NWorker, EGetWorkerPaylist, { 
              status: '完成', 
              current: page, 
              pageSize, 
              workerID 
            })
          }
        }}
      />
    </Modal>
  );
};

export default WorkerChargeLogModal; 