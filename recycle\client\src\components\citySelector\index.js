import { View, Text, ScrollView } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { useState, useEffect, useCallback } from 'react'
import { TENCENT_MAP_KEY, MAP_API } from '../../config/mapConfig'
import './index.less'

/**
 * 城市选择器组件
 * 
 * 提供省份、城市、区县、街道四级地址选择
 * 支持自动定位当前位置
 * 
 * @param {boolean} visible - 控制组件显示/隐藏
 * @param {Function} onClose - 关闭选择器的回调函数
 * @param {Function} onSelect - 选择地址后的回调函数
 * @param {string} currentCity - 当前已选择的城市
 */
const CitySelector = ({ visible, onClose, onSelect, currentCity }) => {
  // 状态管理
  const [activeProvince, setActiveProvince] = useState(null)
  const [selectedCity, setSelectedCity] = useState('')
  const [selectedDistrict, setSelectedDistrict] = useState('')
  const [provinceList, setProvinceList] = useState([])
  const [cityList, setCityList] = useState([])
  const [showDistricts, setShowDistricts] = useState(false)
  const [showStreets, setShowStreets] = useState(false)
  const [currentDistricts, setCurrentDistricts] = useState([])
  const [currentStreets, setCurrentStreets] = useState([])

  // 地理位置API请求
  const fetchLocationData = useCallback((url, params, callback) => {
    Taro.request({
      url,
      data: {
        key: TENCENT_MAP_KEY,
        ...params
      },
      success: (result) => {
        if (result.data?.status === 0 && result.data?.result) {
          callback(result.data.result)
        }
      },
      fail: (error) => {
        console.error('地理位置数据获取失败:', error)
      }
    })
  }, [])

  // 获取省份列表
  const fetchProvinces = useCallback(() => {
    fetchLocationData(MAP_API.CHILDREN, {}, (result) => {
      setProvinceList(result[0] || [])
    })
  }, [fetchLocationData])

  // 获取城市列表
  const fetchCities = useCallback((provinceId) => {
    fetchLocationData(MAP_API.CHILDREN, { id: provinceId }, (result) => {
      setCityList(result)
    })
  }, [fetchLocationData])

  // 获取区县列表
  const fetchDistricts = useCallback((cityId) => {
    fetchLocationData(MAP_API.CHILDREN, { id: cityId }, (result) => {
      setCurrentDistricts(result[0] || [])
    })
  }, [fetchLocationData])

  // 获取街道列表
  const fetchStreets = useCallback((districtId) => {
    fetchLocationData(MAP_API.CHILDREN, { id: districtId }, (result) => {
      setCurrentStreets(result[0] || [])
    })
  }, [fetchLocationData])

  // 直辖市列表
  const MUNICIPALITIES = ['上海市', '北京市', '天津市', '重庆市']

  // 格式化完整地址，确保各部分之间只有一个空格
  const formatFullAddress = useCallback((province, city, district, street) => {
    if (!city) return ''
    
    // 直辖市特殊处理：只需要市→区县→街道
    if (MUNICIPALITIES.includes(city)) {
      let address = city
      
      // 只有当区县名不包含在城市名中时才添加
      if (district && !city.includes(district)) {
        address += ' ' + district
      }

      // 添加街道名
      if (street) {
        address += ' ' + street
      }

      return address
    }

    // 普通城市：省份→城市→区县→街道
    let address = province + ' ' + city

    // 只有当区县名不包含在城市名中时才添加
    if (district && !city.includes(district)) {
      address += ' ' + district
    }

    // 添加街道名
    if (street) {
      address += ' ' + street
    }

    return address
  }, [])

  // 处理省份选择
  const handleProvinceSelect = useCallback((province) => {
    const provinceName = province.fullname
    setActiveProvince(provinceName)
    
    // 如果是直辖市，直接跳到区县选择阶段
    if (MUNICIPALITIES.includes(provinceName)) {
      setSelectedCity(provinceName)
      setShowDistricts(true)
      fetchDistricts(province.id)
    } else {
      // 普通省份，显示城市列表
      fetchCities(province.id)
    }
  }, [fetchCities, fetchDistricts])

  // 处理城市选择
  const handleCitySelect = useCallback((city) => {
    if (typeof city === 'string') {
      setSelectedCity(city)
    } else {
      setSelectedCity(city.fullname)
      fetchDistricts(city.id)
    }
    setShowDistricts(true)
  }, [fetchDistricts])

  // 处理区县选择
  const handleDistrictSelect = useCallback((district) => {
    setSelectedDistrict(district.fullname)
    setShowStreets(true)
    fetchStreets(district.id)
  }, [fetchStreets])

  // 处理街道选择
  const handleStreetSelect = useCallback((street) => {
    const fullAddress = formatFullAddress(activeProvince, selectedCity, selectedDistrict, street)
    onSelect(fullAddress)
    onClose()
  }, [formatFullAddress, onSelect, onClose, selectedCity, selectedDistrict])

  // 返回到城市选择
  const handleBackToCity = useCallback(() => {
    setShowDistricts(false)
    setShowStreets(false)
    setSelectedDistrict('')
    
    // 如果当前选择的是直辖市，返回时清空选择的城市
    if (MUNICIPALITIES.includes(selectedCity)) {
      setSelectedCity('')
      setActiveProvince(null)
    }
  }, [selectedCity])

  // 返回到区县选择
  const handleBackToDistrict = useCallback(() => {
    setShowStreets(false)
  }, [])

  // 处理当前定位
  const handleCurrentLocation = useCallback(() => {
    Taro.showLoading({ title: '定位中...' })

    Taro.getLocation({
      type: 'gcj02',
      success: (res) => {
        // 使用腾讯地图的逆地理编码API获取城市名称
        fetchLocationData(
          MAP_API.GEOCODER,
          { location: `${res?.latitude},${res?.longitude}`, get_poi: 0 },
          (result) => {
            Taro.hideLoading()
            if (result) {
              const addressComponent = result.address_component
              const provinceName = addressComponent.province
              const cityName = addressComponent.city
              const districtName = addressComponent.district
              const streetName = addressComponent.street || ''

              if (cityName) {
                // 直辖市定位处理
                if (MUNICIPALITIES.includes(cityName)) {
                  const fullAddress = formatFullAddress(provinceName, cityName, districtName, streetName)
                  onSelect(fullAddress)
                  onClose()
                } else {
                  // 普通城市定位处理
                  setActiveProvince(provinceName)
                  const fullAddress = formatFullAddress(provinceName, cityName, districtName, streetName)
                  onSelect(fullAddress)
                  onClose()
                }
              } else {
                Taro.showToast({
                  title: '当前城市暂不支持服务，请选择其他城市',
                  icon: 'none'
                })
              }
            }
          }
        )
      },
      fail: (err) => {
        Taro.hideLoading()
        console.error('定位失败:', err)

        Taro.showModal({
          title: '定位权限',
          content: '需要获取您的地理位置，请授权使用位置信息',
          success: (res) => {
            if (res.confirm) {
              Taro.openSetting()
            } else {
              Taro.showToast({
                title: '请手动选择城市',
                icon: 'none'
              })
            }
          }
        })
      }
    })
  }, [fetchLocationData, formatFullAddress, onSelect, onClose])

  // 初始化数据
  useEffect(() => {
    if (visible) {
      fetchProvinces()
    }
  }, [visible, fetchProvinces])

  // 如果不可见，不渲染内容
  if (!visible) return null

  // 渲染城市选择器主体
  return (
    <View className='city-selector-overlay' onClick={onClose}>
      <View className='city-selector-modal' onClick={(e) => e.stopPropagation()}>
        {!showDistricts && !showStreets ? (
          // 城市选择界面
          <>
            <View className='city-selector-header'>
              <Text className='city-selector-title'>选择城市</Text>
              <View className='city-selector-close' onClick={onClose}>×</View>
            </View>

            {/* 当前定位 */}
            <View className='city-selector-section'>
              <View className='city-selector-section-title'>当前定位</View>
              <View className='city-selector-current' onClick={handleCurrentLocation}>
                <View className='location-icon'>📍</View>
                <Text className='location-text'>自动定位</Text>
              </View>
            </View>

            {/* 按省份分类 */}
            <View className='city-selector-section'>
              <View className='city-selector-section-title'>按省份选择</View>
              {/* 省份列表 */}
              <ScrollView
                className='city-selector-list province-scroll-container'
                scrollX
                style={{ whiteSpace: 'nowrap' }}
              >
                {provinceList.map((province) => (
                  <View
                    key={province.id}
                    className={`province-item ${activeProvince === province.fullname ? 'active' : ''}`}
                    onClick={() => handleProvinceSelect(province)}
                  >
                    <Text className='province-item-text'>{province.fullname}</Text>
                  </View>
                ))}
              </ScrollView>

              {/* 城市列表 */}
              <ScrollView
                className='city-selector-list'
                scrollY
                style={{ height: '90vh' }}
              >
                {activeProvince && cityList.length > 0 ? (
                  // 显示选中省份的城市
                  cityList.map((cityGroup) => (
                    <View key={activeProvince} className='province-cities'>
                      <View className='province-title'>{activeProvince}</View>
                      <View className='city-selector-grid'>
                        {cityGroup.map((city) => (
                          <View
                            key={city.id}
                            className={`city-selector-item ${currentCity?.startsWith(city.fullname) ? 'active' : ''}`}
                            onClick={() => handleCitySelect(city)}
                          >
                            <Text className='city-selector-item-text'>{city.fullname}</Text>
                          </View>
                        ))}
                      </View>
                    </View>
                  ))
                ) : (
                  <View className='empty-cities-message'>
                    {activeProvince ? '加载城市列表中...' : '请选择省份'}
                  </View>
                )}
              </ScrollView>
            </View>
          </>
        ) : showDistricts && !showStreets ? (
          // 区县选择界面
          <>
            <View className='city-selector-header'>
              <View className='city-selector-back' onClick={handleBackToCity}>
                <Text className='back-icon'>←</Text>
                <Text className='back-text'>返回</Text>
              </View>
              <Text className='city-selector-title'>选择区县</Text>
              <View className='city-selector-close' onClick={onClose}>×</View>
            </View>

            <View className='city-selector-section'>
              <View className='city-selector-section-title'>
                {MUNICIPALITIES.includes(selectedCity) ? `${selectedCity}各区县` : selectedCity}
              </View>
              <View className='city-selector-grid'>
                {currentDistricts.map((district) => (
                  <View
                    key={district.id}
                    className={`city-selector-item ${currentCity === `${selectedCity} ${district.fullname}` ? 'active' : ''}`}
                    onClick={() => handleDistrictSelect(district)}
                  >
                    <Text className='city-selector-item-text'>{district.fullname}</Text>
                  </View>
                ))}
              </View>
            </View>
          </>
        ) : (
          // 街道选择界面
          <>
            <View className='city-selector-header'>
              <View className='city-selector-back' onClick={handleBackToDistrict}>
                <Text className='back-icon'>←</Text>
                <Text className='back-text'>返回</Text>
              </View>
              <Text className='city-selector-title'>选择街道</Text>
              <View className='city-selector-close' onClick={onClose}>×</View>
            </View>

            <View className='city-selector-section'>
              <View className='city-selector-section-title'>{selectedDistrict}</View>
              <View className='city-selector-grid'>
                {currentStreets.map((street) => (
                  <View
                    key={street.id}
                    className='city-selector-item'
                    onClick={() => handleStreetSelect(street.fullname)}
                  >
                    <Text className='city-selector-item-text'>{street.fullname}</Text>
                  </View>
                ))}
              </View>
            </View>
          </>
        )}
      </View>
    </View>
  )
}

export default CitySelector

