'use strict'

const _ = require('lodash')
const moment = require('moment')

const { DiscoveryComment } = require('../../../Models')
const { ERR, E } = require('../../../../../../constants')
const { CryptUtil } = require('../../../Util')
const Env = use('Env')

//帖子评论
class DiscoveryCommentController {
  //获取帖子评论列表
  async index({ request, response }) {
    let { discoveryID } = request.all()
    if (!discoveryID) {
      throw ERR.INVALID_PARAMS
    }
    let vo = await DiscoveryComment.query()
      .where('discoveryID', discoveryID)
      .fetch()
    response.json(vo)
  }
  //审核评论
  async update({ request, params, response }) {
    let vo = await DiscoveryComment.find(params.id)
    if (!vo) {
      throw ERR.RESTFUL_UPDATE_ID
    }
    _.assign(vo, request.all())
    await vo.save()
    response.json(vo)
  }
}

module.exports = DiscoveryCommentController
